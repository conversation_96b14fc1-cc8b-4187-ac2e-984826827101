package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
public class K3UltimateResponseBySave3 implements Serializable {
    private DataModel data;
    private String errorCode;
    private String message;
    private boolean status;

    @Data
    public static class DataModel implements Serializable {
        private int failCount;
        private int successCount;
        private int totalCount;
        private List<ResultModel> result;
    }

    @Data
    public static class ResultModel implements Serializable {
        /**
         * 单据索引，适用于批量新增或更新，这个是和入参的顺序保持一致
         */
        private int billIndex;
        /**
         * 单据状态
         */
        private boolean billStatus;
        /**
         * 错误信息，这里不一样
         */
        private List<String> errors;
        /**
         * 新增或更新成功，返回单据id
         */
        private String id;
        /**
         * 候选键字段apiName列表
         */
        private Map<String, String> keys;
        /**
         * 新增或更新成功，返回单据编码
         */
        private String number;
        /**
         * 代表新增(Add)或更新(Update)动作
         */
        private String type;
    }
}