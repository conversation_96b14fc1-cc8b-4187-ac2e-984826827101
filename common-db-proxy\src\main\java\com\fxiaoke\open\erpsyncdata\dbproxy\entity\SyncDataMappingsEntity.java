package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 源企业与目标企业关联关系实例
 */
@Data
@DataId("sourceDataId")
@ObjApiName("sourceObjectApiName")
@Table(name = "sync_data_mappings")
public class SyncDataMappingsEntity extends BaseEntity implements Serializable {
    /** 关联关系主键id */
    private String id;
    /**
     * 企业id
     */
    private String tenantId;
    /** 目标企业主键id
     * @deprecated 第一步先将查询source_tenant_id去除，全网后才能完全去掉代码中的
     * */
    @Deprecated
    private String sourceTenantId;
    /** 目标企业id
     * @deprecated 第一步先将查询source_tenant_id去除，全网后才能完全去掉代码中的
     * */
    @Deprecated
    private String destTenantId;
    /** 源企业主对象apiName */
    private String sourceObjectApiName;
    /** 源数据id */
    private String sourceDataId;
    /** 源数据名称 */
    private String sourceDataName;
    /** 目标主对象apiName */
    private String destObjectApiName;
    /** 目标数据id */
    private String destDataId;
    /** 目标数据名称 */
    private String destDataName;
    /** 目标数据是否成功被创建 */
    private Boolean isCreated;
    /** 逻辑删除 */
    private Boolean isDeleted;
    /** 最近一次同步数据id */
    private String lastSyncDataId;
    /** 日志备注 */
    private String remark;
    /** 最近一次同步状态 {@link SyncDataStatusEnum} */
    private Integer lastSyncStatus;
    /** 最近一次同步版本号 */
    private Long lastSourceDataVserion;
    /** 主对象源数据id */
    private String masterDataId;

    public void setTenantId(String tenantId) {
        setAllTenantId(tenantId);
    }

    public void setAllTenantId(String tenantId) {
        this.tenantId = tenantId;
        this.sourceTenantId = tenantId;
        this.destTenantId = tenantId;
    }

    public String getSourceTenantId() {
        return tenantId;
    }

    public void setSourceTenantId(String sourceTenantId) {
        setAllTenantId(sourceTenantId);
    }

    public String getDestTenantId() {
        return tenantId;
    }

    public void setDestTenantId(String destTenantId) {
        setAllTenantId(destTenantId);
    }
}
