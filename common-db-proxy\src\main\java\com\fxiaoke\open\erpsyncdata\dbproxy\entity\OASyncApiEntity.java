package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * oa对接Api
 *
 * <AUTHOR>
 * @date 2021/3/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "oa_sync_api")
public class OASyncApiEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 对象apiName
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 事件类型
     */
    @Column(name = "event_type")
    private String eventType;

    /**
     * 数据模板
     */
    @Column(name = "data_template")
    private String dataTemplate;

    /**
     * 开启状态（1。开启 2.关闭）
     */
    @Column(name = "status")
    private String status;

    /**
     * url
     */
    @Column(name = "url")
    private String url;


    /**
     * 请求模式
     */
    @Column(name = "request_mode")
    private String requestMode;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}