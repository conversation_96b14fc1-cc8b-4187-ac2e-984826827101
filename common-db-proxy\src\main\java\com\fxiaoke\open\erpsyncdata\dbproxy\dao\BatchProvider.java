package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.common.Pair;
import com.github.mybatis.annotation.DynamicTypeHandler;
import com.github.mybatis.annotation.Increment;
import com.github.mybatis.handler.list.ListTypeHandler;
import com.github.mybatis.handler.set.SetTypeHandler;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.ibatis.jdbc.SQL;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.github.mybatis.provider.CrudProvider.FIELD_LEFT;
import static com.github.mybatis.provider.CrudProvider.FIELD_RIGHT;

/**
 * 批量
 * Created by 姬昂 on 16/7/6.
 */
public class BatchProvider {
    public static final String KEY = "value";
    public static final String COLUMN = "column";
    private static final String normalSql = new SQL().toString();
    private static final int ignoreModifier = Modifier.FINAL | Modifier.STATIC | Modifier.VOLATILE | Modifier.TRANSIENT;

    @SuppressWarnings("unchecked")
    public String batchUpdate(final Map<String, Object> map) throws Exception {
        List<Object> dataList = (List<Object>) map.get(KEY);
        if (null == dataList || dataList.isEmpty()) {
            return normalSql;
        }
        Object obj = dataList.get(0);
        PersistMeta meta = EntityUtil.getMeta(obj.getClass());
        if (meta.getColumns() == null) {
            return normalSql;
        }

        // 忽略值都为null的列，忽略不更新的列
        Map<String, Field> names = Maps.newHashMap();
        Multimap<String, Integer> indices = ArrayListMultimap.create(names.size(), dataList.size());
        Multimap<String, Object> uniq = HashMultimap.create();
        meta.getColumns().forEach((name, field) -> {
            // 索引和自增序列单独处理
            if (field.isAnnotationPresent(Increment.class)) {
                return;
            }
            if (field.isAnnotationPresent(Id.class)) {
                dataList.forEach(d -> uniq.put(name, getValue(field, d)));
                return;
            }
            if ((ignoreModifier & field.getModifiers()) > 0) {
                return;
            }
            //解决代码覆盖率植入变量问题:$jacoco_data
            if (field.getName().startsWith("$")) {
                return;
            }
            // 找到不为null的列所在的行索引
            for (int i = 0; i < dataList.size(); i++) {
                if (!isNull(field, dataList.get(i))) {
                    names.putIfAbsent(name, field);
                    indices.put(name, i);
                }
            }
        });

        // 必须包含id字段，否则就会出问题，做全表更新了
        int size = uniq.keySet().size();
        if (size < 1) {
            return normalSql;
        }

        Pair<String, String> pair;
        if (size == 1) {
            // 只有1个id列
            pair = update1(dataList, meta, names, indices, uniq);
        } else {
            if (size == 2) {
                Pair<String, Object> option1 = null;
                Pair<String, Collection<Object>> option2 = null;

                Iterator<Map.Entry<String, Collection<Object>>> it = uniq.asMap().entrySet().iterator();
                Map.Entry<String, Collection<Object>> first = it.next();
                Map.Entry<String, Collection<Object>> second = it.next();
                // 优先选择id列作为option2
                String idColumnName = meta.getIdColumnName();
                if (idColumnName.equals(first.getKey()) && second.getValue().size() == 1) {
                    option1 = Pair.build(second.getKey(), second.getValue().iterator().next());
                    option2 = Pair.build(first.getKey(), first.getValue());
                } else if (idColumnName.equals(second.getKey()) && first.getValue().size() == 1) {
                    option1 = Pair.build(first.getKey(), first.getValue().iterator().next());
                    option2 = Pair.build(second.getKey(), second.getValue());
                }
                if (option1 != null) {
                    // 有多个Id列，但是其中一列只有一个值
                    pair = update2(dataList, meta, names, indices, option1, option2);
                } else {
                    pair = update3(dataList, meta, names, indices);
                }
            } else {
                pair = update3(dataList, meta, names, indices);
            }
        }

        return new SQL().UPDATE(getTableName(meta, obj)).SET(pair.first).WHERE(pair.second).toString();
    }

    /**
     * 优化常见的只有1个Id列的情形
     *
     * @throws IllegalAccessException
     */
    private Pair<String, String> update1(List<Object> dataList, PersistMeta meta, Map<String, Field> names, Multimap<String, Integer> indices,
                                         Multimap<String, Object> uniq) throws IllegalAccessException {
        StringBuilder setSql = new StringBuilder(names.size() * 64);
        int num = 0;
        String idColumnName = meta.getIdColumnName();
        Field idField = meta.getIdField();
        for (Map.Entry<String, Field> mapEntry : names.entrySet()) {
            String name = mapEntry.getKey();
            Field field = mapEntry.getValue();
            setSql.append('\n')
                  .append(FIELD_LEFT)
                  .append(name)
                  .append(FIELD_RIGHT)
                  .append(" =\nCASE ")
                  .append(FIELD_LEFT)
                  .append(idColumnName)
                  .append(FIELD_RIGHT)
                  .append('\n');
            for (Integer idx : indices.get(name)) {
                setSql.append(" WHEN ");
                Object val = idField.get(dataList.get(idx));
                if (val instanceof Number) {
                    setSql.append(val);
                } else {
                    setSql.append('\'').append(StringEscapeUtils.escapeSql(String.valueOf(val))).append('\'');
                }
                setSql.append(" THEN #{value[").append(idx).append("].").append(field.getName());
                DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
                if (typeHandler == null) {
                    if (field.getType().isAssignableFrom(List.class)) {
                        setSql.append(",typeHandler=").append(ListTypeHandler.class.getName()).append("}\n");
                    } else if (field.getType().isAssignableFrom(Set.class)) {
                        setSql.append(",typeHandler=").append(SetTypeHandler.class.getName()).append("}\n");
                    } else {
                        setSql.append("}\n");
                    }
                } else {
                    setSql.append(",typeHandler=").append(typeHandler.value()).append("}\n");
                }
            }
            setSql.append(" ELSE ").append(FIELD_LEFT).append(name).append(FIELD_RIGHT).append("\nEND");
            if (++num < names.size()) {
                setSql.append(',');
            }
        }
        String incrementColumnName = meta.getIncrementColumnName();
        if (incrementColumnName != null) {
            setSql.append(",\n").append(FIELD_LEFT).append(incrementColumnName).append(FIELD_RIGHT).append(" = ").append(incrementColumnName).append("+1\n");
        }

        StringBuilder where = new StringBuilder(names.size() * 16);
        where.append(FIELD_LEFT).append(idColumnName).append(FIELD_RIGHT).append(" IN (");
        uniq.values().forEach(id -> {
            if (id instanceof Number) {
                where.append(id).append(',');
            } else {
                where.append('\'').append(StringEscapeUtils.escapeSql(String.valueOf(id))).append("\',");
            }
        });
        where.setCharAt(where.length() - 1, ')');
        return Pair.build(setSql.toString(), where.toString());
    }

    /**
     * 优化常见的1个tenant_id，多个Id的情形
     *
     * @throws IllegalAccessException
     */
    private Pair<String, String> update2(List<Object> dataList, PersistMeta meta, Map<String, Field> names, Multimap<String, Integer> indices,
                                         Pair<String, Object> option1, Pair<String, Collection<Object>> idSet) throws IllegalAccessException {
        StringBuilder setSql = new StringBuilder(names.size() * 64);
        int num = 0;
        String idColumnName = meta.getIdColumnName();
        Field idField = meta.getIdField();
        for (Map.Entry<String, Field> mapEntry : names.entrySet()) {
            String name = mapEntry.getKey();
            Field field = mapEntry.getValue();
            setSql.append('\n')
                  .append(FIELD_LEFT)
                  .append(name)
                  .append(FIELD_RIGHT)
                  .append(" =\nCASE ")
                  .append(FIELD_LEFT)
                  .append(idColumnName)
                  .append(FIELD_RIGHT)
                  .append('\n');
            for (Integer idx : indices.get(name)) {
                setSql.append(" WHEN ");
                Object val = idField.get(dataList.get(idx));
                if (val instanceof Number) {
                    setSql.append(val);
                } else {
                    setSql.append('\'').append(StringEscapeUtils.escapeSql(String.valueOf(val))).append('\'');
                }
                setSql.append(" THEN #{value[").append(idx).append("].").append(field.getName());
                DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
                if (typeHandler == null) {
                    if (field.getType().isAssignableFrom(List.class)) {
                        setSql.append(",typeHandler=").append(ListTypeHandler.class.getName()).append("}\n");
                    } else if (field.getType().isAssignableFrom(Set.class)) {
                        setSql.append(",typeHandler=").append(SetTypeHandler.class.getName()).append("}\n");
                    } else {
                        setSql.append("}\n");
                    }
                } else {
                    setSql.append(",typeHandler=").append(typeHandler.value()).append("}\n");
                }
            }
            setSql.append(" ELSE ").append(FIELD_LEFT).append(name).append(FIELD_RIGHT).append("\nEND");
            if (++num < names.size()) {
                setSql.append(',');
            }
        }
        String incrementColumnName = meta.getIncrementColumnName();
        if (incrementColumnName != null) {
            setSql.append(",\n").append(FIELD_LEFT).append(incrementColumnName).append(FIELD_RIGHT).append(" = ").append(incrementColumnName).append("+1\n");
        }

        StringBuilder where = new StringBuilder(names.size() * 16);
        where.append(FIELD_LEFT).append(option1.first).append(FIELD_RIGHT).append('=');
        Object val = option1.second;
        if (val instanceof Number) {
            where.append(val).append(" AND ");
        } else {
            where.append('\'').append(StringEscapeUtils.escapeSql(String.valueOf(val))).append('\'').append(" AND ");
        }
        where.append(FIELD_LEFT).append(idColumnName).append(FIELD_RIGHT).append(" IN (");
        idSet.second.forEach(id -> {
            if (id instanceof Number) {
                where.append(id).append(',');
            } else {
                where.append('\'').append(StringEscapeUtils.escapeSql(String.valueOf(id))).append("\',");
            }
        });
        where.setCharAt(where.length() - 1, ')');
        return Pair.build(setSql.toString(), where.toString());
    }

    private Pair<String, String> update3(List<Object> dataList, PersistMeta meta, Map<String, Field> names,
                                         Multimap<String, Integer> indices) throws IllegalAccessException {
        Map<String, Field> pks = meta.getPkColumns();
        StringBuilder setSql = new StringBuilder(names.size() * 64);
        Set<String> conditions = Sets.newHashSet();

        int num = 0;
        for (Map.Entry<String, Field> mapEntry : names.entrySet()) {
            String name = mapEntry.getKey();
            Field field = mapEntry.getValue();
            setSql.append('\n').append(FIELD_LEFT).append(name).append(FIELD_RIGHT).append(" =\nCASE\n");
            for (Integer idx : indices.get(name)) {
                setSql.append(" WHEN ");
                int count = 0;
                StringBuilder when = new StringBuilder(64);
                for (Map.Entry<String, Field> entry : pks.entrySet()) {
                    String k = entry.getKey();
                    Field f = entry.getValue();
                    when.append(k).append('=');
                    Object val = f.get(dataList.get(idx));
                    if (val instanceof Number) {
                        when.append(val);
                    } else {
                        when.append('\'').append(StringEscapeUtils.escapeSql(String.valueOf(val))).append('\'');
                    }
                    if (++count < pks.size()) {
                        when.append(" AND ");
                    }
                }
                conditions.add(when.toString());
                setSql.append(when);
                setSql.append(" THEN #{value[").append(idx).append("].").append(field.getName());
                DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
                if (typeHandler == null) {
                    if (field.getType().isAssignableFrom(List.class)) {
                        setSql.append(",typeHandler=").append(ListTypeHandler.class.getName()).append("}\n");
                    } else if (field.getType().isAssignableFrom(Set.class)) {
                        setSql.append(",typeHandler=").append(SetTypeHandler.class.getName()).append("}\n");
                    } else {
                        setSql.append("}\n");
                    }
                } else {
                    setSql.append(",typeHandler=").append(typeHandler.value()).append("}\n");
                }
            }
            setSql.append(" ELSE ").append(FIELD_LEFT).append(name).append(FIELD_RIGHT).append("\nEND");
            if (++num < names.size()) {
                setSql.append(',');
            }
        }
        String incrementColumnName = meta.getIncrementColumnName();
        if (incrementColumnName != null) {
            setSql.append(",\n").append(FIELD_LEFT).append(incrementColumnName).append(FIELD_RIGHT).append(" = ").append(incrementColumnName).append("+1\n");
        }
        String where = conditions.stream().collect(Collectors.joining(") \n OR \n (", "\n(", ")\n"));
        return Pair.build(setSql.toString(), where);
    }

    @SuppressWarnings("unchecked")
    public String batchInsert(final Map<String, Object> map) {
        List dataList = (List) map.get(KEY);
        if (null == dataList || dataList.isEmpty()) {
            return normalSql;
        }
        Object obj = dataList.get(0);
        PersistMeta meta = EntityUtil.getMeta(obj.getClass());
        StringBuilder fields = new StringBuilder();
        StringBuilder template = new StringBuilder();
        template.append('(');
        int i = 0;

        Map<String, Field> columnMap = Maps.newHashMap();
        dataList.forEach(row -> {
            for (Map.Entry<String, Field> column : meta.getColumns().entrySet()) {
                if (columnMap.containsKey(column.getKey())) {
                    continue;
                }
                if (!isNull(column.getValue(), row)) {
                    columnMap.putIfAbsent(column.getKey(), column.getValue());
                }
            }
        });

        for (Map.Entry<String, Field> kv : columnMap.entrySet()) {
            if (i++ != 0) {
                fields.append(',');
                template.append(',');
            }
            fields.append(kv.getKey());
            Field field = kv.getValue();
            DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
            if (typeHandler == null) {
                if (field.getType().isAssignableFrom(List.class)) {
                    template.append("#'{'value[{0}].").append(field.getName()).append(",typeHandler=").append(ListTypeHandler.class.getName()).append("'}'");
                } else if (field.getType().isAssignableFrom(Set.class)) {
                    template.append("#'{'value[{0}].").append(field.getName()).append(",typeHandler=").append(SetTypeHandler.class.getName()).append("'}'");
                } else {
                    template.append("#'{'value[{0}].").append(field.getName()).append("'}'");
                }
            } else {
                template.append("#'{'value[{0}].").append(field.getName()).append(",typeHandler=").append(typeHandler.value()).append("'}'");
            }
        }
        template.append(')');
        StringBuilder insertSql = new StringBuilder();
        insertSql.append("INSERT INTO ").append(getTableName(meta, obj)).append('(').append(fields).append(')').append(" VALUES ");
        MessageFormat mf = new MessageFormat(template.toString());
        for (int j = 0; j < dataList.size(); j++) {
            if (j != 0) {
                insertSql.append(',');
            }
            insertSql.append(mf.format(new String[] {String.valueOf(j)}));
        }
        return insertSql.toString();
    }

    public String findBatchIds(final Map<String, Object> parameter) {
        List<?> dataList = (List<?>) parameter.get(KEY);
        if (null == dataList || dataList.isEmpty()) {
            return null;
        }
        Class<?> clazz = (Class<?>) parameter.get(CrudProvider.CLASS_KEY);
        String column = (String) parameter.get(COLUMN);
        String where;
        if (dataList.iterator().next() instanceof Number) {
            where = column + " IN (" + Joiner.on(',').join(dataList) + ')';
        } else {
            where =
              column + " IN ('" + Joiner.on("','").join(dataList.stream().map(id -> StringEscapeUtils.escapeSql(String.valueOf(id))).collect(Collectors.toList())) +
                "')";
        }
        return new SQL().SELECT("*").FROM(EntityUtil.getTableName(clazz)).WHERE(where).toString();
    }

    private String getTableName(PersistMeta meta, Object obj) {
        if (meta.getPostfix() != null) {
            try {
                return meta.getTableName() + '_' + meta.getPostfix().invoke(obj);
            } catch (Exception e) {
                throw new RuntimeException("cannot get postfix: " + meta.getPostfix() + ", error: ", e);
            }
        }
        return meta.getTableName();
    }

    private boolean isNull(Field field, Object obj) {
        try {
            return field.get(obj) == null;
        } catch (IllegalAccessException e) {
            return true;
        }
    }

    private Object getValue(Field field, Object obj) {
        try {
            return field.get(obj);
        } catch (IllegalAccessException e) {
            return null;
        }
    }
}