package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 辅助资料特殊逻辑处理
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
@Slf4j
@Component("BOS_ASSISTANTDATA_DETAIL")
public class AssistantDataSpecialBusinessImpl implements SpecialBusiness {
    @Autowired
    private CommonBusinessManager commonBusinessManager;


    /**
     * 查看erp数据后置动作
     * 辅助资料增加上级辅助资料编码
     *
     * @param erpIdArg
     * @param standardData
     * @param apiClient
     */
    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        if (standardData == null) {
            return;
        }
        ObjectData masterFieldVal = standardData.getMasterFieldVal();
        if (masterFieldVal != null) {
            String parentId = masterFieldVal.getString("FParentId");
            if (StringUtils.isNotBlank(parentId)) {
                try {
                    String parentNumber = commonBusinessManager.getNumberById("BOS_ASSISTANTDATA_DETAIL", parentId,
                            "FEntryID", "FNumber", apiClient);
                    masterFieldVal.put("FParentNumber", parentNumber);
                } catch (Exception e) {
                    log.error("get number by id error,", e);
                }
            }
            return;
        }
        log.warn("masterFieldVal return null");
    }
}
