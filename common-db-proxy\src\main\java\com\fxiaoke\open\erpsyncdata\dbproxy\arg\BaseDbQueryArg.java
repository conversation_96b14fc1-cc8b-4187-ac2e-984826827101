package com.fxiaoke.open.erpsyncdata.dbproxy.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/3/4 17:56
 * @desc
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseDbQueryArg implements Serializable {
    private String tenantId;
    private String dataCenterId;
}
