package com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CellStyleData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomCellWriteHandler implements CellWriteHandler {
    private I18NStringManager i18NStringManager;
    private String tenantId;
    private String lang;
    /**
     * 单元格样式对照表，支持自定义表头
     */
    private Map<String, CellStyleData> headCellStyleMap;

    public CustomCellWriteHandler(I18NStringManager i18NStringManager, String tenantId, String lang) {
        this(i18NStringManager, tenantId, lang, new HashMap<>());
    }

    public CustomCellWriteHandler(I18NStringManager i18NStringManager, String tenantId, String lang, Map<String, CellStyleData> headCellStyleMap) {
        this.i18NStringManager = i18NStringManager;
        this.tenantId = tenantId;
        this.lang = lang;
        this.headCellStyleMap = headCellStyleMap;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder,
                                 WriteTableHolder writeTableHolder,
                                 Row row,
                                 Head head,
                                 Integer integer,
                                 Integer integer1,
                                 Boolean aBoolean) {
        if(head!=null) {
            List<String> headNameList = head.getHeadNameList();
            if(CollectionUtils.isNotEmpty(headNameList)) {
                //多级表头需要遍历
                for(int i = 0; i < headNameList.size(); i++) {
                    String i18nKey = headNameList.get(i);
                    headNameList.set(i,i18NStringManager.get(i18nKey,lang,tenantId,i18nKey));
                }
            }
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                WriteTableHolder writeTableHolder,
                                Cell cell,
                                Head head,
                                Integer integer,
                                Boolean aBoolean) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder,
                                       WriteTableHolder writeTableHolder,
                                       CellData cellData,
                                       Cell cell,
                                       Head head,
                                       Integer integer,
                                       Boolean aBoolean) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder,
                                 WriteTableHolder writeTableHolder,
                                 List<CellData> list,
                                 Cell cell,
                                 Head head,
                                 Integer integer,
                                 Boolean aBoolean) {
        // 仅在处理表头单元格时设置样式
        if (Boolean.TRUE.equals(aBoolean)
                && ObjectUtils.isNotEmpty(headCellStyleMap)
                && !headCellStyleMap.isEmpty()) {
            if(ObjectUtils.isNotEmpty(cell)
                    && StringUtils.isNotEmpty(cell.getStringCellValue())
                    && headCellStyleMap.containsKey(cell.getStringCellValue())) {
                Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
                CellStyle cellStyle = workbook.createCellStyle();
                BeanUtils.copyProperties(cell.getCellStyle(), cellStyle);
                if(ObjectUtils.isNotEmpty(headCellStyleMap.get(cell.getStringCellValue()).getHorizontalAlignment())) {
                    cellStyle.setAlignment(headCellStyleMap.get(cell.getStringCellValue()).getHorizontalAlignment());
                }
                if(ObjectUtils.isNotEmpty(headCellStyleMap.get(cell.getStringCellValue()).getVerticalAlignment())) {
                    // 设置单元格为垂直居中对齐
                    cellStyle.setVerticalAlignment(headCellStyleMap.get(cell.getStringCellValue()).getVerticalAlignment());
                }
                if(ObjectUtils.isNotEmpty(headCellStyleMap.get(cell.getStringCellValue()).getIndexedColors())) {
                    cellStyle.setFillForegroundColor(headCellStyleMap.get(cell.getStringCellValue()).getIndexedColors().getIndex());
                }
                if(ObjectUtils.isNotEmpty(headCellStyleMap.get(cell.getStringCellValue()).getFillPatternType())) {
                    cellStyle.setFillPattern(headCellStyleMap.get(cell.getStringCellValue()).getFillPatternType());
                }
                cell.setCellStyle(cellStyle);
            }
        }
    }
}
