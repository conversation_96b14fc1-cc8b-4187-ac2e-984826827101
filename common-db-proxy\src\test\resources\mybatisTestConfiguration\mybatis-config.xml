<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="false"/>
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="3600"/>
        <setting name="safeRowBoundsEnabled" value="false"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="localCacheScope" value="SESSION"/>
        <setting name="jdbcTypeForNull" value="OTHER"/>
        <!--打印mybatis日志-->
        <setting name="logImpl" value="STDOUT_LOGGING"/>
        <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
    </settings>
    <typeHandlers>
        <typeHandler javaType="string"
                     handler="com.github.mybatis.handler.StringTypeUtf8mb4Handler"/>
        <package name="com.fxiaoke.open.erpsyncdata.dbproxy.dao.typehandler"/>
    </typeHandlers>
    <plugins>
        <plugin interceptor="com.github.mybatis.interceptor.MasterSlaveInterceptor"/>
        <plugin interceptor="com.github.mybatis.interceptor.PaginationAutoMapInterceptor"/>
        <plugin interceptor="com.fxiaoke.open.erpsyncdata.common.interceptor.TenantShardingTableInterceptor"/>
        <plugin interceptor="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.EncryptInterceptor">
        </plugin>
        <plugin interceptor="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.DecryptInterceptor">
        </plugin>
    </plugins>
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="org.h2.Driver"/>
                <property name="driver" value="org.h2.Driver"/>
                <property name="url"
                          value="jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH"/>
                <property name="username" value="sa"/>
                <property name="password" value=""/>
            </dataSource>
        </environment>
    </environments>
    <!--dao路径-->
    <mappers>
        <!--需要一个个加，设置包不行-->
        <mapper resource="mapper/SyncDataMappingsDao.xml"/>
    </mappers>

</configuration>
