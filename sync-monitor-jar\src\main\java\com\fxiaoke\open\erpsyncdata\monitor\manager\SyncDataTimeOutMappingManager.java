package com.fxiaoke.open.erpsyncdata.monitor.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @Date 2023/1/13 18:00 好丽友的同步中的数据
 * @Version 1.0
 */
@Component
@Slf4j
public class SyncDataTimeOutMappingManager {

    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private CHSyncDataManager chSyncDataManager;
    @Autowired
    private AlertAndBreakManager alertAndBreakManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;

//    public Result<Void> syncDataTimeOutUpdateStatusToError(String tenantId, Long startUpdateTime, Long endUpdateTime) {
//        log.info("update error params:{}.{}.{}",tenantId,startUpdateTime,endUpdateTime);
//        //获取需要被扫描的集成流的对象apiName
//        List<Pair<String, String>> nameList = syncPloyDetailManager.queryEnableIntegrationStream(tenantId);
//        for (Pair<String, String> sourceDestApiName : nameList) {
//            int limit = 1000;
//            for (int i = 0; i < 10; i++) {
//                //一次最多1000*10条数据
//                if (i == 5) {
//                    log.warn("code has error ,or too many waiting data");
//                }
//                //startUpdateTime不使用了，开始时间是0
//                List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(tenantId).listByStatusTimeOutMappings(tenantId, 0L, endUpdateTime, sourceDestApiName.getLeft(), sourceDestApiName.getRight(), limit, 0);
//                log.info("syncDataMappingsEntities size:{}",syncDataMappingsEntities.size());
//                if (CollectionUtils.isEmpty(syncDataMappingsEntities)) {
//                    break;
//                }
//                syncDataMappingsEntities= syncDataMappingsEntities.stream().filter(item ->!item.getLastSyncStatus().equals(SyncDataStatusEnum.IGNORE.getStatus())).collect(Collectors.toList());
//                log.info("filter syncDataMappingsEntities size:{}",syncDataMappingsEntities.size());
//                Map<String, SyncDataMappingsEntity> syncDataMappingsEntityMap = syncDataMappingsEntities.stream().collect(Collectors.toMap((SyncDataMappingsEntity::getLastSyncDataId), v -> v, (u, v) -> u));
//
//                List<SyncDataMappingsEntity> subtractSyncDataEntity=Lists.newArrayList();//快照不存在的数据
//                List<String> syncDataEntityId = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getLastSyncDataId).collect(Collectors.toList());
//                List<String> mappingIds = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
//                List<SyncDataEntity> syncDataEntityList = adminSyncDataDao.setTenantId(tenantId).listByIds(tenantId, syncDataEntityId);
//                //批量更新中间表失败
//                // adminSyncDataMappingsDao.setTenantId(tenantId).batchUpdateByIds(tenantId, Lists.newArrayList(mappingIds), SyncDataStatusEnum.PROCESS_FAILED.getStatus(), null, "处理失败", System.currentTimeMillis());
//                //查找哪些数据快照没有保存成功的
//                if(syncDataEntityId.size()>syncDataEntityList.size()){
//                    List<String> dbSyncDataEntityIds = syncDataEntityList.stream().map(SyncDataEntity::getId).collect(Collectors.toList());
//                    List<String> subtractSyncDataEntityIds = (List<String>) CollectionUtils.subtract(syncDataEntityId, dbSyncDataEntityIds);
//                    subtractSyncDataEntityIds.forEach(item ->subtractSyncDataEntity.add(syncDataMappingsEntityMap.get(item)));
//                }
//
//                //                List<SyncDataEntity> syncDataEntityList = adminSyncDataDao.setTenantId(tenantId).listByStatusListAndEndUpdateTime(tenantId, idDoingStatus, 0L, endUpdateTime, 0, limit);
//                if (syncDataEntityList.size() == 0) {
//                    break;
//                }
////                for (SyncDataEntity entity : syncDataEntityList) {
////                    int newStatus;
////                    String remark = "处理失败";
////                    newStatus = SyncDataStatusEnum.PROCESS_FAILED.getStatus();
////                    if (StringUtils.isNotBlank(entity.getRemark())) {
////                        remark += ":" + entity.getRemark();
////                    }
////                    this.updateToError(tenantId, entity.getId(), newStatus, -1, remark, entity.getSyncPloyDetailSnapshotId());
////                }
//                //重试
//                reSyncTimeOutSyncData(tenantId, syncDataEntityList,subtractSyncDataEntity);
//                try {
//                    TimeUnit.SECONDS.sleep(10);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//
//        }
//        return Result.newSuccess();
//    }

    public void updateToError(String tenantId, String syncDataId, Integer newStatus, Integer errCode, String errMsg, String snapId) {
        int success = chSyncDataManager.updateStatus(tenantId, syncDataId, newStatus, errMsg);

    }

//    private void reSyncTimeOutSyncData(String tenantId, List<SyncDataEntity> syncDataEntityList, List<SyncDataMappingsEntity> subtractSyncDataEntity) {
//        if (tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NOT_AUTO_SYNC_TENANTS)){
//            return;
//        }
//        List<BatchSendEventDataArg.EventData> eventDataList = Lists.newArrayList();
//        if (ConfigCenter.TIME_OUT_SYNC_DATA_TIME < 0) {//如果超时时间小于0，不重试
//            return;
//        }
//        SetParams params = SetParams.setParams().ex(ConfigCenter.TIME_OUT_SYNC_DATA_TIME).nx();//ex超时时间，nx不存在才set
//        SyncPloyDetailSnapshotEntity entryBySnapshotId = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, syncDataEntityList.get(0).getSyncPloyDetailSnapshotId());
//        String ployDetailId = entryBySnapshotId.getSyncPloyDetailId();
//        SyncPloyDetailEntity ployDetail = syncPloyDetailManager.getEntryById(tenantId, ployDetailId);
//        String dcId = ployDetail.getSourceDataCenterId();
//        if (ployDetail.getSourceTenantType().equals(TenantTypeEnum.CRM.getType())) {
//            dcId = ployDetail.getDestDataCenterId();
//        }
//        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
//        String dcName = connectInfo.getDataCenterName();
//        String streamName = ployDetail.getIntegrationStreamName();
//
//        List<String> sourceMasterData = Lists.newArrayList();
//        List<String> sourceDetailData = Lists.newArrayList();
//        for (SyncDataEntity entity : syncDataEntityList) {
//            StringBuilder redisKey = new StringBuilder();
//            redisKey.append(tenantId).append("_").append(entity.getSourceData().getApiName()).append("_").append(entity.getSourceData().getId());
//            String result = redisDataSource.get(this.getClass().getSimpleName()).set(redisKey.toString(), "1", params);
//            if (result == null) {//为空说明没有设置上，已存在
//                continue;
//            }
//            //好丽友的临时逻辑。通知到实施人员
//            if (entity.getSourceObjectApiName().equals(ployDetail.getSourceObjectApiName())) {
//                sourceMasterData.add(entity.getSourceDataId());
//            } else {
//                sourceDetailData.add(entity.getSourceDataId());
//            }
//        }
//        if(sourceDetailData.size()==0&&sourceMasterData.size()==0){
//            log.info("sync not data:{}");
//            return;
//        }
//        SendAdminNoticeArg sendAdminNoticeArg=SendAdminNoticeArg.builder().tenantId(tenantId).
//                msgTitle(i18NStringManager.getByEi(I18NStringEnum.s711,tenantId)).alwaysSendSuperAdmin(Boolean.TRUE).needFillPreDbName(true).hasSendTenantAdmin(true).dcId(dcId).build();
//        String sourceDataMsg = Joiner.on(";").join(sourceMasterData);
//        String detailDataMsg = Joiner.on(";").join(sourceDetailData);
//        StringBuilder msg =new StringBuilder()
//                .append(i18NStringManager.getByEi(I18NStringEnum.s712,tenantId))
//                .append(streamName).append("\n")
//                .append(i18NStringManager.getByEi(I18NStringEnum.s713,tenantId))
//                .append(sourceDataMsg)
//                .append("\n")
//                .append(i18NStringManager.getByEi(I18NStringEnum.s714,tenantId))
//                .append(detailDataMsg).append("\n");
//        sendAdminNoticeArg.setMsg(msg.toString());
//        sendAdminNoticeArg.setTenantId(tenantId);
//        sendAdminNoticeArg.setMsgTitle(i18NStringManager.getByEi(I18NStringEnum.s711,tenantId));
//        log.info("ERP数据长时间同步中数据通知:{}", JSONObject.toJSONString(sendAdminNoticeArg));
//        notificationService.sendTenantAdminNotice(sendAdminNoticeArg, NotificationType.ALERT);
//    }
//    private void triggerSyncDataEntity(SyncDataEntity syncDataEntity,Map<String, SyncDataMappingsEntity> syncDataMappingsEntityMap,String dcId){
//        if(syncDataEntity.getSourceTenantType().equals(TenantTypeEnum.CRM)){
//            SyncDataMappingsEntity syncDataMappingsEntity = syncDataMappingsEntityMap.get(syncDataEntity.getId());
//            String masterId=syncDataMappingsEntity.getSourceDataId();
//            SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(syncDataEntity.getTenantId(), syncDataEntity.getSyncPloyDetailSnapshotId());
//            String sourceObjectApiName=syncPloyDetailSnapshotEntity.getSourceObjectApiName();
//            if(StringUtils.isNotEmpty(syncDataMappingsEntity.getMasterDataId())){
//                masterId=syncDataMappingsEntity.getMasterDataId();
//            }
//            //获取crm的数据发布
//
//        }
//    }
//    private void triggerSyncDataMappins(SyncDataEntity syncDataEntity){
//
//    }

    public void recoverStatus(String tenantId){
        List<Pair<String, String>> nameList = syncPloyDetailManager.queryEnableIntegrationStream(tenantId);
        for (Pair<String, String> sourceDestApiName : nameList) {
            int limit = 1000;
            for (int i = 0; i < 10; i++) {
                //一次最多1000*10条数据
                if (i == 5) {
                    log.warn("code has error ,or too many waiting data");
                }
                //startUpdateTime不使用了，开始时间是0
                DateTime dateTime = DateUtil.parse("2023-01-16 00:00:00",NORM_DATETIME_PATTERN);
                DateTime endUpdateTime = DateUtil.parse("2023-01-17 14:00:00",NORM_DATETIME_PATTERN);

                List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(tenantId).listByStatusFailMappings(tenantId, dateTime.getTime(), endUpdateTime.getTime(), sourceDestApiName.getLeft(), sourceDestApiName.getRight(), limit, 0);
                log.info("syncDataMappingsEntities size:{}",syncDataMappingsEntities.size());
                if (CollectionUtils.isEmpty(syncDataMappingsEntities)) {
                    break;
                }
                log.info("filter syncDataMappingsEntities size:{}",syncDataMappingsEntities.size());

                List<String> syncDataEntityId = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getLastSyncDataId).collect(Collectors.toList());
                List<String> mappingIds = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
                List<SyncDataEntity> syncDataEntityList = adminSyncDataDao.setTenantId(tenantId).listByIds(tenantId, syncDataEntityId);
                //批量更新中间表失败
               syncDataMappingsDao.setTenantId(tenantId).batchUpdateByIds(tenantId,
                       Lists.newArrayList(mappingIds),
                       SyncDataStatusEnum.WRITE_SUCCESS.getStatus(),
                       null,
                       i18NStringManager.getByEi(I18NStringEnum.s1150,tenantId),
                       System.currentTimeMillis());
                //查找哪些数据快照没有保存成功的
                if (syncDataEntityList.size() == 0) {
                    continue;
                }
                for (SyncDataEntity entity : syncDataEntityList) {
                    int newStatus;
                    String remark = i18NStringManager.getByEi(I18NStringEnum.s1150,tenantId);
                    newStatus = SyncDataStatusEnum.WRITE_SUCCESS.getStatus();
                    if (StringUtils.isNotBlank(entity.getRemark())) {
                        remark += ":" + entity.getRemark();
                    }
                    this.updateToError(tenantId, entity.getId(), newStatus, -1, remark, entity.getSyncPloyDetailSnapshotId());
                }
                //重试
//                reSyncTimeOutSyncData(tenantId, syncDataEntityList,subtractSyncDataEntity);
                try {
                    TimeUnit.SECONDS.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

        }
    }


}
