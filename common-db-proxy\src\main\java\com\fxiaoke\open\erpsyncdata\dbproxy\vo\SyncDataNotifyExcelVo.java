package com.fxiaoke.open.erpsyncdata.dbproxy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 9:40 2020/9/17
 * @Desc:
 */
@Data
public class SyncDataNotifyExcelVo  implements Serializable {
    /**
     * 数据
     */
    @ExcelProperty("数据Id")
    private String dataId;
    /**
     * 数据
     */
    @ExcelProperty("数据名称")
    private String dataName;
    /**
     * 同步状态
     */
    @ExcelProperty("同步状态")
    private String statusMessage;

    /**
     * 状态详情
     */
    @ExcelProperty("状态详情")
    private String remark;



}
