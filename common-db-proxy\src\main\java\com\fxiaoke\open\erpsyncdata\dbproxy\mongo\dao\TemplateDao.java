package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.ChannelCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateDoc.Fields;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 资源
 *
 * <AUTHOR> (^_−)☆
 * @date 2022-11-17
 */
@Repository
@Slf4j
public class TemplateDao {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Qualifier("erpSyncDataMongoStore")
    @Autowired
    private DatastoreExt store;

    private static final String dbName = "erp_sync_data2";

    @PostConstruct
    void init() {
        createIndex();
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<TemplateDoc> coll = getColl();
        //标题不允许重复
        coll.createIndex(Indexes.ascending(Fields.title), new IndexOptions().unique(true).background(true));
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(
                        //我也不知道为什么这个枚举解析不了。。。。只能单独整一个codec
                        CodecRegistries.fromCodecs(new ChannelCodec()),
                        MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder()
                                .register(TemplateDoc.class)
                                .automatic(true).build()));
    }

    private MongoCollection<TemplateDoc> getColl() {
        MongoCollection<TemplateDoc> coll = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("template", TemplateDoc.class);
        return coll;
    }

    public List<TemplateDoc> findAll() {
        List<TemplateDoc> all = new ArrayList<>();
        getColl().find().into(all);
        return all;
    }

    public void upsertIgnore(TemplateDoc newDoc, String oldVersion) {
        try {
            upsertDoc(newDoc, oldVersion);
        } catch (Exception e) {
            log.info("upsert failed,msg:{}", e.getMessage());
        }
    }


    /**
     * 由于有统计数据，改名字也不能删数据，只能根据id替换
     *
     * @param newDoc
     */
    public void replaceById(TemplateDoc newDoc) {
        try {
            //根据名称替换
            getColl().replaceOne(Filters.eq(newDoc.getId()), newDoc, new ReplaceOptions().upsert(true));
        } catch (Exception e) {
            log.info("insert failed,msg:{}", e.getMessage());
        }
    }

    public void upsertDoc(TemplateDoc newDoc, String oldVersion) {
        //校验
        String version = newDoc.getVersion();
        if (StrUtil.isNotEmpty(version)) {
            //校验传输的版本
            String[] split = version.split("\\.");
            if (split.length < 2 || !NumberUtil.isInteger(split[0]) || !NumberUtil.isInteger(split[1])) {
                throw new ErpSyncDataException("version is invalid",null,null);
            }
        }
        MongoCollection<TemplateDoc> coll = getColl();
        Date updateTime = new Date();
        String today = DateUtil.format(updateTime, "yyyyMMdd");
        if (newDoc.getId() == null) {
            if (StrUtil.isEmpty(newDoc.getVersion())) {
                newDoc.setVersion("1.0." + today);
            }
            newDoc.setUpdateTime(updateTime);
            coll.insertOne(newDoc);
        } else {
            //更新
            if (StrUtil.isEmpty(newDoc.getVersion()) || newDoc.getVersion().equals(oldVersion)) {
                //更新版本号
                String[] split = oldVersion.split("\\.");
                String ver1 = split[0];
                String ver2 = split[1];
                //小版本+1
                ver2 = Integer.toString(Integer.parseInt(ver2) + 1);
                String version2 = String.format("%s.%s.%s", ver1, ver2, today);
                newDoc.setVersion(version2);
            }
            List<Bson> updates = CollUtil.newArrayList(Updates.set(Fields.version, newDoc.getVersion()),
                    Updates.currentDate(Fields.updateTime),
                    Updates.set(Fields.version, newDoc.getVersion()));
            Map<String, Object> docMap = BeanUtil.beanToMap(newDoc, false, true);
            docMap.forEach((k, v) -> {
                if (k.equals("id") || k.equals("updateTime")) {
                    return;
                }
                updates.add(Updates.set(k, v));
            });
            UpdateResult updateResult = coll.updateOne(
                    Filters.and(Filters.eq(newDoc.getId()),
                            Filters.eq(Fields.version, oldVersion)),
                    Updates.combine(updates));
            if (updateResult.getMatchedCount() < 1) {
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s3691, null, oldVersion),null,null);
            }
        }
    }

    public void updateOrder(List<String> ids) {
        List<UpdateOneModel<TemplateDoc>> updates = new ArrayList<>();
        for (int i = 0; i < ids.size(); i++) {
            ObjectId id = new ObjectId(ids.get(i));
            updates.add(new UpdateOneModel<>(Filters.eq(id), Updates.set(Fields.order, i * 10)));
        }
        BulkWriteResult bulkWriteResult = getColl().bulkWrite(updates);
    }

    /**
     * 查最新资源
     *
     * @param includeDetails 是否包含明细
     * @return
     */
    public TemplateDoc get(String dataId, boolean includeDetails) {
        FindIterable<TemplateDoc> findIter = getColl().find();
        if (!includeDetails) {
            findIter.projection(Projections.exclude(Fields.detailStr));
        }
        TemplateDoc first = findIter
                .filter(Filters.eq(new ObjectId(dataId)))
                .limit(1).first();
        return first;
    }

    /**
     * 获取所有模板
     *
     * @return
     */
    public List<TemplateDoc> listAllBaseInfo() {
        FindIterable<TemplateDoc> iterable = getColl().find()
                .projection(Projections.exclude(Fields.detailStr))
                .limit(2000);
        ArrayList<TemplateDoc> resultList = CollUtil.newArrayList(iterable);
        return resultList;
    }

    /**
     * 停用其他模板
     * @param disableIds
     */
    public void disableTemplates(Collection<ObjectId> disableIds) {
        List<Bson> updates = CollUtil.newArrayList(
                Updates.currentDate(Fields.updateTime),
                Updates.set(Fields.enable, false));
        getColl().updateMany(Filters.in(Fields.id, disableIds),Updates.combine(updates));
    }


}
