package com.fxiaoke.open.erpsyncdata.apiproxy.manager.dbproxy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpDbProxyConfigDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDBProxyConfigEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DBProxyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.Request;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class DBProxyV2Handler implements ConnectorDataHandler {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private ErpDbProxyConfigDao erpDbProxyConfigDao;

    private final static Map<String, Character> ID_SEPARATOR_MAP = ImmutableMap.of("dashed", CharPool.DASHED,
            "dot", CharPool.DOT, "underline", CharPool.UNDERLINE);

    public String loginAndGetCookie(InterfaceMonitorData interfaceMonitorData, String url, String username, String password, int tries) {
        long callTime = System.currentTimeMillis();
        try {
            FormBody.Builder formb = new FormBody.Builder();
            formb.add("username", username);
            formb.add("password", password);
            Request request = new Request.Builder().url(url)
                    .post(formb.build())
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .build();
            HttpRspLimitLenUtil.ResponseBodyModel responseBodyModel = proxyHttpClient.limitExecute(request);
            if (!responseBodyModel.isOk()) {
                if (tries <= 0) {
                    //没有重试次数了，直接异常
                    throw new ErpSyncDataException(ResultCodeEnum.CALL_OUT_HTTP_FAILED, i18NStringManager.getByEi2(I18NStringEnum.s3701, interfaceMonitorData.getTenantId(), responseBodyModel.buildUnOkMessage()));
                } else {
                    return loginAndGetCookie(interfaceMonitorData, url, username, password, tries - 1);
                }
            } else {
                Headers requestHeader = responseBodyModel.getHeaders();
                return requestHeader.get("Set-Cookie");
            }
        } catch (Exception e) {
            //登录异常才记录日志。
            log.info("login error", e);
            long returnTime = System.currentTimeMillis();
            long costTime = returnTime - callTime;
            //补充调用信息
            interfaceMonitorData.setType(ErpObjInterfaceUrlEnum.login.name());
            interfaceMonitorData.setResult(e.getMessage());
            interfaceMonitorData.setStatus(2);
            interfaceMonitorData.setCallTime(callTime);
            interfaceMonitorData.setReturnTime(returnTime);
            interfaceMonitorData.setCostTime(costTime);
            BaseErpDataManager.saveErpInterfaceMonitor(interfaceMonitorData);
            if (e instanceof ErpSyncDataException) {
                //服务器返回登录错误
                throw e;
            }
            //一般是服务器未返回，异常
            throw ErpSyncDataException.wrap(e, I18NStringEnum.s251).extra(e.getMessage());
        }
    }

    /**
     * 因为允许为null的对象类型传参，容易在修改代码时导致空指针，都换成必传或者基础类型数据。
     */
    public <T> Result<T> postWithMonitor(InterfaceMonitorData interfaceMonitorData,
                                         DBProxyConnectParam connectParam,
                                         String path,
                                         String requestBody,
                                         TypeReference<Result<T>> t,
                                         Long rspReadLimitLenByte,
                                         Integer timeOutSecond) {
        String cookie;
        Map<String, String> header = new HashMap<>();
        try {
            String requestUrl = connectParam.getBaseUrl() + "/login";
            cookie = loginAndGetCookie(interfaceMonitorData, requestUrl, connectParam.getUserName(), connectParam.getPassword(), 1);
        } catch (ErpSyncDataException e) {
            return Result.newError(I18NStringEnum.s39, e.getErrMsg());
        }
        long callTime = System.currentTimeMillis();
        int status = 1;
        //正常时为responseBody，异常时为异常信息
        String rspStr = "";
        String traceId = TraceUtil.get();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "";
        }
        header.put("traceId", traceId);
        String requestUrl = connectParam.getBaseUrl() + path;
        try {
            header.put("Cookie", cookie);
            //指定db
            header.put("db-dskey", connectParam.getDbName());
            HttpRspLimitLenUtil.ResponseBodyModel response = proxyHttpClient.postUrlWithTimeOutSecond(requestUrl, requestBody, header, rspReadLimitLenByte, timeOutSecond);
            if (response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, data:{}", interfaceMonitorData);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            //判断code
            if (!response.isOk()) {
                rspStr = response.buildUnOkMessage();
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, response.buildUnOkMessage());
            }
            rspStr = response.getBody();
            Result<T> result = JacksonUtil.fromJson(rspStr, t);
            if (!result.isSuccess()) {
                status = 2;
                //请求失败
                log.warn("request failed,requestUrl:{},headerMap:{},requestBody:{},response:{}", requestUrl, header, requestBody, response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, result.getErrCode() + "," + rspStr);
            }
            return result;
        } catch (Exception e) {
            status = 2;
            rspStr = e.getMessage() + " " + rspStr;
            log.error("request error,requestUrl:{},headerMap:{},requestBody:{}", requestUrl, header, requestBody, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            long returnTime = System.currentTimeMillis();
            long costTime = returnTime - callTime;
            //补充调用信息
            interfaceMonitorData.setArg(requestBody);
            interfaceMonitorData.setResult(rspStr);
            interfaceMonitorData.setStatus(status);
            interfaceMonitorData.setCallTime(callTime);
            interfaceMonitorData.setReturnTime(returnTime);
            interfaceMonitorData.setCostTime(costTime);
            BaseErpDataManager.saveErpInterfaceMonitor(interfaceMonitorData);
        }
    }

    public Result<Dict> execute(ErpConnectInfoEntity connectInfo, String jsonString) {
        DBProxyConnectParam connectParam = getConnectParam(connectInfo);
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName("")
                .type(ErpObjInterfaceUrlEnum.execute.name())
                .remark("")
                .build();

        Result<Dict> result = postWithMonitor(interfaceMonitorData,
                connectParam,
                "/execute",
                jsonString,
                new TypeReference<Result<Dict>>() {
                },
                ConfigCenter.LIST_CONTENT_LENGTH_LIMIT,
                null);
        return result;
    }

    private SqlConfig parseSqlConfig(String sqlYaml) {
        if (!StrUtil.startWith(sqlYaml, "#YAML")) {
            throw ErpSyncDataException.wrap(I18NStringEnum.kdbcnug);
        }
        //Yaml 非线程安全
        SqlConfig load = new Yaml(new Constructor(SqlConfig.class)).loadAs(sqlYaml, SqlConfig.class);
        return load;
    }

    @Override
    public Result<StandardData> getErpObjData(ErpIdArg arg, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = getConnectParam(connectInfo);
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(arg.getTenantId(), arg.getObjAPIName(), connectInfo.getId());
        Map<String, SqlConfig> detailSqlConfigMap = parseDetailSqlConfigMap(dbProxyConfigEntity);
        SqlConfig sqlConfig = parseSqlConfig(dbProxyConfigEntity.getQuerySql());
        String idWhere = sqlConfig.getIdWhere();
        if (StrUtil.isBlank(idWhere)) {
            return Result.newError(ResultCodeEnum.DB_CONFIG_MISSING);
        }
        String querySql = ReUtil.replaceAll(sqlConfig.getQuerySql(), "\\$\\{__where}", sqlConfig.getIdWhere());
        //查询
        final ExecuteArg<PreparingSql> executeArg = ExecuteArg.queryOne()
                .setReferenceId("main")
                .setBody(new PreparingPageSql(querySql).putAllParametersFromBean(arg)
                );
        //分割dataId
        String dataId = arg.getDataId();
        if (dataId != null) {
            //默认点
            addSplitIdParams("dataId", dataId, executeArg.getBody());
        }
        //查询从
        LinkedHashMap<String, String> detailQuerySqlMap = getDetailQueryMap(detailSqlConfigMap);
        executeArg.getBody().setRelationalQueryMap(detailQuerySqlMap);
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(arg.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(arg.getObjAPIName())
                .type(ErpObjInterfaceUrlEnum.queryMasterById.name())
                .remark("").build();

        Result<ExecuteResult> result = postWithMonitor(interfaceMonitorData,
                connectParam,
                "/execute",
                executeArg.toJsonStr(),
                new TypeReference<Result<ExecuteResult>>() {
                },
                ConfigCenter.CONTENT_LENGTH_LIMIT,
                null);
        if (!result.isSuccess()) {
            return result.error();
        }
        //解析结果
        ObjectData mainData = result.getData().toObjectData();
        StandardData standardData = mainDataToStd(mainData, arg.getObjAPIName(), detailSqlConfigMap.keySet());
        return Result.newSuccess(standardData);
    }

    private static void addSplitIdParams(String paramName, String dataId, PreparingSql preparingSql) {
        ID_SEPARATOR_MAP.forEach((name, separator) -> {
            List<String> split = StrUtil.split(dataId, separator);
            preparingSql.addParameters(paramName + "__" + name, split);
        });
    }

    private @NotNull LinkedHashMap<String, String> getDetailQueryMap(Map<String, SqlConfig> details) {
        LinkedHashMap<String, String> detailQuerySqlMap = new LinkedHashMap<>();
        details.forEach((detailObjApiName, sqlConfig) -> {
            String detailSql = sqlConfig.getQuerySql();
            detailQuerySqlMap.put("detail-" + detailObjApiName, detailSql);
        });
        return detailQuerySqlMap;
    }

    private static DBProxyConnectParam getConnectParam(ErpConnectInfoEntity connectInfo) {
        return JacksonUtil.fromJson(connectInfo.getConnectParams(), DBProxyConnectParam.class);
    }

    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg arg, ErpConnectInfoEntity connectInfo) {
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if(arg.getOperationType()!=null&& EventTypeEnum.INVALID.getType()==arg.getOperationType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
        }
        //先查主
        DBProxyConnectParam connectParam = getConnectParam(connectInfo);
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(arg.getTenantId(), arg.getObjAPIName(), connectInfo.getId());
        SqlConfig sqlConfig = parseSqlConfig(dbProxyConfigEntity.getQuerySql());
        Map<String, SqlConfig> detailSqlConfig = parseDetailSqlConfigMap(dbProxyConfigEntity);
        //构建公共参数
        final ExecuteArg<PreparingPageSql> executeArg = ExecuteArg.queryPage()
                .setReferenceId("main")
                //后面再填充真实的sql
                .setBody(new PreparingPageSql("")
                        .setLimit(arg.getLimit())
                        .setOffset(arg.getOffset())
                        .setNoOffset(dbProxyConfigEntity.isAlwaysOffsetZero())
                        .putAllParametersFromBean(arg)
                );
        //增加一个参数，startTime+1毫秒,不然处理不了精度更细的sql了
        executeArg.getBody().addParameters("plus1StartTime", arg.getStartTime() + 1);
        executeArg.getBody().addParameters("plus1EndTime", arg.getEndTime() + 1);
        //分割lastMaxId
        String lastMaxId = arg.getLastMaxId();
        if (lastMaxId != null) {
            //对id增加分割数组
            addSplitIdParams("lastMaxId", lastMaxId, executeArg.getBody());
        }
        //查询从
        LinkedHashMap<String, String> detailQuerySqlMap = getDetailQueryMap(detailSqlConfig);
        executeArg.getBody().setRelationalQueryMap(detailQuerySqlMap);
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(arg.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(arg.getObjAPIName())
                .type(type.name())
                .remark("")
                .timeFilterArg(arg)
                .build();
        //检查sql
        checkSql(sqlConfig, dbProxyConfigEntity);
        List<ObjectData> mainDataList = new ArrayList<>();

        //只支持主作废，，，单独从作废不支持
        List<String> batchWhereList = arg.getOperationType() == EventTypeEnum.INVALID.getType() ?
                sqlConfig.getBatchInvalidWhere() : sqlConfig.getBatchWhere();

        //当lastMaxId为空时，不执行其查询
        batchWhereList.removeIf(v -> lastMaxId == null && v.contains("${lastMaxId"));
        for (String batchWhere : batchWhereList) {

            String querySql = ReUtil.replaceAll(sqlConfig.getQuerySql(), "\\$\\{__where}", batchWhere);
            executeArg.getBody().setSql(querySql);
            //执行
            Result<ExecuteResult> result = postWithMonitor(interfaceMonitorData,
                    connectParam,
                    "/execute",
                    executeArg.toJsonStr(),
                    new TypeReference<Result<ExecuteResult>>() {
                    },
                    ConfigCenter.LIST_CONTENT_LENGTH_LIMIT,
                    null);
            if (!result.isSuccess()) {
                return result.error();
            }
            //增加结果
            mainDataList.addAll(result.getData().toObjectDataList());
            if (mainDataList.size() >= arg.getLimit()) {
                //够了就结束，不够就继续
                break;
            }
        }
        StandardListData standardListData = new StandardListData();
        for (ObjectData mainData : mainDataList) {
            StandardData standardData = mainDataToStd(mainData, arg.getObjAPIName(), detailSqlConfig.keySet());
            standardListData.getDataList().add(standardData);
        }
        processDynamicArgResult(standardListData, dbProxyConfigEntity);
        return Result.newSuccess(standardListData);
    }

    private @NotNull Map<String, SqlConfig> parseDetailSqlConfigMap(ErpDBProxyConfigEntity dbProxyConfigEntity) {
        Map<String, SqlConfig> detailSqlConfig = new HashMap<>();
        dbProxyConfigEntity.getDetails().forEach((k, v) -> {
            detailSqlConfig.put(k, parseSqlConfig(v.getQuerySql()));
        });
        return detailSqlConfig;
    }

    private void checkSql(SqlConfig sqlConfig, ErpDBProxyConfigEntity dbProxyConfigEntity) {
        //检查sql
        if (dbProxyConfigEntity.isAlwaysOffsetZero()) {
            //无offset轮询，批量查询条件必须包含上次id字段
            List<String> batchWhere = sqlConfig.getBatchWhere();
            if (batchWhere.stream().noneMatch(v -> v.contains("${lastMaxId"))) {
                throw ErpSyncDataException.wrap(I18NStringEnum.knonIdFilter);
            }
            //无offset轮询，批量查询条件必须包含上次日期字段
            if (batchWhere.stream().noneMatch(v -> StrUtil.containsIgnoreCase(v, "startTime"))) {
                throw ErpSyncDataException.wrap(I18NStringEnum.knonDateFilter);
            }
            if (batchWhere.stream().noneMatch(v -> StrUtil.containsIgnoreCase(v, "endTime"))) {
                throw ErpSyncDataException.wrap(I18NStringEnum.knonDateFilter);
            }
            if (batchWhere.stream().noneMatch(v -> StrUtil.containsIgnoreCase(v, "order by"))) {
                throw ErpSyncDataException.wrap(I18NStringEnum.knonOrderBy);
            }
        }
    }


    void processDynamicArgResult(StandardListData data, ErpDBProxyConfigEntity dbProxyConfigEntity) {
        List<StandardData> dataList = data.getDataList();
        if (dataList.isEmpty()) {
            return;
        }
        if (!dbProxyConfigEntity.isAlwaysOffsetZero()) {
            return;
        }
        ObjectData lastData = dataList.get(dataList.size() - 1).getMasterFieldVal();
        String dbKey = dbProxyConfigEntity.getDbKey();
        String maxId = lastData.getString(dbKey);
        String dateField = dbProxyConfigEntity.getDateTimeConditionField();
        String maxDate = lastData.getString(dateField);
        long maxTime;
        if (StrUtil.isBlank(maxId)) {
            throw ErpSyncDataException.wrap(I18NStringEnum.s247);
        }
        if (StrUtil.isBlank(maxDate)) {
            throw ErpSyncDataException.wrap(I18NStringEnum.s248);
        }
        try {
            //尝试解析时间
            DateTime maxDateTime = DateUtil.parse(maxDate);
            maxTime = maxDateTime.getTime();
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(I18NStringEnum.s249);
        }
        data.setMaxId(maxId);
        data.setMaxTime(maxTime);
    }

    private static @NotNull StandardData mainDataToStd(ObjectData mainData, String objApiName, Collection<String> detailObjApiNames) {
        StandardData standardData = new StandardData();
        standardData.setObjAPIName(objApiName);
        standardData.setMasterFieldVal(mainData);
        detailObjApiNames.forEach(detailObjApiName -> {
            Object remove = mainData.remove("detail-" + detailObjApiName);
            if (remove instanceof List) {
                List<ObjectData> detailData = ((List<?>) remove).stream().map(v -> {
                    if (v instanceof Map) {
                        return ObjectData.convert((Map) v);
                    }
                    return new ObjectData();
                }).collect(Collectors.toList());
                standardData.getDetailFieldVals().put(detailObjApiName, detailData);
            }
        });
        return standardData;
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return handleSave(connectInfo, standardData, true, ErpObjInterfaceUrlEnum.create);
    }

    private Result<ErpIdResult> handleSave(ErpConnectInfoEntity connectInfo, StandardData standardData, boolean isCreate, ErpObjInterfaceUrlEnum urlEnum) {
        DBProxyConnectParam connectParam = getConnectParam(connectInfo);
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(), standardData.getObjAPIName(), connectInfo.getId());
        SqlConfig sqlConfig = parseSqlConfig(dbProxyConfigEntity.getQuerySql());
        Map<String, SqlConfig> detailSqlConfigMap = parseDetailSqlConfigMap(dbProxyConfigEntity);
        CompositeExecuteArg compositeExecuteArg = new CompositeExecuteArg();
        ObjectData mainData = standardData.getMasterFieldVal();
        handleSave(isCreate, "pid", sqlConfig, mainData, compositeExecuteArg);
        //执行从
        Map<String, List<ObjectData>> detailFieldVals = standardData.getDetailFieldVals();
        detailFieldVals.forEach((detailApiName, details) -> {
            SqlConfig detailSqlConfig = detailSqlConfigMap.get(detailApiName);
            if (detailSqlConfig == null) {
                throw ErpSyncDataException.wrap(I18NStringEnum.kdbcm);
            }
            for (int i = 0; i < details.size(); i++) {
                ObjectData detailDatum = details.get(i);
                //通过获取返回主键
                handleSave(isCreate, detailApiName + "-" + i, detailSqlConfig, detailDatum, compositeExecuteArg);
            }
        });
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(standardData.getObjAPIName())
                .type(urlEnum.name())
                .remark("").build();
        Result<CompositeExecuteResult> result = postWithMonitor(interfaceMonitorData,
                connectParam,
                "/compositeExecute",
                compositeExecuteArg.toJsonStr(),
                new TypeReference<Result<CompositeExecuteResult>>() {
                },
                ConfigCenter.CONTENT_LENGTH_LIMIT,
                null);
        if (!result.isSuccess()) {
            return result.error();
        }
        ErpIdResult erpIdResult = new ErpIdResult();
        Map<String, ExecuteResult> resultMap = result.getData().toResultMap();
        Object pidO = resultMap.get("pid").getBody();
        if (pidO != null) {
            erpIdResult.setMasterDataId(String.valueOf(pidO));
        }
        detailFieldVals.forEach((detailApiName, details) -> {
            List<String> detailIds = new ArrayList<>();
            for (int i = 0; i < details.size(); i++) {
                Object didO = resultMap.get(detailApiName + "-" + i).getBody();
                if (didO != null) {
                    detailIds.add(String.valueOf(didO));
                }
            }
            erpIdResult.getDetailDataIds().put(detailApiName, detailIds);
        });
        return Result.newSuccess(erpIdResult);
    }

    private static void handleSave(boolean isCreate, String idRef, SqlConfig sqlConfig, ObjectData data, CompositeExecuteArg compositeExecuteArg) {
        String sql = isCreate ? sqlConfig.getInsertSql() : sqlConfig.getUpdateSql();
        String queryIdSql = sqlConfig.getQueryIdSql();
        if (StrUtil.isBlank(sql)) {
            throw ErpSyncDataException.wrap(I18NStringEnum.kdbcm);
        }
        //通过获取返回主键
        PreparingSql preparingSql = new PreparingSql(sql).setParameters(data);
        ExecuteArg<PreparingSql> mainArg;

        if (StrUtil.isBlank(queryIdSql)) {
            //执行直接获取主键
            mainArg = ExecuteArg.executeGetId().setReferenceId(idRef).setBody(preparingSql);
        } else {
            mainArg = ExecuteArg.execute().setReferenceId(idRef).setBody(preparingSql);
            //插入语句的ref设置为随机字符,pid是select id语句的
            mainArg.setReferenceId(IdUtil.nanoId());
            //执行save后，sql获取Id
            ExecuteArg<PreparingSql> mainQueryIdArg = ExecuteArg.queryString().setReferenceId(idRef)
                    .setBody(new PreparingSql(queryIdSql).setParameters(data));
            compositeExecuteArg.getArgs().add(mainQueryIdArg);
        }
        compositeExecuteArg.getArgs().add(mainArg);
    }


    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData, StandardDetailData standardDetailData, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**
     * 主作废
     */
    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = getConnectParam(connectInfo);
        ErpObjInterfaceUrlEnum urlEnum = ErpObjInterfaceUrlEnum.invalid;
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(), standardInvalidData.getObjAPIName(), connectInfo.getId());
        SqlConfig sqlConfig = parseSqlConfig(dbProxyConfigEntity.getQuerySql());
        CompositeExecuteArg compositeExecuteArg = new CompositeExecuteArg();
        ObjectData mainData = standardInvalidData.getMasterFieldVal();
        PreparingSql preparingSql = new PreparingSql(sqlConfig.getInvalidSql()).setParameters(mainData);
        ExecuteArg<PreparingSql> mainArg = ExecuteArg.execute().setReferenceId("main").setBody(preparingSql);
        compositeExecuteArg.getArgs().add(mainArg);
        //关联作废
        List<String> invalidRelationSqls = sqlConfig.getInvalidRelationSqls();
        if (CollUtil.isNotEmpty(invalidRelationSqls)) {
            for (String invalidRelationSql : invalidRelationSqls) {
                preparingSql = new PreparingSql(invalidRelationSql).setParameters(mainData);
                mainArg = ExecuteArg.execute().setReferenceId(IdUtil.nanoId()).setBody(preparingSql);
                compositeExecuteArg.getArgs().add(mainArg);
            }
        }
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(standardInvalidData.getObjAPIName())
                .type(urlEnum.name())
                .remark("").build();
        Result<CompositeExecuteResult> result = postWithMonitor(interfaceMonitorData,
                connectParam,
                "/compositeExecute",
                compositeExecuteArg.toJsonStr(),
                new TypeReference<Result<CompositeExecuteResult>>() {
                },
                ConfigCenter.CONTENT_LENGTH_LIMIT,
                null);
        if (!result.isSuccess()) {
            return result.error();
        }
        return Result.newSuccess();
    }

    /**
     * 从对象作废
     */
    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = getConnectParam(connectInfo);
        ErpObjInterfaceUrlEnum urlEnum = ErpObjInterfaceUrlEnum.invalidDetail;
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(), standardInvalidData.getObjAPIName(), connectInfo.getId());
        Map<String, SqlConfig> detailSqlConfigMap = parseDetailSqlConfigMap(dbProxyConfigEntity);
        Map<String, String> detailFieldVals = standardInvalidData.getDetailFieldVals();
        Optional<Map.Entry<String, String>> any = detailFieldVals.entrySet().stream().findAny();
        if (!any.isPresent()) {
            log.info("detail Id is null,{}", standardInvalidData);
            return Result.newSuccess();
        }
        String detailObjApiName = any.get().getKey();
        String detailId = any.get().getValue();
        ObjectData mainData = standardInvalidData.getMasterFieldVal();
        mainData.put("dId", detailId);
        SqlConfig sqlConfig = detailSqlConfigMap.get(detailObjApiName);
        if (sqlConfig == null) {
            log.info("detail invalid do nothing,{}", standardInvalidData);
            return Result.newSuccess();
        }
        CompositeExecuteArg compositeExecuteArg = new CompositeExecuteArg();
        PreparingSql preparingSql = new PreparingSql(sqlConfig.getInvalidSql()).setParameters(mainData);
        ExecuteArg<PreparingSql> mainArg = ExecuteArg.execute().setReferenceId(IdUtil.nanoId()).setBody(preparingSql);
        compositeExecuteArg.getArgs().add(mainArg);
        //关联作废
        List<String> invalidRelationSqls = sqlConfig.getInvalidRelationSqls();
        if (CollUtil.isNotEmpty(invalidRelationSqls)) {
            for (String invalidRelationSql : invalidRelationSqls) {
                preparingSql = new PreparingSql(invalidRelationSql).setParameters(mainData);
                mainArg = ExecuteArg.execute().setReferenceId(IdUtil.nanoId()).setBody(preparingSql);
                compositeExecuteArg.getArgs().add(mainArg);
            }
        }
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(standardInvalidData.getObjAPIName())
                .type(urlEnum.name())
                .remark("").build();
        Result<CompositeExecuteResult> result = postWithMonitor(interfaceMonitorData,
                connectParam,
                "/compositeExecute",
                compositeExecuteArg.toJsonStr(),
                new TypeReference<Result<CompositeExecuteResult>>() {
                },
                ConfigCenter.CONTENT_LENGTH_LIMIT,
                null);
        if (!result.isSuccess()) {
            return result.error();
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return handleSave(connectInfo, standardData, false, ErpObjInterfaceUrlEnum.update);
    }

    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData, StandardDetailData standardDetailData, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    private ErpDBProxyConfigEntity loadDBProxyEaiConfigByTenantIdAndObjApiName(String tenantId,
                                                                               String objApiName,
                                                                               String dataCenterId) {
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(tenantId, objApiName, dataCenterId);
        List<ErpDBProxyConfigEntity> dbProxyConfigEntities =
                erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(tenantId, objApiName, dataCenterId);
        if (!CollectionUtils.isEmpty(dbProxyConfigEntities)) {
            for (ErpDBProxyConfigEntity entity : dbProxyConfigEntities) {
                dbProxyConfigEntity.getDetails().put(entity.getObjApiName(), entity);
            }
        }
        return dbProxyConfigEntity;
    }
}
