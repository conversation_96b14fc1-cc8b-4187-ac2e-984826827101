package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.ReadPreference;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Repository
public class ErpTempMongoStore extends BaseMongoStore<Document> {
    @Autowired
    protected ConfigCenterConfig configCenterConfig;

    @Autowired
    @Qualifier("shardingSyncTempMongo")
    protected DatastoreExt store;
    private final static String erpTempDataCollectionPrefix = "erp_temp_";

    public UpdateResult updateErpTempDataByIds(String tenantId, List<ObjectId> objectIds, Bson updates) {
        return getOrCreateCollection(tenantId).updateMany(Filters.in("_id", objectIds), updates);
    }

    public UpdateResult updateErpTempDataByFilters(String tenantId, List<Bson> filters, Bson updates) {
        return getOrCreateCollection(tenantId).updateMany(Filters.and(filters), updates);
    }

    public long countErpTempData(String tenantId, List<Bson> filters) {
        return getOrCreateCollection(tenantId).countDocuments(Filters.and(filters));
    }

    public Integer countErpTempDataLimit1000(String tenantId, List<Bson> filters, int offset, int limit) {
        List<Document> result = Lists.newArrayList();
        Bson projections = Projections.fields(Projections.include("_id"));
        FindIterable<Document> iterable = getOrCreateCollection(tenantId).find(Filters.and(filters)).projection(projections).skip(offset).limit(limit);
        for (Document doc : iterable) {
            result.add(doc);
        }
        return result.size();
    }

    public List<Document> listErpTempDataDocument(String tenantId, List<Bson> filters, Bson sort, int offset, int limit) {
        List<Document> result = Lists.newArrayList();
        FindIterable<Document> iterable;
        if (sort == null) {
            iterable = getOrCreateCollection(tenantId).find(Filters.and(filters)).skip(offset).limit(limit);
        } else {
            iterable = getOrCreateCollection(tenantId).find(Filters.and(filters)).sort(sort).skip(offset).limit(limit);
        }

        for (Document doc : iterable) {
            result.add(doc);
        }
        return result;
    }

    /**返回projectFieldList指定的字段，如果传空列表，返回所有字段
     * */
    public List<Document> listErpTempDataDocumentField(String tenantId, List<Bson> filters, Bson sort, int offset, int limit,
                                                       List<String> projectFieldList) {
        List<Document> result = Lists.newArrayList();
        FindIterable<Document> iterable;

        MongoCollection<Document> collection =  getOrCreateCollection(tenantId);
        iterable = collection.find(Filters.and(filters)).skip(offset).limit(limit);
        if (sort != null) {
            iterable = iterable.sort(sort);
        }
        if(projectFieldList != null && projectFieldList.size() > 0) {
            iterable = iterable.projection(Projections.include(projectFieldList));
        }

        for (Document doc : iterable) {
            result.add(doc);
        }
        return result;
    }

    public DeleteResult deleteErpTempDataDocument(String tenantId, Bson filter) {
        return getOrCreateCollection(tenantId).deleteMany(filter);
    }

    @Override
    public MongoDatabase getDatabase(String tenantId) {
        final MongoDatabase database = getMongoDatabase(tenantId);
        if (useSecondaryPreferred(tenantId)) {
            database.withReadPreference(ReadPreference.secondaryPreferred());
        }
        return database;
    }

    protected MongoDatabase getMongoDatabase(String tenantId) {
        return store.setTenantId(tenantId).getMongo().getDatabase(store.setTenantId(tenantId).getDB().getName());
    }

    private boolean useSecondaryPreferred(String tenantId) {
        return configCenterConfig.readTempSecondaryPreference().contains(tenantId) || configCenterConfig.readTempSecondaryPreference().contains("*");
    }

    @Override
    public String getCollName(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            return erpTempDataCollectionPrefix + "common";
        }
        return erpTempDataCollectionPrefix + tenantId;
    }

    @Override
    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
//        String key = "index_id";
//        toBeCreate.add(new IndexModel(Indexes.hashed("_id"), new IndexOptions().name(key).background(true)));
        key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        Bson index_expire_time = Indexes.ascending("expire_time");
        toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(ConfigCenter.ERP_TEMP_DATA_MONGO_EXPIRE_TIME, TimeUnit.HOURS)));
        key = "index_ei_obj_data_id";
        Bson index_ei_obj_data_id = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("obj_api_name"), Indexes.ascending("data_id"));
        toBeCreate.add(new IndexModel(index_ei_obj_data_id, new IndexOptions().name(key).background(true)));
        key = "index_ei_obj_data_Num";
        Bson index_ei_obj_data_Num = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("obj_api_name"), Indexes.ascending("data_number"));
        toBeCreate.add(new IndexModel(index_ei_obj_data_Num, new IndexOptions().name(key).background(true)));
        key = "index_ei_dc_obj_status_new_time_type";
        Bson index_ei_dc_obj_status_time_type = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("dc_id"), Indexes.ascending("obj_api_name"),
                Indexes.ascending("status"), Indexes.ascending("new_last_sync_time"), Indexes.ascending("operation_type"));
        toBeCreate.add(new IndexModel(index_ei_dc_obj_status_time_type, new IndexOptions().name(key).background(true)));
        key = "new_last_sync_time";
        Bson new_last_sync_time = Indexes.compoundIndex(Indexes.descending("new_last_sync_time"));
        toBeCreate.add(new IndexModel(new_last_sync_time, new IndexOptions().name(key).background(true)));
        key = "index_ei_dc_obj_id";
        Bson index_ei_dc_obj = Indexes.compoundIndex(Indexes.ascending("tenant_id"),
                Indexes.ascending("dc_id"), Indexes.ascending("obj_api_name"), Indexes.descending("_id"));
        toBeCreate.add(new IndexModel(index_ei_dc_obj, new IndexOptions().name(key).background(true)));

        key = "index_ei_dc_obj_new_time_id";
        Bson index_ei_dc_obj_new_time_id = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("dc_id"), Indexes.ascending("obj_api_name"), Indexes.ascending("new_last_sync_time"), Indexes.ascending("_id"));
        toBeCreate.add(new IndexModel(index_ei_dc_obj_new_time_id, new IndexOptions().name(key).partialFilterExpression(Filters.eq("operation_type", null)).background(true)));
        return toBeCreate;
    }
}
