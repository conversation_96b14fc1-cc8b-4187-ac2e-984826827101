package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDaoImpl

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData
import org.bson.types.ObjectId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

import java.util.concurrent.CountDownLatch

/**
 * <AUTHOR>
 * @date 2023/2/28 17:45:18
 */
@Ignore
@ContextConfiguration(["classpath:spring-test.xml"])
class SpeedLimitAspectTest_bak extends Specification {
    @Autowired
    private SyncDataFixDaoImpl syncDataFixDao

    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager


    static {
        System.setProperty("process.profile", "fstest");
    }

    def "interfaceMonitorDataDao"() {
        expect:

        Class c = InterfaceMonitorDataDao.class
        println("para:" + c.getMethod("batchUpsertInterfaceMonitorData", String.class, Collection.class).getParameters())
        def ids = JSON.parseArray("[{\"arg\":\"{\\\"FormId\\\":\\\"BD_Customer\\\",\\\"FieldKeys\\\":\\\"FNumber\\\",\\\"FilterString\\\":\\\"((FModifyDate> {ts'2023-02-28 04:00:09'} and FModifyDate<= {ts'2023-02-28 19:06:04'} ) or (FApproveDate> {ts'2023-02-28 04:00:09'} and FApproveDate<= {ts'2023-02-28 19:06:04'} )) and FUseOrgId.FNumber in ( '000' ) \\\",\\\"OrderString\\\":\\\"FNumber asc\\\",\\\"TopRowCount\\\":null,\\\"StartRow\\\":0,\\\"Limit\\\":100,\\\"SubSystemId\\\":null}\",\"callTime\":1677582364294,\"costTime\":139,\"createTime\":1677582364433,\"dcId\":\"780777150699143168\",\"expireTime\":1677651982423,\"id\":{\"counter\":5311165,\"date\":1677582364000,\"machineIdentifier\":13958299,\"processIdentifier\":27631,\"time\":1677582364000,\"timeSecond\":1677582364,\"timestamp\":1677582364},\"logId\":\"J-E.84801.0.BD_Customer.1WrOSfoi7q8\",\"objApiName\":\"BD_Customer\",\"result\":\"{\\\"data\\\":[],\\\"errCode\\\":\\\"s106240000\\\",\\\"errMsg\\\":\\\"成功\\\",\\\"traceMsg\\\":\\\"J-E.84801.-10000-erp190603-BD_Customer.BillHead\\\"}\",\"returnTime\":1677582364433,\"status\":1,\"tenantId\":\"84801\",\"timeFilterArg\":{\"endTime\":1677582364052,\"includeDetail\":true,\"limit\":100,\"offset\":0,\"startTime\":1677528009101},\"traceId\":\"J-E.84801.-10000-erp190603-BD_Customer.BillHead\",\"type\":\"queryMasterBatch\"}]", InterfaceMonitorData.class)
        chInterfaceMonitorManager.batchUpsertInterfaceMonitorData("84801", ids)
    }

    def "syncDataFixDao"() {
        expect:
        def ids = JSON.parseObject("{\"createTime\":*************,\"destData\":{\"object_describe_api_name\":\"PriceBookObj\",\"tenant_id\":\"84801\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"active_status\":\"1\",\"name\":\"新价目表01\",\"remark\":\"新价目表01-编辑明细-移除明细新增明细-编辑去掉客户---编辑加客户\",\"apply_account_range\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"_id\":\"63dcc1503d59930001740b3f\"},\"destDataId\":\"63dcc1503d59930001740b3f\",\"destEventType\":2,\"destObjectApiName\":\"PriceBookObj\",\"destTenantId\":\"84801\",\"destTenantType\":1,\"id\":\"63dccb82f2ab6a70dc82e083\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"84801\",\"FDescription\":\"新价目表01-编辑明细-移除明细新增明细-编辑去掉客户---编辑加客户\",\"object_describe_api_name\":\"BD_SAL_PriceList.BillHead\",\"name\":\"XSJMB0145\",\"_id\":\"816007\",\"FName\":\"新价目表01\"},\"sourceDataId\":\"816007\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_SAL_PriceList.BillHead\",\"sourceTenantId\":\"84801\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.84801.0.BD_SAL_PriceList.1VMaCPlXjWg.0.0\",\"syncPloyDetailSnapshotId\":\"ad41e489f81d44e48982efd17061190f\",\"tenantId\":\"84801\",\"updateTime\":1677238417169}", SyncDataEntity.class)
        syncDataFixDao.insertCache(ids)
        syncDataFixDao.removeCacheAndInsertDb("84801")
    }
}
