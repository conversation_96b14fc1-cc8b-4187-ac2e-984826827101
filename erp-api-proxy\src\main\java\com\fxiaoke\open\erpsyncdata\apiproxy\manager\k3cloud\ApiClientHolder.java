package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/3
 */
@Component
@Slf4j
public class ApiClientHolder {


    /**
     * 通过连接参数获取client，携带cookie信息，不作登录。
     */
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 30)
    @LogLevel(LogLevelEnum.TRACE)
    public K3CloudApiClient getK3ApiClient(final String tenantId, String connectParamJson, String dataCenterId) {
        K3CloudConnectParam connectParam = GsonUtil.fromJson(connectParamJson, K3CloudConnectParam.class);
        K3CloudApiClient  k3CloudApiClient = K3CloudApiClient.newInstance(tenantId, connectParam, dataCenterId);
        return k3CloudApiClient;
    }
}
