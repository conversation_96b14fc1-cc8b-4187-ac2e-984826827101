{"type": "page", "title": "全局配置", "data": {"baseUrl": ".."}, "body": [{"type": "form", "title": "筛选条件", "target": "service1", "mode": "inline", "body": [{"label": "类型", "type": "select", "name": "type", "require": true, "searchable": true, "creatable": true, "clearable": true, "size": "lg", "source": "post:${baseUrl}/config/getConfigTypes"}, {"type": "input-text", "name": "tenantId", "label": "tenantId", "id": "u:b9aa6f01e8d7"}, {"type": "input-text", "label": "dcId", "name": "dataCenterId", "id": "u:d83fb3e0b35b"}, {"label": "解析格式", "type": "select", "size": "md", "name": "formatType", "clearable": true, "source": "post:${baseUrl}/config/getFormatTypes"}], "actions": [{"type": "submit", "label": "查询", "level": "primary"}, {"type": "button", "label": "修改", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "drawer", "drawer": {"type": "drawer", "title": "修改配置", "body": [{"type": "form", "reload": "service1", "body": [{"name": "type", "label": "type", "type": "input-text", "id": "u:b9675f92805a", "static": true, "value": ""}, {"name": "tenantId", "label": "tenantId", "type": "input-text", "id": "u:bb080958b880", "disabled": true, "static": true}, {"name": "dataCenterId", "label": "dataCenterId", "type": "input-text", "id": "u:c79d0e6519ee", "static": true}, {"name": "channel", "label": "channel", "type": "input-text", "id": "u:79791f12fdeb", "static": true}, {"type": "editor", "label": "configuration", "name": "configuration", "id": "u:7f594d5d082d", "minRows": 3, "maxRows": 20, "language": "json"}], "id": "u:32922b8f5ce0", "actions": [{"type": "submit", "label": "提交", "primary": true}], "feat": "Edit", "dsType": "api", "initApi": {"method": "post", "url": "${baseUrl}/config/getConfig", "sendOn": "type != null"}, "api": {"url": "${baseUrl}/config/upsertConfig", "method": "post"}, "autoFocus": false}], "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:5b4427e0fb8c"}, {"type": "button", "actionType": "confirm", "label": "确认", "primary": true, "id": "u:ad57583c2c0e"}], "id": "u:2260cc3cd2a2", "size": "lg"}}]}}}]}, {"type": "alert", "body": "最多加载100条配置", "level": "info"}, {"name": "service1", "type": "service", "initFetchSchema": false, "schemaApi": {"url": "${baseUrl}/config/schemaConfig", "method": "post"}}], "id": "u:f444e533be91"}