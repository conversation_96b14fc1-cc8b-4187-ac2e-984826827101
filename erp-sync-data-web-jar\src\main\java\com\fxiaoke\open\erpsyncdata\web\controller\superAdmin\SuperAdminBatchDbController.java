package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.TenantObjArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushDataDao;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 批量刷库操作
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/8/23
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/batchdb")
@Slf4j
public class SuperAdminBatchDbController extends SuperAdminBaseController {

    @Autowired
    private ErpPushDataDao erpPushDataDao;

    /**
     * 删除processed表数据
     *
     * @return
     */
    @PostMapping("deletePushData")
    public Result<Dict> deletePushData(@RequestBody TenantObjArg arg) {
        long count = 0L;
        for (int i = 0; i < 100000; i++) {
            List<String> ids = erpPushDataDao.listByTenantIdLimit1000(arg.getTenantId());
            if (ids.isEmpty()){
                break;
            }
            int i1 = erpPushDataDao.deleteByIdIn(ids);
            count+=i1;
            log.info("delete push data {},{},count:{}",i,i1,count);
        }
        Dict result = Dict.of("count", count);
        return Result.newSuccess(result);
    }
}
