package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/22 15:58
 * @Version 1.0
 */
@Data
public class NewSaveResult {


    @SerializedName("IsSuccess")
    @JsonProperty("IsSuccess")
    private Boolean isSuccess=false;


    @SerializedName("Message")
    @JsonProperty("Message")
    private String message;
    //错误时，是小写的，先这样兼容，可以返回错误信息
    @SerializedName("message")
    @JsonProperty("message")
    private String msg;
    @SerializedName("Datas")
    @JsonProperty("Datas")
    private List<Datas> datas;
    @SerializedName("Result")
    @JsonProperty("Result")
    private SaveResult.Result result;

    @SerializedName("ValidationErrors")
    @JsonProperty("ValidationErrors")
    private List<ValidationErrors> ValidationErrors;

    @Data
    public static class Datas {
        @SerializedName("FID")
        @JsonProperty("FID")
        private String fId;

        @SerializedName("FBillNo")
        @JsonProperty("FBillNo")
        private String fBillNo;

        @SerializedName("SaleOrderEntry")
        @JsonProperty("SaleOrderEntry")
        private List<K3Model> saleOrderEntries;
    }

    @Data
    public static class ValidationErrors {
        @SerializedName("BillPKID")
        @JsonProperty("BillPKID")
        private String BillPKID;

        @SerializedName("Message")
        @JsonProperty("Message")
        private String Message;

        @SerializedName("DisplayToFieldKey")
        @JsonProperty("DisplayToFieldKey")
        private String DisplayToFieldKey;
    }



}
