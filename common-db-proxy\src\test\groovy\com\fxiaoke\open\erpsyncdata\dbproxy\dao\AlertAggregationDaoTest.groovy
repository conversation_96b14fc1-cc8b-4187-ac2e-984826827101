package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.AlertAggregationDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class AlertAggregationDaoTest extends BaseSpockTest {
    @Autowired
    private AlertAggregationDao alertAggregationDao;

    @Test
    void replace() {
        def tenantId = "81243"
        def dcId = "dcid100"
        def ployDetailId = "ployDetailId100"

        alertAggregationDao.insertOrUpdate(tenantId,
                dcId,
                ployDetailId,
                AlertAggregationType.SYNC_DATA_FAILED_ALERT,
                AlarmLevel.IMPORTANT,
                System.currentTimeMillis(),
                10,
                false,null,null,null)

//        alertAggregationDao.insertOrUpdate(tenantId,
//                dcId,
//                ployDetailId,
//                AlertAggregationType.SYNC_DATA_FAILED_ALERT,
//                AlarmLevel.IMPORTANT,
//                System.currentTimeMillis(),
//                0,
//                false,null,null,null)

        def data = alertAggregationDao.getData(tenantId, dcId, ployDetailId, AlertAggregationType.SYNC_DATA_FAILED_ALERT)
        println(data)
//
//        def delete = alertAggregationDao.delete(tenantId, dcId, ployDetailId, AlertAggregationType.POLLING_ERP_ALERT)
//
//        def dataList2 = alertAggregationDao.getDataList(tenantId,
//                dcId,
//                ployDetailId,
//                AlertAggregationType.POLLING_ERP_ALERT,
//                100)
//        println(dataList2)
    }

    @Test
    void getTenantDataList() {
        def tenantDataList = alertAggregationDao.getTenantDcPloyDetailDataMap()
        println(tenantDataList)

        tenantDataList.each {
            def count = alertAggregationDao.deleteMany(it.value.tenantId)
            println(count)
        }
    }

    @Test
    void batchUpdateLastAlertTime() {
        def tenantId = "81243"
        def dcId = "dcid100"
        def ployDetailId = "ployDetailId"

        List<AlertAggregationEntity> list = new ArrayList<>()
        def ployDetailIdList = new ArrayList()
        for(int i=0;i<5;i++) {
            AlertAggregationEntity entity = new AlertAggregationEntity()
            entity.setTenantId(tenantId)
            entity.setDataCenterId(dcId)
            entity.setPloyDetailId(ployDetailId + (100+i))
            entity.setAlertAggregationType(AlertAggregationType.POLLING_ERP_ALERT)
            entity.setAlarmLevel(AlarmLevel.URGENT)
            entity.setLastAlertTime(System.currentTimeMillis())
            entity.setAlertRecover(false)
            entity.setCount(10)
            entity.setErrCode("0")
            entity.setErrMsg("success")
            entity.setTraceMsg("trace msg test")
            entity.setCreateTime(new Date())
            entity.setUpdateTime(new Date())
            list.add(entity)
            ployDetailIdList.add(entity.getPloyDetailId())
            alertAggregationDao.insertOrUpdate(entity.getTenantId(),
                    entity.getDataCenterId(),
                    entity.getPloyDetailId(),
                    entity.getAlertAggregationType(),
                    entity.getAlarmLevel(),
                    entity.getLastAlertTime(),
                    entity.getCount(),
                    entity.getAlertRecover(),
                    entity.getErrCode(),
                    entity.getErrMsg(),
                    entity.getTraceMsg())
            println("ok")
        }


        def count = alertAggregationDao.batchUpdateLastAlertTime(tenantId,
                dcId,
                ployDetailIdList,
                AlertAggregationType.POLLING_ERP_ALERT,
                100)
        println(count)
    }

    @Test
    void batchUpdateLastAlertTime2() {
        def tenantDataList = alertAggregationDao.batchUpdateLastAlertTime("88521",
                "643f7322b54ea80001767d86",
                Lists.newArrayList("c6921b6cef4e47afb99c1850a20dc4e3"),
                AlertAggregationType.SYNC_DATA_FAILED_ALERT,
                System.currentTimeMillis()-10 * 60 * 1000)
        println(tenantDataList)
    }

    @Test
    void deleteAll() {
        def count = alertAggregationDao.deleteAll()
        println(count)
    }
}
