package com.fxiaoke.open.erpsyncdata.writer.manager.writecrm

import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmDataManager
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import spock.lang.Specification

class SyncBomCoreObjManagerTest extends Specification {

    def "test beforeWrite"() {
        given:
        def tenantId = "123456"
        def syncDataMappingsDao = Mock(SyncDataMappingsDao) {
            listCreatedDetailMappingByMasterDataId(*_) >> {
                SyncDataMappingsEntity entity = new SyncDataMappingsEntity(id: "test", destDataId: "test_id")
                return [entity]
            }
        }
        def crmDataManager = Mock(CrmDataManager) {
            listCRMObjsByIds(*_) >> {
                com.fxiaoke.crmrestapi.common.data.ObjectData data = new com.fxiaoke.crmrestapi.common.data.ObjectData("apiName")
                data.put(ObjectDescribeContants.ID, "test_id")
                return [data]
            }
        }
        def syncPloyDetailSnapshotManager = Mock(SyncPloyDetailSnapshotManager) {
            getEntryBySnapshotId(*_) >> {
                def data = new DetailObjectMappingsData()
                data.add(new DetailObjectMappingsData.DetailObjectMappingData(
                        destObjectApiName: ObjectApiNameEnum.FS_BOM_OBJ.objApiName
                ))
                new SyncPloyDetailSnapshotEntity(
                        syncPloyDetailData: new SyncPloyDetailData(
                                detailObjectMappings: data
                        )
                )
            }
        }
        def syncDataManager = Mock(SyncDataManager) // 空实现即可
        def i18NStringManager = new I18NStringManager()
        def manager = new SyncBomCoreObjManager(syncDataMappingsDao: syncDataMappingsDao, crmDataManager: crmDataManager,
                syncPloyDetailSnapshotManager: syncPloyDetailSnapshotManager, syncDataManager: syncDataManager, i18NStringManager: i18NStringManager)
        def obj = new ObjectData()
        obj.putId("test")
        obj.putApiName(ObjectApiNameEnum.FS_BOM_OBJ.objApiName)
        def event = new SyncDataContextEvent(destEventType: EventTypeEnum.UPDATE.getType(), tenantId: tenantId,
                        ployDetailSnapshotId: "test", sourceData: obj,
                        destDetailSyncDataIdAndDestDataMap: ["test": obj])

        expect:
        manager.beforeWrite(event)
    }
}
