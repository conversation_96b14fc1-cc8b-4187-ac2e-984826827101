package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 10:46:15
 */
public enum ActionEnum {
    GET(1, ActionCategoryEnum.Query),
    GET_LIST_BY_ID(2, ActionCategoryEnum.Query),
    LIST(3, ActionCategoryEnum.Query),
    CREATE(4, ActionCategoryEnum.CREATE),
    CREATE_DETAIL(5, ActionCategoryEnum.CREATE),
    INVALID(6, ActionCategoryEnum.INVALID),
    INVALID_DETAIL(7, ActionCategoryEnum.INVALID),
    RECOVER(8, ActionCategoryEnum.RECOVER),
    UPDATE(9, ActionCategoryEnum.UPDATE),
    UPDATE_DETAIL(10, ActionCategoryEnum.UPDATE),
    PUSH(11, ActionCategoryEnum.PUSH),
    /**
     * 暂时只有函数,集成流使用
     */
    EXECUTE(12, ActionCategoryEnum.EXECUTE),
    // /**
    //  * 发送待办
    //  * 暂时只有SAP有
    //  */
    // SEND_EXTERNAL_TODO(13),
    /**
     * 暂时只有K3有
     */
    QUERY(14, ActionCategoryEnum.Query),
    /**
     * 从临时库获取数据
     */
    GET_FROM_TEMP(15, ActionCategoryEnum.Query),
    /**
     * 从分发框架获取数据
     */
    GET_FROM_DISPATCHER(16, ActionCategoryEnum.Query),
    DELETE(17, ActionCategoryEnum.DELETE),
    /**
     * 批量写crm
     */
    BATCH_WRITE(18, ActionCategoryEnum.UPDATE)
    ;
    @Getter
    private Integer action;
    private ActionCategoryEnum category;

    ActionEnum(final Integer action, final ActionCategoryEnum category) {
        this.action = action;
        this.category = category;
    }

    private static final Map<Integer, ActionEnum> map = Arrays.stream(ActionEnum.values()).collect(Collectors.toMap(ActionEnum::getAction, Function.identity()));

    private static final Map<Integer, List<Integer>> categoryMap = Arrays.stream(ActionEnum.values()).collect(Collectors.groupingBy(actionEnum -> actionEnum.category.getType(), Collectors.mapping(ActionEnum::getAction, Collectors.toList())));

    public static List<Integer> getByCategory(final Integer action) {
        return categoryMap.get(action);
    }

    public ActionEnum valueOf(Integer action) {
        return map.get(action);
    }
}
