<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.OASyncApiDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        obj_api_name,
        description,
        event_type,
        data_template,
        status,
        url,
        request_mode,
        create_time,
        update_time
    </sql>
    <update id="updateOaApiStatus">
     update oa_sync_api set status=#{status} where tenant_id=#{tenantId} and event_type=#{eventType}
    </update>
    <delete id="batchDeleteIds">
        delete from oa_sync_api where tenant_id=#{tenantId} and id in
        <foreach collection="ids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.OASyncApiEntity">
        <!--@mbg.generated-->
        <!--@Table erp_connect_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="data_template" jdbcType="VARCHAR" property="dataTemplate"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="request_mode" jdbcType="VARCHAR" property="requestMode"/>
    </resultMap>

    <!--auto generated by MybatisCodeHelper on 2020-08-31-->
    <select id="getByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_sync_api
        where tenant_id = #{tenantId,jdbcType=VARCHAR} and obj_api_name = #{objApiName,jdbcType=VARCHAR} and event_type =
        #{eventType,jdbcType=VARCHAR} and status = '1'
    </select>

    <select id="getOASyncApiList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_sync_api
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="eventType!=null and eventType!=''">
            and event_type=#{eventType}
        </if>
    </select>

    <select id="getOAUsedSyncApiList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_sync_api
        where tenant_id = #{tenantId,jdbcType=VARCHAR} and status = '1'
        ORDER BY event_type
    </select>
</mapper>