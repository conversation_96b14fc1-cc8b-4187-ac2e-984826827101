package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpOrganizationObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpOrganizationObj;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
@Slf4j
@Component("BD_MATERIAL")
public class ProductSpecialBusinessImpl implements SpecialBusiness {
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;
    @Autowired
    private ErpOrganizationObjManager erpOrganizationObjManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    private static String VIRTUAL_HAS_BATCH_SERIAL= "VirtualHasBatchAndSerial";



    /**
     * 如果物料的id是编码，则查询其id用于调用查看接口
     *
     * @param viewArg
     * @param erpIdArg
     * @return
     */
    @Override
    public void beforeRunView(ViewArg viewArg, ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        ErpFieldExtendEntity idField = erpFieldManager.queryIdField(erpIdArg.getTenantId(),apiClient.getDataCenterId(), erpIdArg.getObjAPIName()).get(0);
        if ("Number".equalsIgnoreCase(idField.getViewCode())) {
            String id = commonBusinessManager.getIdByNumber(erpIdArg.getObjAPIName(),
                    erpIdArg.getDataId(),
                    "FMATERIALID",
                    "FNumber",
                    apiClient);
            viewArg.setId(id);
        }
    }

    /**
     * 查看erp数据后置动作
     * 一些单据增加虚拟字段
     *
     * @param erpIdArg
     * @param standardData
     * @param apiClient
     */
    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        if (standardData == null) {
            return;
        }
        ObjectData masterFieldVal = standardData.getMasterFieldVal();
        if (masterFieldVal != null) {
            String number = masterFieldVal.getString("Number");
            String name = masterFieldVal.getString("name");
            masterFieldVal.put("comName", Joiner.on("#").useForNull("null").join(number, name));
            masterFieldVal.put(VIRTUAL_HAS_BATCH_SERIAL, convertBatchSn(masterFieldVal));
        }
    }

    private String convertBatchSn(ObjectData masterFieldVal) {
        //转换对应的批次序列号的管理值
        //现在的预置key是前面那个
        boolean batchValue =getBool(masterFieldVal,"SubHeadEntity1.FIsBatchManage","MaterialStock[0].IsBatchManage");
        boolean serialValue = getBool(masterFieldVal,"SubHeadEntity1.FIsSNManage","MaterialStock[0].IsSNManage");
        //1 不开启 2 开启序列号管理 3 开启批次号管理
        String openValue="1";
        if(ObjectUtils.isNotEmpty(batchValue)&&batchValue) return "2";
        if(ObjectUtils.isNotEmpty(serialValue)&&serialValue)return "3";//不支持又开启批次号又开启序列号
        return openValue;
    }

    /**
     * @return 找不到就返回false
     */
    private boolean getBool(ObjectData objectData, String... key) {
        if (objectData == null || key == null) {
            return false;
        }
        for (String s : key) {
            Boolean value = objectData.getBoolean(s);
            if (value != null) {
                return value;
            }
        }
        return false;
    }

    /**
     * 去重,通过FNumber
     */
    @Override
    public void distinctByNumber(List<List<Object>> list) {
        Set<Object> tempSet = new HashSet<>(64);
        Iterator<List<Object>> iterator = list.iterator();
        while (iterator.hasNext()) {
            Object obj=iterator.next().get(0);
            String fNumber;
            if(obj instanceof String){
                fNumber = (String) obj;
            }else{
                fNumber = String.valueOf(obj);
            }
            if (tempSet.contains(fNumber)) {
                iterator.remove();
            }
            tempSet.add(fNumber);
        }
        //释放Set
        tempSet.clear();
    }

    /**
     * 获取物料时的前置动作
     *
     * @param queryArg
     * @param timeFilterArg
     * @param k3CloudApiClient
     */
    @Override
    public void beforeRunBillQuery(QueryArg queryArg, TimeFilterArg timeFilterArg, K3CloudApiClient k3CloudApiClient) {
        //调用组织机构对象接口
        setUseOrgFilter(timeFilterArg.getTenantId(),k3CloudApiClient.getDataCenterId(),queryArg);
        //如果没启用了SUPPORT_FMATERIALID_TO_ID，覆盖fieldKeys和orderString，如果启用了，以设置的主键为准
        if(!tenantConfigurationManager.isUsedFMaterialId2Id(timeFilterArg.getTenantId(),k3CloudApiClient.getDataCenterId())){
            queryArg.setFieldKeysByList(ImmutableList.of("FNumber", "FMATERIALID"));
            queryArg.setOrderString("FNumber ASC");
        }
    }

    private void setUseOrgFilter(String tenantId, String dataCenterId, QueryArg queryArg) {
        List<ErpOrganizationObj> erpOrganizationList = erpOrganizationObjManager.queryDcErpOrganizationObj(tenantId,
                dataCenterId);
        String orgNumber = erpOrganizationList.stream()
                .filter(ErpOrganizationObj::getNeedSyncProduct)
                .map(s -> "'" + s.getOrgNumber() + "'")        //拼接''
                .collect(Collectors.joining(","));
        if (StringUtils.isBlank(orgNumber)) {
            throw new ErpSyncDataException(ResultCodeEnum.SYNC_PRODUCT_ORG_NOT_FOUND,tenantId);
        }
        // 设置参数
        queryArg.appendFilterString(String.format("FUseOrgId.FNumber in ( %s ) ", orgNumber));
    }

    /**
     * 更新erp数据前置动作
     *  @param saveArg
     * @param apiClient
     * @param saveExtend
     */
    @Override
    public void beforeRunUpdate(SaveArg saveArg, StandardData standardData, K3DataConverter k3DataConverter, K3CloudApiClient apiClient, IdSaveExtend saveExtend) {
        K3Model model = saveArg.getModel();
        //移除编码，不改变ERP编码
        Object fNumberObj = model.remove("FNumber");
        String id = model.getString("FMATERIALID");
        if (StringUtils.isBlank(id)) {
            if (fNumberObj == null) {
                //没有编码也没有id，无法更新数据
                throw new ErpSyncDataException(I18NStringEnum.s172,apiClient.getTenantId());
            } else {
                //id为空，编码不为空，用编码获取id
                id = commonBusinessManager.getIdByNumber("BD_MATERIAL", String.valueOf(fNumberObj), "FMATERIALID",
                        "FNumber", apiClient);
                //id字段放在最后同样有效
                model.put("FMATERIALID", id);
            }
        }
    }
    @Override
    public void beforeGetDataByBillQuery(String tenantId, QueryArg queryArg, K3CloudApiClient k3CloudApiClient){
        //设置使用组织筛选条件
        setUseOrgFilter(tenantId,k3CloudApiClient.getDataCenterId(),queryArg);
    }
}
