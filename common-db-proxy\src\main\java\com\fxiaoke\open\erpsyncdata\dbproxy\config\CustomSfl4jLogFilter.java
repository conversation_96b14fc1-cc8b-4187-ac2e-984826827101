package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import com.alibaba.druid.filter.logging.Slf4jLogFilter;
import org.postgresql.util.PSQLException;
import org.postgresql.util.PSQLState;

import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 */
public class CustomSfl4jLogFilter extends Slf4jLogFilter {

    @Override
    protected void statementLogError(String message, Throwable error) {
        if (error instanceof PSQLException) {
            String sqlState = ((PSQLException) error).getSQLState();
            if (Objects.equals(PSQLState.UNIQUE_VIOLATION.getState(), sqlState)) {
                return;
            }
        }
        super.statementLogError(message, error);
    }
}
