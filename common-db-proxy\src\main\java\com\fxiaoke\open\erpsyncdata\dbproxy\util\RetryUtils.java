package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.WaitStrategies;
import com.github.rholder.retry.StopStrategies;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;


/**
 * <AUTHOR>
 * @create 2024/6/7 18:46
 * 自定义重试的组件
 * @desc
 */
public class RetryUtils {

    public static <T> T retry(Callable<T> operation, int retryCount, Predicate<T> successCondition) throws Exception {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfResult(result -> !successCondition.test(result)) // 定义失败的条件
                .withWaitStrategy(WaitStrategies.fixedWait(3, TimeUnit.SECONDS)) // 重试间隔
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryCount)) // 重试次数
                .build();

        return retryer.call(operation);
    }

    public static void main(String[] args) {
        // 示例：使用 RetryUtil 进行重试
        try {
            String result = RetryUtils.retry(
                    () -> makeHttpRequest("http://example.com/api", "GET"),
                    3,
                    response -> response != null && response.startsWith("Success"));
            System.out.println("Result: " + result);
        } catch (Exception e) {
            //TODO 重试
            System.err.println("Failed after retries: " + e.getMessage());
        }
    }

    // 示例：模拟 HTTP 请求
    private static String makeHttpRequest(String url, String method) {
        // 模拟 HTTP 请求的逻辑
        System.out.println("http");
       return "http";


    }
}
