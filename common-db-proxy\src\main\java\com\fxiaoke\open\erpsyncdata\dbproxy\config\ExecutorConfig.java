package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import cn.hutool.core.thread.NamedThreadFactory;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.lang.reflect.Method;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * spring异步使用的线程池EnableAsync
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/6/4
 */
@Configuration
@EnableAsync
@Slf4j
public class ExecutorConfig implements AsyncConfigurer {

    /**
     * 操作日志线程池
     * @return
     */
    @Lazy
    @Bean(name = "userOperatorLogExecutor")
    public Executor userOperatorLogExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(1,1,30,TimeUnit.SECONDS,
          new LinkedBlockingQueue(150),
          new NamedThreadFactory("UserOperatorLogAsync-", false),new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 用于syncData异步写
     *
     * @return
     */
    @Bean
    public Executor syncDataExecutor() {
        //会传递TraceId
        NamedThreadPoolExecutor executor = new NamedThreadPoolExecutor(
                "SyncDataAsync",3,3,5000,new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程
        executor.setCorePoolSize(5);
        //最大线程数
        executor.setMaxPoolSize(30);
        //队列长度
        executor.setQueueCapacity(5000);
        executor.setThreadNamePrefix("AsyncExecutor-");
        executor.initialize();
        return executor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler2();
    }

    public static class SimpleAsyncUncaughtExceptionHandler2 extends SimpleAsyncUncaughtExceptionHandler {
        @Override
        public void handleUncaughtException(Throwable ex, Method method, Object... params) {
            log.warn("Unexpected error occurred invoking async method:{},param:{}" + method, params, ex);
        }
    }
}