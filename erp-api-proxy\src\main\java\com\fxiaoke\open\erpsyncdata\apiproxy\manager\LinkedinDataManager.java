package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_LINKEDIN)
public class LinkedinDataManager extends ErpOverseasProxyDataManager {

    public static final String Channel = "linkedin";

    @Override
    protected String getChannel() {
        return Channel;
    }
}
