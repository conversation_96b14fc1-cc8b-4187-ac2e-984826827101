package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FieldMappingData implements Serializable {
    private String sourceApiName;
    private String sourceType;
    private String sourceTargetApiName;
    private String sourceQuoteFieldType;
    private String sourceQuoteRealField;
    private String sourceQuoteFieldTargetObjectApiName;
    private String sourceQuoteFieldTargetObjectField;
    private String destApiName;
    private String destType;
    private String quoteField;
    private String destQuoteFieldType;
    private String destTargetApiName;
    private List<OptionMappingData> optionMappings;
    private Integer mappingType;
    private String function;
    private String value;
    private String defaultValue;//默认值
    private Integer valueType;//值类型，固定值1，默认值2
    private Boolean notUpdateField;
    /** 是否不校验该字段映射，true：不校验，其他：校验 */
    private Boolean notCheckMappingField;
    /**
     * 直接使用原值 ,, 同时对默认值、固定值 会不执行trim
     */
    private Boolean useSourceValueDirectly;
    /**
     * 什么值判断为null
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.NullCandidateEnum
     */
    private List<String> nullCandidateList;
}
