package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class SaveResult {
    @SerializedName("Result")
    @JsonProperty("Result")
    private Result result;

    @Data
    public static class Result {
        @SerializedName("ResponseStatus")
        @JsonProperty("ResponseStatus")
        private ResponseStatus responseStatus;

        @SerializedName("Id")
        @JsonProperty("Id")
        private String id;

        @SerializedName("Number")
        @JsonProperty("Number")
        private String number;

        @SerializedName("NeedReturnData")
        @JsonProperty("NeedReturnData")
        private List<K3Model> needReturnData;

        /**
         * 兼容id为null但是successEntity有值的情况
         * @return id
         */
        public String getId() {
            if (StrUtil.isNotEmpty(id)) {
                return id;
            }
            if (responseStatus != null && responseStatus.getSuccessEntitys() != null) {
                for (ResponseStatus.SuccessEntitys successEntity : responseStatus.getSuccessEntitys()) {
                    if (StrUtil.isNotEmpty(successEntity.getId())) {
                        return successEntity.getId();
                    }
                }
            }
            return null;
        }
    }

}
