package com.fxiaoke.open.erpsyncdata.web.controller;


import com.facishare.converter.EIEAConverter;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.RelationErpShardManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.web.interceptor.UserContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

public class BaseController {
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private RelationErpShardManager relationErpShardManager;
    @Autowired
    private EIEAConverter eieaConverter;

    protected String getEa(){
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return userVo.getEnterpriseAccount();
    }

    protected Integer getIntLoginUserTenantId() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return userVo.getEnterpriseId();
    }

    protected String getPhone() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        if (StringUtils.isBlank(userVo.getMobile()) || "null".equals(userVo.getMobile())) {
            //电话为空,查员工信息获取电话
            GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
            arg.setEnterpriseId(userVo.getEnterpriseId());
            arg.setEmployeeId(userVo.getEmployeeId());
            GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(arg);
            if (employeeDto != null && employeeDto.getEmployeeDto() != null && StringUtils.isNotBlank(employeeDto.getEmployeeDto().getMobile())
                    && !"null".equals(employeeDto.getEmployeeDto().getMobile())) {
                userVo.setMobile(employeeDto.getEmployeeDto().getMobile());
            }
        }
        return userVo.getMobile();
    }

    protected String getName() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())||Objects.isNull(userVo.getEmployeeId())){
            return null;
        }
        if (StringUtils.isBlank(userVo.getName()) || "null".equals(userVo.getName())) {
            //名称为空,查员工信息获取名称
            GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
            arg.setEnterpriseId(userVo.getEnterpriseId());
            arg.setEmployeeId(userVo.getEmployeeId());
            GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(arg);
            if (employeeDto != null && employeeDto.getEmployeeDto() != null) {
                if(StringUtils.isNotBlank(employeeDto.getEmployeeDto().getFullName())
                        && !"null".equals(employeeDto.getEmployeeDto().getFullName())){
                    userVo.setName(employeeDto.getEmployeeDto().getFullName());
                }else{
                    userVo.setName(employeeDto.getEmployeeDto().getName());
                }

            }
        }
        return userVo.getName();
    }

    protected String getLoginUserTenantId() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        return String.valueOf(userVo.getEnterpriseId());
    }

    protected Integer getLoginUserId() {
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEmployeeId())){
            return null;
        }
        return userVo.getEmployeeId();
    }

    protected String getDcId(){
        UserVo userVo = getUserVo();
        if (userVo==null){
            throw new ErpSyncDataException(I18NStringEnum.s115,null);
        }
        String dataCenterId = userVo.getDataCenterId();
        if (StringUtils.isBlank(dataCenterId)){
            throw new ErpSyncDataException(I18NStringEnum.s116,userVo.getEnterpriseId()+"");
        }
        return dataCenterId;
    }


    protected String getLang(){
        return I18nUtil.getLocaleFromTrace();
    }

    public UserVo getUserVo() {
        UserVo upUserVo = UserContextHolder.getUserVo();
        UserVo hostedUserVo = UserContextHolder.getHostedUserVo();
        if (upUserVo != null && hostedUserVo != null) {
            //代管企业处理
            String downStreamId = hostedUserVo.getTenantId();
            String templateDcId = hostedUserVo.getDataCenterId();
            //验证下游企业
            String upTenantId = upUserVo.getTenantId();
            RelationErpShardDto simple = relationErpShardManager.getSimple(downStreamId, templateDcId);
            if (simple != null
                    && Objects.equals(simple.getTenantId(), upTenantId)
                    && simple.getStatus() == RelationErpShardStatusEnum.normal.getStatus()) {
                //填充缺少的信息
                String downEa = eieaConverter.enterpriseIdToAccount(hostedUserVo.getEnterpriseId());
                hostedUserVo.setEnterpriseAccount(downEa);
                hostedUserVo.setEmployeeId(-10000);
                //使用下游身份
                return hostedUserVo;
            } else {
                //抛异常
                throw new ErpSyncDataException(ResultCodeEnum.UNCONNECTED_DOWNSTREAM);
            }
        }
        return upUserVo;
    }

    public String getEnterpriseName(){
        UserVo userVo=getUserVo();
        if (Objects.isNull(userVo)||Objects.isNull(userVo.getEnterpriseId())){
            return null;
        }
        SimpleEnterprise simpleEnterprise = new SimpleEnterprise();
        GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
        getSimpleEnterpriseArg.setEnterpriseAccount(userVo.getEnterpriseAccount());
        getSimpleEnterpriseArg.setEnterpriseId(userVo.getEnterpriseId());
        GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(getSimpleEnterpriseArg);
        simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
        if (Objects.isNull(simpleEnterprise)||Objects.isNull(simpleEnterprise.getEnterpriseName())){
            return null;
        }
        return simpleEnterprise.getEnterpriseName();
    }
}
