package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/26
 */
@AllArgsConstructor(staticName = "of")
@Getter
@Setter
@ToString
public class K3LocVo {
    /**
     * 仓库编码
     */
    private String warehouseNumber;
    /**
     * 仓位id
     */
    private String locId;
    /**
     * 仓位复合编码
     */
    private String locComNumber;
    /**
     * 仓位复合名称
     */
    private String locComName;
}
