package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.mongodb.MongoClient;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Slf4j
public class JsonStringCodec<T> implements Codec<T> {
    private final Codec<String> stringCodec;
    private Class<T> clazz;

    /**
     * Default constructor.
     */
    public JsonStringCodec(Class<T> cls) {
        clazz = cls;
        this.stringCodec = MongoClient.getDefaultCodecRegistry().get(String.class);
    }


    private T convert(String document) {
        if (document == null) {
            return null;
        }
        try {
            T objectData = JacksonUtil.fromJson(document, clazz);
            return objectData;
        } catch (Exception e) {
            log.warn("invalid document");
        }
        return null;
    }

    @Override
    public T decode(BsonReader reader, DecoderContext decoderContext) {
        String document = stringCodec.decode(reader, decoderContext);
        T syncData = convert(document);
        return syncData;
    }

    @Override
    public void encode(BsonWriter writer, T value, EncoderContext encoderContext) {
        String str;
        try {
            str = JacksonUtil.toJson(value);
        } catch (Exception e) {
            log.error("objectDataCodec encode value error", e);
            str = "{}";
        }
        stringCodec.encode(writer, str, encoderContext);
    }

    @Override
    public Class<T> getEncoderClass() {
        return clazz;
    }
}
