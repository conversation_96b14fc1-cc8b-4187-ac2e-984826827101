package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Deprecated
public @interface ErpChannel {
    /**
     * 渠道
     */
    ErpChannelEnum value();
}