package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDaoAccess;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/29 19:46:27
 *
 * 查询下游企业的部分配置,按模板企业的配置走
 */
@Slf4j
@Aspect
/**
 * 指定order为最低优先级,确保在JetCache后执行
 */
@Order
@Component
public class TenantConfigReplaceEnterpriseAspect extends AbstractReplaceEnterpriseAspect {

    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;

    @Autowired
    private ErpTenantConfigurationDaoAccess erpTenantConfigurationDaoAccess;

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao.queryList(..)) && args(arg))")
    public Object tenantConfigReplaceManageData(ProceedingJoinPoint jp, ErpTenantConfigurationEntity arg) throws Throwable {
        final Object proceed = jp.proceed();


        if (!(proceed instanceof List) || !((List<?>) proceed).stream().allMatch(ErpTenantConfigurationEntity.class::isInstance)) {
            return proceed;
        }

        final List<ErpTenantConfigurationEntity> entities = (List<ErpTenantConfigurationEntity>) proceed;

        final String tenantId = arg.getTenantId();
        if (Objects.isNull(tenantId) || !managedEnterprise(tenantId, entities)) {
            return proceed;
        }

        final String templateId = getTemplateId(tenantId);
        if (Objects.isNull(templateId)) {
            return proceed;
        }


        return replaceTemplateConfig(entities, templateId, tenantId);
    }

    /**
     * 全局配置
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager.inWhiteList(String, ..)) || execution(* com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager.isPassDataSource(String,..))")
    public Object tenantConfigCheck(ProceedingJoinPoint jp) throws Throwable {
        final Object[] args = jp.getArgs();
        final String tenantId = (String) args[0];
        if (Objects.isNull(tenantId) || !managedEnterprise(tenantId)) {
            return jp.proceed();
        }

        final String templateId = getTemplateId(tenantId);
        if (Objects.isNull(templateId)) {
            return jp.proceed();
        }

        args[0] = templateId;
        return jp.proceed(args);
    }


    /**
     * 这里不能使用在调用ErpTenantConfigurationDao 和 TenantConfigurationManager 的方法,防止死循环
     */
    protected boolean managedEnterprise(String tenantId, List<ErpTenantConfigurationEntity> entities) {
        if (StringUtils.isBlank(tenantId)) {
            return false;
        }
        return entities.stream()
                .filter(e -> Objects.equals(e.getType(), TenantConfigurationTypeEnum.MANAGED_ENTERPRISE.name()))
                .findFirst()
                .map(e -> StringUtils.isNotBlank(e.getConfiguration()))
                .orElse(false);
    }

    /**
     * 替换下游企业需要替换的type
     */
    @NotNull
    private List<ErpTenantConfigurationEntity> replaceTemplateConfig(List<ErpTenantConfigurationEntity> entities, String templateId, String tenantId) {
        final List<ErpTenantConfigurationEntity> collect = entities.stream()
                .filter(e -> !ConfigCenter.replaceDownstreamConfigTypeSet.contains(e.getType()))
                .collect(Collectors.toList());

        final List<ErpTenantConfigurationEntity> templateReplaceConfig = getTemplateReplaceConfig(templateId, tenantId);
        collect.addAll(templateReplaceConfig);
        return collect;
    }

    public List<ErpTenantConfigurationEntity> getTemplateReplaceConfig(String templateId, String downstreamId) {
        final ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
        entity.setTenantId(templateId);

        return ConfigCenter.replaceDownstreamConfigTypeSet.stream()
                .map(type -> {
                    entity.setType(type);
                    final List<ErpTenantConfigurationEntity> erpTenantConfigurationEntities = erpTenantConfigurationDao.queryList(entity);
                    erpTenantConfigurationEntities.forEach(e -> e.setTenantId(downstreamId));
                    return erpTenantConfigurationEntities;
                })
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

}
