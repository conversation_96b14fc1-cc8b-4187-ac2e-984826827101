package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BaseCustomFunctionCommonServiceImpl implements CustomFunctionCommonService {


    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        return Result.newError("-100", I18NStringEnum.s26, arg.getType());
    }
}
