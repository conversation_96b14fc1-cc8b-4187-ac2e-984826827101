package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class K3UltimateResponseByQuery implements Serializable {
    private DataModel data;
    private String errorCode;
    private String message;
    private boolean status;

    @Data
    public static class DataModel implements Serializable {
        private String filter;
        private boolean lastPage;
        private int pageNo;
        private int pageSize;
        private int totalCount;
        private List<JSONObject> rows;
    }
}