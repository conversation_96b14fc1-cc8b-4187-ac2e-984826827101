package com.fxiaoke.open.erpsyncdata.dbproxy.dao;
import java.util.List;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @dateTime 2020/12/7 12:02
 */
@Repository
@ManagedTenantReplace
public interface ErpPushIdentifyDao extends ErpBaseDao<ErpPushIdentifyEntity>, ITenant<ErpPushIdentifyDao> {
    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    ErpPushIdentifyEntity findByTokenByTenantId(@Param("tenantId") String tenantId);

    /**
     * 别在这加缓存！！！！！！！！！！！！！！！！！
     */
    ErpPushIdentifyEntity getByTenantId(@Param("tenantId")String tenantId);

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    ErpPushIdentifyEntity findByTokenByTenantIdAndVersion(@Param("tenantId") String tenantId,
                                                          @Param("version")String version);
}