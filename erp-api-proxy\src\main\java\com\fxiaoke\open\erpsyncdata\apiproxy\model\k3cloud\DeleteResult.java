package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

public class DeleteResult extends Submit.Result {
    public static String checkDeleteResult(com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<DeleteResult> delete){
        if (!delete.isSuccess()){
            return delete.getErrMsg();
        }else {
            Submit.Result submitResult = delete.getData();
            if (submitResult == null || submitResult.getResult().getResponseStatus() == null) {
                return "Submission failed, return result is empty";
            }else {
                ResponseStatus responseStatus = submitResult.getResult().getResponseStatus();
                if (!responseStatus.getIsSuccess()) {
                    return responseStatus.printErrors();
                }
            }
        }
        return null;
    }
}
