package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.*;

/**
 * 限速类型
 */
@AllArgsConstructor
@Getter
public enum SpeedLimitTypeEnum {
    /**
     * 轮询ERP
     */
    QUERY_ERP_DATA("轮询ERP数据", "ERPDSS:QUERY:ERP", QUERY_ERP_DATA_SPEED_LIMIT, 25.0, 60.0),
    QUERY_ERP_DATA_HISTORY("历史任务轮询ERP数据", "ERPDSS:QUERY:ERP:HISTORY", QUERY_ERP_DATA_HISTORY_SPEED_LIMIT, 300.0, 60.0),
    TO_ERP("往ERP数据同步", "ERPDSS:LIMIT:2ERP", TENANT_LIMIT_PER_COUNT_SECOND_2ERP, 300.0, 60.0),
    TO_CRM("往CRM数据同步", "ERPDSS:LIMIT:2CRM", TENANT_LIMIT_PER_COUNT_SECOND_2CRM, 5.0, 60.0),
    TO_CRM_BATCHWRITE("往CRM批量写PAAS库数据同步", "ERPDSS:LIMIT:2CRMBATCH", TENANT_LIMIT_PER_COUNT_SECOND_2CRM_BATCHWRITE, 30.0, 60.0),
    /**
     * 使用和2CRM一样的配置文件
     */
    QUERY_ERP_TEMP("轮询临时库数据", "ERPDSS:LIMIT:QUE:TMP2", TENANT_LIMIT_PER_COUNT_SECOND_2CRM, 5.0, 60.0),
    /**
     * 自定义函数每分钟调用限制
     * redisKeyPrefix: custom_function_tpm
     * defaultTps: 3000.0 (默认每秒3000.0次)
     * defaultIntervalS: 60L (统计间隔60秒)
     */
    CUSTOM_FUNCTION_TPS("custom_function_tps", "customer:limit:tps", CUSTOMER_FUNCTION_SPEED_LIMIT,300.0, 60.0),
    ;

    /**
     * 名称，用于返回信息
     */
    private final String name;
    /**
     * redisKey前缀,只从代码取。配置文件不可配置
     */
    private final String redisKeyPrefix;
    /**
     * 配置名
     */
    private final TenantConfigurationTypeEnum configType;
    /**
     * 默认tps
     */
    private final Double defaultTps;
    /**
     * 默认的统计间隔，单位s，可以在配置里配置，key为interval;
     */
    private final Double defaultIntervalS;
}
