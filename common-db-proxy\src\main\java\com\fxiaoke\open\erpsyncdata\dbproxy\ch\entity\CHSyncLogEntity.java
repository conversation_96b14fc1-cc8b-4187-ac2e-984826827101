package com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CHSyncLogEntity {
    private String appName; // 服务名称
    private String traceId; // 分布式跟踪id
    private String serverIp; // 发出日志的ip
    private String tenantId; // 租户ei信息
    private Date createTime; // 日志上报时间
    private Date updateTime; // 日志上报时间
    private Date expireTime;//过期时间

    private String logType;//日志类型：sync_data\sync_log\interface_monitor
    private String id;//mongoId
    private String logId ;//logId
    private String type;//sync_log类型
    private String realObjApiName;//CRM为CRM对象apiName
    private String sourceObjectApiName;//源对象apiName
    private String streamId;//集成流id
    private String data;//数据
    private Integer syncLogStatus;//节点数据同步状态
    private String erpTempData;//对象类数据需要代码设置字段
    private String erpTempDataDataId;
    private String erpTempDataDataNumber;
    private List<String> erpTempDataTaskNum= Lists.newArrayList();

    public ErpTempData getErpTempDataObj() {
        if(StringUtils.isNotBlank(this.erpTempData)){
            return JacksonUtil.fromJson(this.erpTempData, ErpTempData.class);
        }
        return null;
    }
}
