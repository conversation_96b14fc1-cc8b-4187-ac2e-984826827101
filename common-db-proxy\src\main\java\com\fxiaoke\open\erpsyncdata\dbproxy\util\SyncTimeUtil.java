package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExtraEventTypeEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-10-17
 */
public class SyncTimeUtil {
    //相同策略聚合新增和更新
    public static List<ErpSyncExtentDTO> mergeEventTypeByPloy(List<ErpSyncExtentDTO> syncTimeVos) {
        if (CollectionUtils.isEmpty(syncTimeVos)) {
            return syncTimeVos;
        }
        //找到包含更新的策略
        Set<String> updateSnapIds = syncTimeVos.stream()
                .filter(v ->
                        v.getOperationType() == ExtraEventTypeEnum.UPDATE.getExtraType()
                                || v.getOperationType() == ExtraEventTypeEnum.PUSH_UPDATE.getExtraType())
                .map(v -> v.getSnapshotId()).collect(Collectors.toSet());
        //移除包含了更新的策略的新增轮询
        syncTimeVos.removeIf(v -> updateSnapIds.contains(v.getSnapshotId())
                && (v.getOperationType() == ExtraEventTypeEnum.ADD.getExtraType()
                || v.getOperationType() == ExtraEventTypeEnum.PUSH_ADD.getExtraType()));
        return syncTimeVos;
    }

    /**
     * 相同对象聚合新增和更新
     *
     * @param syncTimeVos
     * @return
     */
    public static List<ErpSyncExtentDTO> mergeEventTypeByObj(List<ErpSyncExtentDTO> syncTimeVos) {
        List<ErpSyncExtentDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(syncTimeVos)) {
            return result;
        }
        //存在同步时间的事件类型，存在201,202,转换为1,2
        Map<String, Set<Integer>> objSyncTimeEvents = new HashMap<>();
        //找出对象所有开启的事件类型，不存在201,202
        Map<String, Set<Integer>> objOpenTypes = new HashMap<>();
        for (ErpSyncExtentDTO syncTimeVo : syncTimeVos) {
            objOpenTypes.computeIfAbsent(syncTimeVo.getObjectApiName(), v -> new HashSet<>())
                    .addAll(syncTimeVo.getSyncPloyDetailData().getSyncRules().getEvents());
            Integer operationType = syncTimeVo.getOperationType();
            Integer transTimeType = ExtraEventTypeEnum.TRANS_MAP.getOrDefault(operationType, operationType);
            objSyncTimeEvents.computeIfAbsent(syncTimeVo.getObjectApiName(), v -> new HashSet<>())
                    .add(transTimeType);
        }
        objOpenTypes.forEach((obj, openTypes) -> {
            openTypes.retainAll(objSyncTimeEvents.get(obj));
            if (openTypes.contains(ExtraEventTypeEnum.UPDATE.getExtraType())) {
                openTypes.remove(ExtraEventTypeEnum.ADD.getExtraType());
            }
        });
        for (ErpSyncExtentDTO syncTimeVO : syncTimeVos) {
            Integer operationType = syncTimeVO.getOperationType();
            Integer transTimeType = ExtraEventTypeEnum.TRANS_MAP.getOrDefault(operationType, operationType);
            //保留需要同步的事件
            if (objOpenTypes.get(syncTimeVO.getObjectApiName()).contains(transTimeType)) {
                result.add(syncTimeVO);
            }
        }
        return result;
    }
}
