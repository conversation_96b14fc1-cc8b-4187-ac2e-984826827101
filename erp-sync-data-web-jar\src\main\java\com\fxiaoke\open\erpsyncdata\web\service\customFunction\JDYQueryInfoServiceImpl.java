package com.fxiaoke.open.erpsyncdata.web.service.customFunction;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.JDYInfoArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("JDYQueryInfoService")
public class JDYQueryInfoServiceImpl implements CustomFunctionCommonService {

   @Autowired
   private ProxyHttpClient proxyHttpClient;
    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        String requestUrl=ConfigCenter.JDY_INNER_URL.concat("?tenantId=").concat(tenantId);
        Result<JDYInfoArg> jdyInfoArg=proxyHttpClient.getUrl(requestUrl, Maps.newHashMap(),new TypeReference<Result<JDYInfoArg>>(){
        });
        log.info("execute logic jdy:{}", JSONObject.toJSONString(jdyInfoArg));
        return Result.newSuccess(JsonUtil.toJson(jdyInfoArg.getData()));
    }
}
