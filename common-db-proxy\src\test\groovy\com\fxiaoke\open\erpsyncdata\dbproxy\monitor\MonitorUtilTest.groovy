package com.fxiaoke.open.erpsyncdata.dbproxy.monitor

import cn.hutool.core.thread.ConcurrencyTester
import cn.hutool.core.thread.ThreadUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType
import spock.lang.Ignore
import spock.lang.Specification

import java.util.concurrent.atomic.AtomicLong

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/1/6
 */
@Ignore
class MonitorUtilTest extends Specification {
    void setup() {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-erp-sync-data-common")
    }

    def "sendTestMonitor"() {
        AtomicLong atomicLong = new AtomicLong();
        for (i in 0..<10) {
            println("batch ${i} begin")
            ConcurrencyTester tester = ThreadUtil.concurrencyTest(100, { ->
                def id = atomicLong.getAndIncrement()
                MonitorUtil.send("test0223001 ${i} ${id}".toString(), MonitorType.TEST)
            })
            println("batch ${i} cost ${tester.getInterval()}")
        }
        expect:
        true
    }


    def "sendTestMonitorOrder"() {
        AtomicLong atomicLong = new AtomicLong();
        for (i in 0..<10) {
            println("batch ${i} begin")
            for (j in 0..<100) {
                def id = atomicLong.getAndIncrement()
                MonitorUtil.send("test0223002 ${i} ${id}".toString(), String.valueOf(i), MonitorType.TEST)
            }
            println("batch ${i} end ")
        }
        expect:
        true
    }
}
