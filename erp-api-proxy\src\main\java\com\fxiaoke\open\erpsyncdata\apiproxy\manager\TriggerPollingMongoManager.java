package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.HttpUrlUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 15:05 2022/9/13
 * @Desc:
 */
@Component
@Slf4j
public class TriggerPollingMongoManager {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    public void triggerPolling(TriggerPollingData triggerPollingData) {
        log.info("trigger start arg:{}", triggerPollingData);
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "UTF-8");
        String url = HttpUrlUtils.buildTriggerPollingMongoUrl();
        HttpRspLimitLenUtil.ResponseBodyModel triggerResponse = proxyHttpClient.postUrl(url, triggerPollingData, header, (Long)null);
        log.info("trigger end response:{}", triggerResponse);
    }

    public void triggerPolling(String tenantId, Set<String> allSplitObjApiNames) {
        TriggerPollingData triggerPollingData=new TriggerPollingData();
        triggerPollingData.setTenantId(tenantId);
        triggerPollingData.setObjApiName(allSplitObjApiNames);
        triggerPollingData.setTriggerTime(System.currentTimeMillis());
        this.triggerPolling(triggerPollingData);
    }

}
