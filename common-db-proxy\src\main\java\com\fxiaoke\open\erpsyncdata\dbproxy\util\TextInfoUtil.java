package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.facishare.qixin.api.model.message.content.AdvanceText;
import com.facishare.qixin.api.model.message.content.TextInfo;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TextInfo工具类
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/2/17
 */
@UtilityClass
public class TextInfoUtil {

    public TextInfo text(String content, String color, boolean newLine) {
        TextInfo textInfo = new TextInfo();
        textInfo.setContent(content);
        if (color != null) {
            textInfo.setColor(color);
        }
        textInfo.setNewLine(newLine);

        return textInfo;
    }

    public TextInfo url(String content, String url) {
        return url(content, url, true);
    }


    public TextInfo url(String content, String url, boolean newLine) {
        TextInfo textInfo = new TextInfo();
        textInfo.setUrl(url);
        textInfo.setContent(content);
        textInfo.setActionType(TextInfo.URL);
        textInfo.setNewLine(newLine);
        //公司UI的蓝色
        textInfo.setColor("#0c6cff");
        return textInfo;
    }

    /**
     * 默认换行
     *
     * @param content
     * @return
     */
    public TextInfo text(String content) {
        //默认黑色
        return text(content, "#000000", true);
    }

    public TextInfo text(String content, boolean newLine) {
        //默认黑色
        return text(content, "#000000", newLine);
    }

    /**
     * 默认黑色
     *
     * @param
     * @return
     */
    public TextInfo colorText () {
        //默认黑色
        return text(null, "#000000", true);
    }

    public List<TextInfo> textInfoList(TextInfo... infos) {
        List<TextInfo> textInfoList = new ArrayList<>(Arrays.asList(infos));
        return textInfoList;
    }

    public AdvanceText advanceText(TextInfo... infos) {
        AdvanceText advanceText = new AdvanceText();
        List<TextInfo> textInfoList = textInfoList(infos);
        advanceText.setTextInfoList(textInfoList);
        return advanceText;
    }


    /**
     * 文本转换，按行
     * null不转换
     */
    public AdvanceText lines2Text(String... contents) {
        AdvanceText advanceText = new AdvanceText();
        List<TextInfo> textInfoList = Arrays.stream(contents).filter(v -> v != null)
                .map(TextInfoUtil::text).collect(Collectors.toList());
        advanceText.setTextInfoList(textInfoList);
        return advanceText;
    }
}
