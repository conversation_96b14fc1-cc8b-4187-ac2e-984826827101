package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant.U8ObjIdFieldConfig;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("customer")
public class U8CustomerManager extends U8DefaultMananger {



  @Override
  protected Map transferStandardToU8(String objApiName, StandardData standardData) {

    Map map = super.transferStandardToU8(objApiName, standardData);
    Map customer= (Map) map.get("customer");
    generatorCustomerDetailStruct(customer,standardData);
    return map;
  }

  private static Map generatorCustomerDetailStruct(Map customer,StandardData standardData) {

    Map<String, List<ObjectData>> detailFieldVals = standardData.getDetailFieldVals();
    if (detailFieldVals == null) {
      detailFieldVals = new HashMap<>();
    }
    Object code = customer.get("code");
    Map<String, List<ObjectData>> addressesDetail = new HashMap<>();
    Map<String, List<ObjectData>> banksDetail = new HashMap<>();
    //Map<String, List<ObjectData>> invoicecustomersDetail = new HashMap<>();
    Map<String, List<ObjectData>> usersDetail	= new HashMap<>();
    Map<String, List<ObjectData>> authsDetail = new HashMap<>();

    //明细主从字段复写
    List<ObjectData> addressesList = detailFieldVals.get("addresses");
    if (addressesList!=null){
      for (ObjectData objectData : addressesList) {
        objectData.put("ccuscode",code);
      }
    }

    List<ObjectData> banksList = detailFieldVals.get("banks");
    if (banksList != null) {
      for (ObjectData objectData : banksList) {
        objectData.put("ccuscode", code);
        objectData.remove("tenant_id");
        objectData.remove("owner");
        objectData.remove("_id");
        objectData.remove("created_by");
      }
    }

    List<ObjectData> invoicecustomersList = detailFieldVals.get("invoicecustomers");
    if (invoicecustomersList != null) {
      for (ObjectData objectData : invoicecustomersList) {
        objectData.put("ccuscode", code);
      }
    }

    List<ObjectData> usersList = detailFieldVals.get("users");
    if (usersList != null) {
      for (ObjectData objectData : usersList) {
        objectData.put("ccuscode", code);
      }
    }

    List<ObjectData> authsList = detailFieldVals.get("auths");
    if (authsList != null) {
      for (ObjectData objectData : authsList) {
        objectData.put("ccuscode", code);
      }
    }

    if (standardData.getMasterFieldVal().getString("_id")!=null){
      //客户地址
      if (addressesList != null) {
        addressesDetail.put("cusdeliveradd", addressesList);
        customer.put("devliver_site",addressesDetail);
        customer.remove("addresses");
      }

      //客户银行
      if (banksList != null) {
        banksDetail.put("customerbank", banksList);
        customer.put("bank_open",banksDetail);
        customer.remove("banks");
      }

      //客户开票，不支持更新
      if (invoicecustomersList != null) {
//        invoicecustomersDetail.put("sa_invoicecustomers", invoicecustomersList);
//        map.put("sa_invoicecustomersall",invoicecustomersDetail);
        customer.remove("invoicecustomers");
      }

      //相关员工
      if (usersList != null) {
        usersDetail.put("customer_user", usersList);
        customer.put("customer_userall",usersDetail);
        customer.remove("users");
      }

      //管理维度
      if (authsList != null) {
        authsDetail.put("customer_auth", authsList);
        customer.put("customer_authall",authsDetail);
        customer.remove("auths");
      }
    }

    return customer;
  }

  @Override
  public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
    StandardData  standardData2= BeanUtil.deepCopy(standardData, standardData.getClass());
    Result<ErpIdResult> erpObjData = super.createErpObjData(standardData, connectInfo);
    generatorCustomerDetailIds(standardData2,erpObjData);
    return erpObjData;
  }

  @Override
  public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
    StandardData  standardData2= BeanUtil.deepCopy(standardData, standardData.getClass());
    Result<ErpIdResult> erpObjData = super.updateErpObjData(standardData, connectInfo);
    generatorCustomerDetailIds(standardData2,erpObjData);
    return erpObjData;
  }


  private void generatorCustomerDetailIds(StandardData standardData,Result<ErpIdResult> erpObjData){
    String apiName = standardData.getObjAPIName();
    if (erpObjData.isSuccess()) {
      String masterId = erpObjData.getData().getMasterDataId();
      Map<String, List<String>> detailIdMap = new HashMap<>();
      erpObjData.getData().setDetailDataIds(detailIdMap);

      for (Map.Entry<String, List<ObjectData>> entry : standardData.getDetailFieldVals().entrySet()) {
        String entryApiName = entry.getKey();
        String entryIdCodes = U8ObjIdFieldConfig.getApiName(apiName + "." + entryApiName);

        String[] idCodes = entryIdCodes.split(",");
        List<String>detailIds=new ArrayList<>();
        for (ObjectData objectData : entry.getValue()) {
          String detailId = masterId;
          for (String idCode : idCodes) {
            detailId = detailId + "#" + objectData.get(idCode);
          }
          detailIds.add(detailId);
        }
        detailIdMap.put(entryApiName,detailIds);
      }

    }
  }

}
