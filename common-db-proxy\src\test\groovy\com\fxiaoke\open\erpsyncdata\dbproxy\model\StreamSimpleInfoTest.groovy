package com.fxiaoke.open.erpsyncdata.dbproxy.model

import cn.hutool.core.io.unit.DataSizeUtil
import cn.hutool.core.util.RandomUtil
import com.facishare.paas.pod.util.IdUtil
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class StreamSimpleInfoTest extends Specification {

    def "testStreamInfoSize"() {
        def infos = (1..10000).collect {
            def simpleInfo = new StreamSimpleInfo()
                    .setStreamId(IdUtil.generateId())
                    .setCrmObjApiName("crmObjApiName_" + RandomUtil.randomString(15))
                    .setErpObjApiName("erpObjApiName_" + RandomUtil.randomString(15))
                    .setErpDcId(IdUtil.generateId())
                    .setSourceTenantType(RandomUtil.randomInt(1, 3))
                    .setStatus(RandomUtil.randomInt(1, 3))
                    .setConnectorKey(RandomUtil.randomEle(ErpChannelEnum.values()).name())
                    .setStreamLastUpdateTime(RandomUtil.randomLong(*************, *************))
        }
        def size = RamUsageEstimateUtil._sizeOfPojoList(infos)
        print(DataSizeUtil.format(size))
        expect:
        size < 10 * 1024 * 1024
    }

    def "testStreamInfoSize1"() {
        def infos = (1..60000).collect {
            def simpleInfo = new StreamSimpleInfo()
                    .setStreamId(UUID.randomUUID().toString())
                    .setTenantId("fktest"+it)
                    .setEnterpriseAccount("测试用数据"+it)
                    .setIntegrationStreamName("测试用集成流"+it)
                    .setStatus(1)
                    .setErpDcId(UUID.randomUUID().toString())
                    .setSourceTenantType(RandomUtil.randomInt(1, 3))
                    .setCrmObjApiName("crmObjApiName_" + RandomUtil.randomString(15))
                    .setErpObjApiName("erpObjApiName_" + RandomUtil.randomString(15))
                    .setConnectorKey(RandomUtil.randomEle(ErpChannelEnum.values()).name())
                    .setStreamLastUpdateTime(RandomUtil.randomLong(*************L, *************L))
//                    .setMongoLastSyncTime(new Date(RandomUtil.randomLong(*************L, *************L)));
        }
        def size = RamUsageEstimateUtil._sizeOfPojoList(infos)
        print(DataSizeUtil.format(size))
        expect:
        size < 50 * 1024 * 1024
    }
}
