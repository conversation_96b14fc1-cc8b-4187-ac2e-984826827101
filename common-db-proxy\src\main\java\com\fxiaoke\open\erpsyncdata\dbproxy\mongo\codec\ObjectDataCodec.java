package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.mongodb.MongoClient;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Slf4j
public class ObjectDataCodec implements Codec<ObjectData> {
    private final Codec<String> stringCodec;

    /**
     * Default constructor.
     */
    public ObjectDataCodec() {
        this.stringCodec = MongoClient.getDefaultCodecRegistry().get(String.class);
    }


    private ObjectData convert(String document) {
        if (document == null) {
            return null;
        }
        try {
            ObjectData objectData = JacksonUtil.fromJson(document, ObjectData.class);
            return objectData;
        } catch (Exception e) {
            log.warn("invalid document");
        }
        return null;
    }

    @Override
    public ObjectData decode(BsonReader reader, DecoderContext decoderContext) {
        String document = stringCodec.decode(reader, decoderContext);
        ObjectData syncData = convert(document);
        return syncData;
    }

    @Override
    public void encode(BsonWriter writer, ObjectData value, EncoderContext encoderContext) {
        String str;
        try {
            str = JacksonUtil.toJson(value);
        } catch (Exception e) {
            log.error("objectDataCodec encode value error", e);
            str = "{}";
        }
        stringCodec.encode(writer, str, encoderContext);
    }

    @Override
    public Class<ObjectData> getEncoderClass() {
        return ObjectData.class;
    }
}
