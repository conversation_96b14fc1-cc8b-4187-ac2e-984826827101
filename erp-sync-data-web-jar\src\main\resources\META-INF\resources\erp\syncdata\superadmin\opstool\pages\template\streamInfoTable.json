{"type": "page", "remark": "", "name": "streamInfoTable", "body": [{"type": "crud", "name": "streamInfoCRUD", "api": {"type": "get", "url": "../template/listInfos", "autoRefresh": false, "replaceData": false, "data": {"type": "stream", "templateId": "${templateId}"}}, "loadDataOnce": true, "primaryField": "streamName", "defaultParams": {"perPage": 100}, "filter": {"title": "查询条件", "name": "streamFilter", "body": {"name": "templateId", "label": "模板", "type": "select", "mode": "inline", "size": "lg", "labelField": "title", "valueField": "id", "selectFirst": true, "source": {"method": "get", "url": "../template/list"}, "extractValue": true, "searchable": true}}, "headerToolbar": ["export-csv", "reload"], "columns": [{"name": "streamName", "label": "集成流名称"}, {"name": "sourceTenantType", "label": "源企业类型"}, {"name": "sourceObjectApiName", "label": "源对象ApiName"}, {"name": "sourceObjectName", "label": "源对象名称"}, {"name": "destObjectApiName", "label": "目标对象ApiName"}, {"name": "destObjectName", "label": "目标对象名称"}], "affixHeader": true, "placeholder": "暂无数据"}]}