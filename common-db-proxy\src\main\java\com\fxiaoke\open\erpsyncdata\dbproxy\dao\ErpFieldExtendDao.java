package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/4
 */
@Repository
public interface ErpFieldExtendDao extends ErpBaseDao<ErpFieldExtendEntity>, ITenant<ErpFieldExtendDao> {

    int deleteByTenantId(@Param("tenantId")String tenantId);

    /**
     * 查找所有字段扩展
     * @param tenantId
     * @param objApiName
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    List<ErpFieldExtendEntity> queryByObjApiName(@Param("tenantId")String tenantId,
                                                 @Param("dataCenterId")String dataCenterId,
                                                 @Param("objApiName")String objApiName);

    /**
     * 查找所有指定字段queryCode的扩展
     */
    List<ErpFieldExtendEntity> queryByObjQueryCode(@Param("tenantId")String tenantId,
                                                   @Param("dataCenterId")String dataCenterId,
                                                   @Param("objApiName")String objApiName,
                                                   @Param("codeList") Collection<String> queryCode);
    /**
     * 查找所有字段扩展
     * @param tenantId
     * @param objApiName
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    List<ErpFieldExtendEntity> getAllNeedQueryFieldExtend(@Param("tenantId")String tenantId,
                                                          @Param("dataCenterId")String dataCenterId,
                                                          @Param("objApiName")String objApiName);
    /**
     * 查找真实ID字段扩展
     * @param tenantId
     * @param objApiName
     * @return
     */
    List<ErpFieldExtendEntity> queryIdFieldByObjApiName(@Param("tenantId")String tenantId,
                                                        @Param("dataCenterId")String dataCenterId,
                                                        @Param("objApiName")String objApiName);

    int deleteByTenantIdAndObjApiName(@Param("tenantId")String tenantId,
                                      @Param("dataCenterId")String dataCenterId,
                                      @Param("objApiName")String objApiName);

    int deleteFieldExtendOne(@Param("tenantId")String tenantId,
                             @Param("dataCenterId")String dataCenterId,
                             @Param("objApiName")String objApiName,
                             @Param("fieldApiName")String fieldApiName);

    ErpFieldExtendEntity findOne(@Param("tenantId")String tenantId,
                                  @Param("dataCenterId")String dataCenterId,
                                  @Param("objApiName")String objApiName,
                                  @Param("fieldApiName")String fieldApiName);

    List<ErpFieldExtendEntity> findByDefineType(@Param("tenantId") String tenantId,
                                          @Param("dataCenterId") String dataCenterId,
                                          @Param("objApiName") String objApiName,
                                          @Param("fieldDefineType") ErpFieldTypeEnum fieldDefineType);

    /**
     * 通过对象和字段名修改
     * tenantId传01修改所有的
     */
    int updateByApiName(@Param("updated")ErpFieldExtendEntity updated);
    void superInsertSql(@Param(value="sqlStr") String sqlStr, @Param(value="tenantId") String tenantId);
    List<Map<String,Object>> superQuerySql(@Param(value="sqlStr") String sqlStr, @Param(value="tenantId") String tenantId);
    void superUpdateSql(@Param(value="sqlStr") String sqlStr);

    /**
     * 查找
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    ErpFieldExtendEntity getNumFieldByObjApiName(@Param("tenantId")String tenantId,
                                                         @Param("dataCenterId")String dataCenterId,
                                                         @Param("objApiName")String objApiName);

    int updateQueryCodeById(@Param("id")String id,@Param("queryCode")String queryCode,@Param("erpFieldType")String erpFieldType);

    int updatePriorityField(@Param("tenantId")String tenantId,
                            @Param("dataCenterId")String dataCenterId,
                                 @Param("objApiName")String objApiName,
                                 @Param("fieldApiName")String fieldApiName,
                                 @Param("priority")Long priority);

    int updateSaveExtendById(@Param("id")String id,@Param("updatedSaveExtend")String updatedSaveExtend);

    int batchUpdateFieldExtendStatus(@Param("tenantId") String tenantId,
                                     @Param("dataCenterId")String dataCenterId,
                                     @Param("fieldApiNames") Set<String> fieldApiNames,
                                     @Param("objApiName") String objApiName,
                                     @Param("status") Boolean queryStatus);

    void updateErpFieldTypeById(@Param("id") String id, @Param("erpFieldType") String erpFieldType);

    List<ErpFieldExtendEntity> pageByDcId(@Param("tenantId") String tenantId, @Param("dataCenterId")String dcId, @Param("id") String maxId, @Param("pageSize") int pageSize);

    int deleteByTenantIdAndDataCenterId(@Param("tenantId") String tenantId, @Param("dataCenterId")String dataCenterId);
}