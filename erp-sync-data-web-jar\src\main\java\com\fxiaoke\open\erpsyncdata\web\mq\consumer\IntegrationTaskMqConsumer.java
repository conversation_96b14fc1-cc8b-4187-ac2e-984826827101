package com.fxiaoke.open.erpsyncdata.web.mq.consumer;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.IntegrationTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.IntegrationTaskDao;
import com.fxiaoke.open.erpsyncdata.web.mq.consumer.base.AbstractMQConsumer;
import com.fxiaoke.open.erpsyncdata.web.mq.model.IntegrationTaskEvent;
import com.fxiaoke.open.erpsyncdata.web.service.IntegrationTaskService;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import redis.clients.jedis.params.SetParams;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/23 11:07:06
 * hardy 不想使用mq,认为没有大数据量,直接调用就好
 */
@Slf4j
//@Component
public class IntegrationTaskMqConsumer extends AbstractMQConsumer<IntegrationTaskEvent> {
    @Autowired
    private IntegrationTaskDao integrationTaskDao;
    @Autowired
    private MergeJedisCmd jedisSupport;
    @Autowired
    private IntegrationTaskService integrationTaskService;

    public IntegrationTaskMqConsumer() {
        super("erp-sync-data-mq", "enable-integration-mq");
    }

    public static final String INTEGRATION_TASK_TOPIC = "erpdss-integration-task";

    @Override
    protected void consumeOneMessage(IntegrationTaskEvent event) throws Exception {
        final IntegrationTaskEntity integrationTaskEntity = integrationTaskDao.startProcess(event.getId());

        final Pair<Boolean, String> booleanStringPair = integrationTaskService.doTask(integrationTaskEntity);
        integrationTaskDao.updateStatus(event.getId(), BooleanUtils.isTrue(booleanStringPair.getLeft()) ? 2 : 3, booleanStringPair.getRight());

//            全部完成,发送通知
        if (integrationTaskDao.allTaskDone(integrationTaskEntity.getTaskId())) {
            sendNotify(integrationTaskEntity.getTaskId());
        }
    }

    private void sendNotify(String taskId) {
//        抢锁,防止重复发送.不需要解锁,超时删除就好
        if (jedisSupport.set("notify:" + taskId, "1", SetParams.setParams().nx().ex(60L)) == null) {
            return;
        }

        final long success = integrationTaskDao.countSuccess(taskId);
        final List<IntegrationTaskEntity> allFailTask = integrationTaskDao.getAllFailTask(taskId);
        integrationTaskService.sendResultMessage(taskId, success, allFailTask);
    }
}
