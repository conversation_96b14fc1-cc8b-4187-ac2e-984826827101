package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import java.io.Serializable;
import java.util.Date;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 特征变化同步配置表
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/5/20
 */
@Data
@Table(name = "erp_feature_sync")
public class ErpFeatureSyncEntity implements Serializable {
    @Id
    private String id;

    @TenantID
    private String tenantId;

    /**
     * 策略明细Id
     * ERP往CRM方向
     */
    private String syncPloyDetailId;

    /**
     * crm对象筛选条件，仅为主对象条件
     * com.fxiaoke.crmrestapi.arg.ControllerListArg
     */
    private String condition;

    /**
     * 特征计算方法，json
     */
    private String calculation;

    private Date createTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;
}