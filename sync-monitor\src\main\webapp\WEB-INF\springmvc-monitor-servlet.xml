<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:beans="http://www.springframework.org/schema/beans"
       xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context-3.2.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
	                       http://www.springframework.org/schema/mvc
	                       http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">


    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.monitor">
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
    </context:component-scan>

    <aop:aspectj-autoproxy/>

    <mvc:default-servlet-handler/>

    <mvc:annotation-driven>
        <mvc:message-converters>
            <!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
            <beans:bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <beans:constructor-arg value="UTF-8"/>
            </beans:bean>
        </mvc:message-converters>
        <!--异步请求超时配置-->
        <mvc:async-support default-timeout="15000" task-executor="taskExecutor"/>
    </mvc:annotation-driven>
    <beans:import resource="classpath*:spring/monitor-applicationContext.xml" />
</beans>
