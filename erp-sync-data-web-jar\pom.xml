<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-erp-sync-data</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>erp-sync-data-web-jar</artifactId>
    <packaging>jar</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>erp-sync-data-web-jar</name>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>


        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-common</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-remoting</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-util</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.util.bundles</groupId>
                    <artifactId>javassist-3.14.0-GA</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-core</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-runtime</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency><!-- jackson -->
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-core-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-mapper-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jeromq</artifactId>
                    <groupId>org.zeromq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-expression</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-annotations</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cglib-nodep</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>java-utils</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>joda-time</artifactId>
                    <groupId>joda-time</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-annotations</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>


        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rpc-trace</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eye-j4log</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-kafka-support</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>java-utils</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.cloud</groupId>
            <artifactId>datapersist</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eye-j4log</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-text</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>java-utils</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java-util</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>core-filter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rpc-trace</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>java-utils</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eye-j4log</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-kafka-support</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>http-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rpc-trace</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-circuit-breaker</artifactId>
                    <groupId>com.fxiaoke.circuit.breaker</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>rpc-trace</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>biz-log-client</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eye-j4log</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>java-utils</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-converter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-aviator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-writer</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-crm-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-cep-plugin-document</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-dubbo-rest-plugin</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-enterprise-id-account-converter</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>http-spring-support</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rpc-trace</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>datapersist</artifactId>
                    <groupId>com.fxiaoke.cloud</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-preprocess-data-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-api-proxy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-preprocess-data</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--数据同步 依赖平台底层的模块 end-->

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.zhxing</groupId>
            <artifactId>retrofit-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-id-account-converter</artifactId>
            <version>1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>common-db-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>json</artifactId>
                    <groupId>org.json</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version>
        </dependency>

        <!-- cookie换取身份信息 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-active-session-manage-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>
        <!--文件系统-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-stone-sdk</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--文件上传-->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.4</version>
        </dependency>
        <!--文件服务-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-uc-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-license-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-sync-data-admin</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ehcache</artifactId>
                    <groupId>org.ehcache</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.12.2</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-main</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-cep-spring-plugin</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-jwt</artifactId>
            <version>5.7.9</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-transfer</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>net.shibboleth.utilities</groupId>
            <artifactId>java-support</artifactId>
            <version>7.2.0</version>
        </dependency>

        <!--com.alibaba.rocketmq 升级为 org.apache.rocketmq 后,我们的代码里面已经不再使用com.alibaba的mq了。
              alibaba的mq还不能去掉, 因为我们引入的fs-uc-cache-no-dubbo.xml文件里面用到了。 -->
        <!--        <dependency>-->
        <!--            <artifactId>rocketmq-client</artifactId>-->
        <!--            <groupId>com.alibaba.rocketmq</groupId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-user-login-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport</artifactId>
        </dependency>


        <!--powermock-->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-common</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-html-parser</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>


        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- JUnit Vintage Engine (allows JUnit 4 tests to run via JUnit 5 platform) -->
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
