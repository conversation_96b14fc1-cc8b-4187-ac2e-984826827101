package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorConfigHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
public class ConnectorManager {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.HOURS)
    public Long getConnectorFeatures(String tenantId, String dcId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
        if (Objects.isNull(configHandler)) {
            return 0L;
        }
        StandardConnectParam connectParam = ErpChannelEnum.STANDARD_CHANNEL.getConnectParam(connectInfo.getConnectParams());
        if (connectParam != null && connectParam.getAuthType() != null) {
            GetConnectorIntroArg getConnectorIntroArg = new GetConnectorIntroArg();
            getConnectorIntroArg.setAuthType(connectParam.getAuthType());
            Result<ConnectorIntro> connectorIntro = configHandler.getConnectorIntro(connectInfo, getConnectorIntroArg);
            if (connectorIntro.isSuccess()) {
                return connectorIntro.getData().getFeatures();
            } else {
                //请求失败时，返回null，不缓存
                return null;
            }
        }
        //不支持
        return 0L;
    }
}
