package com.fxiaoke.open.erpsyncdata.monitor.handler

import com.fxiaoke.common.http.handler.AsyncCallback
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.OuterConnectorManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.CheckStatusUrl
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil
import com.fxiaoke.open.erpsyncdata.monitor.util.MonitorConfigCenter
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo
import com.google.common.collect.ImmutableList
import com.xxl.job.core.biz.model.ReturnT
import com.xxl.job.core.biz.model.TriggerParam
import okhttp3.*
import spock.lang.Specification
import spock.lang.Subject

/**
 *
 * <AUTHOR> (^_−)☆
 */
class ServerStatusMonitorHandlerTest extends Specification {
    @Subject
    ServerStatusMonitorHandler serverStatusMonitorHandler

    OkHttpSupport okHttpSupport = Mock()
    OuterConnectorManager outerConnectorManager = Mock()

    def setup() {
        MonitorBizLogUtil.enable = false

        outerConnectorManager.getHubInfoList() >> {
            return [
                    HubInfo.builder().baseUrl('http://hub1.cn').name('hub1').build()
            ]
        }
        MonitorConfigCenter.checkStatusUrls = ImmutableList.copyOf([
                CheckStatusUrl.builder().url("http://test.cn/check").name("test").type("test").build()
        ])
        serverStatusMonitorHandler = new ServerStatusMonitorHandler(
                okHttpSupport: okHttpSupport,
                outerConnectorManager: outerConnectorManager
        )
    }

    def "test execute with valid trigger param"() {
        given:
        TriggerParam triggerParam = new TriggerParam()

        when:
        okHttpSupport.asyncExecute(_ as Request, _ as AsyncCallback) >> { Request request, AsyncCallback callback ->
            ResponseBody responseBody = ResponseBody.create(
                    "{\"message\":\"Hello, world!\"}",
                    MediaType.get("application/json; charset=utf-8"))
            // 使用 Response.Builder 创建一个 Response
            Response response = new Response.Builder()
                    .code(200) // HTTP 状态码
                    .message("OK") // 状态信息
                    .protocol(Protocol.HTTP_2) // 使用的协议
                    .request(request) // 关联的请求
                    .body(responseBody) // 响应体
                    .build();
            callback.response(response)
        }

        def result = serverStatusMonitorHandler.execute(triggerParam)

        then:
        result == ReturnT.SUCCESS
    }


    def "test temp hub unOK"() {
        given:
        TriggerParam triggerParam = new TriggerParam()
        when:

        okHttpSupport.asyncExecute(_ as Request, _ as AsyncCallback) >> { Request request, AsyncCallback callback ->
            ResponseBody responseBody = ResponseBody.create(
                    "{\"message\":\"Hello, world!\"}",
                    MediaType.get("application/json; charset=utf-8"))
            // 使用 Response.Builder 创建一个 Response
            Response response = new Response.Builder()
                    .code(500) // HTTP 状态码
                    .message("OK") // 状态信息
                    .protocol(Protocol.HTTP_2) // 使用的协议
                    .request(request) // 关联的请求
                    .body(responseBody) // 响应体
                    .build();
            callback.response(response)
        }

        def result = serverStatusMonitorHandler.execute(triggerParam)

        then:
        result == ReturnT.SUCCESS
    }

}
