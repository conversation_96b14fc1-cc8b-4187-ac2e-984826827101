package com.fxiaoke.open.erpsyncdata.monitor.model.result;

import cn.hutool.core.map.multi.ListValueMap;
import com.fxiaoke.open.erpsyncdata.monitor.constant.CompareDataErrorType;
import com.fxiaoke.open.erpsyncdata.monitor.constant.CompareResultType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@Data
@Accessors(chain = true)
public class CompareResult {
    /**
     * 即便异常了，可能也有部分成功的结果
     */
    private CompareResultType resultType = CompareResultType.SUCCESS;
    private Long totalCount = 0L;
    private Long errorCount = 0L;
    private final ListValueMap<CompareDataErrorType, String> dataErrorTypeMap = new ListValueMap<>();

    public void addError(String dataId, CompareDataErrorType errorType) {
        dataErrorTypeMap.putValue(errorType, dataId);
        errorCount++;
    }
}
