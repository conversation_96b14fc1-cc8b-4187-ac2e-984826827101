package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate;

/**
 * 云星空旗舰版对象apiName汇总
 * <AUTHOR>
 * @date 2023-09-19
 */
public interface K3UltimateObjApiName {
    /**
     * 业务单元（组织机构）对象apiName
     */
    String bos_org = "bos_org";
    /**
     * 客户对象apiName
     */
    String bd_customer = "bd_customer";
    /**
     * 客户联系人对象apiName，虚拟对象，ERP不存在，对接专用
     */
    String bd_customer_contacts = "bd_customer_contacts";
    /**
     * 客户分组对象apiName
     */
    String bd_customergroup = "bd_customergroup";
    /**
     * 客户地址对象apiName
     */
    String bd_address = "bd_address";
    /**
     * 物料对象apiName
     */
    String bd_material = "bd_material";
    /**
     * 物料分组对象apiName
     */
    String bd_materialgroup = "bd_materialgroup";
    /**
     * 销售订单对象apiName
     */
    String sm_salorder = "sm_salorder";
    /**
     * 销售订单变更单对象apiName
     */
    String sm_xssalorder = "sm_xssalorder";
}
