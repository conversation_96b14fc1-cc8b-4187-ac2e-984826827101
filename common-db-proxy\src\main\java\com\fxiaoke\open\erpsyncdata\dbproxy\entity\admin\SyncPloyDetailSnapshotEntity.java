package com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin;


import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapStringStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 同步快照实例
 */
@Data
@Table(name = "sync_ploy_detail_snapshot")
public class SyncPloyDetailSnapshotEntity extends BaseEntity implements Serializable {
    /** 快照详情主键id */
    private String id;
    /** 源企业账号id */
    @TenantID
    private String sourceTenantId;
    /** 源企业主对象apiName */
    private String sourceObjectApiName;
    /** 目标企业账号id */
    @TenantID
    private String destTenantId;
    /** 目标企业主对象apiName */
    private String destObjectApiName;
    /** 策略状态 1.启用 2.停用 {@link  SyncPloyDetailStatusEnum} */
    private Integer status;
    /**
     * 策略对象主键id
     * 已作废，仅为刷库使用
     */
    private String syncPloyId;
    /** 策略对象所含详情主键id */
    private String syncPloyDetailId;
    /**
     * 策略对象数据封装
     * 已作废，仅为刷库使用
     */
    @Deprecated
    private SyncPloyData syncPloyData;
    /** 策略对象所含详情数据封装 */
    private SyncPloyDetailData syncPloyDetailData;
    private String syncConditionsExpression;
    /** 从对象表达式 */
    private MapStringStringData detailObjectSyncConditionsExpressions = new MapStringStringData();
}
