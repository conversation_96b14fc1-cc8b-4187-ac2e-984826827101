package com.fxiaoke.open.erpsyncdata.dbproxy.Impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.qixin.api.constant.AuthSourceType;
import com.facishare.qixin.api.model.EnterpriseEnv;
import com.facishare.qixin.api.model.message.content.AdvanceText;
import com.facishare.qixin.api.model.message.content.TextInfo;
import com.facishare.qixin.api.model.open.arg.OpenSendMessageBatchAsyncArg;
import com.facishare.qixin.api.model.session.InternationalInfo;
import com.facishare.qixin.api.open.OpenMessageBatchService;
import com.facishare.restful.client.FRestApiProxyFactory;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ConfigurationConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MessageNotificationConfiguration;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataIntegrationNotificationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.BuildExcelFileResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TextInfoUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.vo.SyncDataNotifyExcelVo;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendSuperAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AlertArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EmailNotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SmsNotificationService;
import com.fxiaoke.otherrestapi.eservice.common.util.UUIDUtil;
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg;
import com.fxiaoke.paasauthrestapi.result.RoleUserResult;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/1
 */
@Slf4j
@Lazy
@Service("notificationService")
public class NotificationServiceImpl implements NotificationService {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ObjectDataService objectDataService;
    private MessageServiceV2 messageServiceV2;
    // no usage
//    @Autowired
//    private EmployeeProviderService employeeProviderService;
    @Autowired
    private PaasAuthService paasAuthService;
    @Autowired
    private OpenMessageBatchService openMessageBatchService;
    @Autowired
    private DBFileManager DBFileManager;
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private EmailNotificationService emailNotificationService;
    @Autowired
    private SmsNotificationService smsNotificationService;

    // no usage
//    @Autowired
//    private ObjectDataServiceV3 objectDataServiceV3;

    //通知消息的单条文本大小限度
    public static Integer MAX_LENGTH=180;
    //列表的长度
    public static Integer DATA_SIZE=3;

    //集成平台通知消息在消息中心的备案key（岩超需要这个）
    private static String ERPDSS_NOTIFY_KEY = "erpdssnotify";

    @PostConstruct
    public void init() throws Exception {
        messageServiceV2 = FRestApiProxyFactory.getInstance().create(MessageServiceV2.class);
    }

    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile failed for ea:{}, userid:{} ",ea,userId);
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,ei+"");
    }

    /**
     * 发送ERP数据同步应用通知到企信
     * 文本消息
     *
     * @param arg
     * @return
     */
    @Override
    public Result<Void> sendErpSyncDataAppNotice(SendTextNoticeArg arg,
                                                 AlarmRuleType alarmRuleType,
                                                 String alarmRuleName,
                                                 AlarmType alarmType,
                                                 AlarmLevel alarmLevel) {
        final Result<Void> result = sendAppTextMsg(arg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);

        // 蒙牛下游信息发送到实施人员
        sendToOtherTenant(arg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);

        return result;
    }

    public Result<Void> sendAppTextMsg(SendTextNoticeArg arg,
                                       AlarmRuleType alarmRuleType,
                                       String alarmRuleName,
                                       AlarmType alarmType,
                                       AlarmLevel alarmLevel) {
        String appId = ConfigCenter.ERP_SYNC_DATA_APP_ID;
        return sendAppTextMsg(arg, appId,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
    }

    private Result<Void> sendAppTextMsg(SendTextNoticeArg arg,
                                        String appId,
                                        AlarmRuleType alarmRuleType,
                                        String alarmRuleName,
                                        AlarmType alarmType,
                                        AlarmLevel alarmLevel) {
        String msg = arg.getMsg();
        String ea;
        if (StringUtils.isBlank(arg.getEnterpriseAccount())) {
            String tenantId = arg.getTenantId();
            int intTenantId = Integer.parseInt(tenantId);
            ea = eieaConverter.enterpriseIdToAccount(intTenantId);
        } else {
            ea = arg.getEnterpriseAccount();
        }
        List<Integer> receivers = arg.getReceivers();
        String msgTitle = arg.getMsgTitle();
        if (StringUtils.isNotBlank(msgTitle)) {
            if (!arg.isUseOriginalTitle()) {
                msgTitle = String.format("---%s---", msgTitle);
            }
            //String alarmTypeName = alarmLevel.getName(i18NStringManager,null,arg.getTenantId()) + alarmType.getName(i18NStringManager,null,arg.getTenantId());
            msg = String.format("%s\n%s", msgTitle ,msg);
        }
        //如果文本信息过长，则转换成文本文件发送
        if (msg.length() < ConfigCenter.NOTICE_MAX_SIZE) {
            sendRichTextMessage(ea, receivers, appId, msg,arg,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        } else {
            sendTextTransFileMsg(ea, receivers, appId, msgTitle, msg,arg,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendErpSyncDataAppMultiNotice(List<TextInfo> textInfos,
                                                      String tenantId,
                                                      List<Integer> toUserLists,
                                                      AlarmRuleType alarmRuleType,
                                                      String alarmRuleName,
                                                      AlarmType alarmType,
                                                      AlarmLevel alarmLevel,
                                                      List<NotifyType> notifyType) {
        AdvanceText advanceText = new AdvanceText();
        advanceText.setTextInfoList(textInfos);
        String enterpriseAccount;
        int intTenantId = Integer.parseInt(tenantId);
        enterpriseAccount = eieaConverter.enterpriseIdToAccount(intTenantId);
        sendMultiNoticeWithType(tenantId,
                enterpriseAccount,
                null,
                null,
                advanceText,
                toUserLists,
                ConfigCenter.ERP_SYNC_DATA_APP_ID,
                alarmRuleType,
                alarmRuleName,
                alarmType,
                alarmLevel,
                notifyType);
        return Result.newSuccess();
    }
    private void sendMultiNoticeWithType(String tenantId,
                                      String ea,
                                      String dataCenterId,
                                      String ployDetailId,
                                      AdvanceText advanceText,
                                      List<Integer> toUserLists,
                                      String appId,
                                      AlarmRuleType alarmRuleType,
                                      String alarmRuleName,
                                      AlarmType alarmType,
                                      AlarmLevel alarmLevel,
                                      List<NotifyType> notifyType) {
        String content = JSON.toJSONString(advanceText, SerializerFeature.DisableCircularReferenceDetect);
        OpenSendMessageBatchAsyncArg asyncArg = new OpenSendMessageBatchAsyncArg();
        asyncArg.setPostId( UUID.randomUUID().toString());//postId
        asyncArg.setToUserList(toUserLists);//接收人员
        asyncArg.setMessageType("AT");//消息类型
        asyncArg.setMessageContent(content);
        asyncArg.setInnerApp(false);
        asyncArg.setSource(AuthSourceType.system);
        asyncArg.setEnv(EnterpriseEnv.INNER);//内部企业
        asyncArg.setEnterpriseAccount(ea);//企业EA
        asyncArg.setAppId(appId);//服务号ID
        asyncArg.setContentInfo(new InternationalInfo());//内容国际化信息
        asyncArg.setSummaryInfo(new InternationalInfo());//推送描述国际化信息
        log.info("send MSG arg:{}",JSONObject.toJSONString(asyncArg));
        boolean result = openMessageBatchService.sendMessageBatchAsyncV2(asyncArg);

        if(CollectionUtils.isNotEmpty(notifyType)){
            if (notifyType.contains(NotifyType.QIXIN)) {
                sendMsg2CRM(tenantId,toUserLists,content,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
            }
            if (notifyType.contains(NotifyType.EMAIL)) {
                String subject = i18NStringManager.getByEi(I18NStringEnum.kjcpttz, tenantId);
                StringBuilder msg = new StringBuilder();
                for(TextInfo text : advanceText.getTextInfoList()){
                    msg.append(text.getContent()).append(System.lineSeparator());
                }
                emailNotificationService.sendEmailNotice(tenantId, toUserLists, null, subject, msg.toString());
            }
            if (notifyType.contains(NotifyType.SMS)) {
                StringBuilder msg = new StringBuilder();
                for(TextInfo text : advanceText.getTextInfoList()){
                    msg.append(text.getContent()).append(System.lineSeparator());
                }
                smsNotificationService.sendSmsNotice(tenantId, toUserLists, null, msg.toString());
            }
        }else{
            sendMsg2CRM(tenantId,toUserLists,content,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        }

        if(!(alarmType.equals(AlarmType.OTHER) || alarmType.equals(AlarmType.SUPER_ADMIN))) {
            List<String> notifyTypes=CollectionUtils.isEmpty(notifyType)?null:notifyType.stream().map(v->v.name()).collect(Collectors.toList());
            dataIntegrationNotificationManager.insert(tenantId,
                    dataCenterId,
                    StringUtils.isEmpty(ployDetailId) ? null : Lists.newArrayList(ployDetailId),
                    alarmRuleType,
                    alarmRuleName,
                    alarmType,
                    alarmLevel,
                    content,
                    toUserLists,
                    notifyTypes);
        }
    }
    private void sendMultiNoticeByApp(String tenantId,
                                      String ea,
                                      String dataCenterId,
                                      String ployDetailId,
                                      AdvanceText advanceText,
                                      List<Integer> toUserLists,
                                      String appId,
                                      AlarmRuleType alarmRuleType,
                                      String alarmRuleName,
                                      AlarmType alarmType,
                                      AlarmLevel alarmLevel) {
        sendMultiNoticeWithType(tenantId,
                ea,
                dataCenterId,
                ployDetailId,
                advanceText,
                toUserLists,
                appId,
                alarmRuleType,
                alarmRuleName,
                alarmType,
                alarmLevel,
                null);
    }

    @Override
    public Result<Void> sendNoticeByConfig(SendTextNoticeArg arg,
                                           List<Integer> userIdList,
                                           List<String> roleIdList,
                                           AlarmRuleType alarmRuleType,
                                           String alarmRuleName,
                                           AlarmType alarmType,
                                           AlarmLevel alarmLevel) {
        Set<Integer> receiverSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIdList)){
            receiverSet.addAll(userIdList);
        }
        if (CollectionUtils.isNotEmpty(roleIdList)){
            //该接口不传也行
            Integer tenantId = Integer.valueOf(arg.getTenantId());
            com.fxiaoke.paasauthrestapi.common.data.HeaderObj headerObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(tenantId);
            for (String roleCode : roleIdList) {
                //角色
                try{
                    com.fxiaoke.paasauthrestapi.common.result.Result<RoleUserResult> roleUserRes = paasAuthService.roleUser(headerObj, new RoleUserArg("CRM", tenantId, -10000, roleCode));
                    if (roleUserRes.getErrCode()!=0){
                        log.warn("find role user failed,{},{}",roleCode,roleUserRes);
                    }else {
                        roleUserRes.getResult().getUsers().stream().mapToInt(Integer::parseInt).forEach(receiverSet::add);
                    }
                }catch (Exception e){
                    log.error("find role user error",e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(receiverSet)){
            arg.setReceivers(new ArrayList<>(receiverSet));
            return sendErpSyncDataAppNotice(arg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        }

        return Result.newSuccess();
    }

    private void sendToOtherTenant(SendTextNoticeArg arg,
                                   AlarmRuleType alarmRuleType,
                                   String alarmRuleName,
                                   AlarmType alarmType,
                                   AlarmLevel alarmLevel) {
        if (!AlarmType.GET_BY_ID_API_BREAK.equals(alarmType) && !AlarmType.POLLING_ERP_API_EXCEPTION.equals(alarmType)){
            return;
        }

        String tenantId = Optional.ofNullable(arg.getTenantId())
                .orElseGet(() -> Optional.ofNullable(arg.getEnterpriseAccount())
                        .filter(StringUtils::isNotEmpty)
                        .map(Integer::parseInt)
                        .map(ea -> {
                            try {
                                return eieaConverter.enterpriseIdToAccount(ea);
                            } catch (Exception e) {
                                log.warn("找不到ea对应的ei ea:{}", ea, e);
                                return null;
                            }
                        })
                        .orElse(null));
        if (StringUtils.isEmpty(tenantId)) {
            return;
        }

        final Map<String, List<Integer>> sendNotifyToOtherTenantConfig = configCenterConfig.getSendNotifyToOtherTenantConfig(tenantId);
        if (MapUtils.isEmpty(sendNotifyToOtherTenantConfig)){
            return;
        }
        arg.setMsg(i18NStringManager.getByEi(I18NStringEnum.s622,tenantId) + tenantId + "\n" + arg.getMsg());
        sendNotifyToOtherTenantConfig.forEach((ei, userIds) -> {
            if (StringUtils.equals(ei, tenantId) || CollectionUtils.isEmpty(userIds)) {
                return;
            }
            arg.setTenantId(ei);
            arg.setReceivers(userIds);
            try {
                sendAppTextMsg(arg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);
            } catch (Exception e) {
                log.warn("发送其他企业错误通知失败 ei:{} userIds:{} msg:{}", ei, userIds, arg.getMsg(), e);
            }
        });
    }

    @Override
    public Result<Void> sendSuperAdminNotice(SendAdminNoticeArg arg) {
        //发送到超级管理员
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setMsgTitle(arg.getMsgTitle());
        sendTextNoticeArg.setMsg(arg.getFullMsg());
        if(arg.getNeedFillPreDbName()){//有些文案经过产品经理调整后，有些文案已经填充企业信息了
            fillTenantDcInfoProMsg(arg);
            sendTextNoticeArg.setMsg(arg.getFullMsg());
        }else {
            sendTextNoticeArg.setMsg(arg.getMsg());
        }
        Map<String, List<Integer>> superAdminMap = ConfigCenter.SUPER_ADMINS.stream()
                .map(v -> v.split("\\."))
                .filter(v -> v.length == 2 && NumberUtil.isInteger(v[1]))
                .collect(Collectors.groupingBy(v -> v[0],
                        Collectors.mapping(u -> Integer.valueOf(u[1]), Collectors.toList())));
        superAdminMap.forEach((ea, superUserIds) -> {
            sendTextNoticeArg.setEnterpriseAccount(ea);
            sendTextNoticeArg.setReceivers(superUserIds);
            notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager,null,arg.getTenantId()),
                    AlarmType.SUPER_ADMIN,
                    AlarmLevel.URGENT);
        });
        return Result.newSuccess();
    }


    @Override
    public Result<Void> sendSuperAdminNoticeToManageTool(SendSuperAdminNoticeArg arg) {
        String action = "erpSync-notice";
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(ProcessInfo2.appName)
                .serverIp(ProcessInfo2.serverIp)
                .profile(ProcessInfo2.profile)
                .action(action)
                .tenantId(arg.getTenantId())
                //记录删除数量
                .extra(i18NStringManager.getByEi(arg.getNoticeType().getI18nKey(),arg.getTenantId(),arg.getNoticeType().getTitle()))
                .message(arg.getMsg())
                .traceId(TraceUtil.get())
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendCustomerRoomNotice(SendAdminNoticeArg arg,
                                               AlarmLevel alarmLevel) {
//        Date time = new Date();
//        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
//        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
//        searchTemplateQuery.addFilter(ConfigCenter.FS_ACCOUNT_TENANT_ID, Lists.newArrayList(arg.getTenantId()), "EQ");
//        searchTemplateQuery.setPermissionType(0);
//        searchTemplateQuery.setSearchSource("es");
//        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> queryAccount;
//        try{
//            //从客户对象获取客群
//            queryAccount = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName(), searchTemplateQuery);
//            log.info("query accountObj data arg:{},result:{}",searchTemplateQuery,queryAccount);
//            if(!queryAccount.isSuccess()){
//                return Result.newSuccess();
//            }
//        }catch (Exception e){
//            log.warn("query account failed",e);
//            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
//        }
//        if (!CollectionUtils.isNotEmpty(queryAccount.getData().getQueryResult().getData())) {
//            log.info("not found account");
//            return Result.newSuccess();
//        }
//        String accountId = queryAccount.getData().getQueryResult().getData().get(0).getId();
//        com.fxiaoke.model.message.SendTextMessageArg sendTextMessageArg = new com.fxiaoke.model.message.SendTextMessageArg();
//        sendTextMessageArg.setEi(Integer.parseInt(ConfigCenter.FS_ENTERPRISE_ID));
//        sendTextMessageArg.setReceiverChannelType(ReceiverChannelType.TRUST_GROUP);
//        sendTextMessageArg.setUuid(UUIDUtil.getUUID());
//        // 获取客户对象Id
//        sendTextMessageArg.setReceiverChannelData(ReceiverChannelUtils.buildTrustChannelData("AccountObj", accountId));
//        sendTextMessageArg.setReceiverIds(Lists.newArrayList(CrmConstants.SYSTEM_USER));
//        sendTextMessageArg.setMessageContent(arg.getMsg());
//        MessageResponse messageResponse = null;
//        try {
//            messageResponse = messageServiceV2.sendTextMessage(sendTextMessageArg);
//            log.info("send customer room message:{}.result:{}",sendTextMessageArg,messageResponse);
//        } catch (FRestClientException e) {
//            log.warn("send customer room message failed",e);
//        }
        return Result.newSuccess();
    }

    // 这个方法没被调用过，先注释掉了
//    private String getRelationUserName(String msg,List<Integer> relationUsrIds){
//        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = null;
//        StringBuilder notifyBuilder=new StringBuilder(msg);
//        try {
//            BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
//            batchGetEmployeeDtoArg.setEnterpriseId(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID));
//            batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);
//            batchGetEmployeeDtoArg.setEmployeeIds(relationUsrIds);
//            batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
//            notifyBuilder.append("\n");
//            batchGetEmployeeDtoResult.getEmployeeDtos().forEach(item ->notifyBuilder.append("@").append(item.getName()).append(" "));
//            return notifyBuilder.toString();
//        } catch (Exception e) {
//            log.info("getFs userId fail:{}",e.getMessage());
//            e.printStackTrace();
//        }
//        return notifyBuilder.toString();
//    }


    @Override
    public Result<Void> sendTenantAdminNotice(SendAdminNoticeArg arg,
                                              AlarmRuleType alarmRuleType,
                                              String alarmRuleName,
                                              AlarmType alarmType,
                                              AlarmLevel alarmLevel) {
        ErpTenantConfigurationEntity messageNotification = tenantConfigurationManager.findOne(arg.getTenantId(), arg.getDcId(), null, TenantConfigurationTypeEnum.messageNotification.name());
        boolean hasSendTenantAdmin = false;
        Result<Void> sendTenantAdminResult = null;
        if (messageNotification != null) {
            MessageNotificationConfiguration erpConfiguration = JsonUtil.fromJson(messageNotification.getConfiguration(), MessageNotificationConfiguration.class);

            if (erpConfiguration.getStatus() != null
                    && ConfigurationConstant.messageNotificationPloyOpen.equals(erpConfiguration.getStatus())
                    && !CollectionUtils.isEmpty(erpConfiguration.getUsers())) {
                //发送到企业负责人
                SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
                sendTextNoticeArg.setTenantId(arg.getTenantId());
                sendTextNoticeArg.setDataCenterId(arg.getDcId());
                sendTextNoticeArg.setPloyDetailId(arg.getPloyDetailId());
                sendTextNoticeArg.setReceivers(erpConfiguration.getUsers());
                sendTextNoticeArg.setMsgTitle(arg.getMsgTitle());
                sendTextNoticeArg.setMsg(arg.getFullMsg());
                sendTenantAdminResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
                hasSendTenantAdmin = true;
                if (MapUtils.isNotEmpty(erpConfiguration.getFsAdmin())) {
                    erpConfiguration.getFsAdmin().forEach((fs, fsAdmins) -> {
                        sendTextNoticeArg.setTenantId(fs);
                        sendTextNoticeArg.setReceivers(fsAdmins);
                        //发送到实施负责人
                        if(arg.getNeedFillPreDbName()){
                            fillTenantDcInfoProMsg(arg);
                            sendTextNoticeArg.setMsg(arg.getFullMsg());
                        }else {
                            sendTextNoticeArg.setMsg(arg.getMsg());
                        }
                        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);
                    });
                }
            }
        }
        boolean needSendSuperAdmin = (!hasSendTenantAdmin && arg.getSendSuperAdminIfNoSendTenantAdmin());
        if (needSendSuperAdmin) {
            arg.setHasSendTenantAdmin(sendTenantAdminResult != null && sendTenantAdminResult.isSuccess());
            //未设置企业管理员是发送到
            notificationService.sendSuperAdminNotice(arg);
        }
        //发送给CSM
        sendCSM(arg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendCSM(SendAdminNoticeArg arg,
                                AlarmRuleType alarmRuleType,
                                String alarmRuleName,
                                AlarmType alarmType,
                                AlarmLevel alarmLevel) {
        //通知CSM相关人员-->获取需要通知到的人
        List<Integer> csmUserIds = getCustomerNotifyUses(arg.getTenantId());
        if (CollectionUtils.isNotEmpty(csmUserIds)) {
            SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
            sendTextNoticeArg.setTenantId(ConfigCenter.FS_ENTERPRISE_ID);
            sendTextNoticeArg.setTenantIdOld(arg.getTenantId());
            sendTextNoticeArg.setDataCenterId(arg.getDcId());
            sendTextNoticeArg.setPloyDetailId(arg.getPloyDetailId());
            sendTextNoticeArg.setReceivers(csmUserIds);
            sendTextNoticeArg.setMsgTitle(arg.getMsgTitle());
            if(arg.getNeedFillPreDbName()){
                fillTenantDcInfoProMsg(arg);
                sendTextNoticeArg.setMsg(arg.getFullMsg());
            }else {
                sendTextNoticeArg.setMsg(arg.getMsg());
            }
            //发送到CSM和实施负责人
            notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg, alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        }

        //通知超级管理员
        if (arg.getAlwaysSendSuperAdmin()){
            //发送到超级管理员,如果发送到客群，会有@人的信息
            notificationService.sendSuperAdminNotice(arg);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendFsUserIds(String tenantId,
                                      String dcId,
                                      String message,
                                      AlarmRuleType alarmRuleType,
                                      String alarmRuleName,
                                      AlarmType alarmType,
                                      AlarmLevel alarmLevel) {
        ErpTenantConfigurationEntity messageNotification = tenantConfigurationManager.findOne(tenantId,dcId, null, TenantConfigurationTypeEnum.messageNotification.name());

        if (messageNotification != null) {
            MessageNotificationConfiguration erpConfiguration = JsonUtil.fromJson(messageNotification.getConfiguration(), MessageNotificationConfiguration.class);
            if (erpConfiguration.getStatus() != null
              && ConfigurationConstant.messageNotificationPloyOpen.equals(erpConfiguration.getStatus())
              && !CollectionUtils.isEmpty(erpConfiguration.getUsers())) {
                //发送到企业负责人
                SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
                sendTextNoticeArg.setMsgTitle(i18NStringManager.getByEi(I18NStringEnum.s623,tenantId));
                sendTextNoticeArg.setMsg(message);

                if (MapUtils.isNotEmpty(erpConfiguration.getFsAdmin())) {
                    erpConfiguration.getFsAdmin().forEach((fs, fsAdmins) -> {
                        sendTextNoticeArg.setTenantId(fs);
                        sendTextNoticeArg.setDataCenterId(dcId);
                        sendTextNoticeArg.setReceivers(fsAdmins);
                        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                                alarmRuleType,
                                alarmRuleName,
                                alarmType,
                                alarmLevel);
                    });
                }
            }
        }

        //通知CSM相关人员
        List<Integer> csmUserIds = getCSMUserIds(tenantId);
        if (CollectionUtils.isNotEmpty(csmUserIds)) {
            SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
            sendTextNoticeArg.setTenantId(ConfigCenter.FS_ENTERPRISE_ID);
            sendTextNoticeArg.setTenantIdOld(tenantId);
            sendTextNoticeArg.setDataCenterId(dcId);
            sendTextNoticeArg.setReceivers(csmUserIds);
            //发送到实施负责人
            sendTextNoticeArg.setMsg(message);
            notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    alarmRuleType,
                    alarmRuleName,
                    alarmType,
                    alarmLevel);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendAlertMsg(AlertArg arg,
                                     AlarmRuleType alarmRuleType,
                                     String alarmRuleName,
                                     AlarmType alarmType,
                                     AlarmLevel alarmLevel) {
        if (StrUtil.isBlank(arg.getEa())) {
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(arg.getTenantId()));
            arg.setEa(ea);
        }
        if (arg.isSendNormalApp()){
            String appId = ConfigCenter.ERP_SYNC_DATA_APP_ID;
            sendMultiNoticeByApp(arg.getTenantId(),
                    arg.getEa(),
                    null,
                    null,
                    arg.getAdvanceText(),
                    arg.getToUserList(),
                    appId,
                    alarmRuleType,
                    alarmRuleName,
                    alarmType,
                    alarmLevel);
        }
        if (arg.isSendPrimaryApp()){
            String appId = ConfigCenter.PRIMARY_ERP_SYNC_DATA_APP_ID;
            sendMultiNoticeByApp(arg.getTenantId(),
                    arg.getEa(),
                    null,
                    null,
                    arg.getAdvanceText(),
                    arg.getToUserList(),
                    appId,
                    alarmRuleType,
                    alarmRuleName,
                    alarmType,
                    alarmLevel);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<TextInfo>> generateDataSyncResult(SyncStatusMessageArg syncStatusMessageArg) {
        List<TextInfo> textInfos=Lists.newArrayList();
        StringBuilder syncNotifyText=new StringBuilder();
        syncNotifyText.append(i18NStringManager.getByEi(I18NStringEnum.s627,syncStatusMessageArg.getTenantId())+"\n");
        if(ObjectUtils.isNotEmpty(syncStatusMessageArg.getStartTime())&&ObjectUtils.isNotEmpty(syncStatusMessageArg.getEndTime())){
            String startDate = DateUtil.format(DateTime.of(syncStatusMessageArg.getStartTime()), DatePattern.NORM_DATETIME_FORMAT);
            String endDate = DateUtil.format(DateTime.of(syncStatusMessageArg.getEndTime()), DatePattern.NORM_DATETIME_FORMAT);
            syncNotifyText.append(startDate).append("-").append(endDate).append("  ");
        }
        if(CollectionUtils.isNotEmpty(syncStatusMessageArg.getSuccessList())){
            syncNotifyText.append(i18NStringManager.get2(I18NStringEnum.s628,
                    null,
                    syncStatusMessageArg.getTenantId(),
                    syncStatusMessageArg.getSuccessList().size()+""));
            List<SyncStatusMessageArg.SyncDataStatusMessage> reverseWriteFailed = syncStatusMessageArg.getSuccessList().stream()
                    .filter(v -> v.getReverseWrite2CrmFailed() != null && v.getReverseWrite2CrmFailed()).collect(Collectors.toList());
            List<SyncStatusMessageArg.SyncDataStatusMessage> afterFuncFailed = syncStatusMessageArg.getSuccessList().stream()
                    .filter(v -> v.getAfterFuncFailed() != null && v.getAfterFuncFailed()).collect(Collectors.toList());
            Boolean needBrackets=false;
            if(CollectionUtils.isNotEmpty(reverseWriteFailed)){
                needBrackets=true;
                syncNotifyText.append("(").append(i18NStringManager.get2(I18NStringEnum.s1014,
                        null,
                        syncStatusMessageArg.getTenantId())).append(reverseWriteFailed.size()+"");
            }
            if(CollectionUtils.isNotEmpty(afterFuncFailed)){
                needBrackets=true;
                if(needBrackets){
                    syncNotifyText.append(",");
                }else{
                    syncNotifyText.append("(");
                }
                syncNotifyText.append(i18NStringManager.get2(I18NStringEnum.s965,
                        null,
                        syncStatusMessageArg.getTenantId())).append(afterFuncFailed.size()+"");
            }
            if(needBrackets){
                syncNotifyText.append(")");
            }
            if(CollectionUtils.isNotEmpty(syncStatusMessageArg.getErrorList())){
                syncNotifyText.append(",");
            }else{
                syncNotifyText.append("\n");
            }
        }
        if(CollectionUtils.isNotEmpty(syncStatusMessageArg.getErrorList())){
            syncNotifyText.append(i18NStringManager.get2(I18NStringEnum.s629.getI18nKey(),
                    null,
                    syncStatusMessageArg.getTenantId(),
                    String.format(I18NStringEnum.s629.getI18nValue(), syncStatusMessageArg.getErrorList().size()),
                    Lists.newArrayList(syncStatusMessageArg.getErrorList().size()+"")))
                    .append("\n");
        }
        if(ObjectUtils.isNotEmpty(syncStatusMessageArg.getConnInfoName())){
            syncNotifyText.append(i18NStringManager.getByEi(I18NStringEnum.s630,syncStatusMessageArg.getTenantId()))
                    .append(syncStatusMessageArg.getConnInfoName())
                    .append("\n");
        }
        if(ObjectUtils.isNotEmpty(syncStatusMessageArg.getSyncDirection())){
            syncNotifyText.append(i18NStringManager.getByEi(I18NStringEnum.s631,syncStatusMessageArg.getTenantId()))
                    .append(syncStatusMessageArg.getSyncDirection())
                    .append(" ")
                    .append(syncStatusMessageArg.getSrcObjectName())
                    .append("\n");
        }
        TextInfo titleInfo= TextInfoUtil.colorText();
        titleInfo.setContent(syncNotifyText.toString());
        titleInfo.setActionType(TextInfo.None);
        textInfos.add(titleInfo);
        if(ObjectUtils.isNotEmpty(syncStatusMessageArg.getSuccessList())){
            TextInfo successListInfo=TextInfoUtil.colorText();
            successListInfo.setActionType(TextInfo.None);
            StringBuilder successContent=new StringBuilder();
            List<String> dataList = Lists.newArrayList();
            for (SyncStatusMessageArg.SyncDataStatusMessage msg : syncStatusMessageArg.getSuccessList()) {
                if((msg.getAfterFuncFailed()!=null&&msg.getAfterFuncFailed())||(msg.getReverseWrite2CrmFailed()!=null&&msg.getReverseWrite2CrmFailed())){
                    dataList.add(msg.getDataName()+"("+msg.getRemark()+")");
                }else{
                    dataList.add(msg.getDataName());
                }
            }
            int endIndex=dataList.size()>DATA_SIZE?DATA_SIZE:dataList.size();
            List<String> successSubList = dataList.subList(0, endIndex);
            String successList = Joiner.on(",").join(successSubList);
            //控制字符大小
            if(successList.length()>MAX_LENGTH||dataList.size()>DATA_SIZE){
                String substring = successList.substring(0, successList.length() > MAX_LENGTH ? MAX_LENGTH : successList.length());
                successContent.append(i18NStringManager.getByEi(I18NStringEnum.s632,syncStatusMessageArg.getTenantId()))
                        .append("：")
                        .append(substring)
                        .append("\n");
                successListInfo.setContent(successContent.toString());
                textInfos.add(successListInfo);
                String tnFilePath = getSyncDataStatusExcelTnFilePath(syncStatusMessageArg.getTenantId(), syncStatusMessageArg.getSuccessList());
                if (StringUtils.isNotEmpty(tnFilePath)) {
                    String previewUrl = String.format(userCenterService.getTnViewUrlFormat(syncStatusMessageArg.getTenantId()), tnFilePath, "xlsx");
                    String downloadUrl = String.format(userCenterService.getDownloadFilePath(syncStatusMessageArg.getTenantId()), tnFilePath, "sync_data_success_" + DateUtil.date().toString("yyyyMMdd") + ".xlsx");
                    TextInfo text1 = TextInfoUtil.text(I18NStringEnum.s633.getText() + "  ");
                    TextInfo text2 = TextInfoUtil.url(I18NStringEnum.kPreviewOnline.getText(), previewUrl,false);
                    TextInfo text3 = TextInfoUtil.text("  ",false);
                    TextInfo text4 = TextInfoUtil.url(I18NStringEnum.kDownLoadFile.getText(), downloadUrl,false);
                    textInfos.add(text1);
                    textInfos.add(text2);
                    textInfos.add(text3);
                    textInfos.add(text4);
                }

            }else{
                successContent.append(i18NStringManager.getByEi(I18NStringEnum.s632,syncStatusMessageArg.getTenantId()))
                        .append("：")
                        .append(successList)
                        .append("\n");
                successListInfo.setContent(successContent.toString());
                textInfos.add(successListInfo);
            }
        }

        if(ObjectUtils.isNotEmpty(syncStatusMessageArg.getErrorList())){
            TextInfo errorInfo=TextInfoUtil.colorText();
            errorInfo.setActionType(TextInfo.None);
            StringBuilder errorContent=new StringBuilder();
            errorContent.append(i18NStringManager.getByEi(I18NStringEnum.s634,syncStatusMessageArg.getTenantId())).append("：\n");
            //只展示指定的数据长度，超过的发送链接
            for (int item = 0; item < syncStatusMessageArg.getErrorList().size()&&item<=DATA_SIZE; item++) {
                SyncStatusMessageArg.SyncDataStatusMessage syncDataStatusMessage = syncStatusMessageArg.getErrorList().get(item);
                errorContent.append(syncDataStatusMessage.getDataName())
                        .append(","+i18NStringManager.getByEi(I18NStringEnum.s635,syncStatusMessageArg.getTenantId()))
                        .append(syncDataStatusMessage.getRemark())
                        .append("\n");
            }
            errorInfo.setContent(errorContent.toString());
            if(errorContent.length()>MAX_LENGTH){
                String substring = errorContent.substring(0, errorContent.length() > MAX_LENGTH ? MAX_LENGTH : errorContent.length());
                errorInfo.setContent(substring);
            }
            textInfos.add(errorInfo);


            if(syncStatusMessageArg.getErrorList().size()>DATA_SIZE||errorContent.length()>MAX_LENGTH){
                String tnFilePath = getSyncDataStatusExcelTnFilePath(syncStatusMessageArg.getTenantId(), syncStatusMessageArg.getErrorList());
                if (StringUtils.isNotEmpty(tnFilePath)) {
                    String previewUrl = String.format(userCenterService.getTnViewUrlFormat(syncStatusMessageArg.getTenantId()), tnFilePath, "xlsx");
                    String downloadUrl = String.format(userCenterService.getDownloadFilePath(syncStatusMessageArg.getTenantId()), tnFilePath, "sync_data_error_" + DateUtil.date().toString("yyyyMMdd") + ".xlsx");
                    TextInfo text1 = TextInfoUtil.text(I18NStringEnum.s636.getText() + "  ");
                    TextInfo text2 = TextInfoUtil.url(I18NStringEnum.kPreviewOnline.getText(), previewUrl,false);
                    TextInfo text3 = TextInfoUtil.text("  ",false);
                    TextInfo text4 = TextInfoUtil.url(I18NStringEnum.kDownLoadFile.getText(), downloadUrl,false);
                    textInfos.add(text1);
                    textInfos.add(text2);
                    textInfos.add(text3);
                    textInfos.add(text4);
                }
            }

        }
        return Result.newSuccess(textInfos);
    }


    private String getSyncDataStatusExcelTnFilePath(String tenantId, List<SyncStatusMessageArg.SyncDataStatusMessage> syncDataStatusMessages){
        List<SyncDataNotifyExcelVo> syncDataNotifyExcelVos = BeanUtil.copyList(syncDataStatusMessages, SyncDataNotifyExcelVo.class);
        BuildExcelFileResult.Arg<SyncDataNotifyExcelVo> syncDataNotifyExcelVoArg = new BuildExcelFileResult.Arg<>();
        syncDataNotifyExcelVoArg.setTenantId(tenantId);
        syncDataNotifyExcelVoArg.setFileName(i18NStringManager.getByEi2(I18NStringEnum.s626.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s626.getI18nValue(), tenantId, LocalDateTime.now().toString()),
                Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
        syncDataNotifyExcelVoArg.setDataList(syncDataNotifyExcelVos);
        syncDataNotifyExcelVoArg.setSheetNames(Lists.newArrayList(i18NStringManager.getByEi(I18NStringEnum.s625,tenantId)));
        Result<BuildExcelFileResult.Result> excelResult = DBFileManager.buildExcelFileResult(i18NStringManager,null,tenantId,syncDataNotifyExcelVoArg);
        log.info("get Excel urlResult:{},{}",excelResult.getErrMsg(),excelResult.getData());
        if(excelResult.isSuccess()){
            return excelResult.getData().getTnFilePath();
        }
        return null;
    }


    // no usage
//    private SendTextNoticeArg convertRoom(SendAdminNoticeArg adminNoticeArg) {
//        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
//        sendTextNoticeArg.setTenantId(adminNoticeArg.getTenantId());
//        sendTextNoticeArg.setMsg(adminNoticeArg.getMsg());
//        sendTextNoticeArg.setMsgTitle(adminNoticeArg.getMsgTitle());
//        return sendTextNoticeArg;
//    }

    public List<Integer> getCustomerNotifyUses(String tenantId){
        //获取CSM相关团队人员列表
        List<Integer> csmUserIds = getCSMUserIds(tenantId);
        List<Integer> impUserIds = getImplementUsers(tenantId);
        log.info("trace getCustomerNotifyUses csmUserIds:{},implementUsers:{}",csmUserIds,impUserIds);
        List<Integer> retainUses = Stream.of(csmUserIds, impUserIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        return retainUses;
    }


    public List<Integer> getCSMUserIds(String tenantId) {

        String enterpriseAccount = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
        searchTemplateQuery.addFilter(ConfigCenter.CS_CUSTOMER_ENTERPRISE_API_NAME, Lists.newArrayList(String.valueOf(enterpriseAccount)), "EQ");
        searchTemplateQuery.addFilter("life_status", Lists.newArrayList(String.valueOf("normal")), "EQ");
        searchTemplateQuery.setPermissionType(0);

        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, ConfigCenter.CSM_CUSTOMER_API_NAME, searchTemplateQuery);
        if (!oldSpuDataResult.isSuccess()) {
            return Lists.newArrayList();
        }
        log.info("query csm customer result:{}", oldSpuDataResult);
        QueryBySearchTemplateResult.QueryResult queryResult = oldSpuDataResult.getData().getQueryResult();
        if (CollectionUtils.isEmpty(queryResult.getData())) {
            log.info("not find CSM teamMember tenantId:{}", tenantId);
            return Lists.newArrayList();
        }

        Set<Integer> notifyUser = Sets.newHashSet();
        Integer owner = queryResult.getData().get(0).getOwner();
        notifyUser.add(owner);
        Object object1 = queryResult.getData().get(0).get("relevant_team");
        String teamList = JSONObject.toJSONString(object1);
        List<Map> maps = JSONArray.parseArray(teamList, Map.class);
        for (int i = 0; i < maps.size(); i++) {
            if (Integer.valueOf(maps.get(i).get("teamMemberType").toString()).equals(0)) {
                Object teamMemberEmployee = maps.get(i).get("teamMemberEmployee");
                if (teamMemberEmployee != null && teamMemberEmployee instanceof List && ((List) teamMemberEmployee).size() > 0) {
                    Iterator var3 = ((List)teamMemberEmployee).iterator();
                    while(var3.hasNext()) {
                        Object o = var3.next();
                        notifyUser.add(Integer.valueOf(o.toString()));
                    }
                }
            }
        }
        List<Integer> notifyTeamUser = new ArrayList<>(notifyUser);
        return notifyTeamUser;
    }
    // 获取实施人员
    public List<Integer> getImplementUsers(String tenantId) {

        String enterpriseAccount = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
        searchTemplateQuery.addFilter(ConfigCenter.IMPLEMENT_PROJECT_ENTERPRISE_ID, Lists.newArrayList(String.valueOf(enterpriseAccount)), "EQ");
        searchTemplateQuery.addFilter("life_status", Lists.newArrayList("normal"), "EQ");
        searchTemplateQuery.addFilter("record_type", Lists.newArrayList("record_ZGzY2__c"), "EQ");//集成开发项目
        searchTemplateQuery.setPermissionType(0);

        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, ConfigCenter.IMPLEMENT_PROJECT_API_NAME, searchTemplateQuery);
        if (!oldSpuDataResult.isSuccess()) {
            return Lists.newArrayList();
        }
        log.info("query implement project result:{}", oldSpuDataResult);
        QueryBySearchTemplateResult.QueryResult queryResult = oldSpuDataResult.getData().getQueryResult();
        if (CollectionUtils.isEmpty(queryResult.getData())) {
            log.info("not find implement teamMember tenantId:{}", tenantId);
            return Lists.newArrayList();
        }

        Set<Integer> notifyUser = Sets.newHashSet();
        Integer owner = queryResult.getData().get(0).getOwner();
        notifyUser.add(owner);
        Object object1 = queryResult.getData().get(0).get("relevant_team");
        String teamList = JSONObject.toJSONString(object1);
        List<Map> maps = JSONArray.parseArray(teamList, Map.class);
        for (int i = 0; i < maps.size(); i++) {
            if (Integer.valueOf(maps.get(i).get("teamMemberType").toString()).equals(0)) {
                Object teamMemberEmployee = maps.get(i).get("teamMemberEmployee");
                if (teamMemberEmployee != null && teamMemberEmployee instanceof List && ((List) teamMemberEmployee).size() > 0) {
                    Iterator var3 = ((List)teamMemberEmployee).iterator();
                    while(var3.hasNext()) {
                        Object o = var3.next();
                        notifyUser.add(Integer.valueOf(o.toString()));
                    }
                }
            }
        }
        List<Integer> notifyTeamUser = new ArrayList<>(notifyUser);
        return notifyTeamUser;
    }



    private void fillTenantDcInfoProMsg(SendAdminNoticeArg arg) {
        if (arg.getTenantDcInfoPreMsg() != null) {
            return;
        }
        StringBuilder msg = new StringBuilder();
        if (StringUtils.isNotBlank(arg.getTenantId())) {
            List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoManager.listByTenantId(arg.getTenantId());
            msg.append(i18NStringManager.get2(I18NStringEnum.s638.getI18nKey(),
                    null,
                    arg.getTenantId(),
                    String.format(I18NStringEnum.s638.getI18nValue(), arg.getTenantId(), erpConnectInfoEntities.get(0).getEnterpriseName()),
                    Lists.newArrayList(arg.getTenantId(), erpConnectInfoEntities.get(0).getEnterpriseName()))).append("\n");
            if (StringUtils.isNotBlank(arg.getDcId())) {
                erpConnectInfoEntities.stream()
                        .filter(v -> v.getId().equals(arg.getDcId())).findAny()
                        .ifPresent(v -> msg.append(i18NStringManager.get2(I18NStringEnum.s639.getI18nKey(),
                                null,
                                arg.getTenantId(),
                                String.format(I18NStringEnum.s639.getI18nValue(), arg.getDcId(), v.getDataCenterName()),
                                Lists.newArrayList(arg.getDcId(), v.getDataCenterName()))).append("\n"));
            }
        }
        arg.setTenantDcInfoPreMsg(msg.toString());
    }

    private void sendTextTransFileMsg(String ea,
                                      List<Integer> toUserList,
                                      String appId,
                                      String title,
                                      String content,
                                      SendTextNoticeArg sendTextNoticeArg,
                                      AlarmRuleType alarmRuleType,
                                      String alarmRuleName,
                                      AlarmType alarmType,
                                      AlarmLevel alarmLevel) {
        String tnPath = uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, content.getBytes(Charset.defaultCharset()));
        if (tnPath == null) {
            log.error("sendTextTransFileMsg uploadTnFile failed,ea:{},toUserList:{},content:{}", ea, toUserList, content);
            return;
        }
        String previewUrl = String.format(userCenterService.getPreviewFilePathFormat(ea), tnPath + ".txt");
        String msg = i18NStringManager.get2(I18NStringEnum.s637.getI18nKey(),
                null,
                sendTextNoticeArg.getTenantId(),
                String.format(I18NStringEnum.s637.getI18nValue(), title,"\n", previewUrl),
                Lists.newArrayList(title,"\n", previewUrl));
        sendRichTextMessage(ea, toUserList, appId, msg,sendTextNoticeArg,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
    }

    /**
     * 发送文本消息
     *
     * @param ea
     * @param toUserList
     * @param appId
     * @param content
     */
    private void sendRichTextMessage(String ea,
                                 List<Integer> toUserList,
                                 String appId,
                                 String content,
                                 SendTextNoticeArg sendTextNoticeArg,
                                 AlarmRuleType alarmRuleType,
                                 String alarmRuleName,
                                 AlarmType alarmType,
                                 AlarmLevel alarmLevel) {
        //最大长度为2000
        if (content.length() > 1998) {
            content = content.substring(0, 1900) + "...";
        }
        List<String> lineList = Splitter.on("\n").splitToList(content);
        List<TextInfo> textInfoList = new ArrayList<>();
        for(String line : lineList) {
            textInfoList.add(TextInfoUtil.text(line));
        }
        textInfoList.add(TextInfoUtil.text(""));
        textInfoList.add(TextInfoUtil.text(""));

        if(StringUtils.isNotEmpty(sendTextNoticeArg.getDataCenterId())) {
            String enter_integration_stream_url = ConfigCenter.ERP_DOMAIN_URL+"/XV/UI/manage#erpdss/policy?dataCenterId="+sendTextNoticeArg.getDataCenterId();
            if(StringUtils.isNotEmpty(sendTextNoticeArg.getPloyDetailId())) {
                enter_integration_stream_url += "&streamId="+sendTextNoticeArg.getPloyDetailId();
            }

            //不记录告警信息，需要在这里屏蔽
            textInfoList.add(TextInfoUtil.url(i18NStringManager.get(I18NStringEnum.s2272,
                    null,
                    sendTextNoticeArg.getTenantId()),
                    enter_integration_stream_url));
            if(!(alarmType.equals(AlarmType.OTHER) || alarmType.equals(AlarmType.SUPER_ADMIN))) {
                textInfoList.add(TextInfoUtil.url(i18NStringManager.get(I18NStringEnum.s2273,
                        null,
                        sendTextNoticeArg.getTenantId()),
                        ConfigCenter.ERP_DOMAIN_URL+"/XV/UI/manage#erpdss/alarmManagement?tab=alarmRecord"));
            }
        }

        AdvanceText advanceText = new AdvanceText();
        advanceText.setTextInfoList(textInfoList);
        sendMultiNoticeByApp(sendTextNoticeArg.getTenantId(),
                ea,
                sendTextNoticeArg.getDataCenterId(),
                sendTextNoticeArg.getPloyDetailId(),
                advanceText,
                toUserList,
                appId,
                alarmRuleType,
                alarmRuleName,
                alarmType,
                alarmLevel);

//        SendTextMessageArg arg = new SendTextMessageArg();
//        arg.setMessageSendType(MessageSendTypeEnum.THIRD_PARTY_PUSH.getValue());
//        MessageData textMessageVO = new MessageData();
//        textMessageVO.setEnterpriseAccount(ea);
//        textMessageVO.setToUserList(toUserList);
//        textMessageVO.setAppId(appId);
//        textMessageVO.setContent(content);
//        textMessageVO.setType(MessageTypeEnum.TEXT.getType());
//        textMessageVO.setPostId(idGenerator.get());
//        arg.setTextMessageVO(textMessageVO);
//        SendTextMessageResult sendTextMessageResult = sendMessageService.sendTextMessage(arg);
//        if (!sendTextMessageResult.isSuccess()) {
//            log.error("send text msg failed,arg:{},result:{}", arg, sendTextMessageResult);
//        }
//        sendMsg2CRM(sendTextNoticeArg.getTenantId(),toUserList,content,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
//
//        String tenantId = StringUtils.isNotEmpty(sendTextNoticeArg.getTenantIdOld()) ? sendTextNoticeArg.getTenantIdOld() : sendTextNoticeArg.getTenantId();
//        String ployDetailId = null;
//        if(CollectionUtils.isNotEmpty(sendTextNoticeArg.getPloyDetailIdList())) {
//            ployDetailId = sendTextNoticeArg.getPloyDetailIdList().get(0);
//        }
//        erpAlarmRecordManager.insert(tenantId,
//                sendTextNoticeArg.getDataCenterId(),
//                ployDetailId,
//                alarmRuleType,
//                alarmRuleName,
//                alarmType,
//                alarmLevel,
//                content,
//                toUserList);
    }

    @Override
    public Result<Void> sendMsg2CRM(String tenantId,
                                    List<Integer> userIdList,
                                    String msg,
                                    AlarmRuleType alarmRuleType,
                                    String alarmRuleName,
                                    AlarmType alarmType,
                                    AlarmLevel alarmLevel) {
        if(alarmType==AlarmType.SUPER_ADMIN || StringUtils.isEmpty(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_ALLOWED_SEND_MSG_2_CRM);
        }
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId,
                CommonConstant.configUniformIdentifier,
                ErpChannelEnum.ALL.name(),
                TenantConfigurationTypeEnum.SEND_ERPDSS_MSG_2_FS_MSG_CENTER.name());
        log.info("sendMsg2CRM,configuration={}",configuration);

        if(configuration==null) {
            if(!ConfigCenter.sendErpdssMsg2Crm)
                return Result.newError(ResultCodeEnum.NOT_ALLOWED_SEND_MSG_2_CRM);
        }

        com.fxiaoke.model.message.SendTextMessageArg sendTextMessageArg = new com.fxiaoke.model.message.SendTextMessageArg();
        sendTextMessageArg.setEi(Integer.valueOf(tenantId));
        sendTextMessageArg.setReceiverChannelType(ReceiverChannelType.NOTICE);
        sendTextMessageArg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData(ERPDSS_NOTIFY_KEY));
        sendTextMessageArg.setUuid(UUIDUtil.getUUID());
        sendTextMessageArg.setReceiverIds(userIdList);
        sendTextMessageArg.setMessageContent(msg);
        MessageResponse messageResponse = null;
        try {
            messageResponse = messageServiceV2.sendTextMessage(sendTextMessageArg);
            log.info("send msg 2 crm,arg={},result={}",sendTextMessageArg,messageResponse);
        } catch (FRestClientException e) {
            log.warn("send msg 2 crm failed,exception={}",e.getMessage(),e);
        }
        return Result.newSuccess();
    }
}
