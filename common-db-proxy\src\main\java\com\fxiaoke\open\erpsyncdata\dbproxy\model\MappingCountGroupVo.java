package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class MappingCountGroupVo implements Serializable {
    private List<MappingCountVo> mappingCountVos;
    private Long beginTime;
    private Long endTime;

    public Long countByObjsAndStatus(String sourceObjApiName, String destObjApiName, Integer status) {
        long sumCount = mappingCountVos.stream()
                .filter(v ->
                        v.getSourceObjectApiName().equals(sourceObjApiName)
                                && v.getDestObjectApiName().equals(destObjApiName)
                                && (status == null || status.equals(v.getSyncStatus()))
                )
                .mapToLong(v -> v.getCount()).sum();
        return sumCount;

    }
}
