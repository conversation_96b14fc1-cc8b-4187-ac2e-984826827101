package com.fxiaoke.open.erpsyncdata.monitor.handler;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.AlertAggregationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.monitor.manager.AlertAndBreakManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 每天定时推送的告警统计数据到 客群，推送过去24小时 新增的、还未解决的告警中集成流统计数据
 * 模板：
 * --集成平台告警通知--
 *
 * 企业最近24小时告警的集成流中：
 *
 * 还有3个紧急告警，10个重要告警，1个一般告警未解决
 *
 * 为避免影响数据同步，请相关负责人员尽快解决异常!
 *
 * <AUTHOR>
 * @date 2023.11.29
 */
@Slf4j
@Component
@JobHander(value = "dailyAlarmStatisticsHandler")
public class DailyAlarmStatisticsHandler extends IJobHandler {
    @Autowired
    private AlertAggregationDao alertAggregationDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    AlertAndBreakManager alertAndBreakManager;

    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam triggerParam) throws Exception {
        TraceUtil.initTrace(triggerParam.getJobId()+"_"+triggerParam.getLogId()+"_"+triggerParam.getLogDateTim());
        log.info("DailyAlarmStatisticsHandler.executeJob,triggerParam:{}", triggerParam);

        RLock rLock = redissonClient.getLock(CommonConstant.REDIS_LOCK_DAILY_ALARM_STATISTICS);
        if (rLock.tryLock(10, 60 * 5, TimeUnit.SECONDS)) {
            log.info("DailyAlarmStatisticsHandler.executeJob,get lock success");
            try {
                long curTime = System.currentTimeMillis();
                long startTime = curTime - 24 * 60 * 60 * 1000; //过去24小时
                int limit = 10000;
                Map<String, List<AlertAggregationEntity>> totalAlertDataMap = alertAggregationDao.getAlertDataList(startTime, limit);
                log.info("DailyAlarmStatisticsHandler.executeJob,totalAlertDataMap={}",totalAlertDataMap);
                if(totalAlertDataMap.isEmpty()) return;

                for(String tenantId : totalAlertDataMap.keySet()) {
                    Map<AlarmLevel, List<AlertAggregationEntity>> alertDataMap = alertAggregationDao.getAlertDataMap(totalAlertDataMap.get(tenantId));
                    log.info("DailyAlarmStatisticsHandler.executeJob,alertDataMap={}",alertDataMap);
                    alertAndBreakManager.sendDailyAlarmStatistics2CustomerGroup(tenantId, alertDataMap);
                }
            } finally {
                alertAggregationDao.deleteAll();
                rLock.unlock();
            }
        } else {
            log.info("DailyAlarmStatisticsHandler.executeJob,get lock failed");
        }

        alertAggregationDao.deleteAll();
    }
}
