package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 16:00 2022/12/6
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class ErpReSyncData implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    /**
     *企业id
     */
    private String tenantId;
    /**
     *集成流id
     */
    private String ployDetailId;
    /**
     *对象apiName
     */
    private String objApiName;
    /**
     *数据id
     */
    private String dataId;
    /**
     *快照id
     */
    private String syncDataId;
    /**
     *重试类型
     */
    private Integer type;
    /**
     *状态
     */
    private Integer status;

    private String remark;
    /**
     *校验数据
     */
    private CheckMessageListData checkMessage;
    /**
     * 多语
     */
    private String locale;
    /**
     *创建时间
     */
    private Long createTime;
    /**
     *更新时间
     */
    private Long updateTime;
    /**
     *过期时间
     */
    private Date expireTime;

}
