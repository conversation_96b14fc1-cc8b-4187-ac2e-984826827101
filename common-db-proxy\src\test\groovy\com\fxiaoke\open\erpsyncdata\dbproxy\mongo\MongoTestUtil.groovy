package com.fxiaoke.open.erpsyncdata.dbproxy.mongo

import cn.hutool.core.date.DateUtil
import cn.hutool.core.lang.Singleton
import com.github.mongo.support.DatastoreExt
import com.github.mongo.support.MongoDataStoreFactoryBean
import com.github.mongo.support.TenantPolicy
import com.mongodb.client.MongoClients
import de.bwaldvogel.mongo.MongoServer
import de.bwaldvogel.mongo.backend.h2.H2Backend
import groovy.util.logging.Slf4j
import org.bson.Document
import spock.lang.Specification

/**
 * 本地 Mongo Test
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
class MongoTestUtil extends Specification {
    private static MongoServer server

    static {
        //使用内存的H2，，，每次启动都会是一个新的。
        server = new MongoServer(H2Backend.inMemory())
        server.bind("localhost", 27018)
        log.info("mongo server connection string:${server.getConnectionString()}")
    }

    static DatastoreExt createStore() {
        return Singleton.get("testStore", {
            def bean = new MongoDataStoreFactoryBean()
            bean.setConfigName("erp-sync-log-mongo")
            bean.setTenantPolicy(new TenantPolicy() {
                //无路由，直接返回server mongo
                @Override
                String getUri(String tenantId) {
                    return server.getConnectionString() + "/localdb"
                }
            })
            bean.afterPropertiesSet()
            def store = bean.getObject()
            return store
        })
    }

    def testMongoServer() {
        def client = MongoClients.create(server.getConnectionString())
        def col = client.getDatabase("testdb").getCollection("testcollection")
        col.insertOne(new Document("test1", DateUtil.now()))
        def all = col.find().toList()
        log.info("all:${all}")
        expect:
        !all.isEmpty()
    }

    def testLogMongo() {
        def store = createStore().setTenantId("-10001")
        def mongo = store.getMongo()
        def list = mongo.listDatabaseNames().toList()
        log.info("dbs:${list}")
        expect:
        mongo != null
        list != null
    }


}
