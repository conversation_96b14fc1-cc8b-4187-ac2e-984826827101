package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpAlarmRuleDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AlarmRuleMatchModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.AlertNoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.PloyBreakNoticeConfig;
import com.fxiaoke.paasauthrestapi.common.contants.RoleCodeContants;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 告警规则管理器
 * <AUTHOR>
 * @date 2023-11-23
 */
@Component
@Slf4j
public class ErpAlarmRuleManager {
    @Autowired
    private ErpAlarmRuleDao erpAlarmRuleDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    private ExecutorService  executorService= Executors.newFixedThreadPool(20);

    public int insert(ErpAlarmRuleEntity entity) {
        entity.setId(idGenerator.get());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                .insert(entity);
    }

    public int update(ErpAlarmRuleEntity entity) {
        entity.setUpdateTime(System.currentTimeMillis());

        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                .updateById(entity);
    }

    public int delete(String tenantId, String id) {
        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .deleteByEiAndId(tenantId, id);
    }

    public List<ErpAlarmRuleEntity> findData(String tenantId) {
        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findData(tenantId,null,null,null,null,null);
    }

    public List<ErpAlarmRuleEntity> findData(String tenantId, String dataCenterId, Integer limit, Integer offset) {
        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findData(tenantId, dataCenterId, null, null, limit, offset);
    }

    public List<ErpAlarmRuleEntity> findData(String tenantId, String dataCenterId, AlarmRuleType alarmRuleType) {
        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findData(tenantId, dataCenterId,alarmRuleType,null,null,null);
    }

    public List<ErpAlarmRuleEntity> findData(String tenantId, String dataCenterId, AlarmRuleType alarmRuleType, AlarmType alarmType) {
        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findData(tenantId, dataCenterId,alarmRuleType,alarmType,null,null);
    }

    public int count(String tenantId, String dataCenterId, AlarmRuleType alarmRuleType, AlarmType alarmType) {
        return erpAlarmRuleDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .count(tenantId, dataCenterId,alarmRuleType,alarmType);
    }

    //初始化当前企业所有数据中心的告警规则配置
    public void checkAndInitAlarmRuleData(String tenantId, String lang) {
        executorService.submit(() -> {
            erpConnectInfoManager.listByTenantId(tenantId).forEach(connectInfoEntity -> {
                if(connectInfoEntity.getChannel()== ErpChannelEnum.CRM) return;
                if(StringUtils.isEmpty(connectInfoEntity.getConnectParams())) return;

                checkAndInitAlarmRuleData(tenantId, connectInfoEntity.getId(), lang);
            });
        });
    }

    //初始化告警规则配置
    @Transactional
    public void checkAndInitAlarmRuleData(String tenantId, String dataCenterId, String lang) {
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        if(connectInfoEntity==null
                || connectInfoEntity.getChannel() == ErpChannelEnum.CRM
                || connectInfoEntity.getChannel() == ErpChannelEnum.CONNECTOR_QYWX
                || connectInfoEntity.getChannel() == ErpChannelEnum.CONNECTOR_FEISHU) return;

        AlertNoticeConfig alertNoticeConfig = null;
        PloyBreakNoticeConfig ployBreakNoticeConfig = null;

        ErpTenantConfigurationEntity config = tenantConfigurationManager.findNoCache(tenantId,
                dataCenterId,
                TenantConfigurationTypeEnum.ALERT_NOTICE);
        if (config != null) {
            alertNoticeConfig = JacksonUtil.fromJson(config.getConfiguration(), AlertNoticeConfig.class);
        }

        config = tenantConfigurationManager.findNoCache(tenantId,
                dataCenterId,
                TenantConfigurationTypeEnum.BREAK_PLOY_NOTICE);
        if (config != null) {
            ployBreakNoticeConfig = JacksonUtil.fromJson(config.getConfiguration(), PloyBreakNoticeConfig.class);
        }

        //1.初始化 接口服务异常 配置
        List<ErpAlarmRuleEntity> entityList = findData(tenantId, dataCenterId, AlarmRuleType.GENERAL, AlarmType.POLLING_ERP_API_EXCEPTION);
        if(CollectionUtils.isEmpty(entityList)) {
            int threshold = 0;
            String userIds = null;
            String roleIds = null;
            if(alertNoticeConfig == null) {
                threshold = ConfigCenter.POLLING_ALERT_THRESHOLDS;
                roleIds = RoleCodeContants.CRM_ADMIN;
            } else {
                threshold = alertNoticeConfig.getPollingErpAlertThresholds();
                if(CollectionUtils.isNotEmpty(alertNoticeConfig.getUsers())) {
                    userIds = Joiner.on(";").join(alertNoticeConfig.getUsers());
                }
                if(CollectionUtils.isNotEmpty(alertNoticeConfig.getRoles())) {
                    roleIds = Joiner.on(";").join(alertNoticeConfig.getRoles());
                }
            }
            ErpAlarmRuleEntity entity = ErpAlarmRuleEntity.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dataCenterId)
                    .ployDetailIds("all")
                    .alarmRuleType(AlarmRuleType.GENERAL)
                    .alarmRuleName(AlarmRuleType.GENERAL.getName(i18NStringManager,lang,tenantId))
                    .alarmType(AlarmType.POLLING_ERP_API_EXCEPTION)
                    .alarmLevel(AlarmLevel.URGENT)
                    .threshold(threshold)
                    .userIds(userIds)
                    .roleIds(roleIds)
                    .build();
            int count = insert(entity);
            log.info("ErpAlarmRuleManager.initAlarmRuleData,insert alarmType={},count={}",AlarmType.POLLING_ERP_API_EXCEPTION,count);
        }

        //2.初始化 集成流熔断 配置
        entityList = findData(tenantId, dataCenterId, AlarmRuleType.GENERAL, AlarmType.INTEGRATION_STREAM_BREAK);
        if(CollectionUtils.isEmpty(entityList)) {
            int threshold = 0;
            String userIds = null;
            String roleIds = null;
            if(alertNoticeConfig == null || ployBreakNoticeConfig == null) {
                threshold = ConfigCenter.FAILED_INCREMENT_BREAK_THRESHOLDS;
                roleIds = RoleCodeContants.CRM_ADMIN;
            } else {
                threshold = ployBreakNoticeConfig.getFailedIncrementBreakThresholds();
                if(CollectionUtils.isNotEmpty(ployBreakNoticeConfig.getUsers())) {
                    userIds = Joiner.on(";").join(ployBreakNoticeConfig.getUsers());
                }
                if(CollectionUtils.isNotEmpty(ployBreakNoticeConfig.getRoles())) {
                    roleIds = Joiner.on(";").join(ployBreakNoticeConfig.getRoles());
                }
            }
            ErpAlarmRuleEntity entity = ErpAlarmRuleEntity.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dataCenterId)
                    .ployDetailIds("all")
                    .alarmRuleType(AlarmRuleType.GENERAL)
                    .alarmRuleName(AlarmRuleType.GENERAL.getName(i18NStringManager,lang,tenantId))
                    .alarmType(AlarmType.INTEGRATION_STREAM_BREAK)
                    .alarmLevel(AlarmLevel.URGENT)
                    .threshold(threshold)
                    .userIds(userIds)
                    .roleIds(roleIds)
                    .build();
            int count = insert(entity);
            log.info("ErpAlarmRuleManager.initAlarmRuleData,insert alarmType={},count={}",AlarmType.INTEGRATION_STREAM_BREAK,count);
        }

        //3.初始化 接口熔断 配置
        entityList = findData(tenantId, dataCenterId, AlarmRuleType.GENERAL, AlarmType.GET_BY_ID_API_BREAK);
        if(CollectionUtils.isEmpty(entityList)) {
            int threshold = 0;
            String userIds = null;
            String roleIds = null;
            if(alertNoticeConfig == null || ployBreakNoticeConfig == null || ployBreakNoticeConfig.getGetByIdsFailCount() == null) {
                threshold = configCenterConfig.getGetByIdBreakCount().intValue();
                roleIds = RoleCodeContants.CRM_ADMIN;
            } else {
                threshold = ployBreakNoticeConfig.getGetByIdsFailCount().intValue();
                if(CollectionUtils.isNotEmpty(ployBreakNoticeConfig.getUsers())) {
                    userIds = Joiner.on(";").join(ployBreakNoticeConfig.getUsers());
                }
                if(CollectionUtils.isNotEmpty(ployBreakNoticeConfig.getRoles())) {
                    roleIds = Joiner.on(";").join(ployBreakNoticeConfig.getRoles());
                }
            }
            ErpAlarmRuleEntity entity = ErpAlarmRuleEntity.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dataCenterId)
                    .ployDetailIds("all")
                    .alarmRuleType(AlarmRuleType.GENERAL)
                    .alarmRuleName(AlarmRuleType.GENERAL.getName(i18NStringManager,lang,tenantId))
                    .alarmType(AlarmType.GET_BY_ID_API_BREAK)
                    .alarmLevel(AlarmLevel.IMPORTANT)
                    .threshold(threshold)
                    .userIds(userIds)
                    .roleIds(roleIds)
                    .build();
            int count = insert(entity);
            log.info("ErpAlarmRuleManager.initAlarmRuleData,insert alarmType={},count={}",AlarmType.GET_BY_ID_API_BREAK,count);
        }

        //4.初始化 同步异常 配置
        entityList = findData(tenantId, dataCenterId, AlarmRuleType.GENERAL, AlarmType.SYNC_EXCEPTION);
        if(CollectionUtils.isEmpty(entityList)) {
            int threshold = 0;
            String userIds = null;
            String roleIds = null;
            if(alertNoticeConfig == null) {
                threshold = ConfigCenter.FAILED_INCREMENT_ALERT_THRESHOLDS;
                roleIds = RoleCodeContants.CRM_ADMIN;
            } else {
                threshold = alertNoticeConfig.getFailedIncrementAlertThresholds();
                if(CollectionUtils.isNotEmpty(alertNoticeConfig.getUsers())) {
                    userIds = Joiner.on(";").join(alertNoticeConfig.getUsers());
                }
                if(CollectionUtils.isNotEmpty(alertNoticeConfig.getRoles())) {
                    roleIds = Joiner.on(";").join(alertNoticeConfig.getRoles());
                }
            }
            ErpAlarmRuleEntity entity = ErpAlarmRuleEntity.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dataCenterId)
                    .ployDetailIds("all")
                    .alarmRuleType(AlarmRuleType.GENERAL)
                    .alarmRuleName(AlarmRuleType.GENERAL.getName(i18NStringManager,lang,tenantId))
                    .alarmType(AlarmType.SYNC_EXCEPTION)
                    .alarmLevel(AlarmLevel.IMPORTANT)
                    .threshold(threshold)
                    .userIds(userIds)
                    .roleIds(roleIds)
                    .build();
            int count = insert(entity);
            log.info("ErpAlarmRuleManager.initAlarmRuleData,insert alarmType={},count={}",AlarmType.SYNC_EXCEPTION,count);
        }
    }

    //获取告警类型配置
    public List<ErpAlarmRuleEntity> getAlarmTypeConfig(String tenantId, String dataCenterId, String ployDetailId, AlarmType alarmType) {
        List<AlarmType> supportAlarmTypeList = Lists.newArrayList(AlarmType.POLLING_ERP_API_EXCEPTION,
                AlarmType.INTEGRATION_STREAM_BREAK,
                AlarmType.GET_BY_ID_API_BREAK,
                AlarmType.SYNC_EXCEPTION);
        if(!supportAlarmTypeList.contains(alarmType)) return null;

        //检查并初始化配置
        checkAndInitAlarmRuleData(tenantId, dataCenterId,i18NStringManager.getDefaultLang(tenantId));
        List<ErpAlarmRuleEntity> entityList = findData(tenantId, dataCenterId, null, alarmType);
//        if(CollectionUtils.isEmpty(entityList)) {
//            log.info("ErpAlarmRuleManager.getAlarmTypeConfig,alarmType={},entityList is empty",alarmType);
//            //如果没有配置，则自动初始化配置
//            checkAndInitAlarmRuleData(tenantId, dataCenterId, null);
//            entityList = findData(tenantId, dataCenterId, null, alarmType);
//        }

        entityList = entityList.stream().filter((item) -> StringUtils.containsIgnoreCase(item.getPloyDetailIds(), ployDetailId) || StringUtils.containsIgnoreCase(item.getPloyDetailIds(), "all"))
                .sorted(Comparator.comparingInt((ErpAlarmRuleEntity::getThreshold)))
                .collect(Collectors.toList());
        log.info("ErpAlarmRuleManager.getAlarmTypeConfig,tenantId={},dataCenterId={},ployDetailId={},alarmType={},entityList={}",
                tenantId,dataCenterId,ployDetailId,alarmType,entityList);
        return entityList;
    }

    public ErpAlarmRuleEntity getAlarmTypeConfig2(String tenantId, String dataCenterId, AlarmRuleType alarmRuleType, AlarmType alarmType) {
        List<AlarmType> supportAlarmTypeList = Lists.newArrayList(AlarmType.POLLING_ERP_API_EXCEPTION,
                AlarmType.INTEGRATION_STREAM_BREAK,
                AlarmType.GET_BY_ID_API_BREAK,
                AlarmType.SYNC_EXCEPTION);
        if(!supportAlarmTypeList.contains(alarmType)) return null;

        //检查并初始化配置
        checkAndInitAlarmRuleData(tenantId, dataCenterId,i18NStringManager.getDefaultLang(tenantId));
        List<ErpAlarmRuleEntity> entityList = findData(tenantId, dataCenterId, alarmRuleType, alarmType);
//        if(CollectionUtils.isEmpty(entityList)) {
//            log.info("ErpAlarmRuleManager.getAlarmTypeConfig2,alarmType={},entityList is empty",alarmType);
//            //如果没有配置，则自动初始化配置
//            checkAndInitAlarmRuleData(tenantId, dataCenterId, null);
//            entityList = findData(tenantId, dataCenterId, null, alarmType);
//        }

        ErpAlarmRuleEntity alarmRuleEntity = entityList.get(0);
        log.info("ErpAlarmRuleManager.getAlarmTypeConfig2,tenantId={},dataCenterId={},alarmRuleType={},alarmType={},alarmRuleEntity={}",
                tenantId,dataCenterId,alarmRuleType,alarmType,alarmRuleEntity);
        return alarmRuleEntity;
    }

    public AlarmRuleMatchModel getThresholdMathResult(List<ErpAlarmRuleEntity> alarmRuleEntityList, int threshold) {
        if(CollectionUtils.isEmpty(alarmRuleEntityList)) {
            log.info("getThresholdMathResult,alarmRuleEntityList is empty,threshold={}",threshold);
            return new AlarmRuleMatchModel();
        }
        AlarmRuleMatchModel model = new AlarmRuleMatchModel();
        List<ErpAlarmRuleEntity> thresholdList = alarmRuleEntityList.stream()
                .sorted(Comparator.comparingInt((ErpAlarmRuleEntity::getThreshold)))
                .collect(Collectors.toList());

        model.setMinThreshold(thresholdList.get(0).getThreshold());
        model.setMaxThreshold(thresholdList.get(thresholdList.size() - 1).getThreshold());

        ErpAlarmRuleEntity matchEntity = null;
        for (ErpAlarmRuleEntity item : thresholdList) {
            if(threshold >= item.getThreshold()) {
                matchEntity = item;
            }
        }
        model.setMatchEntity(matchEntity);
        if(matchEntity != null) {
            model.setMatchThreshold(matchEntity.getThreshold());
        }
        model.setGreaterThanMaxThreshold(threshold >= model.getMaxThreshold());

        log.info("ErpAlarmRuleManager.getThresholdMathResult,threshold={},model={}",threshold,model);
        return model;
    }
}
