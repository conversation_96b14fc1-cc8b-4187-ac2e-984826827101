---
description: groovy spock test
globs: *.groovy
alwaysApply: false
---
# Groovy 测试类编写规范 (v1.0)

## 规则标识
- 规则ID: GROOVY-TEST-001
- 适用范围: Groovy测试类
- 强制等级: 必须遵守

## 快速检查表
- [ ] 类包含完整的文档注释
- [ ] 继承自 Specification
- [ ] setup() 方法中正确初始化依赖
- [ ] 测试方法使用清晰的描述性命名
- [ ] 使用 @Unroll 进行参数化测试
- [ ] Mock 定义位置正确
- [ ] 测试用例数据多样化（边界、临界、逻辑覆盖完全）
- [ ] 断言清晰明确
- [ ] 所有必要的类都已正确导入

## 1. 类结构规范

### 1.1 基本结构 [必须]
```groovy
/**
 * XXX测试类
 * <AUTHOR>
 * @date 创建时间
 */
class XXXTest extends Specification {
    // 被测试的主类
    XXX xxx
    
    def setup() {
        // 初始化
    }
}
```

### 1.2 依赖注入 [必须]
```groovy
依赖的Service1类要正确import,比如：import xxx.xxx.Service1
def setup() {
    xxx = new XXX(
        service1: Mock(Service1) {
            // 默认行为定义
            method1(*_) >> { args -> ... }
        },
        service2: Mock(Service2)
    )
}
```

## 2. 测试方法规范

### 2.1 命名规则 [必须]
- 格式：`"测试{功能名称}-#{场景描述}"`
- 示例：`"测试用户注册-#desc"`

### 2.2 方法结构 [必须]
```groovy
@Unroll
def "测试{功能名称}-#desc"() {
    given: "准备测试数据"
    def input = new Input(field: value)
    
    and: "准备Mock行为"
    service.method(*_) >> result
    
    when: "执行被测试方法"
    def output = xxx.process(input)
    
    then: "验证测试结果"
    with(output) {
        field1 == expected1
        field2 == expected2
    }
    
    where: "测试数据集"
    desc      | value  || expected1 | expected2
    "正常场景" | "test" || "result" | 200
}
```

## 3. Mock 使用规范

### 3.1 基本语法 [推荐]
```groovy
// 1. 固定返回值
service.method(*_) >> "result"

// 2. 条件返回值
service.method(*_) >> { args ->
    args[0] == "test" ? "success" : "fail"
}

// 3. 异常模拟
service.method(*_) >> { throw new CustomException() }

// 4. 多次调用不同返回值
service.method(*_) >>> ["first", "second", "third"]
```

### 3.2 Mock 定义位置 [必须]
- setup(): 通用行为
- given: 测试特定行为
- 避免在 when/then 中定义

## 4. 数据准备规范

### 4.1 测试数据构建 [推荐]
```groovy
// 1. 构建器模式
def user = User.builder()
    .name("test")
    .age(20)
    .build()

// 2. Map构造
def data = new TestData(
    field1: value1,
    field2: value2
)
```

### 4.2 参数化测试数据 [必须]
```groovy
where:
desc          | input        || expected
"正常值"      | "valid"      || "success"
"空值"        | ""          || "error"
"特殊字符"    | "@#$"       || "error"
```

## 5. 断言规范

### 5.1 基本断言 [必须]
```groovy
then: "验证结果"
with(result) {
    id != null
    name == expected
    age > 0
    list.size() == 2
}
```

### 5.2 异常断言 [必须]
```groovy
when: "执行可能抛出异常的方法"
service.riskyMethod()

then: "验证异常"
def ex = thrown(ExpectedException)
with(ex) {
    message == "期望的错误信息"
    code == 500
}
```

## 6. 最佳实践

### 6.1 测试覆盖 [必须]
- 正常流程测试
- 边界值测试
- 异常流程测试
- 并发测试（如需要）

### 6.2 代码组织 [推荐]
- 相关测试用例组合
- 复杂场景拆分多个方法
- 合理使用 setup/cleanup

### 6.3 性能考虑 [推荐]
- 避免不必要的对象创建
- 重用测试数据
- 合理使用 @Shared 注解

## 7. 文档规范

### 7.1 必要注释 [必须]
- 类级别文档（作者、时间、用途）
- 复杂测试用例说明
- given/when/then 注释

### 7.2 代码示例
```groovy
/**
 * 用户服务测试类
 * <AUTHOR>
 * @date 2024-03-06
 */
class UserServiceTest extends Specification {
    @Shared
    def commonData
    
    def setup() {
        // 每个测试方法前执行
    }
    
    def cleanup() {
        // 每个测试方法后执行
    }
    
    def setupSpec() {
        // 所有测试方法执行前执行一次
        commonData = prepareCommonData()
    }
    
    def cleanupSpec() {
        // 所有测试方法执行后执行一次
    }
}