package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ConvertFile;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.EmployeeData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.model.OutFile;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SimpleEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
public interface ConnectorDataHandler extends ConnectorPushHandler {
    /**
     * 根据Id获取erp对象数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#erpIdArg.objAPIName", dataId = "#erpIdArg.dataId", sourceEventType = "#erpIdArg.sourceEventType", snapshotId = "#erpIdArg.syncPloyDetailSnapshotId", action = ActionEnum.GET, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outDataCount = "1", outSideObjApiName = "#erpIdArg.objAPIName", outSideObjId = "#erpIdArg.dataId", sourceSystemType = "2", operationType = CommonConstant.READ_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.queryMasterById, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo);

    /**
     * 通过id获取数据，返回list,适配一条数据被扩展为多条
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "#result?.getData()?.size()?:0", objAPIName = "#erpIdArg.objAPIName", dataId = "#erpIdArg.dataId", sourceEventType = "#erpIdArg.sourceEventType", snapshotId = "#erpIdArg.syncPloyDetailSnapshotId", action = ActionEnum.GET_LIST_BY_ID, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#erpIdArg.objAPIName", outDataCount = "#result?.getData()?.size()?:0", outSideObjId = "#erpIdArg.dataId", sourceSystemType = "2", operationType = CommonConstant.READ_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.queryMasterById, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    default Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if (!erpObjDataResult.isSuccess()) {
            return Result.copy(erpObjDataResult);
        }
        return Result.newSuccess(Lists.newArrayList(erpObjDataResult.getData()));
    }

    /**
     * 根据时间获取erp对象数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "#result?.getData()?.dataList?.size()?:0", objAPIName = "#timeFilterArg.objAPIName", snapshotId = "#timeFilterArg.snapshotId", action = ActionEnum.LIST, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#timeFilterArg.objAPIName", outDataCount = "#result?.getData()?.dataList?.size()?:0", sourceSystemType = "2", operationType = CommonConstant.READ_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.queryMasterBatch, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo);

    /**
     * 新建erp对象数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", data = "#standardData", objAPIName = "#standardData.objAPIName", dataId = "#result?.data?.masterDataId", action = ActionEnum.CREATE, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardData.objAPIName", outSideObjId = "#result?.data?.masterDataId", sourceSystemType = "1", operationType = CommonConstant.WRITE_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.create, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo);

    /**
     * 新建erp对象明细数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", data = "#standardDetailData", objAPIName = "#standardDetailData.objAPIName", dataId = "#standardDetailData.masterDataId", snapshotId = "#doWriteMqData.syncPloyDetailSnapshotId", action = ActionEnum.CREATE_DETAIL, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardDetailData.objAPIName", outSideObjId = "#standardDetailData?.masterDataId", sourceSystemType = "1", operationType = CommonConstant.WRITE_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.createDetail, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                    StandardDetailData standardDetailData,
                                                    ErpConnectInfoEntity connectInfo);

    /**
     * 作废erp对象数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#standardInvalidData.objAPIName", dataId = "#standardInvalidData.masterFieldVal.getId()", action = ActionEnum.INVALID)
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardInvalidData.objAPIName", outSideObjId = "#standardInvalidData?.masterFieldVal.getId()", sourceSystemType = "1", operationType = CommonConstant.WRITE_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.invalid, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo);

    /**
     * 作废erp对象明细数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#standardInvalidData.objAPIName", dataId = "#standardInvalidData.masterFieldVal.getId()", action = ActionEnum.INVALID_DETAIL)
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardInvalidData.objAPIName", outSideObjId = "#standardInvalidData?.masterDataId", sourceSystemType = "1", operationTypeDetail = ErpObjInterfaceUrlEnum.invalidDetail, operationType = CommonConstant.WRITE_OPERATE_TYPE, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo);

    /**
     * 恢复erp对象数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#standardRecoverData.objAPIName", dataId = "#standardRecoverData.masterFieldVal.getId()", action = ActionEnum.RECOVER)
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardRecoverData.objAPIName", outSideObjId = "#standardRecoverData?.masterFieldVal.getId()", sourceSystemType = "1", operationType = CommonConstant.WRITE_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.recover, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo);

    /**
     * 更新erp对象数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", data = "#standardData", objAPIName = "#standardData.objAPIName", dataId = "#standardData.masterFieldVal.getId()", action = ActionEnum.UPDATE, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardData.objAPIName", outSideObjId = "#standardData?.masterFieldVal.getId()", sourceSystemType = "1", operationType = CommonConstant.WRITE_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.update, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo);

    /**
     * 更新erp对象明细数据
     */
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", data = "#standardDetailData", objAPIName = "#standardDetailData.objAPIName", dataId = "#standardDetailData.masterDataId", snapshotId = "#doWriteMqData.syncPloyDetailSnapshotId", action = ActionEnum.UPDATE_DETAIL, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#connectInfo.tenantId", dataCenterId = "#connectInfo.id", outSideObjApiName = "#standardDetailData.objAPIName", outSideObjId = "#standardDetailData?.masterDataId", sourceSystemType = "1", operationType = CommonConstant.WRITE_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.updateDetail, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                    StandardDetailData standardDetailData,
                                                    ErpConnectInfoEntity connectInfo);

    /**
     * 推送验证
     *
     * @param checkAuth
     * @param connectInfo
     * @return
     */
    default Result<Void> checkPushAuth(CheckAuthArg checkAuth, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.NO_USER);
    }

    /**
     * 处理推送的数据
     */
    default Result<List<StandardData>> processPushData(PushDataProcessArg pushDataProcessArg, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#standardDeleteData.objAPIName", dataId = "#standardDeleteData.masterFieldVal.getId()", action = ActionEnum.DELETE)
    default Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    /**
     * 集成平台内部事件通知
     */
    default Result<Void> processChangeEmployeeMapping(SimpleEmployeeMapping simpleEmployeeMapping, ErpConnectInfoEntity connectInfo) {
        return Result.newSuccess();
    }

    default Result<List<EmployeeData>> queryAllEmployee(ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**
     * 处理crm->erp的文件转换
     */
    default Result<List<OutFile>> convertCrmFile2Out(ConvertFile.Crm2OutArg arg, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    /**
     * 处理erp->crm的文件转换
     *
     */
    default Result<List<CrmFileModel>> convertOutFile2Crm(ConvertFile.Out2CrmArg arg, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }
}
