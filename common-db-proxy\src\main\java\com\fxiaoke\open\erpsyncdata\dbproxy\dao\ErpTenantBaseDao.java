package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.github.mybatis.annotation.FillEntityType;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;

import java.util.List;

/**
 * Created by fengyh on 2020/8/26.
 */
public interface ErpTenantBaseDao<T> {
    /**
     * 插入一条记录
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = CrudProvider.class, method = "insert")
    @Result(javaType = int.class)
    int insert(T record);

    /**
     * 批量插入记录
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = BatchProvider.class, method = "batchInsert")
    @Result(javaType = int.class)
    int batchInsert(@Param("tenantId") String tenantId,@Param(BatchProvider.KEY)List<T> record);


    /**根据Id删除数据库中的记录。
     * 如果没有记录被删除，返回0. 大于0表示有记录被删除。a
     * */
    @DeleteProvider(type = CrudProvider.class, method = "deleteByEiAndId")
    @FillEntityType
    @Result(javaType = int.class)
    int deleteByEiAndId(String ei, String Id);
}
