package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.lang.Assert;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UserOperatorLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Executor;

@Component
@Slf4j
public class UserOperatorLogManager {

    private static UserOperatorLogDao userOperatorLogDao;

    private static Executor executor;

    @Autowired
    public void setUserOperatorLogDao(UserOperatorLogDao userOperatorLogDao) {
        UserOperatorLogManager.userOperatorLogDao = userOperatorLogDao;
    }

    @Autowired
    @Qualifier("userOperatorLogExecutor")
    public void setExecutor(Executor executor) {
        UserOperatorLogManager.executor = executor;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public static void asyncSavaLog(UserOperatorLog userOperatorLog){
        executor.execute(()->{
            try {
                userOperatorLogDao.save(userOperatorLog);
            }catch (Exception e){
                log.warn("save log data error,data:{}",userOperatorLog,e);
            }
        });
    }

    public static List<UserOperatorLog> queryUserOperatorLogs(String tenantId,String datacenterId,String module,String moduleId,int offset,int limit){
        return userOperatorLogDao.queryUserOperatorLogs(tenantId,datacenterId,module,moduleId,offset,limit);
    }


}
