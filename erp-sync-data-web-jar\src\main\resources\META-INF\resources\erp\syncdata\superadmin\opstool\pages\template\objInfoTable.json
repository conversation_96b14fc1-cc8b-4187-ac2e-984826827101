{"type": "page", "remark": "", "name": "objInfoTable", "body": [{"type": "crud", "name": "objInfoCRUD", "api": "../template/listInfos?type=obj&templateId=${templateId}", "loadDataOnce": true, "primaryField": "realObjApiName", "defaultParams": {"perPage": 100}, "filter": {"title": "查询条件", "body": {"name": "templateId", "label": "模板", "type": "select", "mode": "inline", "size": "lg", "labelField": "title", "valueField": "id", "selectFirst": true, "source": {"method": "get", "url": "../template/list"}, "extractValue": true, "searchable": true}}, "headerToolbar": ["export-csv", "reload"], "columns": [{"name": "realObjApiName", "label": "真实编码"}, {"name": "objName", "label": "名称"}, {"name": "mainRealObjApiName", "label": "主对象真实编码"}], "affixHeader": true, "placeholder": "暂无数据"}]}