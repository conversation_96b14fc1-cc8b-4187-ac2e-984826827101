package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.map.multi.RowKeyTable;
import cn.hutool.core.map.multi.Table;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DoubleWrite;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ErpTempDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationIdStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.ErpTempDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.ErpTempMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BsonTimeUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SearchTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/15
 * @Desc:
 */
@Slf4j
@Repository
public class ErpTempDataDao {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ErpTempMongoStore mongoStore;
    @Autowired
    @Lazy
    private SyncLogManager syncLogManager;

    @DoubleWrite
    public Result<Long> deleteAllErpTempData(String tenantId) {
        Bson filter = Filters.ne("tenant_id", "");
        DeleteResult deleteResult = mongoStore.deleteErpTempDataDocument(tenantId, filter);
        return Result.newSuccess(deleteResult.getDeletedCount());
    }

    @DoubleWrite
    public Result<Long> updateSyncStatusByIds(String tenantId, String ployDetailId, List<String> objectIds, Integer syncStatus, String remark) {
        return updateSyncStatusByIds(tenantId, ployDetailId, objectIds, syncStatus, remark, null);
    }

    @DoubleWrite
    public Result<Long> updateSyncStatusByIds(String tenantId, String ployDetailId, List<String> objectIds, Integer syncStatus, String remark, List<String> cleanFields) {
        List<Bson> updateList = Lists.newArrayList();
        updateList.add(Updates.set("update_time", System.currentTimeMillis()));
        if (syncStatus != null && !StringUtils.isEmpty(ployDetailId)) {
            updateList.add(Updates.set("sync_status." + ployDetailId, syncStatus));
        }
        if (!StringUtils.isEmpty(remark)) {
            updateList.add(Updates.set("remark", remark));
        }
        if (CollectionUtils.isNotEmpty(cleanFields)) {
            cleanFields.forEach(field -> {
                updateList.add(Updates.unset(field));
            });
        }
        Bson updates = Updates.combine(updateList);
        List<ObjectId> objectIdList = objectIds.stream().map(id -> new ObjectId(id)).collect(Collectors.toList());
        UpdateResult updateResult = mongoStore.updateErpTempDataByIds(tenantId, objectIdList, updates);
        return Result.newSuccess(updateResult.getModifiedCount());
    }

    @DoubleWrite
    public void batchUpsertErpTempData(String tenantId, List<ErpTempData> dataList, Boolean updateByNum) {
        if (dataList.isEmpty()) {
            return;
        }

        int size = dataList.size();
        StopWatch sw = new StopWatch("batchUpsert-" + tenantId + '[' + size + ']');
        List<WriteModel<Document>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        if (LogIdUtil.get().contains(LogIdUtil.VIRTUAL_API_LOG_NAME) && CollectionUtils.isNotEmpty(dataList)) {
            //有些数据的来源千变万化，可能前置logId未设置，为了避免影响到后面数据同步，这里做个兜底
            syncLogManager.initLogId(tenantId, dataList.get(0).getObjApiName());
        }
        log.info("batchUpsertErpTempData datalist:{},logId：{}", JSONObject.toJSONString(dataList), LogIdUtil.get());
        for (int i = 0; i < dataList.size(); i++) {
            ErpTempData erpTempData = dataList.get(i);
            erpTempData.setId(ObjectId.get());
            //临时库数据增加id
            if (erpTempData.getSyncLogId() == null) {
                erpTempData.setSyncLogId(LogIdUtil.get());
            }
            UpdateOneModel<Document> updateOneModel;
            if (updateByNum) {
                updateOneModel = new UpdateOneModel<>(ErpTempDataHelper.updateByEiObjDataNum(erpTempData), ErpTempDataHelper.upsert(erpTempData), updateOption);
            } else {
                updateOneModel = new UpdateOneModel<>(ErpTempDataHelper.updateByEiObjDataId(erpTempData), ErpTempDataHelper.upsert(erpTempData), updateOption);
            }
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        sw.start("bulkWrite-" + tenantId + '[' + size + ']');
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
            sw.stop();
            long cost = sw.getTotalTimeMillis();
            if (cost > 5000) {
                log.warn("bulkWrite collection: {}, cost: {}ms, items: {}", tenantId, cost, size);
                log.warn(sw.prettyPrint());
            }
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }

        dataList.forEach(item -> {
//            log.debug("batchUpsertErpTempData item:{},logId：{}", JSONObject.toJSONString(item), item.getSyncLogId());
            LogIdUtil.setRealObjApiName(item.getObjApiName());
            //轮询id的日志已经去除，不会调用
                syncLogManager.saveErpTempLog(tenantId, SyncLogTypeEnum.TEMP, SyncLogStatusEnum.SYNC_SUCCESS.getStatus(), item.getSyncLogId(), item);
        });
    }

    //获取需要轮询的数据
    public List<Document> listErpObjDataFromMongo(@TenantID("#timeFilterArg.getTenantId()") QueryTempTimeFilterArg timeFilterArg, String dataCenterId) {
        final String tenantId = timeFilterArg.getTenantId();
        //根据时间和id排序，先查询new_last_sync_time等于上一次最大的时间并且_id更大的
        Bson sort = Sorts.ascending("new_last_sync_time", "_id");
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dataCenterId));
        filters.add(Filters.eq("obj_api_name", timeFilterArg.getObjAPIName()));
        //不筛选status为了走索引
        if (EventTypeEnum.DELETE_DIRECT.getType() == timeFilterArg.getOperationType()) {
            filters.add(Filters.eq("operation_type", EventTypeEnum.DELETE_DIRECT.getType()));//轮询删除数据
        } else if (EventTypeEnum.INVALID.getType() == timeFilterArg.getOperationType()) {
            filters.add(Filters.eq("operation_type", EventTypeEnum.INVALID.getType()));//轮询作废数据
        } else {//不用ne是为了走索引，目前除了作废的数据，其他数据都是没有operation_type这个字段的
            filters.add(Filters.eq("operation_type", null));//轮询非作废数据
        }
        int limit = timeFilterArg.getLimit();
        List<Document> documents = new ArrayList<>();
        if (StrUtil.isNotEmpty(timeFilterArg.getLastErpTempId())) {
            List<Bson> eqFilters = new ArrayList<>(filters);
            eqFilters.add(Filters.eq("new_last_sync_time", BsonTimeUtil.long2BsonDate(timeFilterArg.getStartTime())));
            eqFilters.add(Filters.gt("_id", new ObjectId(timeFilterArg.getLastErpTempId())));
            List<Document> eqDocs = mongoStore.listErpTempDataDocument(tenantId, eqFilters, sort, 0, limit);
            if (eqDocs.size() == limit) {
                return eqDocs;
            } else {
                documents.addAll(eqDocs);
                //未查询够数据
                limit = limit - eqDocs.size();
            }
        }
        if (timeFilterArg.getEndTime() > timeFilterArg.getStartTime()) {

            //如果限速时，endTime=startTime，不需要再执行这一步查询
            Bson lastSyncTime = Filters.and(Filters.gt("new_last_sync_time", BsonTimeUtil.long2BsonDate(timeFilterArg.getStartTime())),
                    Filters.lte("new_last_sync_time", BsonTimeUtil.long2BsonDate(timeFilterArg.getEndTime())));
            filters.add(lastSyncTime);
            //不通过offset翻页轮询，通过new_last_sync_time和id排序查询
            List<Document> gtDocs = mongoStore.listErpTempDataDocument(tenantId, filters, sort, 0, limit);
            documents.addAll(gtDocs);
        }
        return documents;
    }

    public Result<Document> getErpObjDataFromMongo(@TenantID("#erpIdArg.getTenantId()") ErpIdArg erpIdArg, String dataCenterId, Boolean useNumber) {
        final String tenantId = erpIdArg.getTenantId();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dataCenterId));
        filters.add(Filters.eq("obj_api_name", erpIdArg.getObjAPIName()));
        if (useNumber) {
            filters.add(Filters.eq("data_number", erpIdArg.getDataId()));
        } else {
            filters.add(Filters.eq("data_id", erpIdArg.getDataId()));
        }
        List<Document> documents = mongoStore.listErpTempDataDocument(tenantId, filters, null, 0, 1);
        if (!CollectionUtils.isEmpty(documents)) {
            Document document = documents.get(0);
            if (!ErpTempDataStatusEnum.STATUS_NOT_READY.getStatus().equals(document.getInteger("status"))) {
                return Result.newSuccess(document);
            } else {
                return Result.newError("-10000", document.getString("remark"));
            }
        }
        return Result.newError("-10000", I18NStringEnum.s3656, erpIdArg.getDataId());
    }

    public Result<List<Document>> listErpObjData(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                                 Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType, Integer pageNum, Integer pageSize) {

        List<Bson> filters = buildFilters(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, searchType);
        List<Document> documents = mongoStore.listErpTempDataDocument(tenantId, filters, Sorts.orderBy(Sorts.descending("_id")), (pageNum - 1) * pageSize, pageSize);
        return Result.newSuccess(documents);
    }

    private List<Bson> buildFilters(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                    Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dataCenterId));
        filters.add(Filters.eq("obj_api_name", erpRealObjectApiName));
        if (startTime != null) {
            filters.add(Filters.gte("update_time", startTime));
        }
        if (endTime != null) {
            filters.add(Filters.lte("update_time", endTime));
        }
        if (sourceDataStatus != null) {
            filters.add(Filters.eq("status", sourceDataStatus));
        }
        if (!StringUtils.isEmpty(ployDetailId)) {
            if (tempDataSyncStatus != null) {
                filters.add(Filters.eq("sync_status." + ployDetailId, tempDataSyncStatus));
            } else {
                filters.add(Filters.exists("sync_status." + ployDetailId, true));
            }
        }
        if (!StringUtils.isEmpty(taskNum)) {
            filters.add(Filters.all("task_num", taskNum));
        }
        if (!StringUtils.isEmpty(idOrNum)) {
            if (searchType == null) {
                Bson id = Filters.eq("data_id", idOrNum);
                Bson num = Filters.eq("data_number", idOrNum);
                filters.add(Filters.or(id, num));
            } else {
                if (SearchTypeEnum.DATA_ID.equals(searchType)) {
                    Bson id = Filters.eq("data_id", idOrNum);
                    filters.add(id);
                } else if (SearchTypeEnum.DATA_NUMBER.equals(searchType)) {
                    Bson num = Filters.eq("data_number", idOrNum);
                    filters.add(num);
                }
            }
        }
        return filters;
    }

    public long countErpObjData(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType, Integer pageNum, Integer pageSize) {

        List<Bson> filters = buildFilters(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, searchType);
        return mongoStore.countErpTempData(tenantId, filters);
    }

    public Integer countErpTempDataLimit1000(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                             Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType, Integer pageNum, Integer pageSize) {

        List<Bson> filters = buildFilters(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, searchType);
        return mongoStore.countErpTempDataLimit1000(tenantId, filters, 0, 1000);
    }

    public Result<Document> getErpObjData(String tenantId, String id) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("_id", new ObjectId(id)));
        Bson sort = Sorts.orderBy(Sorts.ascending("_id"));
        List<Document> documents = mongoStore.listErpTempDataDocument(tenantId, filters, sort, 0, 1);
        if (!CollectionUtils.isEmpty(documents)) {
            Document document = documents.get(0);
            return Result.newSuccess(document);
        }
        return Result.newError("-10000", I18NStringEnum.s18 + id);
    }

    public Document getErpObjDataById(String tenantId, String dataCenterId, String objApiName, String dataId) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dataCenterId));
        filters.add(Filters.eq("obj_api_name", objApiName));
        filters.add(Filters.eq("data_id", dataId));
        List<Document> documents = mongoStore.listErpTempDataDocument(tenantId, filters, null, 0, 1);
        if (!CollectionUtils.isEmpty(documents)) {
            return documents.get(0);
        }
        return null;
    }

public Document getErpObjDataByNum(String tenantId, String dataCenterId, String objApiName, String dataNum) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dataCenterId));
        filters.add(Filters.eq("obj_api_name", objApiName));
        filters.add(Filters.eq("data_number", dataNum));

        List<Document> documents = mongoStore.listErpTempDataDocument(tenantId, filters, null, 0, 1);
        if (!CollectionUtils.isEmpty(documents)) {
            return documents.get(0);
        }
        return null;
    }

    public List<Document> batchGetErpField(String tenantId, String dataCenterId, String objApiName,
                                           String field, List<String> dataIdList,List<String> projectFields) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dataCenterId));
        filters.add(Filters.eq("obj_api_name", objApiName));
        filters.add(Filters.in(field, dataIdList));
        //List<String> projectFields = Lists.newArrayList("tenant_id","dc_id", "obj_api_name", "data_id", "data_md5");
        List<Document> documents = mongoStore.listErpTempDataDocumentField(tenantId, filters, null, 0, dataIdList.size(),projectFields);
        return documents;
    }

    public List<Document> listErpObjDataByMongoIds(String tenantId, List<String> mongoIds) {
        List<ObjectId> ids = mongoIds.stream().map(id -> new ObjectId(id)).collect(Collectors.toList());

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.in("_id", ids));
        //只查询列表大小，所以必须唯一
        //指定不返回字段
        Bson projections = Projections.fields(Projections.exclude("data_body"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        FindIterable<Document> findIterable = collection.find(Filters.and(filters))
                .projection(projections)
                .limit(mongoIds.size());
        return Lists.newArrayList(findIterable);
    }

    @DoubleWrite
    public Result<Long> removeErpTempDataByMongoId(String tenantId, ObjectId mongoId) {
        Bson filter = Filters.eq("_id", mongoId);
        DeleteResult deleteResult = mongoStore.deleteErpTempDataDocument(tenantId, filter);
        return Result.newSuccess(deleteResult.getDeletedCount());
    }

    @DoubleWrite
    public Result<Long> removeErpObjData(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                         Integer tempDataSyncStatus, String taskNum, String idOrNum) {

        List<Bson> filters = buildFilters(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, null);
        DeleteResult result = mongoStore.deleteErpTempDataDocument(tenantId, Filters.and(filters));
        return Result.newSuccess(result.getDeletedCount());
    }

    @DoubleWrite
    public Result<Long> removeErpObjDataByIdLists(String tenantId, List<ObjectId> idList) {

        DeleteResult result = mongoStore.deleteErpTempDataDocument(tenantId, Filters.in("_id", idList));
        return Result.newSuccess(result.getDeletedCount());
    }

    public List<Document> listErpTempByIdFilter(String tenantId, String dcId, String objApiName, String gtId, String ltId, int limit) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dcId));
        filters.add(Filters.eq("obj_api_name", objApiName));
        if (gtId != null) {
            filters.add(Filters.gt("_id", new ObjectId(gtId)));
        }
        if (ltId != null) {
            filters.add(Filters.lt("_id", new ObjectId(ltId)));
        }
        //指定返回字段
        Bson projections = Projections.fields(Projections.include("data_id"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        FindIterable<Document> findIterable = collection.find(Filters.and(filters))
                .projection(projections)
                .sort(Sorts.descending("_id"))
                .limit(limit);
        return Lists.newArrayList(findIterable);
    }

    public Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> listNotTempIdList(String tenantId, String dcId, String historyTaskNum, String objApiName, List<String> ids, Boolean checkByNum) {
        Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> result = Maps.newHashMap();
        result.put(IdSyncStatus.not_temp, Maps.newHashMap());
        result.put(IdSyncStatus.temp, Maps.newHashMap());
        List<String> notTempIdList = BeanUtil.deepCopyList(ids, String.class);

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dcId));
        filters.add(Filters.eq("obj_api_name", objApiName));
        if (checkByNum != null && checkByNum) {
            filters.add(Filters.in("data_number", ids));
        } else {
            filters.add(Filters.in("data_id", ids));
        }
        if (!StringUtils.isEmpty(historyTaskNum)) {
            filters.add(Filters.all("task_num", historyTaskNum));
        }
        //指定返回字段
        Bson projections = Projections.fields(Projections.include("data_id", "data_number", "sync_log_id", "remark","update_time"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        FindIterable<Document> findIterable = collection.find(Filters.and(filters))
                .projection(projections)
                .sort(Sorts.descending("_id"))
                .limit(ids.size());
        if (checkByNum != null && checkByNum) {
            for (Document document : findIterable) {
                String dataNumber = document.getString("data_number");
                buildDataVerificationIdStatus(dataNumber, result, IdSyncStatus.temp, document);
                notTempIdList.remove(dataNumber);
            }
        } else {
            for (Document document : findIterable) {
                String dataId = document.getString("data_id");
                buildDataVerificationIdStatus(dataId, result, IdSyncStatus.temp, document);
                notTempIdList.remove(dataId);
            }
        }
        for (String id : notTempIdList) {
            DataVerificationIdStatus idStatus = new DataVerificationIdStatus();
            idStatus.setDataId(id);
            idStatus.setStatus(IdSyncStatus.not_temp);
            result.get(IdSyncStatus.not_temp).put(id, idStatus);
        }
        return result;
    }

    private void buildDataVerificationIdStatus(String dataId, Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> result, IdSyncStatus status, Document document) {
        DataVerificationIdStatus idStatus = new DataVerificationIdStatus();
        idStatus.setDataId(dataId);
        idStatus.setSyncLogId(document.getString("sync_log_id"));
        idStatus.setStatusReason(document.getString("remark"));
        idStatus.setLogEndTime(document.getLong("update_time"));
        idStatus.setStatus(status);
        result.get(status).put(dataId, idStatus);
    }

    public List<Document> listHistoryTaskIdList(String tenantId, String dcId, String historyTaskNum, String objApiName, ObjectId lastId, Integer limit) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dcId));
        filters.add(Filters.eq("obj_api_name", objApiName));
        filters.add(Filters.all("task_num", historyTaskNum));
        if (lastId != null) {
            filters.add(Filters.gt("_id", lastId));
        }
        //指定返回字段
        Bson projections = Projections.fields(Projections.include("data_id", "data_number", "sync_log_id", "remark"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        FindIterable<Document> findIterable = collection.find(Filters.and(filters))
                .projection(projections)
                .sort(Sorts.ascending("_id"))
                .limit(limit);
        return Lists.newArrayList(findIterable);
    }


    public FindIterable<Document> scanNotTrigger(String tenantId, ObjectId gtId, ObjectId ltId) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.exists("sync_status", false));
        filters.add(Filters.gt("_id", gtId));
        filters.add(Filters.lt("_id", ltId));
        //指定返回字段
        Bson projections = Projections.fields(Projections.include("_id"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        FindIterable<Document> findIterable = collection.find(Filters.and(filters))
                .projection(projections)
                .sort(Sorts.ascending("_id"))
                .limit(100000);
        return findIterable;
    }


    @DoubleWrite
    public UpdateResult batchUpdateLastSyncTime(String tenantId, List<ObjectId> updateTemp, Long lastSyncTime) {

        Document currentDateDoc = new Document();
        Document timestamp = new Document();
        timestamp.append("$type", "date");
        currentDateDoc.append("new_last_sync_time", timestamp);
        Document doc = new Document();
        doc.put("$currentDate", currentDateDoc);//更新为数据库当前时间，不触发轮询，等待补偿轮询，或者有其他数据触发轮询临时库
        UpdateResult updateResult = mongoStore.updateErpTempDataByIds(tenantId, updateTemp, doc);
        return updateResult;
    }


    @DoubleWrite
    public Long batchUpdateLastSyncTimeByTime(String tenantId, List<String> apiNames, Long startTime, Long endTime) {
        Document currentDateDoc = new Document();
        Document timestamp = new Document();
        timestamp.append("$type", "date");
        currentDateDoc.append("new_last_sync_time", timestamp);
        Document doc = new Document();
        doc.put("$currentDate", currentDateDoc);//更新为数据库当前时间，不触发轮询，等待补偿轮询，或者有其他数据触发轮询临时库

        final Bson time = Filters.and(Filters.gt("update_time", startTime), Filters.lt("update_time", endTime));
        final Bson tenantId1 = Filters.eq("tenant_id", tenantId);
        final ArrayList<Bson> filters = Lists.newArrayList(tenantId1, time);

        if (Objects.nonNull(apiNames)) {
            final Bson objApiName = Filters.in("obj_api_name", apiNames);
            filters.add(objApiName);
        }
        UpdateResult updateResult = mongoStore.updateErpTempDataByFilters(tenantId, filters, doc);
        return updateResult.getModifiedCount();
    }

    public Long getTenantLastSyncTime(String tenantId) {

        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        FindIterable<Document> last_sync_time = collection.find().sort(Sorts.descending("new_last_sync_time")).limit(1);
        List<Document> list = Lists.newArrayList(last_sync_time);
        if (CollectionUtils.isNotEmpty(list) && list.get(0).get("new_last_sync_time") != null) {
            return BsonTimeUtil.bsonTime2Long(list.get(0).get("new_last_sync_time"));
        }
        return null;
    }

    @DoubleWrite
    public UpdateResult batchUpdateLastSyncTimeByDataIds(String tenantId, String dcId, String realObjApiName, Collection<String> dataIds) {

        Document currentDateDoc = new Document();
        Document timestamp = new Document();
        timestamp.append("$type", "date");
        currentDateDoc.append("new_last_sync_time", timestamp);
        Document updateTime = new Document();
        updateTime.put("$currentDate", currentDateDoc);
        Bson updates = Updates.combine(
                updateTime,
                Updates.set("update_time", System.currentTimeMillis())
        );
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("dc_id", dcId));
        filters.add(Filters.eq("obj_api_name", realObjApiName));
        filters.add(Filters.in("data_id", dataIds));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        UpdateResult updateResult = collection.updateMany(Filters.and(filters), updates);
        return updateResult;
    }

    public List<ErpTempData> pageByPollingTime(String tenantId, String realObjApiName, Long startTime, Long endTime, ObjectId maxId, int size) {

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("obj_api_name", realObjApiName));
        filters.add(Filters.and(
                Filters.gte("last_polling_time", startTime),
                Filters.lt("last_polling_time", endTime)
        ));
        if (Objects.nonNull(maxId)) {
            filters.add(Filters.gt("_id", maxId));
        }
        //指定返回字段
        Bson projections = Projections.fields(Projections.include("data_id", "data_body"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        final List<ErpTempData> list = new ArrayList<>();
        collection.find(Filters.and(filters))
                .projection(projections)
                .sort(Sorts.ascending("_id"))
                .limit(size)
                .forEach((Consumer<Document>) document -> list.add(buildErpTempDataFromDoc(document)));
        return list;
    }

    public List<String> getDataIdByTaskAndDataId(String tenantId, String taskNum, String objApiName, Set<String> dataIds, Integer status) {
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);

        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", tenantId));
        filters.add(Filters.eq("task_num", taskNum));
        filters.add(Filters.eq("obj_api_name", objApiName));
        filters.add(Filters.in("data_id", dataIds));
        filters.add(Filters.gt("status", status));

        Bson projections = Projections.fields(Projections.include("data_id"));
        List<String> dataIdList = new ArrayList<>();
        collection.find(Filters.and(filters))
                .projection(projections)
                .forEach((Consumer<Document>) document -> dataIdList.add(document.getString("data_id")));
        return dataIdList;
    }

    public static ErpTempData buildErpTempDataFromDoc(Document document) {
        ErpTempData erpTempData = new ErpTempData();
        erpTempData.setId(document.getObjectId("_id"));
        erpTempData.setTenantId(document.getString("tenant_id"));
        erpTempData.setObjApiName(document.getString("obj_api_name"));
        erpTempData.setOperationType(document.getInteger("operation_type"));
        erpTempData.setDataId(document.getString("data_id"));
        erpTempData.setDataNumber(document.getString("data_number"));
        erpTempData.setDataBody(document.getString("data_body"));
        erpTempData.setStatus(document.getInteger("status"));
        erpTempData.setSyncStatusMap((Map) document.get("sync_status"));
        erpTempData.setRemark(document.getString("remark"));
        erpTempData.setTraceId(document.getString("trace_id"));
        if (CollectionUtils.isNotEmpty(document.getList("task_num", String.class))) {
            erpTempData.setTaskNum(Sets.newHashSet(document.getList("task_num", String.class)));
        }
        if (StringUtils.isNotBlank(document.getString("locale"))) {
            erpTempData.setLocale(document.getString("locale"));
        }
        erpTempData.setLastSyncTime(BsonTimeUtil.bsonTime2Long(document.get("last_sync_time")));
        if (document.get("new_last_sync_time") instanceof Date) {
            erpTempData.setNewLastSyncTime(document.getDate("new_last_sync_time"));
        }
        erpTempData.setUpdateTime(document.getLong("update_time"));
        erpTempData.setCreateTime(document.getLong("create_time"));
        erpTempData.setExpireTime(document.getDate("expire_time"));
        return erpTempData;
    }

    /**
     * 因为NewLastSyncTime是mongodb赋值的，需要反查回来,
     * 只能根据dataId或者dataNum去查
     */
    public void fillNewLastSyncTime(String tenantId, String dcId, String objApiName, List<ErpTempData> erpTempDataList, boolean byNum) {
        //指定返回字段
        Bson projections = Projections.fields(Projections.include("_id", "data_id", "data_number", "new_last_sync_time"));
        MongoCollection<Document> collection = mongoStore.getOrCreateCollection(tenantId);
        List<Bson> filters = Lists.newArrayList(
                Filters.eq("tenant_id", tenantId),
                Filters.eq("dc_id", dcId),
                Filters.eq("obj_api_name", objApiName)
        );
        if (byNum) {
            filters.add(Filters.in("data_number", erpTempDataList.stream().map(v -> v.getDataNumber()).collect(Collectors.toList())));
        } else {
            filters.add(Filters.in("data_id", erpTempDataList.stream().map(v -> v.getDataId()).collect(Collectors.toList())));
        }
        ArrayList<Document> docs = collection.find(Filters.and(filters))
                .projection(projections)
                .into(new ArrayList<>());
        Table<String, String, Document> idTable = new RowKeyTable<>();
        for (Document doc : docs) {
            idTable.put(doc.getString("data_id"), doc.getString("data_number"), doc);
        }
        for (ErpTempData erpTempData : erpTempDataList) {
            Document doc = idTable.get(erpTempData.getDataId(), erpTempData.getDataNumber());
            if (doc != null) {
                erpTempData.setId(doc.getObjectId("_id"));
                erpTempData.setNewLastSyncTime(doc.getDate("new_last_sync_time"));
            }
        }
    }
}
