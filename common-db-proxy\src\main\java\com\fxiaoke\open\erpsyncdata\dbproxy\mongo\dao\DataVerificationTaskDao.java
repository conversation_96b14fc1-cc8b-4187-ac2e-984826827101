package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.DataVerificationTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationTask;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.WriteConcern;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.Sorts;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @Date: 15:41 2022/11/3
 * @Desc:
 */

@Slf4j
@Repository
@DependsOn("erpSyncDataMongoStore")
public class DataVerificationTaskDao {
    private static final String _id = "_id";
    private static final String f_tenantId = "tenantId";
    private static final String f_streamId = "streamId";
    private static final String f_taskId = "taskId";
    private static final String f_fileNPath = "fileNPath";
    private static final String f_fileName = "fileName";
    private static final String f_pollingListSize = "pollingListSize";
    private static final String f_allIdListSize = "allIdListSize";
    private static final String f_notTempIdListSize = "notTempIdListSize";
    private static final String f_tempIdListSize = "tempIdListSize";
    private static final String f_notMappingIdListSize = "notMappingIdListSize";
    private static final String f_mappingIdListSize = "mappingIdListSize";
    private static final String f_notCreatedIdListSize = "notCreatedIdListSize";
    private static final String f_createdIdListSize = "createdIdListSize";
    private static final String f_status = "status";
    private static final String f_needStop = "needStop";
    private static final String f_createTime = "createTime";
    private static final String f_updateTime = "updateTime";


    private DatastoreExt store;

    private String DATABASE;

    private final Set<String> taskCollectionCache = Sets.newConcurrentHashSet();
    private final static String collectionName = "data_verification_task";


    DataVerificationTaskDao(@Qualifier("erpSyncDataLogMongoStore") DatastoreExt store) {
        this.store = store;
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
        store.getMongo().getDatabase(DATABASE)
                .listCollectionNames().iterator().forEachRemaining(v -> {
            if (v.equals(collectionName)) {
                taskCollectionCache.add(v);
            }
        });
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(DataVerificationTask.class)
                        .automatic(true).build()));
    }

    public DataVerificationTask getById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        Bson andFilter = Filters.and(Filters.eq(_id, new ObjectId(id)));
        FindIterable<DataVerificationTask> limit = this.getOrCreateDataVerificationTaskCollection().find(andFilter)
                .sort(Sorts.descending(f_createTime))
                //加个limit保险
                .limit(1);
        List<DataVerificationTask> dataList = Lists.newArrayList(limit);
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    /**
     * streamId+taskNum（包括taskNum为空）业务限制只能有一个，
     *
     * @param tenantId
     * @param streamId
     * @param taskId
     * @return
     */
    public List<DataVerificationTask> queryDataVerificationTasks(String tenantId,
                                                                 String streamId,
                                                                 String taskId) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_streamId, streamId));
        if (StringUtils.isNotBlank(taskId)) {
            filters.add(Filters.eq(f_taskId, taskId));
        } else {
            filters.add(Filters.exists(f_taskId, false));
        }

        Bson andFilter = Filters.and(filters);
        FindIterable<DataVerificationTask> limit = this.getOrCreateDataVerificationTaskCollection().find(andFilter)
                .sort(Sorts.descending(f_createTime))
                .limit(100);
        return Lists.newArrayList(limit);
    }

    public String createDataVerificationTask(DataVerificationTask dataVerificationTask) {
        this.getOrCreateDataVerificationTaskCollection().withWriteConcern(WriteConcern.JOURNALED).insertOne(dataVerificationTask);
        return dataVerificationTask.getId().toString();
    }

    public String createOrUpdateDataVerificationTaskById(ObjectId id, String tenantId, String streamId, String taskId, String fileNPath,
                                                         String fileName, Integer allIdListSize, Integer notTempIdListSize, Integer tempIdListSize,
                                                         Integer notMappingIdListSize, Integer mappingIdListSize, Integer notCreatedIdListSize,
                                                         Integer createdIdListSize, DataVerificationTaskStatusEnum status, Boolean needStop, Date createTime, Date updateTime) {
        if (id == null) {
            return createDataVerificationTask(new ObjectId(), tenantId, streamId, taskId, fileNPath, fileName, allIdListSize, notTempIdListSize,
                    tempIdListSize, notMappingIdListSize,mappingIdListSize, notCreatedIdListSize,createdIdListSize, status, createTime, updateTime);
        } else {
            updateDataVerificationTaskById(id, tenantId, streamId, taskId, fileNPath, fileName, allIdListSize, allIdListSize, notTempIdListSize, tempIdListSize,
                    notMappingIdListSize,mappingIdListSize, notCreatedIdListSize,createdIdListSize, status, needStop, createTime, updateTime);
            return id.toString();
        }
    }

    public Integer updateDataVerificationTaskById(ObjectId id, String tenantId, String streamId, String taskId, String fileNPath,
                                                  String fileName, Integer pollingListSize, Integer allIdListSize, Integer notTempIdListSize, Integer tempIdListSize,
                                                  Integer notMappingIdListSize, Integer mappingIdListSize, Integer notCreatedIdListSize,
                                                  Integer createdIdListSize, DataVerificationTaskStatusEnum status, Boolean needStop,
                                                  Date createTime, Date updateTime) {
        List<Bson> filters = new ArrayList<>();
        if (StringUtils.isNotBlank(tenantId)) {
            filters.add(eq(f_tenantId, tenantId));
        }
        if (id != null) {
            filters.add(eq(_id, id));
        } else {
            filters.add(eq(f_streamId, streamId));
            if (StringUtils.isNotBlank(taskId)) {
                filters.add(eq(f_taskId, taskId));
            }
        }

        List<Bson> updates = new ArrayList<>();
        if (StringUtils.isNotBlank(streamId)) {
            updates.add(set(f_streamId, streamId));
        }
        if (StringUtils.isNotBlank(taskId)) {
            updates.add(set(f_taskId, taskId));
        }
        if (fileNPath!=null) {
            updates.add(set(f_fileNPath, fileNPath));
        }
        if (fileName!=null) {
            updates.add(set(f_fileName, fileName));
        }
        if (pollingListSize != null) {
            updates.add(set(f_pollingListSize, pollingListSize));
        }
        if (allIdListSize != null) {
            updates.add(set(f_allIdListSize, allIdListSize));
        }
        if (notTempIdListSize != null) {
            updates.add(set(f_notTempIdListSize, notTempIdListSize));
        }
        if (tempIdListSize != null) {
            updates.add(set(f_tempIdListSize, tempIdListSize));
        }
        if (notMappingIdListSize != null) {
            updates.add(set(f_notMappingIdListSize, notMappingIdListSize));
        }
        if (mappingIdListSize != null) {
            updates.add(set(f_mappingIdListSize, mappingIdListSize));
        }
        if (notCreatedIdListSize != null) {
            updates.add(set(f_notCreatedIdListSize, notCreatedIdListSize));
        }
        if (createdIdListSize != null) {
            updates.add(set(f_createdIdListSize, createdIdListSize));
        }
        if (status != null) {
            updates.add(set(f_status, status.name()));
        }
        if (needStop != null) {
            updates.add(set(f_needStop, needStop));
        }
        if (createTime != null) {
            updates.add(set(f_createTime, createTime));
        }
        if (updateTime != null) {
            updates.add(set(f_updateTime, updateTime));
        }
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = this.getOrCreateDataVerificationTaskCollection().updateOne(filter, update);
        return (int) updateResult.getMatchedCount();
    }

    public Integer deleteDataVerificationTaskById(ObjectId id, String tenantId, String streamId, String taskId) {
        List<Bson> filters = new ArrayList<>();
        if (StringUtils.isNotBlank(tenantId)) {
            filters.add(eq(f_tenantId, tenantId));
        }
        if (id != null) {
            filters.add(eq(_id, id));
        } else {
            filters.add(eq(f_streamId, streamId));
            if (StringUtils.isNotBlank(taskId)) {
                filters.add(eq(f_taskId, taskId));
            }
        }

        Bson filter = and(filters);
        DeleteResult deleteResult=this.getOrCreateDataVerificationTaskCollection().deleteMany(filter);
        return (int) deleteResult.getDeletedCount();
    }

    public String createDataVerificationTask(ObjectId id, String tenantId, String streamId, String taskId, String fileNPath,
                                             String fileName, Integer allIdListSize, Integer notTempIdListSize, Integer tempIdListSize,
                                             Integer notMappingIdListSize, Integer mappingIdListSize, Integer notCreatedIdListSize,
                                             Integer createdIdListSize, DataVerificationTaskStatusEnum status, Date createTime, Date updateTime) {
        DataVerificationTask task = buildDataVerificationTask(id, tenantId, streamId, taskId, fileNPath, fileName, allIdListSize, notTempIdListSize,
                tempIdListSize, notMappingIdListSize, mappingIdListSize, notCreatedIdListSize, createdIdListSize, status, false, createTime, updateTime);
        return createDataVerificationTask(task);
    }

    public DataVerificationTask buildDataVerificationTask(ObjectId id, String tenantId, String streamId, String taskId, String fileNPath,
                                                          String fileName, Integer allIdListSize, Integer notTempIdListSize, Integer tempIdListSize,
                                                          Integer notMappingIdListSize, Integer mappingIdListSize, Integer notCreatedIdListSize,
                                                          Integer createdIdListSize, DataVerificationTaskStatusEnum status, Boolean needStop, Date createTime, Date updateTime) {
        if (id == null) {
            id = new ObjectId();
        }
        return DataVerificationTask.builder()
                .id(id)
                .tenantId(tenantId)
                .streamId(streamId)
                .taskId(taskId)
                .fileNPath(fileNPath)
                .fileName(fileName)
                .allIdListSize(allIdListSize)
                .notTempIdListSize(notTempIdListSize)
                .tempIdListSize(tempIdListSize)
                .notMappingIdListSize(notMappingIdListSize)
                .mappingIdListSize(mappingIdListSize)
                .notCreatedIdListSize(notCreatedIdListSize)
                .createdIdListSize(createdIdListSize)
                .status(status)
                .needStop(needStop)
                .createTime(createTime)
                .updateTime(updateTime)
                .build();
    }

    public MongoCollection<DataVerificationTask> getOrCreateDataVerificationTaskCollection() {
        MongoCollection<DataVerificationTask> collectionList = store.getMongo().getDatabase(DATABASE).withCodecRegistry(SingleCodecHolder.codecRegistry).getCollection(collectionName, DataVerificationTask.class);
        if (!taskCollectionCache.add(collectionName)) {
            return collectionList;
        }
        List<IndexModel> toBeCreate = Lists.newArrayList();

        //过期自动清理时间,3天
        Bson idxExpire = Indexes.descending("createTime");
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(3L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending("streamId")
                , Indexes.ascending("tenantId")
                , Indexes.ascending("taskNum"));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("index_ei_stream_task").background(true)));

        List<String> created = collectionList.createIndexes(toBeCreate);
        log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        return collectionList;
    }


}
