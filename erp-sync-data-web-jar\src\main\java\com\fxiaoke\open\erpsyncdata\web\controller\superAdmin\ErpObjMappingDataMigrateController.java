package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjDataMappingMigrateService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.Executors;

/**
 * K3C对象映射数据代理服务
 * <AUTHOR>
 * @date 2023.03.14
 */
@Slf4j
@Api(tags = "K3C对象映射数据代理服务")
@RestController("ErpObjMappingDataMigrateController")
@RequestMapping("erp/syncdata/superadmin/k3c/obj/mapping/data")
public class ErpObjMappingDataMigrateController {
    @Autowired
    private ErpObjDataMappingMigrateService erpObjDataMappingMigrateService;

    @ApiOperation(value = "初始化指定企业所有数据中心的K3C映射数据")
    @RequestMapping(value = "/init",method = RequestMethod.POST)
    public Result<String> initData(@RequestParam String tenantId,@RequestParam String erpObjApiName) {
        String traceId = TraceUtil.get();
        Executors.newSingleThreadExecutor().submit(()->{
            TraceUtil.initTrace(traceId);
            erpObjDataMappingMigrateService.initData(tenantId,erpObjApiName);
        });
        return Result.newSuccess("正在初始化，请稍等");   // ignoreI18n      开发使用
    }

}
