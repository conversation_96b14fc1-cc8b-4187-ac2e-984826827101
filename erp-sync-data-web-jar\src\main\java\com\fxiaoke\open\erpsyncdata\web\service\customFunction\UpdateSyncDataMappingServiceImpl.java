package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryFieldDescArg;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("updateSyncDataMapping")
public class UpdateSyncDataMappingServiceImpl implements CustomFunctionCommonService {

    @Autowired
    private AdminSyncDataMappingService syncDataMappingService;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("UpdateEmployeeMappingServiceImpl.arg="+arg);
        String tenantId=arg.getTenantId();
        SyncDataMappingResult syncDataMappingArg = JsonUtil.fromJson(arg.getParams(), SyncDataMappingResult.class);

        Result<String> updateResult = syncDataMappingService.updateSyncDataMapping2(tenantId, syncDataMappingArg,null);
        return updateResult;
    }

}
