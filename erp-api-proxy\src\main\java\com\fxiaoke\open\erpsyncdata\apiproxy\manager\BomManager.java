package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.GetRelatedDataListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * BOM管理器类
 * <AUTHOR>
 * @date 2022-06-17
 */

@Component
@Slf4j
public class BomManager {
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 从BomInstanceObj对象中查询tree_id
     * @param tenantId
     * @param bomInstanceId
     * @return
     */
    public String getBomInstanceTreeId(String tenantId, String bomInstanceId) {
        bomInstanceId = BomUtils.getBomInstanceId(bomInstanceId, ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName());
        log.info("BillOfMaterialsSpecialBusinessImpl.getBomInstanceTreeId,bomInstanceId={}",bomInstanceId);
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDataId(bomInstanceId);
        arg.setObjectDescribeApiName(ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName());
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = metadataControllerService.detail(headerObj,
                ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName(),
                arg);
        log.info("BillOfMaterialsSpecialBusinessImpl.getBomInstanceTreeId,result={}",result);
        if(result.isSuccess()) {
            return result.getData().getData().getString("tree_id");
        }
        return null;
    }

    /**
     * 查询销售订单明细
     * @param tenantId
     * @param saleOrderId 销售订单ID
     * @return
     */
    public List<ObjectData> querySaleOrderRelatedList(String tenantId,
                                                        String saleOrderId) {

        GetRelatedDataListArg arg = new GetRelatedDataListArg();
        arg.setAssociateObjectDataId(saleOrderId);
        arg.setAssociateObjectDescribeApiName(ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
        arg.setAssociatedObjectDescribeApiName(ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName());
        arg.setAssociatedObjectFieldRelatedListName("order_id_list");
        arg.setIncludeAssociated(true);
        arg.setOrdered(true);

        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(2000);

        arg.setSearchQueryInfo(GsonUtil.toJson(searchQuery));

        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> result = metadataControllerService.getRelatedDataList(headerObj,
                ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName(),
                arg);

        return result.getData().getDataList();
    }

    /**
     * 查询BomInstanceObj对象数据
     * @param tenantId
     * @param bomInstanceTreeId
     * @return
     */
    public List<ObjectData> getOrderProductList(String tenantId, String bomInstanceTreeId) {
        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(500);

        searchQuery.addFilter("bom_instance_tree_id",Lists.newArrayList(bomInstanceTreeId), "EQ");


        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName());
        controllerListArg.setSearchQuery(searchQuery);

        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj,
                ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName(),
                controllerListArg);
        if(listResult.isSuccess()) {
            return listResult.getData().getDataList();
        }
        return new ArrayList<>();
    }

//    public String getBomId(String tenantId, String bomName) {
//        Filter filter=new Filter();
//        filter.setFieldName("name");
//        filter.setOperator("EQ");
//        filter.setFieldValues(Lists.newArrayList(bomName));
//
//        List<ObjectData> dataList = getBomInstanceList(tenantId,Lists.newArrayList(filter),ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName());
//        if(CollectionUtils.isNotEmpty(dataList)) {
//            return dataList.get(0).getId();
//        }
//        return null;
//    }

    /**
     * 根据BomObj对象的名称查询对应的id
     * @param tenantId
     * @param bomNameList
     * @return
     */
    public Map<String,String> getBomIdList(String tenantId, List<String> bomNameList) {
        Filter filter=new Filter();
        filter.setFieldName("name");
        filter.setOperator("IN");
        filter.setFieldValues(bomNameList);

        Map<String,String> bomMap = new HashMap<>();
        List<ObjectData> dataList = getBomInstanceList(tenantId,Lists.newArrayList(filter),ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName());
        if(CollectionUtils.isNotEmpty(dataList)) {
            for(ObjectData objectData : dataList) {
                bomMap.put(objectData.getName(),objectData.getId());
            }
        }
        return bomMap;
    }

    /**
     * 查询BomInstanceObj对象数据
     * @param tenantId
     * @param filterList
     * @param objectApiName
     * @return
     */
    public List<ObjectData> getBomInstanceList(String tenantId, List<Filter> filterList,String objectApiName) {
        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(500);
        searchQuery.setFilters(filterList);

        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(objectApiName);
        controllerListArg.setSearchQuery(searchQuery);

        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj,
                objectApiName,
                controllerListArg);
        if(listResult.isSuccess() && listResult.getData()!=null && CollectionUtils.isNotEmpty(listResult.getData().getDataList())) {
            List<ObjectData> dataList = listResult.getData().getDataList();
            String bom_id = dataList.get(0).getString("bom_id");
            String root_id = dataList.get(0).getString("root_id");
            if(StringUtils.equalsIgnoreCase(bom_id,root_id)) {
                return dataList;
            } else {
                //如果BOM实例的顺序是逆序，即先子后父，刚需要把数组的顺序改成正序，父先后子
                Collections.reverse(dataList);
                return dataList;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 根据bomTreeId查询BomInstanceObj对象数据
     * @param tenantId
     * @param bomTreeId
     * @return
     */
    public List<ObjectData> getBomInstanceList(String tenantId, String bomTreeId) {
        Filter filter=new Filter();
        filter.setFieldName("tree_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList(bomTreeId));

        return getBomInstanceList(tenantId,Lists.newArrayList(filter),ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName());
    }

    /**
     * 根据bomTreeId和bomId查询BomInstanceObj对象数据
     * @param tenantId
     * @param bomTreeId
     * @param bomId
     * @return
     */
    public String getBomInstanceId(String tenantId, String bomTreeId, String bomId) {
        Filter filter=new Filter();
        filter.setFieldName("tree_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList(bomTreeId));

        Filter filter2=new Filter();
        filter2.setFieldName("bom_id");
        filter2.setOperator("EQ");
        filter2.setFieldValues(Lists.newArrayList(bomId));

        List<ObjectData> dataList = getBomInstanceList(tenantId,Lists.newArrayList(filter,filter2),ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName());
        if(CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0).getId();
        }
        return null;
    }

    public SyncDataMappingsEntity getBomInstanceMapping(String tenantId,
                                                        String bom_instance_id,
                                                        String bom_tree_id,
                                                        String bom_id,
                                                        String materialNumber) {
        if(StringUtils.isNotEmpty(bom_instance_id) && StringUtils.isNotEmpty(bom_id)) {
            SyncDataMappingsEntity productMappingEntity = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId,
                    "BD_MATERIAL.BillHead",
                    ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),
                    materialNumber);
            if(productMappingEntity!=null) {
                String srcDataId = BomUtils.getSrcDataId(bom_instance_id,bom_tree_id,bom_id,productMappingEntity.getDestDataId());
                SyncDataMappingsEntity bomMappingEntity = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId,
                        ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName(),
                        "ENG_BOM.BillHead",
                        srcDataId);
                return bomMappingEntity;
            }
        }
        return null;
    }

    public String getMaterialUnitNumber(K3CloudApiClient apiClient, String materialNumber) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BD_MATERIAL);
        queryArg.setFieldKeys("FNumber,FBaseUnitId.FNumber");
        queryArg.appendEqualFilter("FNumber", materialNumber);
        Result<List<List<Object>>> result = apiClient.executeBillQuery(queryArg);

        String unitNumber = null;
        if (result.isSuccess()) {
            unitNumber = result.getData().get(0).get(1).toString();
        }
        return unitNumber;
    }
}
