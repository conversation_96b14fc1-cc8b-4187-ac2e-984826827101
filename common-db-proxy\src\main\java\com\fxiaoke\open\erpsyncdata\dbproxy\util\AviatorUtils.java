package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/1/10 17:06
 * @Version 1.0
 */
@Slf4j
public class AviatorUtils {

    /**
     * 普通表达式校验
     *
     * @param expression
     * @param env
     * @param cached
     * @return
     */
    public static boolean normalConditionValid(String expression, Map<String, Object> env, boolean cached) {
        try {
            Object result = AviatorEvaluator.execute(expression, env, cached);
            return (boolean) result;
        } catch (Exception e) {
            log.warn("AviatorUtils normalConditionValid error", e);
        }
        return false;
    }
}
