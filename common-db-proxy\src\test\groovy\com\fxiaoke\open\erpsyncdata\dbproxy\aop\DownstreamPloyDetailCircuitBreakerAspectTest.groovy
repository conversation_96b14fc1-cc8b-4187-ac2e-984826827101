package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.Executor

/**
 * <AUTHOR>
 * @date 2024/6/5
 */
class DownstreamPloyDetailCircuitBreakerAspectTest extends Specification {

    Map cache = [:]
    DownstreamPloyDetailCircuitBreakerAspect aop

    def setup() {
        def config = Mock(ConfigCenterConfig) {
            isDropWhenOverSaveLogLimitPloy() >> false
            getAllSaveSyncLogLimit((String) _) >> ["1234": 1000, "4567": 1000, "ALL": 1000]
            isManagedEnterprise(_, _) >> { args ->
                if ("4567".equals(args[0]) || "9999".equals(args[0]))
                    return true
                return args[1] as boolean
            }
        }
        def redis = Mock(RedisCacheManager) {
            getCache(*_) >> { args ->
                println("getCache: " + args)
                return cache.get(args[0])
            }
            setCache(*_) >> { args ->
                def key = args[0] as String
                println("setCache: " + args)
                def res = cache.put(key, args[1])
                println("set key for " + res)
                return res == key
            }
        }
        def relationErpShardDao = Mock(RelationErpShardDao) {
//            queryFirstNormalByDownstreamId(*_) >> ["011", "012", "013"]
            queryFirstNormalByDownstreamId(*_) >> { args ->
                def id = args[0] as String
                if ("9999".equals(id)) {
                    return null
                }
                new RelationErpShardDto(templateId: id)
            }
        }
        aop = new DownstreamPloyDetailCircuitBreakerAspect(configCenterConfig: config,
                relationErpShardDao: relationErpShardDao,
                redisCacheManager: redis)
        aop.init()

    }

    @Unroll
    def "停用#status-#name"() {
        given:
        def a = Mock(AdminSyncPloyDetailDao) {
            setTenantId(*_) >> {
                def dao = Mock(AdminSyncPloyDetailDao) {
                    updateValid(*_) >> 1
                    updateStatusById(*_) >> {
                        if ("成功".equals(status)) {
                            return 1
                        }
                        return -1
                    }
                }
                return dao
            }
        }
        def aa = Mock(AdminSyncPloyDetailSnapshotDao) {
            setTenantId(*_) >> Mock(AdminSyncPloyDetailSnapshotDao) {
                updateStatusByPloyDetailId(*_) >> 1
            }
        }
        def r = Mock(RedisDataSource) {
            saddAndExpire(*_) >> 100L
        }
        def rr = Mock(RedisCacheManager) {
            setCache(*_) >> true
        }
        def s = Mock(SyncPloyDetailSnapshotManager)
        def i = new I18NStringManager()
        SyncPloyManager syncPloyManager = new SyncPloyManager(adminSyncPloyDetailDao: a, adminSyncPloyDetailSnapshotDao: aa,
                redisDataSource: r, redisCacheManager: rr, syncPloyDetailSnapshotManager: s, i18NStringManager: i)
        def proxy = getAspect(syncPloyManager)

        when:
        def ex = Mock(Executor)
        new UserOperatorLogManager().setExecutor(ex)
        proxy.disablePloyDetailByStreamId(tenantId, "null", "null", "null", "", needMark)

        then:
        noExceptionThrown()
        where:
        status  | name        | tenantId    | needMark // 只有成功才需要标记
        "成功"   | "非管控企业"    | "1234"    | true
        "成功"   | "管控企业"      | "4567"   | false
        "失败"   | "非管控企业"    | "1234"    | false
        "失败"   | "管控企业"      | "4567"   | false
        "触发熔断"| "无下游"       | "9999"    | false
    }

//    def "停用失败-#name"() {
//        when:
//        proxy.disablePloyDetailByStreamId(tenantId, "null", "null", "null", "null", true)
//        then:
//        noExceptionThrown()
//        where:
//        name        | tenantId
//        "非管控企业"    | "1234"
//        "管控企业"      | "4567"
//    }

    private <T> T getAspect(T o) {
        // 使用AspectJProxyFactory来创建被增强的代理对象
        AspectJProxyFactory factory = new AspectJProxyFactory(o);

        factory.setProxyTargetClass(true) // 使用CGLib代理
        factory.addAspect(aop); // 添加切面到代理工厂中
        return factory.getProxy(); // 获取代理对象
    }
}
