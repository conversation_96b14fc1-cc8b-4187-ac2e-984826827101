<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.dbproxy,com.fxiaoke.open.erpsyncdata.i18n"/>

    <import resource="all-rest-api.xml"/>
    <import resource="classpath*:mongo/mongo-store.xml"/>
    <!--文件服务-->
    <import resource="common-fileserver.xml"/>
    <!--- 纷享内部调用组织架构服务-->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>
    <import resource="db-dubbo-consumer.xml"/>
    <!--postgreSql 数据源-->
    <import resource="common-pg-ds.xml"/>


    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="myBatisDataSource"/>
        <!-- 传入PageHelper的插件 -->
        <property name="plugins">
            <array>
                <!-- 传入插件的对象 好像没有使用-->
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <props>
                            <prop key="helperDialect">postgresql</prop>
                            <prop key="reasonable">true</prop>
                        </props>
                    </property>
                </bean>
            </array>
        </property>
        <property name="typeAliasesPackage" value="com.fxiaoke.open.erpsyncdata.dbproxy.entity.*"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
        <property name="mapperLocations">
            <array>
                <value>classpath*:/mapper/*.xml</value>
                <value>classpath*:/mybatis-admin/mapper/*.xml</value>
            </array>
        </property>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean id="dbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.fxiaoke.open.erpsyncdata.dbproxy.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="myBatisDataSource"/>
    </bean>
    
    <bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="transactionManager"/>
    </bean>

    <!-- Redis配置 -->
    <bean id="jedisSupport" class="com.github.jedis.support.JedisFactoryBean"
          p:configName="erp-sync-data-all"/>
    <bean id="redisDataSource" class="com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource">
        <property name="jedisCmd" ref="jedisSupport"/>
    </bean>


    <!-- define the SqlSessionFactory -->
    <bean id="hikariSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="hikariDataSource"/>
        <property name="mapperLocations">
            <array>
                <value>classpath*:/table.mapper/*.xml</value>
            </array>
        </property>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean id="hikariDbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.fxiaoke.open.erpsyncdata.dbproxy.table.dao"/>
        <property name="sqlSessionFactoryBeanName" value="hikariSqlSessionFactory"/>
    </bean>

    <import resource="common-ch-ds.xml"/>
    <!-- define the SqlSessionFactory -->
    <bean id="chSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="chDataSource"/>
        <property name="configLocation" value="classpath:spring/ch-mybatis-config.xml"/>
        <property name="mapperLocations">
            <array>
                <value>classpath*:/ch-mapper/*.xml</value>
            </array>
        </property>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean id="chDbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao"/>
        <property name="sqlSessionFactoryBeanName" value="chSqlSessionFactory"/>
    </bean>

    <!--  es  -->
    <bean class="com.fxiaoke.elasticsearch.rest.AutoConfESClient" p:configName="fs-erpdss-es"/>
    <bean class="com.fxiaoke.elasticsearch.service.ElasticSearchService"/>

    <!-- PostgreSQL事务配置(hikari数据源) -->
    <bean id="hikariTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="hikariDataSource"/>
    </bean>
    <bean id="hikariTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="hikariTransactionManager"/>
    </bean>

    <!-- ClickHouse事务配置 -->
    <bean id="chTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="chDataSource"/>
    </bean>
    <bean id="chTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="chTransactionManager"/>
    </bean>

    <!-- 开启注解事务 -->
    <tx:annotation-driven/>

</beans>