package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory;

import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.MongoStore;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:32 2023/07/24
 * @Desc:
 */
@Slf4j
@Component
public class CompareSyncDataMongoDao {
    @Autowired
    private MongoStore mongoStore;

    public CompareResultDoc getByDataId(String tenantId, String objApiName, String dataId, String ployDetailId) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenantId", tenantId));
        filters.add(Filters.eq("objApiName", objApiName));
        filters.add(Filters.eq("dataId", dataId));
        if(StringUtils.isNotBlank(ployDetailId)){
            filters.add(Filters.eq("ployDetailId", ployDetailId));
        }
        FindIterable<CompareResultDoc> limit = this.getMongoCollection(tenantId).find(Filters.and(filters)).limit(1);
        if (limit.iterator().hasNext()) {
            return limit.iterator().next();
        } else {
            return null;
        }
    }

    public List<CompareResultDoc> listByTenantIdAndTypeAndStatusAndUpdateTime(String tenantId, Integer offset, Integer limit) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenantId", tenantId));
        filters.add(Filters.eq("hasCompare", false));
        filters.add(Filters.lte("updateTime", System.currentTimeMillis()));
        filters.add(Filters.gt("updateTime", System.currentTimeMillis()-30*60*1000));
        FindIterable<CompareResultDoc> list = this.getMongoCollection(tenantId).find(Filters.and(filters)).skip(offset).limit(limit);
        return Lists.newArrayList(list);
    }

    public List<CompareResultDoc> listByTenantIdAndTypeAndStatusAndUpdateTime(String tenantId, CompareResultDoc compareResultDoc) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenantId", tenantId));
        filters.add(Filters.lte("updateTime", System.currentTimeMillis()));
        filters.add(Filters.gt("updateTime", System.currentTimeMillis()-30*60*1000));
       if(ObjectUtils.isNotEmpty(compareResultDoc.getSourceObjApiName())){
           filters.add(Filters.gt("sourceObjApiName", compareResultDoc.getSourceObjApiName()));
       }
        if(ObjectUtils.isNotEmpty(compareResultDoc.getDestObjectApiName())){
            filters.add(Filters.gt("destObjectApiName", compareResultDoc.getDestObjectApiName()));
        }
        if(ObjectUtils.isNotEmpty(compareResultDoc.getSourceDataId())){
            filters.add(Filters.gt("sourceDataId", compareResultDoc.getSourceDataId()));
        }
        if(ObjectUtils.isNotEmpty(compareResultDoc.getSyncType())){
            filters.add(Filters.gt("syncType", compareResultDoc.getSyncType()));
        }

        FindIterable<CompareResultDoc> list = this.getMongoCollection(tenantId).find(Filters.and(filters)).limit(1);
        return Lists.newArrayList(list);
    }

    /**
     * 覆盖旧的
     *
     * @param tenantId
     * @param updated
     */
    public void updateErpReSyncData(String tenantId, CompareResultDoc updated) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("_id", updated.getId()));
        this.getMongoCollection(tenantId).findOneAndReplace(Filters.and(filters), updated);
    }

//    public Long updateStatusByIdAndOldStatus(String tenantId, ObjectId id, Integer status, Integer oldStatus, Long updateTime) {
//        List<Bson> filters = Lists.newArrayList();
//        filters.add(Filters.eq("_id", id));
//        filters.add(Filters.eq("status", oldStatus));
//
//        List<Bson> updateList = Lists.newArrayList();
//        updateList.add(Updates.set("updateTime", updateTime));
//        updateList.add(Updates.set("status", status));
//        Bson updates = Updates.combine(updateList);
//
//        UpdateResult updateResult = this.getMongoCollection(tenantId).updateOne(Filters.and(filters), updates);
//        return updateResult.getMatchedCount();
//    }

//    public Long deleteByTypeAndApiName(String tenantId, Integer type, String objApiName) {
//        List<Bson> filters = Lists.newArrayList();
//        filters.add(Filters.eq("tenantId", tenantId));
//        filters.add(Filters.eq("type", type));
//        filters.add(Filters.eq("objApiName", objApiName));
//        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteMany(Filters.and(filters));
//        return deleteResult.getDeletedCount();
//    }

    public MongoCollection<CompareResultDoc> getMongoCollection(String tenantId) {
        return mongoStore.getOrCreateCompare(tenantId);
    }

//    public Integer insert(String tenantId, CompareResultDoc entity) {
//        if (entity == null) {
//            return 0;
//        }
//        this.getMongoCollection(tenantId).insertOne(entity);
//        return 1;
//    }


    public void batchInsert(String tenantId, List<CompareResultDoc> syncLogs) {
        UpdateOneModel<CompareResultDoc> updateOneModel;
        List<WriteModel<CompareResultDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (CompareResultDoc syncLog : syncLogs) {
            updateOneModel = new UpdateOneModel<>(this.updateBySyncLogId(syncLog), this.upsert(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<CompareResultDoc> collection = this.getMongoCollection(tenantId);
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }
    public Bson updateBySyncLogId(CompareResultDoc message) {
        Document result = new Document();
        result.put("tenantId", message.getTenantId());
        result.put("sourceObjApiName", message.getSourceObjApiName());
        result.put("destObjectApiName", message.getDestObjectApiName());
        result.put("sourceDataId",message.getSourceDataId());
        result.put("syncLogId",message.getSyncLogId());
        result.put("syncType",message.getSyncType());
        return result;
    }

    public Bson upsert(CompareResultDoc message){
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();
        //更新

        updateDoc.append("updateTime", System.currentTimeMillis());
        //不为空,才更新
        if(message.getCompareField()!=null){
            updateDoc.append("compareField", message.getCompareField());
        }
        if (ObjectUtils.isNotEmpty(message.getInputParamsMap())) {
            updateDoc.append("inputParamsMap", message.getInputParamsMap());
        }
        if ( ObjectUtils.isNotEmpty(message.getOutputParamsMap())) {
            updateDoc.append("outputParamsMap", message.getOutputParamsMap());
        }
        if (ObjectUtils.isNotEmpty(message.getHasCompare())) {
            updateDoc.append("hasCompare", message.getHasCompare());
        }
        if (ObjectUtils.isNotEmpty(message.getTheSame())) {
            updateDoc.append("theSame", message.getTheSame());
        }
        if (ObjectUtils.isNotEmpty(message.getTheSame())) {
            updateDoc.append("message", message.getTheSame());
        }

        //插入
        setOnInsertDoc
                .append("tenantId", message.getTenantId())
                .append("sourceObjApiName", message.getSourceObjApiName())
                .append("destObjectApiName", message.getDestObjectApiName())
                .append("sourceDataId", message.getSourceDataId())
                .append("syncLogId", message.getSyncLogId())
                .append("syncType", message.getSyncType())
                .append("_id",ObjectId.get()).append("createTime",System.currentTimeMillis()).append("expireTime",new Date());


        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);

        return doc;
    }


//    public Long deleteById(String tenantId, ObjectId id) {
//        List<Bson> filters = Lists.newArrayList();
//        filters.add(Filters.eq("_id", id));
//        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteOne(Filters.and(filters));
//        return deleteResult.getDeletedCount();
//    }

//    public Long updateTime(String tenantId, List<ObjectId> objectIds, Long updateTime) {
//        List<Bson> filters = Lists.newArrayList();
//        filters.add(Filters.in("_id", objectIds));
//        List<Bson> updateList = Lists.newArrayList();
//        updateList.add(Updates.set("updateTime", updateTime));
//        Bson updates = Updates.combine(updateList);
//        UpdateResult updateResult = this.getMongoCollection(tenantId).updateMany(Filters.and(filters), updates);
//        return updateResult.getMatchedCount();
//    }
}
