package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncLogBaseInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

/**
 * syncLogId工具类
 * <p>
 * 请不要在这个类注入任何spring的bean或者将这个类转换为bean！
 * 请不要在这个类注入任何spring的bean或者将这个类转换为bean！
 * 请不要在这个类注入任何spring的bean或者将这个类转换为bean！
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/6/7
 */
@Slf4j
public class LogIdUtil {
    private static final TransmittableThreadLocal<SyncLogBaseInfo> BASE_LOG = new TransmittableThreadLocal<>();

    public static final String VIRTUAL_API_LOG_NAME="VIRTUAL_API_LOG_NAME";
    public static SyncLogBaseInfo getBaseLogNoCreate() {
        SyncLogBaseInfo baseLog = BASE_LOG.get();
        return baseLog;
    }

    public static void setBaseLog(SyncLogBaseInfo syncLogBaseInfo) {
        BASE_LOG.set(syncLogBaseInfo);
    }

    private static SyncLogBaseInfo getBaseLog() {
        SyncLogBaseInfo baseLog = BASE_LOG.get();
        if (baseLog == null|| ObjectUtils.isEmpty(baseLog.getLogId())) {
            baseLog = new SyncLogBaseInfo();
            randomLogId(baseLog);
            BASE_LOG.set(baseLog);
        }else {
            //避免随机logId一直都是一个，判断是随机logId重新生成
            if (baseLog.getLogId().contains(VIRTUAL_API_LOG_NAME)) {
                randomLogId(baseLog);
            }
        }
        return baseLog;
    }

    private static void randomLogId(SyncLogBaseInfo baseLog) {
        String idStr = UUID.randomUUID().toString().replaceAll("-", "");
        String logId = buildRootLogId("0", VIRTUAL_API_LOG_NAME, idStr);
        baseLog.setLogId(logId);
    }

    public static void reset(String logId) {
        getBaseLog().setLogId(logId);
    }

     /**
     * 有时候原traceId会被logId顶替掉。所以这里打印下logId被traceId替换的
     * @param objApiName
     * @param logId
     */
    public static void resetBaseLog(String objApiName, String logId,
                                    TenantConfigurationManager tenantConfigurationManager,
                                    EIEAConverter eieaConverter) {
        SyncLogBaseInfo baseLog = new SyncLogBaseInfo();
        baseLog.setLogId(logId);
        baseLog.setRealObjApiName(objApiName);
        BASE_LOG.set(baseLog);

        try {
            int endIndex = logId.indexOf('.', 4);
            String tenantea = logId.substring(4, endIndex);
            String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(tenantea));
            TraceUtil.initColorLogEi(tenantId, tenantConfigurationManager);
        }catch (Exception e) {
            log.warn("get exception, ", e);
        }
    }

    /**
     * 设置当前真实ApiName
     *
     * @param realObjApiName
     */
    public static void setRealObjApiName(String realObjApiName) {
        getBaseLog().setRealObjApiName(realObjApiName);
    }
    /**
     * sourceData里面的apiName
     *
     * @param sourceObjApiName
     */
    public static void setSourceObjApiName(String sourceObjApiName) {
        getBaseLog().setSourceObjApiName(sourceObjApiName);
    }

    /**
     * 设置当前的数据是否需要重试
     * retry设置false的时候，只有bulkUpdateBySyncDataId出现pg超时异常或者指定的异常的时候，因为已经执行了update destSystem.不需要重试
     * 其他地方报上面的报错，统一设置true
     * 设置的时候判断是不是已经设置了
     *
     * @param retry
     */
    public static void setNeedRetry(Boolean retry) {
        Boolean originResult=getBaseLog().getNeedRetry();
        if(ObjectUtils.isEmpty(originResult)){
            getBaseLog().setNeedRetry(retry);
        }

    }
    public static void removeRetry() {
        getBaseLog().setNeedRetry(null);

    }
    /**
     * 设置数据realObjId
     *
     * @param dataId
     */
    public static void setDataId(String dataId) {
        getBaseLog().setDataId(dataId);
    }


    /**
     * 设置当前集成流Id
     *
     * @param streamId
     */
    public static void setStreamId(String streamId) {
        getBaseLog().setStreamId(streamId);
    }
    /**
     * 设置当前erp数据中心Id
     *
     * @param dataCenterId
     */
    public static void setDataCenterId(String dataCenterId,boolean erp2crm) {
        getBaseLog().setDataCenterId(dataCenterId);
        getBaseLog().setErp2crm(erp2crm);
    }
    /**
     * remove当前集成流Id
     *
     *
     */
    public static void removeDataCenterId() {
        getBaseLog().setDataCenterId(null);
        getBaseLog().setErp2crm(null);
    }

    public static String get() {
        return getBaseLog().getLogId();
    }

    public static String getRealObjApiName() {
        return getBaseLog().getRealObjApiName();
    }

    /**
     * 在线程结束前调用防止内存泄露
     */
    public static void clear() {
        BASE_LOG.remove();
    }

    public static void clearTrace(){
        clear();
        TraceUtil.removeTrace();
    }

    /**
     * 增加.0是为了作为traceId时，kibana上面能够解析tenantId
     * 以.划分
     * 第一部分为 J-E
     * 第二部分为 tenantId
     * 第三部分为 0,假的userId
     * 第四部分为 objApiName
     * 第五部分为 唯一id
     * 往后如果还有则为子id序号
     *
     * @param ea
     * @param objApiName
     * @param id
     * @return
     */
    public static String buildRootLogId(String ea, String objApiName, String id) {
        //依赖-和.拆分，不允许apiName存在这两个字符
        objApiName = StrUtil.removeAll(objApiName, '-', '.');
        id = StrUtil.removeAll(id, '-', '.');
        String logId = String.format("J-E.%s.0.%s.%s", ea, objApiName, id);
        return logId;
    }

    /**
     * 根据logID获取所有父LogId
     * 包含自身，父Id在前面
     *
     * @param logId
     * @return
     */
    public static List<String> listLogIdLine(String tenantId,String logId) {
        List<String> split = Splitter.on(".").splitToList(logId);
        if (split.size() < 5) {
            throw new ErpSyncDataException(I18NStringEnum.s160,tenantId);
        }
        LinkedList<String> result = new LinkedList<>();
        Joiner joiner = Joiner.on(".");
        String parentId = joiner.join(split.subList(0, 4));
        for (String childId : split.subList(4, split.size())) {
            parentId = joiner.join(parentId, childId);
            result.add(parentId);
        }
        return result;
    }

    /**
     * 根据类型返回对应的logid
     *
     * @param logId
     * @return
     */
    public static String getLogIdByType(String tenantId,String logId, SyncLogTypeEnum syncLogTypeEnum) {
        List<String> split = Splitter.on(".").splitToList(logId);
        if (split.size() < 5) {
            throw new ErpSyncDataException(I18NStringEnum.s160,tenantId);
        }
        LinkedList<String> result = new LinkedList<>();
        Integer logSize = SyncLogTypeEnum.getSize(syncLogTypeEnum);
        Joiner joiner = Joiner.on(".");
        String finalLogId = "";
        if (logSize > split.size()) {
            finalLogId = joiner.join(split.subList(0, split.size()));
        } else {
            finalLogId = joiner.join(split.subList(0, logSize));
        }
        return finalLogId;
    }


    public static boolean inValidLogId(String logId) {
        List<String> split = Splitter.on(".").splitToList(logId);
        if (split.size() < 5) {
            return true;
        }
        return false;
    }


    /**
     * 构建子Id
     *
     * @param index
     * @return
     */
    public static String buildChildLogId(String parent, int index) {
        parent = parent + "." + index;
        return parent;
    }

    public static String buildChildLogIdRestId(String parent, int index) {
        String newLogId = buildChildLogId(parent, index);
        reset(newLogId);
        return newLogId;
    }

}
