package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllTenantInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.model.fstool.SystemSearch;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 所有企业信息
 *
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("erp/syncdata/superadmin")
public class SuperAdminAllTenantController extends SuperAdminBaseController {
    @Autowired
    private AllTenantInfoManager allTenantInfoManager;
    @Autowired
    private CacheManager cacheManager;

    @GetMapping("getInfoHolder")
    public Result<Dict> getInfoHolder() {
        Dict dict = Dict.of();
        SystemSearch.InfoHolder infoHolder = allTenantInfoManager.getInfoHolder(true);
        dict.put("infoHolder", infoHolder);
        dict.put("infoHolderSize", RamUsageEstimateUtil.sizeOfObjectIgnoreException(infoHolder));
        return Result.newSuccess(dict);
    }

    @GetMapping("invalidCache")
    public Result<Void> invalidCache(@RequestParam String name,@RequestParam(required = false) String key) {
        key = StrUtil.blankToDefault(key,"_$JETCACHE_NULL_KEY$_");
        Cache<Object, Object> cache = cacheManager.getCache(name);
        if (cache!=null){
            cache.remove(key);
            return Result.newSuccess();
        }
        return Result.newError("not found cache");
    }
}
