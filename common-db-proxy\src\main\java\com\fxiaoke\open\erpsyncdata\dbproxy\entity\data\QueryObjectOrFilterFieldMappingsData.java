package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class QueryObjectOrFilterFieldMappingsData extends ArrayList<QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData> implements Serializable{
    @Data
    public static class QueryObjectAndFilterFieldMappingData  extends ArrayList<FilterData> implements Serializable {
    }
}
