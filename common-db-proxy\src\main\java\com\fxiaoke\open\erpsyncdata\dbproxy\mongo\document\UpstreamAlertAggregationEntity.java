package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25 16:19:21
 * <p>
 * 上游聚合下游告警通知
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(value = "upstream_alert_aggregation", noClassnameStored = true)
@Indexes({@Index(fields = {@Field("tenant_id"), @Field("dc_id")}, options = @IndexOptions(background = true, unique = true))}
)
public class UpstreamAlertAggregationEntity {
    @Id
    private ObjectId id;
    /**
     * 上游企业Id
     */
    @Property("tenant_id")
    private String tenantId;
    /**
     * 连接器
     */
    @Property("dc_id")
    private String dcId;

    /**
     * 紧急
     */
    @Property("urgent_tenant_ids")
    private List<String> urgentTenantIds;
    @Property("urgent_ploy_ids")
    private List<String> urgentPloyIds;
    @Property("urgent_count")
    private Integer urgentCount;

    /**
     * 重要
     */
    @Property("important_tenant_ids")
    private List<String> importantTenantIds;
    @Property("important_ploy_ids")
    private List<String> importantPloyIds;
    @Property("important_count")
    private Integer importantCount;

    /**
     * 一般
     */
    @Property("general_tenant_ids")
    private List<String> generalTenantIds;
    @Property("general_ploy_ids")
    private List<String> generalPloyIds;
    @Property("general_count")
    private Integer generalCount;

    @Property("create_time")
    private Long createTime;
    @Property("update_time")
    private Long updateTime;
}
