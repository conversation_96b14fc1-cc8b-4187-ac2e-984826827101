package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingTempFailEntity;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.mongodb.client.model.Filters;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/22 19:44:29
 */
@Component
public class PollingTempFailDao extends BaseDao<PollingTempFailEntity> {

    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }


    public PollingTempFailEntity getById(String id) {
        return findOne("_id", id);
    }

    public List<String> getNextFailIds(String tenantId, String maxId, Integer limit) {
        final Query<PollingTempFailEntity> query = createQuery(ImmutableMap.of("tenant_id", tenantId, "status", PollingTempFailEntity.status_init));
        query.field("next_time").lessThan(System.currentTimeMillis());
        query.order("next_time");

        query.limit(limit);
        if (Objects.nonNull(maxId)) {
            query.field("_id").greaterThan(maxId);
        }

        query.retrievedFields(true, "_id");
        return query.asList().stream().map(PollingTempFailEntity::getId).collect(Collectors.toList());
    }

    public PollingTempFailEntity getAndRetry(String id) {
        final Query<PollingTempFailEntity> query = createQuery(ImmutableMap.of("_id", id, "status", PollingTempFailEntity.status_init));
        query.field("next_time").lessThan(System.currentTimeMillis());
        final UpdateOperations<PollingTempFailEntity> update = createUpdate(ImmutableMap.of("status", PollingTempFailEntity.status_executing, "update_time", System.currentTimeMillis()));
        update.inc("time");
        return datastore.findAndModify(query, update);
    }

    public PollingTempFailEntity doFirst(String tenantId) {
        final Query<PollingTempFailEntity> query = createQuery(ImmutableMap.of("tenant_id", tenantId, "status", PollingTempFailEntity.status_init));
        query.field("next_time").lessThan(System.currentTimeMillis());
        query.order("next_time");
        query.limit(1);
        final UpdateOperations<PollingTempFailEntity> update = createUpdate(ImmutableMap.of("status", PollingTempFailEntity.status_executing, "update_time", System.currentTimeMillis()));
        update.inc("tryTime");
        return datastore.findAndModify(query, update);
    }

    public PollingTempFailEntity successful(String id) {
        return updateStatus(id, PollingTempFailEntity.status_success, null, null, null);
    }

    public List<PollingTempFailEntity> listByData(String tenantId, Integer limit,Integer offset, List<Integer> statusList,Long startTime,Long endTime) {
        final Query<PollingTempFailEntity> query = createQuery();
        if (StringUtils.isNotEmpty(tenantId)) {
            query.field("tenant_id").equal(tenantId);
        }

        if (CollectionUtils.isNotEmpty(statusList)) {
            query.field("status").in(statusList);
        }
        if(ObjectUtils.isNotEmpty(startTime)){
            query.field("updateTime").greaterThanOrEq(startTime);

        }
        if(ObjectUtils.isNotEmpty(endTime)){
            query.field("updateTime").lessThanOrEq(endTime);

        }
        query.limit(limit);
        query.offset(offset);
        return query.asList();
    }

    public Integer calculateCount(String tenantId, List<Integer> statusList,Long startTime,Long endTime) {
        final Query<PollingTempFailEntity> query = createQuery();
        if (StringUtils.isNotEmpty(tenantId)) {
            query.field("tenant_id").equal(tenantId);
        }

        if (CollectionUtils.isNotEmpty(statusList)) {
            query.field("status").in(statusList);
        }
        if(ObjectUtils.isNotEmpty(startTime)){
            query.field("updateTime").greaterThanOrEq(startTime);

        }
        if(ObjectUtils.isNotEmpty(endTime)){
            query.field("updateTime").lessThanOrEq(endTime);

        }
        return Math.toIntExact(query.countAll());

    }

    public PollingTempFailEntity retry(String id, String errMsg, Long retryTime) {
        return updateStatus(id, PollingTempFailEntity.status_init, null, errMsg, retryTime);
    }

    public PollingTempFailEntity retry(String id, String arg, String errMsg, Long retryTime) {
        return updateStatus(id, PollingTempFailEntity.status_init, arg, errMsg, retryTime);
    }

    public PollingTempFailEntity fail(String id, String errMsg) {
        return updateStatus(id, PollingTempFailEntity.status_fail, null, errMsg, null);
    }

    public PollingTempFailEntity fail(String id, String arg,String errMsg) {
        return updateStatus(id, PollingTempFailEntity.status_fail, null, errMsg, null);
    }

    public PollingTempFailEntity updateStatus(String id, int status, String arg, String errMsg, Long retryTime) {
        final Query<PollingTempFailEntity> query = createQuery("_id", id);

        final UpdateOperations<PollingTempFailEntity> update = createUpdate(ImmutableMap.of("status", status, "update_time", System.currentTimeMillis()));
        if (Objects.nonNull(retryTime)) {
            update.set("nextRetryTime", retryTime);
        }
        if (StringUtils.isNotBlank(arg)) {
            update.set("arg", arg);
        }
        if (StringUtils.isNotBlank(errMsg)) {
            update.set("errMsg", errMsg);
        }
        return datastore.findAndModify(query, update);
    }
}
