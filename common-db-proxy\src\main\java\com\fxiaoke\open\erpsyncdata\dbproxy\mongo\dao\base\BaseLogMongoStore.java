package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base;

import com.github.mongo.support.DatastoreExt;
import com.mongodb.client.MongoDatabase;
import org.bson.codecs.configuration.CodecRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * 日志Mongo baseDao，支持分片
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/29
 */
public abstract class BaseLogMongoStore<T> extends BaseMongoStore<T> {
    protected DatastoreExt store;

    protected BaseLogMongoStore(CodecRegistry codecRegistry) {
        super(codecRegistry);
    }

    protected BaseLogMongoStore() {
        super();
    }

    @Qualifier("shardingSyncLogMongo")
    @Autowired
    public void setStore(DatastoreExt store) {
        this.store = store;
    }


    public MongoDatabase getDatabase(final String tenantId) {
        //默认走 企业路由 分片;注意，传0不走分片
        return store.setTenantId(tenantId).getMongo()
                .getDatabase(store.setTenantId(tenantId).getDB().getName());
    }



}
