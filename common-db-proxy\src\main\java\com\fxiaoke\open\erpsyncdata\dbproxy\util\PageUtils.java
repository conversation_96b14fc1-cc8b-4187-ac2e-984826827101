package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PageUtils {
    /**
     * 数据分页
     * @param list
     * @param pageSize
     * @return
     */
    public static <T> List<List<T>> getPageList(List<T> list, int pageSize) {
        List<List<T>> pageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) return pageList;

        if (list.size() < pageSize || pageSize <= 0) {
            pageList.add(list);
        } else {
            for (int i = 0; i < list.size(); i += pageSize) {
                List<T> page = new ArrayList<>();
                int toIndex = i + pageSize >= list.size() ? list.size() : i + pageSize;
                page.addAll(list.subList(i, toIndex));
                pageList.add(page);
            }
        }
        log.info("PageUtils.getPageList,pageList={}", pageList);
        return pageList;
    }

    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        for(int i=0;i<100;i++) {
            list.add("item"+i);
        }

        List<List<String>> pageList = getPageList(list,30);
        System.out.println(pageList);
    }
}
