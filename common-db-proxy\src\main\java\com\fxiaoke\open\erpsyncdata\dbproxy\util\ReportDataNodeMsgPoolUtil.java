package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DataNodeMsg;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 16:03 2023/1/10
 * @Desc:
 */
@Slf4j
@Data
public class ReportDataNodeMsgPoolUtil {
    private static final ThreadPoolExecutor default_executor = new NamedThreadPoolExecutor("erpReportNodePoolUtil", 5, 5);


    public static void sendDataNodeMsgAndWait(List<DataNodeMsg> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            return;
        }
        List<Future> futureList = Lists.newArrayList();
        for (DataNodeMsg msg : msgList) {
            Future<?> submit = default_executor.submit(() -> MonitorUtil.send(msg, msg.getTenantId() + msg.getObjApiName(), MonitorType.DATA_NODE_MSG));
            futureList.add(submit);
        }
        for (Future<?> future : futureList) {
            try {
                future.get(1, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.info("sendDataNodeMsgAndWait Exception e={}", e);
            }
        }

    }

    public static List<Future> sendDataNodeMsgAndNotWait(List<DataNodeMsg> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            return Lists.newArrayList();
        }
        List<Future> futureList = Lists.newArrayList();
        for (DataNodeMsg msg : msgList) {
            Future<?> submit = default_executor.submit(() -> MonitorUtil.send(msg, msg.getTenantId() + msg.getObjApiName(), MonitorType.DATA_NODE_MSG));
            futureList.add(submit);
        }
        return futureList;

    }
    public static void sendDataNodeMsg(DataNodeMsg msg) {
        if (msg==null) {
            return;
        }
        MonitorUtil.send(msg, msg.getTenantId() + msg.getObjApiName(), MonitorType.DATA_NODE_MSG);

    }
}
