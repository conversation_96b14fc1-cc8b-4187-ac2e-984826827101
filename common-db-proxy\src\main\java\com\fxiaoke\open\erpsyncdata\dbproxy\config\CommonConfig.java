package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import com.jayway.jsonpath.spi.cache.CacheProvider;
import com.jayway.jsonpath.spi.cache.LRUCache;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommonConfig {


    static {
        try {
            //加大JsonPath缓存数量
            CacheProvider.setCache(new LRUCache(4000));
        } catch (Exception ignored) {
        }
    }
}
