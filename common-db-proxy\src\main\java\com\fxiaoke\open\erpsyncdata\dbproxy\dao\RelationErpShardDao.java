package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/21 12:01:52
 */
@Repository
public interface RelationErpShardDao extends BaseTenantDao<RelationErpShardEntity, RelationErpShardDao> {

    /**
     * 根据下游代管企业,获取模板企业信息,只查第一条
     */
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    RelationErpShardDto queryFirstNormalByDownstreamId(@Param("tenantId") String tenantId);

    String queryFirstGroupByDownstreamIdWithDB(@Param("tenantId") String tenantId);

    /**
     * 根据下游企业id,获取所有下游代管企业信息
     */
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    List<RelationErpShardDto> queryByDownstreamId(@Param("tenantId") String tenantId);

    /**
     * 根据分组id和下游企业id,获取对应的数据
     */
    RelationErpShardEntity queryByGroupIdAndDownstreamId(@Param("groupId") String groupId, @Param("tenantId") String tenantId);

    /**
     * 根据分组id和下游企业id,获取对应的数据
     */
    List<RelationErpShardEntity> queryByGroupIdAndDownstreamIds(@Param("groupId") String groupId,@Param("downStreamIds") List<String> downStreamIds, @Param("status") List<Integer> status);

    /**
     * 根据模板企业,获取下游代管企业信息
     */
    List<RelationErpShardDto> queryByTemplateId(@Param("tenantId") String tenantId);

    /**
     * 根据模板企业,获取下游代管企业id
     */
    @Cached(expire = 10, cacheType = CacheType.LOCAL)
    List<String> getAllDownstreamIdsByTemplateId(@Param("tenantId") String tenantId);

    /**
     * 根据上游企业,获取下游代管企业id
     */
    List<String> getAllDownstreamIdsByUpstreamId(@Param("tenantId") String tenantId);

    /**
     * 根据模板企业和连接器id,获取下游代管企业id
     */
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    List<String> getAllDownstreamIdsByTemplateIdAndDcId(@Param("tenantId") String templateId, @Param("dcId") String dcId);

    /**
     * 根据模板企业和连接器id,获取下游代管企业id
     */
    List<String> batchGetAllDownstreamIdsByTemplateIdAndDcId(@Param("tenantId") String templateId, @Param("dcIds") List<String> dcIds);

    /**
     * 获取主要信息，根据下游id和dcId
     */
    RelationErpShardDto getSimple(@Param("downStreamId") String downStreamId, @Param("dcId") String dcId);


    List<RelationErpShardEntity> pageByStatusAndTenantId(@Param("tenantId") String tenantId, @Param("groupId") String groupId, @Param("status") Integer status, @Param("offset") int offset, @Param("limit") int limit);

    int countByStatusAndTenantId(@Param("tenantId") String tenantId, @Param("groupId") String groupId, @Param("status") Integer status);

    int deleteListByDownstreamIdAndGroupId(@Param("groupId") String groupId, @Param("downStreamIds") List<String> downstreamIds);

    int deleteDownstreamEnterprise(@Param("groupId") String groupId, @Param("downStreamId") String downstreamId);

    List<String> getAllDownstreamIdsByGroupId(@Param("groupId") String groupId, @Param("status") Integer status);

    List<RelationErpShardEntity> getAllByGroupId(@Param("groupId") String groupId, @Param("status") Integer status);
}
