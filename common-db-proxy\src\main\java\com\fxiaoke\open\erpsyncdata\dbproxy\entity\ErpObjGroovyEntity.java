package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * erp对象自定义函数表
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objApiName")
@Table(name = "erp_obj_groovy")
public class ErpObjGroovyEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 对象apiName
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 路径
     */
    @Column(name = "url")
    private ErpObjInterfaceUrlEnum url;

    /**
     * 转换脚本，自定义函数apiName上线后，这个字段不再使用了
     */
    @Column(name = "groovy_script")
    private String groovyScript;

    /**
     * 自定义函数apiName
     */
    @Column(name = "func_api_name")
    private String funcApiName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}