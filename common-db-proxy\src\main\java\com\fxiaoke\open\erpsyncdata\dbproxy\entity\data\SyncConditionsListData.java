package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class SyncConditionsListData extends ArrayList<SyncConditionsData> {
    public static SyncConditionsListData convert(List<SyncConditionsData> syncConditionsDataList){
        if (CollectionUtils.isEmpty(syncConditionsDataList)){
            return null;
        }
        SyncConditionsListData syncConditionsListData = new SyncConditionsListData();
        syncConditionsListData.addAll(syncConditionsDataList);
        return syncConditionsListData;
    }
}
