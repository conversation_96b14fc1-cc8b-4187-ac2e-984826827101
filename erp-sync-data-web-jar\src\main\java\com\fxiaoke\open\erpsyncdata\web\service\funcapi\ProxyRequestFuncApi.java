package com.fxiaoke.open.erpsyncdata.web.service.funcapi;

import com.fxiaoke.open.erpsyncdata.admin.model.FuncApiContext;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpRequest2;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Service
public class ProxyRequestFuncApi  implements FuncApiService {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @FuncApiMethod
    public Result<HttpResponse2> proxyRequest(FuncApiContext ctx, HttpRequest2 arg) {
        if (!tenantConfigurationManager.inWhiteList(ctx.getTenantId(), TenantConfigurationTypeEnum.PROXY_REQUEST_EIS)) {
            log.info("invalid tenant call proxyRequest,{}", ctx.getTenantId());
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        HttpResponse2 execute = OkHttpUtils.execute(arg);
        return Result.newSuccess(execute);
    }
}
