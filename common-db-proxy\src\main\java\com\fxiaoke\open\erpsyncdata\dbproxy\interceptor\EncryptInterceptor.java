package com.fxiaoke.open.erpsyncdata.dbproxy.interceptor;

import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityObj;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.interceptorUtil.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.core.annotation.AnnotationUtils;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023/4/23 19:13
 * @Version 1.0
 */
@Intercepts({
        @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class),
})
@Slf4j
public class EncryptInterceptor implements Interceptor {
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //@Signature 指定了 type= parameterHandler 后，这里的 invocation.getTarget() 便是parameterHandler
        //若指定ResultSetHandler ，这里则能强转为ResultSetHandler
//        StatementHandler statementHandler=(StatementHandler)invocation.getTarget();
//        BoundSql boundSql = statementHandler.getBoundSql();

        ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
        MetaObject metaObject= SystemMetaObject.forObject(parameterHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("mappedStatement");
        // 获取参数对像，即 mapper 中 paramsType 的实例
        Field parameterField = parameterHandler.getClass().getDeclaredField("parameterObject");
        parameterField.setAccessible(true);
        Object parameterObject = parameterField.get(parameterHandler);
        // 根据 MappedStatement 的信息判断是否拦截目标 DAO
        String mapperMethodId = mappedStatement.getId();
        if(mapperMethodId.contains("com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao")){
            if (parameterObject != null) {
                Object targetObject = parameterObject;
                if (parameterObject instanceof MapperMethod.ParamMap) {
                    MapperMethod.ParamMap paramMap = (MapperMethod.ParamMap) parameterObject;
                    targetObject = paramMap.get("param1"); // 依赖connectinfo是第一个params
                }
                if(ObjectUtils.isNotEmpty(targetObject)){
                    Class<?> parameterObjectClass = targetObject.getClass();
                    SecurityObj sensitiveData = AnnotationUtils.findAnnotation(parameterObjectClass, SecurityObj.class);
                    if (Objects.nonNull(sensitiveData)) {
                        try {
                            SecurityUtil.encrypt(targetObject);
                        } catch (IllegalAccessException e) {
                            log.error("SecurityUtil encrypt error", e);
                        }
                    }
                }
            }
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
