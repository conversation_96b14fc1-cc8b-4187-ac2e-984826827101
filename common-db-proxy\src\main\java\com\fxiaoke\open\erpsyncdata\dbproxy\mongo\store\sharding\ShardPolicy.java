package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.sharding;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.mongo.support.TenantPolicy;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.net.UrlEscapers;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.HierarchicalINIConfiguration;
import org.apache.commons.configuration.SubnodeConfiguration;
import org.apache.commons.configuration2.INIConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;

/**
 * 基于分片的路由策略
 * 修改了getUri方法
 *
 * @see com.github.mongo.support.TenantPolicy
 *
 * 不能使用common,要不会被识别为com.github.mongo.support.shard.ShardPolicy
 * @see com.github.mongo.support.MongoDataStoreFactoryBean#loadConfig(com.github.autoconf.api.IConfig)
 * 为了兼容运维给的变量名,修改username/password的获取方式,修改instance的逻辑
 */
@Slf4j
public class ShardPolicy implements TenantPolicy, InitializingBean {
    @Setter
    private String configName;

    private int max;
    private String username;
    private String password;
    private Map<String, String> instances = Maps.newConcurrentMap();
    private Map<String, String> shards = Maps.newConcurrentMap();
    private Map<String, String> specials = Maps.newConcurrentMap();

    @Override
    public void afterPropertiesSet() throws Exception {
        ConfigFactory.getConfig(configName, config -> {
            try {
                this.loadConfig(config);
            } catch (Exception e) {
                log.error("cannot reload config name:{}", configName, e);
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public String getUri(String tenantId) {
        if (instances.containsKey(tenantId)) {
            return instances.get(tenantId);
        }
        return dispatch(tenantId);
    }

    public Set<String> getAllInstancesKey() {
        return instances.keySet();
    }

    public void loadConfig(IConfig config) throws IOException, ConfigurationException {
        // 验证配置的合法性
        validateConfig(config);
        INIConfiguration ini = new INIConfiguration();
        try (Reader reader = new StringReader(new String(config.getContent(), StandardCharsets.UTF_8))) {
            ini.read(reader);
        } catch (Exception e) {
            log.error("cannot load config: {}, ", config.getName(), e);
            throw new RuntimeException("cannot load config:" + config.getName(), e);
        }
        ini.getSections().forEach(name -> {
            final org.apache.commons.configuration2.SubnodeConfiguration section = ini.getSection(name);
            switch (name) {
                case "mongo-common":
                    max = section.getInt("max");
                    // 解析后会将.变成..
                    final String userPwd = section.getString("mongo..userPwd");
                    final String[] split = userPwd.split(":");
                    username = split[0];
                    password = split[1];
                    break;
                case "instance":
                    String scheme = "mongodb://";
                    String pwd = UrlEscapers.urlFormParameterEscaper().escape(password);
                    section.getKeys().forEachRemaining(key -> {
                        String instance = section.getString(key);
                        int pos = instance.indexOf(scheme);
                        if (pos == -1) {
                            instance = scheme + username + ':' + pwd + '@' + instance;
                        } else {
                            instance = scheme + username + ':' + pwd + '@' + instance.substring(10);
                        }
                        instances.put(key, instance);
                    });
                    break;
                case "shard":
                    section.getKeys().forEachRemaining(key -> shards.put(key, section.getString(key)));
                    break;
                case "special":
                    section.getKeys().forEachRemaining(key -> {
                        String value = Joiner.on(',').join(section.getList(key));
                        if (!Strings.isNullOrEmpty(value)) {
                            Splitter.on('|').omitEmptyStrings().trimResults().split(value).forEach(item -> specials.put(item, key));
                        }
                    });
                    break;
                default:
                    log.error("unknown name {}", name);
            }
        });
    }

    private void validateConfig(IConfig config) throws IOException, ConfigurationException {
        int shardNum = config.getInt("max");
        Preconditions.checkState(shardNum > 0, "please set max in [mongo-common]");
        Preconditions.checkState(config.has("mongo.userPwd"), "please set mongo.userPwd in [mongo-common]");
        Set<String> instance = Sets.newHashSet();
        Map<String, String> shard = Maps.newHashMap();
        Set<String> special = Sets.newHashSet();
        try (InputStream inputStream = new ByteArrayInputStream(config.getContent())) {
            HierarchicalINIConfiguration configuration = new HierarchicalINIConfiguration();
            configuration.load(inputStream);
            configuration.getSections().forEach(name -> {
                SubnodeConfiguration section = configuration.getSection(name);
                switch (name) {
                    case "mongo-common":
                        break;
                    case "instance":
                        section.getKeys().forEachRemaining(instance::add);
                        break;
                    case "shard":
                        section.getKeys().forEachRemaining(i -> shard.put(i, section.getString(i)));
                        break;
                    case "special":
                        section.getKeys().forEachRemaining(special::add);
                        break;
                    default:
                        log.error("unknown name {}", name);
                }
            });
        }
        special.forEach(i -> Preconditions.checkState(shard.containsKey(i), String.format("please set %s in [shard]", i)));
        shard.values().forEach(i -> Preconditions.checkState(instance.contains(i), String.format("please set %s in [instance]", i)));
        for (int i = 0; i < shardNum; i++) {
            Preconditions.checkState(shard.containsKey("ds" + i), String.format("please set ds%d in [shard]", i));
        }
    }

    private String dispatch(String tenantId) {
        //先看有没有指定特例规则
        String datasource = specials.get(tenantId);
        if (Strings.isNullOrEmpty(datasource)) {
            // 使用企业id取模,方便查问题的时候找到对应的数据库
            final int num = StringUtils.isNumeric(tenantId) ? Integer.parseInt(tenantId) : tenantId.hashCode();
            datasource = "ds" + Math.abs(num % max);
        }
        return instances.get(shards.get(datasource));
    }
}