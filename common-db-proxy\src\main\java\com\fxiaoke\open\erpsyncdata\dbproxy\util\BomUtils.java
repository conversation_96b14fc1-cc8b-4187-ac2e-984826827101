package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * BOM工具类
 * <AUTHOR>
 * @date 2002-06-17
 */
public class BomUtils {
    public final static String srcDataFormat = "{bom_instance_id}={bom_tree_id}{bom_id}{product_id}";
    public final static String destDataFormat = "{bom_id=bom_version}{bom_entry_id}{material_number}";
    public static String getSrcDataId(ObjectData objectData) {
        String tree_id = objectData.getString("tree_id");
        String bom_id = objectData.getString("bom_id");
        String product_id = objectData.getString("product_id");
        String comId = BomUtils.getSrcDataId(objectData.getId(),tree_id,bom_id,product_id);
        return comId;
    }

    public static String getSrcDataId(String bom_instance_id,String tree_id,String bom_id,String product_id) {
        String comId = BomUtils.srcDataFormat.replace("bom_instance_id",bom_instance_id)
                .replace("bom_tree_id",tree_id)
                .replace("bom_id",bom_id)
                .replace("product_id",product_id);

        return comId;
    }

    /**
     * 从dataId中解析出bom_instance_id，
     * dataId格式为  srcDataFormat
     * @param dataId
     * @param objectApiName
     * @return
     */
    public static String getBomInstanceId(String dataId,String objectApiName) {
        if(StringUtils.equalsIgnoreCase(objectApiName, ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName())) {
            List<String> items = Splitter.on("=").splitToList(dataId);
            if(CollectionUtils.isNotEmpty(items)) {
                dataId = items.get(0).replace("{","").replace("}","");
            }
        }
        return dataId;
    }

    /**
     * 获取bom_version
     * dataId格式为  destDataFormat
     * @param dataId
     * @return
     */
    public static String getBomVersion(String dataId) {
        int pos1=dataId.indexOf("=");
        int pos2= dataId.indexOf("}");
        dataId = dataId.substring(pos1+1,pos2);
        return dataId;
    }

    /**
     * 从dataId中解析出bom_entry_id
     * dataId格式为 destDataFormat
     * @param dataId
     * @return
     */
    public static String getBomEntryId(String dataId) {
        int pos1=dataId.indexOf("}");
        int pos2= dataId.indexOf("}",pos1+2);
        dataId = dataId.substring(pos1+2,pos2);
        return dataId;
    }

    public static void main(String[] args) {
        String value = getBomEntryId("{bom_id=bom_version}{bom_entry_id}{material_number}");
        System.out.println(value);
    }
}
