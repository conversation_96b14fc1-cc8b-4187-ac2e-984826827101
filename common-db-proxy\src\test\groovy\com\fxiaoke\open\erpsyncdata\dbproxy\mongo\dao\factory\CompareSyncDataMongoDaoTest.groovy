package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory

import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultDoc
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.MongoStore
import com.mongodb.bulk.BulkWriteResult
import com.mongodb.client.FindIterable
import com.mongodb.client.MongoCollection
import com.mongodb.client.MongoCursor
import spock.lang.Shared
import spock.lang.Specification

class CompareSyncDataMongoDaoTest extends Specification {

    // TODO: 应该是模拟一个mongo出来的，但太麻烦了，暂时先都返回相同的数据吧
    @Shared
    def dataLists = []
    @Shared
    CompareSyncDataMongoDao dao
    @Shared
    def tenantId = "123456"
    def objApiName = "test_api_name"
    def dataId = "test"
    def ployDetailId = "test_ploy_detail_id"

//    def setupSpec() {
    def setup() {
        def collection = Mock(MongoCollection) {
            FindIterable<CompareResultDoc> list = Mock(FindIterable) {
                limit(*_) >> it
                skip(*_) >> it
                iterator() >> Mock(MongoCursor){
                    CompareResultDoc doc = new CompareResultDoc(tenantId: tenantId)
//                    def docList = []
//                    def i = docList.iterator()
//                    hasNext() >> i.hasNext()
//                    next() >> i.next()
                    hasNext() >> false
                    next() >> null
                }
            }
            find(*_) >> list
            updateOne(*_) >> 1
            bulkWrite(*_) >> {
                def res = Mock(BulkWriteResult)
                res.wasAcknowledged() >> true
                return res
            }
        }
        def mongoStore = Mock(MongoStore) {
            getOrCreateCompare(*_) >> collection
        }
        dao = new CompareSyncDataMongoDao(mongoStore: mongoStore)
    }

    def "test getByDataId"() {
        expect:
        def res = dao.getByDataId(tenantId, objApiName, dataId, ployDetailId)
        println(res)
    }

    def "test listByTenantIdAndTypeAndStatusAndUpdateTime"() {
        given:
        def doc = new CompareResultDoc(sourceObjApiName: objApiName, destObjectApiName: objApiName, sourceDataId: dataId, syncType: "test")
        expect:
        def res = dao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, 0, 10)
        println(res)
        res == dao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, doc)
        println(res)
    }

    def "test updateErpReSyncData"() {
        given:
        def doc = new CompareResultDoc()
        expect:
        def res = dao.updateErpReSyncData(tenantId, doc)
        println(res)
    }

    def "test batchInsert"() {
        given:
        def msg = new CompareResultDoc(tenantId: tenantId, sourceObjApiName: objApiName, destObjectApiName: objApiName,
                sourceDataId: dataId, syncLogId: UUID.randomUUID().toString(), syncType: "test", compareField: ["test": []],
                inputParamsMap: ["input": "test"], outputParamsMap: ["output": "test"], hasCompare: true, theSame: false)
        expect:
        dao.batchInsert(tenantId, [msg])
    }
}
