package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import lombok.Data;

import java.io.Serializable;

@Data
public class K3UltimateBaseRequest implements Serializable {
    /**
     * 集成平台扩展字段，非云星空旗舰版字段
     * 服务端返回的数据是否需要序列化Null
     */
    private boolean serializeNull;
    /**
     * 集成平台扩展字段，非云星空旗舰版字段
     * 接口监控日志数据
     */
    InterfaceMonitorData interfaceMonitorData;
}