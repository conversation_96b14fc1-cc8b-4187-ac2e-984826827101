package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 收款退款单特殊逻辑
 *
 * <AUTHOR>
 * @date 2020/12/11
 */

@Slf4j
@Component("AR_REFUNDBILL")
public class RefundBillSpecialBusinessImpl extends BaseSpecialBusiness {

    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData,K3CloudApiClient apiClient) {
        super.afterRunView(erpIdArg,standardData,erpData,apiClient);

        standardData.getDetailFieldVals().forEach((key,objectDataList)->{
            objectDataList.forEach((objectData ->
            {
                //给退款主对象也添加一个单选的结算方式字段
                String settleTypeNumber = objectData.getString("FSETTLETYPEID.FNumber");
                standardData.getMasterFieldVal().put("FSETTLETYPEID.FNumber",settleTypeNumber);
            }));
        });
    }
}
