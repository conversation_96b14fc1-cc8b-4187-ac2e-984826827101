package com.fxiaoke.open.erpsyncdata.writer.model;

import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
public class BatchDoWriteData implements RAMEstimable {
    private SyncDataContextEvent mainContext;
    private List<SyncDataContextEvent> detailContextList;

    public int count() {
        return detailContextList == null ? 1 : detailContextList.size() + 1;
    }

    @Override
    public long ramBytesUsed(int depth) {
        return RamUsageEstimateUtil.sizeOfObject(mainContext, depth) + RamUsageEstimateUtil.sizeOfObject(detailContextList, depth);
    }
}
