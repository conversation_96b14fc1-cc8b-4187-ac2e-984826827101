package com.fxiaoke.open.erpsyncdata.web.controller;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.github.trace.executor.MonitorTaskWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Controller同步转异步支持
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/11/8
 */
@Slf4j
public abstract class AsyncSupportController extends BaseController {
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static final TimedCache<String, Semaphore> semaphoreMap = CacheUtil.newTimedCache(1000 * 60 * 5);//为防止对象随企业越来越多，设置过期时间

    static {
        //定时清理，不然超时后不会自动清理。10分钟
        semaphoreMap.schedulePrune(TimeUnit.MINUTES.toMillis(10));
    }

    private final static ExecutorService executorService = AssertUtil.notNull(
            TtlExecutors.getTtlExecutorService(
                    new NamedThreadPoolExecutor("async-ctrl-pool", ConfigCenter.THREAD_POOL_CORE_SIZE,
                            100, 1000)),
            "async support controller init error", null, null);

    public <T> DeferredResult<Result<T>> asyncExecute(Callable<Result<T>> callable,
                                                      Integer timeoutSecond,
                                                      Boolean alwaysNotify,
                                                      I18NStringEnum actionNameEnum,
                                                      String lang) {
        Result<T> timeoutResult = defaultTimeoutResult();
        String actionName = i18NStringManager.get(actionNameEnum, lang, getLoginUserTenantId());
        return asyncExecute(callable, timeoutSecond, alwaysNotify, actionName, timeoutResult, lang);
    }

    public <T> DeferredResult<Result<T>> asyncExecuteJustName(String actionName,
                                                              Callable<Result<T>> callable) {
        Result<T> timeoutResult = defaultTimeoutResult();
        return asyncExecute(callable, 10, false, actionName, timeoutResult, getLang());
    }

    public <T> DeferredResult<Result<T>> asyncExecute(Callable<Result<T>> callable,
                                                      Integer timeoutSecond,
                                                      Boolean alwaysNotify,
                                                      String actionName,
                                                      String lang) {
        Result<T> timeoutResult = defaultTimeoutResult();
        return asyncExecute(callable, timeoutSecond, alwaysNotify, actionName, timeoutResult, lang);
    }

    @NotNull
    public <T> Result<T> defaultTimeoutResult() {
        Result<T> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        timeoutResult.setErrMsg(i18NStringManager.get(timeoutResult.getI18nKey(), getLang(), getLoginUserTenantId(), timeoutResult.getErrMsg()));
        return timeoutResult;
    }

    public <T> DeferredResult<Result<T>> asyncExecute(Callable<Result<T>> callable,
                                                      Integer timeoutSecond,
                                                      Boolean alwaysNotify,
                                                      String actionName,
                                                      String message,
                                                      Result<T> timeoutResult,
                                                      String lang) {
        return asyncExecute(callable, timeoutSecond, alwaysNotify, actionName, r -> message, timeoutResult, lang);
    }

    public <T> DeferredResult<Result<T>> asyncExecute(Callable<Result<T>> callable,
                                                      Integer timeoutSecond,
                                                      Boolean alwaysNotify,
                                                      String actionName,
                                                      Result<T> timeoutResult,
                                                      String lang) {
        final String tenantId = getLoginUserTenantId();
        return asyncExecute(callable,
                timeoutSecond,
                alwaysNotify,
                actionName,
                r -> Objects.isNull(r.getData()) ? null : i18NStringManager.get(I18NStringEnum.s2024, lang, tenantId) + JSONObject.toJSONString(r.getData()),
                timeoutResult,
                lang);
    }

    public <T> DeferredResult<Result<T>> asyncExecute(Callable<Result<T>> callable,
                                                      Integer timeoutSecond,
                                                      Boolean alwaysNotify,
                                                      String actionName,
                                                      Function<Result<T>, String> messageFunction,
                                                      Result<T> timeoutResult,
                                                      String lang) {
        return asyncExecute(callable,
                timeoutSecond,
                alwaysNotify,
                actionName,
                messageFunction,
                timeoutResult,
                getLoginUserTenantId(),
                getEa(),
                Collections.singletonList(getLoginUserId()),
                lang);
    }

    public <T> DeferredResult<Result<T>> asyncExecute(Callable<Result<T>> callable,
                                                      Integer timeoutSecond,
                                                      Boolean alwaysNotify,
                                                      String actionName,
                                                      Function<Result<T>, String> messageFunction,
                                                      Result<T> timeoutResult,
                                                      String tenantId,
                                                      String enterpriseAccount,
                                                      List<Integer> receivers,
                                                      String lang) {
        String beginTime = DateUtil.now();
        String traceId = TraceUtil.get();
        UserVo userVo = getUserVo();
        String dataCenterId = userVo != null ? userVo.getDataCenterId() : null;
        BiConsumer<Boolean, Result<T>> finalResultConsumer = (setSuccess, r) -> {
            if (!alwaysNotify && setSuccess) {
                //直接返回结果时，不发送企信通知
                log.info("direct return result success");
                return;
            }
            if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(enterpriseAccount) || Objects.isNull(receivers) || receivers.isEmpty()) {
                log.info("没给通知人");
                return;
            }
            SendTextNoticeArg sendTextNoticeArg = initSendTextNoticeArg(tenantId, enterpriseAccount, receivers, dataCenterId);
            sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s1135, lang, tenantId));
            String msg = i18NStringManager.get(I18NStringEnum.s1136, lang, tenantId) + actionName + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1137, lang, tenantId) + beginTime + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1138, lang, tenantId) + DateUtil.now() + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1139, lang, tenantId) + traceId + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1140, lang, tenantId) + r.getErrMsg();
            final String apply = messageFunction.apply(r);
            if (StringUtils.isNotEmpty(apply)) {
                msg += "\n" + apply;
            }
            sendTextNoticeArg.setMsg(msg);
            Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager, lang, tenantId),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
            if (!voidResult.isSuccess()) {
                log.error("send task result failed,arg:{}", sendTextNoticeArg);
            }
        };
        DeferredResult<Result<T>> deferredResult = asyncExecute(actionName, callable, finalResultConsumer, timeoutResult, timeoutSecond, lang);
        //onTimeout是在tomcat-http线程执行，需要先把线程变量取出来
        deferredResult.onTimeout(() -> {
            TraceUtil.initTrace(traceId);
            if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(enterpriseAccount) || Objects.isNull(receivers) || receivers.isEmpty()) {
                log.info("没给通知人");
                return;
            }
            SendTextNoticeArg sendTextNoticeArg = initSendTextNoticeArg(tenantId, enterpriseAccount, receivers, dataCenterId);
            sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s1135, lang, tenantId));
            String msg = i18NStringManager.get(I18NStringEnum.s1136, lang, tenantId) + actionName + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1137, lang, tenantId) + beginTime + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1139, lang, tenantId) + traceId + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1141, lang, tenantId);
            sendTextNoticeArg.setMsg(msg);
            Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager, lang, tenantId),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
            if (!voidResult.isSuccess()) {
                log.error("send task timeout msg failed,arg:{}", sendTextNoticeArg);
            }
        });

        // 处理异常
        deferredResult.onCompletion(() -> {
            if (deferredResult.hasResult()) {
                Object o = deferredResult.getResult();
                if (Objects.isNull(o) || !(o instanceof Result)) {
                    return;
                }
                Result<?> result = (Result<?>) o;
                if (!result.isSuccess() && StringUtils.isNotEmpty(result.getI18nKey())) {
                    result.setErrMsg(i18NStringManager.get2(result.getI18nKey(), lang, tenantId, result.getErrMsg(), result.getI18nExtra()));
                }
            }
        });
        return deferredResult;
    }

    private SendTextNoticeArg initSendTextNoticeArg(String tenantId, String enterpriseAccount, List<Integer> receivers, String dataCenterId) {
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(dataCenterId);
        sendTextNoticeArg.setEnterpriseAccount(enterpriseAccount);
        sendTextNoticeArg.setReceivers(receivers);
        return sendTextNoticeArg;
    }


    public <T> DeferredResult<Result<T>> asyncExecuteByCustomerConsumer(Callable<Result<T>> callable,
                                                                        Integer timeoutSecond,
                                                                        Boolean timeoutNotify,
                                                                        String actionName, BiConsumer<Boolean, Result<T>> finalResultConsumer,
                                                                        Result<T> timeoutResult,
                                                                        String lang) {
        String beginTime = DateUtil.now();
        String traceId = TraceUtil.get();
        DeferredResult<Result<T>> deferredResult = asyncExecute(actionName, callable, finalResultConsumer, timeoutResult, timeoutSecond, lang);
        //onTimeout是在tomcat-http线程执行，需要先把线程变量取出来
        UserVo userVo = getUserVo();
        final String tenantId = getLoginUserTenantId();
        String dataCenterId = userVo != null ? userVo.getDataCenterId() : null;
        final List<Integer> employeeId = userVo != null ? Collections.singletonList(userVo.getEmployeeId()) : new ArrayList<>();
        final String enterpriseAccount = userVo != null ? userVo.getEnterpriseAccount() : null;
        deferredResult.onTimeout(() -> {
            TraceUtil.initTrace(traceId);
            SendTextNoticeArg sendTextNoticeArg = initSendTextNoticeArg(tenantId, enterpriseAccount, employeeId, dataCenterId);
            sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s1135, lang, tenantId));
            String msg = i18NStringManager.get(I18NStringEnum.s1136, lang, tenantId) + actionName + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1137, lang, tenantId) + beginTime + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1139, lang, tenantId) + traceId + "\n" +
                    i18NStringManager.get(I18NStringEnum.s1141, lang, tenantId);
            sendTextNoticeArg.setMsg(msg);
            if (timeoutNotify) {
                Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                        AlarmRuleType.OTHER,
                        AlarmRuleType.OTHER.getName(i18NStringManager, lang, null),
                        AlarmType.OTHER,
                        AlarmLevel.GENERAL);
                if (!voidResult.isSuccess()) {
                    log.error("send task timeout msg failed,arg:{}", sendTextNoticeArg);
                }
            } else {
                log.info("asyncExecuteByCustomerConsumer future result:{}", JSONObject.toJSONString(sendTextNoticeArg));
            }

        });
        return deferredResult;
    }

    /**
     * @param notifyWhenTimeout 当超时的时候，会发送结果通知
     */
    public <T> BiConsumer<Boolean, Result<T>> generateConsumer(String actionName, boolean notifyWhenTimeout, String lang) {
        String beginTime = DateUtil.now();
        String traceId = TraceUtil.get();
        UserVo userVo = getUserVo();
        final String tenantId = getLoginUserTenantId();
        String dataCenterId = userVo != null ? userVo.getDataCenterId() : null;
        final List<Integer> employeeId = userVo != null ? Collections.singletonList(userVo.getEmployeeId()) : new ArrayList<>();
        final String enterpriseAccount = userVo != null ? userVo.getEnterpriseAccount() : null;
        BiConsumer<Boolean, Result<T>> finalResultConsumer = (setSuccess, r) -> {
            if (setSuccess) {
                //直接返回结果时，不发送企信通知
                log.info("direct return result success");
                return;
            }
            if (notifyWhenTimeout) {
                SendTextNoticeArg sendTextNoticeArg = initSendTextNoticeArg(tenantId, enterpriseAccount, employeeId, dataCenterId);
                sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s1135, lang, tenantId));
                StringBuilder result = new StringBuilder(r.getErrMsg());
                if (ObjectUtils.isNotEmpty(r.getData())) {
                    result.append(":").append(JSONObject.toJSONString(r.getData()));
                }
                String msg = i18NStringManager.get(I18NStringEnum.s1136, lang, tenantId) + actionName + "\n" +
                        i18NStringManager.get(I18NStringEnum.s1137, lang, tenantId) + beginTime + "\n" +
                        i18NStringManager.get(I18NStringEnum.s1138, lang, tenantId) + DateUtil.now() + "\n" +
                        i18NStringManager.get(I18NStringEnum.s1139, lang, tenantId) + traceId + "\n" +
                        i18NStringManager.get(I18NStringEnum.s1140, lang, tenantId) + result.toString();
                sendTextNoticeArg.setMsg(msg);
                Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                        AlarmRuleType.OTHER,
                        AlarmRuleType.OTHER.getName(i18NStringManager, lang, null),
                        AlarmType.OTHER,
                        AlarmLevel.GENERAL);
                if (!voidResult.isSuccess()) {
                    log.error("send task result failed,arg:{}", sendTextNoticeArg);
                }
            } else {
                log.info("async future result:{}", JSONObject.toJSONString(r));
            }

        };
        return finalResultConsumer;
    }


    /**
     * 异步执行方法
     * 不使用AsyncWebTask是因为超时会导致异步线程挂起状态中断，没细看源码原因。
     *
     * @param callable            执行方法
     * @param finalResultConsumer 最终结果处理
     * @param timeoutResult       超时返回的结果
     * @param timeoutSecond       超时秒
     * @param <T>                 泛型
     * @return DeferredResult
     */
    public <T> DeferredResult<Result<T>> asyncExecute(String actionName, Callable<Result<T>> callable,
                                                      BiConsumer<Boolean, Result<T>> finalResultConsumer,
                                                      Result<T> timeoutResult,
                                                      Integer timeoutSecond,
                                                      String lang) {
        String tenantId = getLoginUserTenantId();
        Semaphore semaphore = getSemaphore(tenantId);
        //控制并发
        boolean concurrency;
        try {
            concurrency = semaphore.tryAcquire(2000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (!concurrency) {
            timeoutResult.setErrCode(ResultCodeEnum.EXCEEDING_MAXIMUM_CONCURRENCY.getErrCode());
            timeoutResult.setErrMsg(String.format(ResultCodeEnum.EXCEEDING_MAXIMUM_CONCURRENCY.getErrMsg(), actionName));
            DeferredResult<Result<T>> deferredResult = new DeferredResult<>(500L, timeoutResult);
            return deferredResult;
        } else {
            DeferredResult<Result<T>> deferredResult = new DeferredResult<>(1000L * timeoutSecond, timeoutResult);
            executorService.submit(MonitorTaskWrapper.wrap(() -> {
                Result<T> call;
                try {
                    call = callable.call();
                } catch (ErpSyncDataException e) {
                    log.info("call ErpSyncDataException", e);
                    call = Result.newError(e.getErrCode(), e.getErrMsg());
                } catch (Exception e) {
                    log.error("call exception,", e);
                    call = Result.newError(ResultCodeEnum.SYSTEM_ERROR, e.getClass().getName());
                } finally {
                    semaphore.release();
                }
                log.info("AsyncSupportController.asyncExecute,call={}", JSONObject.toJSONString(call));
                if (StringUtils.isNotEmpty(call.getI18nKey())) {
                    call.setErrMsg(i18NStringManager.get2(call.getI18nKey(), lang, getLoginUserTenantId(), call.getErrMsg(), call.getI18nExtra()));
                }
                call.setI18nKey(null);
                call.setI18nExtra(null);
                boolean b = deferredResult.setResult(call);
                finalResultConsumer.accept(b, call);
            }));
            return deferredResult;
        }
    }

    public <T> DeferredResult<Result<T>> asyncExecuteWithTimeOutResult(String actionName,
                                                                       Consumer<DeferredResult<Result<T>>> consumer,
                                                                       Integer timeoutSecond,
                                                                       Result<T> timeoutResult) {
        return asyncExecuteWithTimeoutResultSup(actionName, timeoutSecond, consumer, () -> timeoutResult);
    }

    public <T> DeferredResult<Result<T>> asyncExecuteWithTimeoutResultSup(String actionName,
                                                                          Integer timeoutSecond,
                                                                          Consumer<DeferredResult<Result<T>>> consumer,
                                                                          Supplier<Result<T>> timeoutResultSup) {

        String tenantId = getLoginUserTenantId();
        Semaphore semaphore = getSemaphore(tenantId);
        //控制并发
        boolean concurrency;
        try {
            concurrency = semaphore.tryAcquire(2000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (!concurrency) {
            Result<T> timeoutResult = new Result<>();
            timeoutResult.setErrCode(ResultCodeEnum.EXCEEDING_MAXIMUM_CONCURRENCY.getErrCode());
            timeoutResult.setErrMsg(String.format(ResultCodeEnum.EXCEEDING_MAXIMUM_CONCURRENCY.getErrMsg(), actionName));
            DeferredResult<Result<T>> deferredResult = new DeferredResult<>(500L, timeoutResult);
            return deferredResult;
        } else {
            DeferredResult<Result<T>> deferredResult = new DeferredResult<>(1000L * timeoutSecond);
            deferredResult.onTimeout(() -> deferredResult.setResult(timeoutResultSup.get()));
            executorService.submit(MonitorTaskWrapper.wrap(() -> {
                try {
                    consumer.accept(deferredResult);
                } catch (Exception e) {
                    throw e;
                } finally {
                    semaphore.release();
                }
            }));
            return deferredResult;
        }
    }

    private Semaphore getSemaphore(String tenantId) {
        Integer permits = 3;
        if (StringUtils.isBlank(tenantId)) {
            permits = 15;
            tenantId = "default";
        }
        if (semaphoreMap.get(tenantId) == null) {
            synchronized (this) {
                if (semaphoreMap.get(tenantId) == null) {
                    Semaphore semaphore = new Semaphore(permits);
                    semaphoreMap.put(tenantId, semaphore);
                }
            }

        }
        return semaphoreMap.get(tenantId);
    }

    /**
     * 仅异步
     *
     * @param runnable
     */
    public void asyncExecute(Runnable runnable) {
        executorService.submit(MonitorTaskWrapper.wrap(runnable));
    }

    /**
     * @param consumer arg1: sseEmitter,arg2: timeoutMs
     * @return
     */
    public SseEmitter asyncExecuteSSE(BiConsumer<SseEmitter, Long> consumer) {
        //10分钟超时
        long timeoutMs = TimeUnit.MINUTES.toMillis(10L);
        SseEmitter sseEmitter = new SseEmitter(timeoutMs);
        sseEmitter.onCompletion(() -> log.info("asyncExecuteSSE complete"));
        //onTimeout 的处理无法send了，所以不要让其出现~~~
        Semaphore semaphore = getSemaphore(getLoginUserTenantId());
        try {
            boolean concurrency = semaphore.tryAcquire(2000, TimeUnit.MILLISECONDS);
            if (!concurrency) {
                Result<Object> exceedingResult = Result.newError(ResultCodeEnum.EXCEEDING_MAXIMUM_CONCURRENCY);
                exceedingResult.setErrMsg(String.format(ResultCodeEnum.EXCEEDING_MAXIMUM_CONCURRENCY.getText(), "SSE"));
                sendAndCompleteSSE(sseEmitter, exceedingResult);
            } else {
                asyncExecute(() -> {
                    try {
                        // 传给consumer的超时时间，少5s
                        consumer.accept(sseEmitter, timeoutMs - 5000);
                        sseEmitter.complete();
                    } catch (Exception e) {
                        log.error("asyncExecuteSSE call exception,", e);
                        sendAndCompleteSSE(sseEmitter, Result.wrapException(e));
                    } finally {
                        //释放 信号量
                        semaphore.release();
                    }
                });
            }
        } catch (Exception e) {
            sendAndCompleteSSE(sseEmitter, Result.wrapException(e));
        }
        return sseEmitter;
    }

    private void sendAndCompleteSSE(SseEmitter sseEmitter, Result<?> result) {
        try {
            sseEmitter.send(result);
            sseEmitter.complete();
        } catch (IOException e) {
            sseEmitter.completeWithError(e);
        }
    }
}
