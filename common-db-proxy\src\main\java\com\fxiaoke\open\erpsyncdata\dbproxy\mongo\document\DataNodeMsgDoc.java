package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 19:01 2023/1/12
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class DataNodeMsgDoc {

    /**
     * ei+objApiName+dataId+version+streamId
     */
    private String uniqueKey;
    /**
     * ei+objApiName+dataId+streamId
     */
    private String noVersionUniqueKey;
    /**
     * id
     */
    @BsonId
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * 是否top60
     */
    private Boolean isTop60;
    /**
     * 对象名称
     */
    private String objApiName;
    /**
     * 数据唯一标识
     */
    private String dataId;
    /**
     * 数据版本
     */
    private Long version;
    /**
     * 集成流id
     */
    private String streamId;
    /**
     * 节点类型
     */
    private List<String> nodeTypes;
    /**
     * 节点名称
     */
    private List<String> nodeNames;
    /**
     * 备注
     */
    private List<String> nodeRemarks;
    /**
     * 操作详情
     */
    private String operateMsg;
    /**
     * 节点时间
     */
    private List<Long> nodeTimes;
    /**
     * 时间,只插入，已第一个为准
     */
    private Long time;
    /**
     * traceId,覆盖
     */
    private String traceId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 数据创建时间
     */
    private Date createTime;


    public static DataNodeMsgDoc create(String tenantId, String objApiName, String dataId, Long version, String streamId,
                                        DataNodeTypeEnum nodeType, DataNodeNameEnum nodeName, Long nodeTime, String remark, String traceId, String operateMsg) {
        Date createTime = new Date();
        StringBuffer uniqueKey = new StringBuffer();
        uniqueKey.append(tenantId).append("_").append(objApiName).append("_").append(dataId).append("_").append(version).append("_").append(streamId);
        DataNodeMsgDoc timePoint = DataNodeMsgDoc.builder()
                .uniqueKey(uniqueKey.toString())
                .tenantId(tenantId)
                .dataId(dataId)
                .objApiName(objApiName)
                .version(version)
                .streamId(streamId)
                .time(nodeTime)
                .createTime(createTime)
                .traceId(traceId)
                .operateMsg(operateMsg)
                .build();
        if(nodeType!=null){
            timePoint.setNodeTypes(Lists.newArrayList(nodeType.name()));
        }
        if(nodeName!=null){
            timePoint.setNodeNames(Lists.newArrayList(nodeName.name()));
        }
        if(nodeTime!=null){
            timePoint.setNodeTimes(Lists.newArrayList(nodeTime));
        }
        if(remark!=null){
            timePoint.setNodeRemarks(Lists.newArrayList(remark));
        }
        return timePoint;
    }
}
