package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import com.fxiaoke.open.erpsyncdata.common.interceptor.TenantShardingTableInterceptor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/7/16
 */
@Configuration
@Slf4j
public class TenantShardingTableConfig {
    //放到TenantShardingTableInterceptor随插件启动
//    static {
//        Map<String, String> logicTableTenantField = new HashMap<>();
//        logicTableTenantField.put("sync_data", "tenant_id");
//        logicTableTenantField.put("sync_data_mappings", "tenant_id");
//        logicTableTenantField.put("erp_processed_data", "tenant_id");
//        TenantShardingTableInterceptor.resetLogicTableTenantField(logicTableTenantField);
//    }

    @PostConstruct
    public void init() {
        //设置不启用专表企业supplier
        Set<String> supTenants = TenantShardingTableInterceptor.resetDisableDynamicTenantsSup(this::getDisableDynamicTenants);
    }


    private Set<String> getDisableDynamicTenants() {
        return ConfigCenter.DISABLE_DYNAMIC_TENANTS;
    }
}
