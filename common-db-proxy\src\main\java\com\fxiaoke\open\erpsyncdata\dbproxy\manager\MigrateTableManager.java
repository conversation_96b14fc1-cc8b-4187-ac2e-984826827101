package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.pod.exception.DbRouterException;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.common.interceptor.TenantShardingTableInterceptor;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/11/5
 */
@Component
@Slf4j
public class MigrateTableManager {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private ErpTableDao erpTableDao;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;


    public String initTenantTable(String tenantId) throws SQLException {
        return initDestDataBaseTenantTable(tenantId, tenantId, null);
    }

    private String defaultConfigRoute(String tenantId, String resourceId) {
        if (StringUtils.isBlank(resourceId)) {
            resourceId = getDefaultResourceId(tenantId);
        }
        if (StringUtils.isBlank(resourceId)) {
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId);
        }
        Map<String, List<String>> configRoute = tenantConfigurationManager.getConfigRouteTenant();
        for (String sourceId : configRoute.keySet()) {
            List<String> tenants = configRoute.get(sourceId);
            if (tenants != null && tenants.contains(tenantId)) {
                return i18NStringManager.getByEi(I18NStringEnum.s3649,tenantId) + sourceId;
            }
        }
        Boolean isRoute = configRouteManager.configRoute(tenantId, resourceId);
        if (isRoute) {
            if (configRoute.containsKey(resourceId)) {
                configRoute.get(resourceId).add(tenantId);
            } else {
                configRoute.put(resourceId, Lists.newArrayList(tenantId));
            }
            Boolean update = tenantConfigurationManager.updateConfigRouteTenant(configRoute);
            if (!update) {
                return i18NStringManager.getByEi(I18NStringEnum.s3650,tenantId) + tenantId + " resourceId=" + resourceId;
            }
            return i18NStringManager.getByEi(I18NStringEnum.s3651,tenantId);
        } else {
            return i18NStringManager.getByEi(I18NStringEnum.s3652,tenantId);
        }
    }

    /**
     * 1. 沙盒企业走沙盒路由
     * 2. vip企业走vip路由
     * 3. 预分配的企业,按 (ei%resource数量) 获取对应的resourceId
     * 4. 其他企业走默认路由
     */
    private String getDefaultResourceId(String tenantId) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        if (ea != null && ea.contains("sandbox")) {
            return ConfigCenter.DEFAULT_SANDBOX_CONFIG_ROUTE_SOURCE_ID;//沙盒路由
        }

        Map<String, String> tenant2Level = this.queryEnterpriseLevel(Lists.newArrayList(tenantId));
        if (tenant2Level != null && tenant2Level.containsKey(tenantId) && StringUtils.isNotBlank(tenant2Level.get(tenantId))) {
            if (ConfigCenter.VIP_TENANT_LEVEL_VALUE.contains(tenant2Level.get(tenantId)) && StringUtils.isNotBlank(ConfigCenter.DEFAULT_VIP_CONFIG_ROUTE_SOURCE_ID)) {
                return ConfigCenter.DEFAULT_VIP_CONFIG_ROUTE_SOURCE_ID;//vip路由
            }
        }

        if (ConfigCenter.isPgShardTenant(tenantId)) {
            return ConfigCenter.getPgShardResourceId(tenantId);
        }

        return ConfigCenter.DEFAULT_CONFIG_ROUTE_SOURCE_ID;
    }

    public String getSourceId(String tenantId){
        Map<String, List<String>> configRoute = tenantConfigurationManager.getConfigRouteTenant();
        for (String sourceId : configRoute.keySet()) {
            List<String> tenants = configRoute.get(sourceId);
            if (tenants != null && tenants.contains(tenantId)) {
                return sourceId;
            }
        }
        return null;
    }

    public Map<String, String> queryEnterpriseLevel(List<String> tenantIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
        searchTemplateQuery.addFilter(ConfigCenter.FS_ACCOUNT_TENANT_ID, tenantIds, "IN");
        searchTemplateQuery.addFilter(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL, Lists.newArrayList(""), "ISN");
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setSearchSource("es");
        searchTemplateQuery.setLimit(10000);
        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> queryAccount;
        //从客户对象获取
        try {
            queryAccount = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName(), searchTemplateQuery);
        } catch (Exception e) {
            return Maps.newHashMap();
        }
        log.info("query accountObj data arg:{},result:{}", searchTemplateQuery, queryAccount);
        if (queryAccount.isSuccess()
                && queryAccount.getData() != null
                && queryAccount.getData().getQueryResult() != null
                && CollectionUtils.isNotEmpty(queryAccount.getData().getQueryResult().getData())) {
            List<ObjectData> dataList = queryAccount.getData().getQueryResult().getData();
            Map<String, String> tenantId2Level = dataList.stream().filter(u -> StringUtils.isNotBlank(u.getString(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL)))
                    .collect(Collectors.toMap(v -> v.getString(ConfigCenter.FS_ACCOUNT_TENANT_ID),
                            u -> u.getString(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL)));
            return tenantId2Level;
        }
        return Maps.newHashMap();
    }

    private List<String> getDisableDynamicTenants() {
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("00")).findById(CommonConstant.GLOBAL_DISABLE_DYNAMIC_TENANTS_CONFIG_ID);
        List<String> tenantIds = new ArrayList<>(Arrays.asList(config.getConfiguration().split(",")));
        return tenantIds;
    }

    public static List<String> getSql(String tenantId, Boolean onlyTable) {

        if (onlyTable != null) {
            if (onlyTable) {
                return new ArrayList<>(Arrays.asList(getTableSql().replaceAll("\\{tenantId}", tenantId).split("\n")));
            } else {
                return new ArrayList<>(Arrays.asList(getIndexSql().replaceAll("\\{tenantId}", tenantId).split("\n")));
            }
        }
        return new ArrayList<>(Arrays.asList(getSql().replaceAll("\\{tenantId}", tenantId).split("\n")));
    }

    private static String getSql() {
        return getTableSql() + getIndexSql();
    }

    private static String getTableSql() {
        return "--不再创建表syncData\n" +
                "--创建表syncDataMappings\n" +
                "CREATE table sync_data_mappings_{tenantId} ( id varchar NOT NULL, source_tenant_id varchar NULL, source_object_api_name varchar NULL, source_data_id varchar NULL, source_data_name varchar NULL, dest_object_api_name varchar NULL, dest_tenant_id varchar NULL, dest_data_name varchar NULL, last_sync_data_id varchar NULL, last_sync_status int2 NULL, last_source_data_vserion int8 NULL, dest_data_id varchar NULL, create_time int8 NULL, update_time int8 NULL, is_created bool NULL, remark varchar NULL, is_deleted bool NULL, tenant_id varchar NULL, master_data_id varchar NULL, CONSTRAINT mappings_{tenantId}_id_pk PRIMARY KEY (id) );\n" +
                "--不再创建表processedData" ;   // ignoreI18n   sql注解
    }

    /**
     * 增加了concurrently
     * @return
     */
    private static String getIndexSql() {
        return "--创建表syncDataMappings索引\n" +
                "create index concurrently if not exists \"sync_data_mappings_{tenantId}_last_sync_data_id_index\" on sync_data_mappings_{tenantId} (last_sync_data_id);\n" +
                "create index concurrently if not exists \"idx_mappings_status_sort_{tenantId}\" on sync_data_mappings_{tenantId} (tenant_id, source_object_api_name, dest_object_api_name , trans_sync_status(last_sync_status::integer) , update_time desc, id desc);\n" +
                "create index concurrently if not exists \"idx_mappings_update_time_{tenantId}\" on sync_data_mappings_{tenantId} (tenant_id, source_object_api_name, dest_object_api_name , update_time desc, id desc);\n" +
                "create index concurrently if not exists \"mappings_{tenantId}_s_d_dest_name\" on sync_data_mappings_{tenantId} (tenant_id, source_object_api_name, dest_object_api_name, dest_data_name,update_time desc);\n" +
                "create index concurrently if not exists \"mappings_{tenantId}_s_d_source_name\" on sync_data_mappings_{tenantId} (tenant_id, source_object_api_name, dest_object_api_name, source_data_name,update_time desc);\n" +
                "CREATE unique index concurrently if not exists \"unique_mappings_dest_id_{tenantId}\" ON sync_data_mappings_{tenantId} (dest_data_id, dest_object_api_name, source_object_api_name, tenant_id);\n" +
                "CREATE unique index concurrently if not exists \"unique_mappings_source_id_{tenantId}\" ON sync_data_mappings_{tenantId} (source_data_id, source_object_api_name, dest_object_api_name, tenant_id);\n" +
                "CREATE index concurrently if not exists \"mappings_main_id_{tenantId}\" ON sync_data_mappings_{tenantId} (master_data_id, source_object_api_name, dest_object_api_name, tenant_id);\n"+
                "CREATE index concurrently if not exists \"mappings_main_id_src_{tenantId}\" ON sync_data_mappings_{tenantId} (master_data_id, source_object_api_name, tenant_id);\n"+
                "CREATE index concurrently if not exists \"mappings_main_id_dest_{tenantId}\" ON sync_data_mappings_{tenantId} (master_data_id, dest_object_api_name, tenant_id);\n"+
                "CREATE index concurrently if not exists \"idx_mappings_not_created_{tenantId}\" ON sync_data_mappings_{tenantId} (tenant_id, source_object_api_name, dest_object_api_name) where is_created = false;\n"+
                "--不创建表processedData索引" ;   // ignoreI18n   sql注解
    }

    /**
     * 给目标数据源创建某个企业的专表
     *
     * @param destSource
     * @param tenantId
     * @param onlyTable  true：只建表  false:只创建索引  null:表和索引都创建
     * @return
     * @throws SQLException
     */
    public String initDestDataBaseTenantTable(String destSource, String tenantId, Boolean onlyTable) throws SQLException {
        StringBuilder resMsg = new StringBuilder(DateUtil.now()).append(" ").append(tenantId);
        if (TenantShardingTableInterceptor.disableDynamicTable(tenantId)) {
            return resMsg.append(i18NStringManager.getByEi(I18NStringEnum.s3730, tenantId)).toString();
        }
        List<String> sqls = getSql(tenantId, onlyTable);
        String checkSql = StrFormatter.format("select * from pg_class where relname = 'sync_data_mappings_{}';", tenantId);
        try {
            List<Map<String, Object>> list = erpTableDao.setTenantId(destSource).superQuerySql(checkSql, tenantId);
            if (list != null && list.size() > 0) {
                if (onlyTable == null || onlyTable) {
                    return resMsg.append(i18NStringManager.getByEi(I18NStringEnum.s3724, tenantId)).toString();
                } else {
                    resMsg.append(i18NStringManager.getByEi(I18NStringEnum.s3724, tenantId));//只创建索引，存在专表不返回，继续往下走
                }
            } else {
                if (onlyTable != null && !onlyTable) {//onlyTable=false
                    return resMsg.append(i18NStringManager.getByEi(I18NStringEnum.s3731, tenantId)).toString();
                }
            }
        } catch (Exception e) {
            log.info("check exception config next", e);
            if (e.getCause() != null && e.getCause().getCause() != null && e.getCause().getCause() instanceof DbRouterException) {
                String msg = defaultConfigRoute(tenantId, null);
                resMsg.append(msg);
            }
            List<Map<String, Object>> list = erpTableDao.setTenantId(destSource).superQuerySql(checkSql, tenantId);
            if (list != null && list.size() > 0) {
                return resMsg.append(i18NStringManager.getByEi(I18NStringEnum.s3724, tenantId)).toString();
            }
        }
        ImmutableSet<String> whiteList = tenantConfigurationManager.getWhiteList(TenantConfigurationTypeEnum.pauseInitTableSourceId);
        if (CollUtil.isNotEmpty(whiteList)){
            //存在暂停建表的sourceId
            String sourceId = getSourceId(tenantId);
            if (sourceId == null){
                throw new ErpSyncDataException("can not get sourceId for "+tenantId,null,null);
            }
            if (whiteList.contains(sourceId)){
                throw new ErpSyncDataException(ResultCodeEnum.PAUSE_INIT_TABLE,tenantId);
            }
        }
        for (String sql : sqls) {
            if (sql.startsWith("--")) {
                resMsg.append("\n").append(sql);
            } else {
                try {
                    erpTableDao.setTenantId(destSource).superUpdateSql(sql);
                } catch (Exception e) {
                    log.error("execute sql exception,{}", sql, e);
                    resMsg.append(e.getMessage()).append(" ");
                }
            }
        }
        return resMsg.toString();
    }

    public void renameTables(String tenantId) {
        ArrayList<String> oldTableNames = CollUtil.newArrayList("sync_data_mappings_" + tenantId, "sync_data_" + tenantId, "erp_processed_data_" + tenantId);
        List<String> existTables = erpTableDao.setTenantId(tenantId).listTables(oldTableNames);
        DateTime now = DateUtil.date();
        String date = now.toString("yyyyMMdd");
        String prefix = "deleted__" + date + "_";
        for (String tableName : existTables) {
            String renameSql = String.format("alter table  %s  rename to %s;", tableName, prefix + tableName);
            erpTableDao.setTenantId(tenantId).superUpdateSql(renameSql);
        }
    }
}
