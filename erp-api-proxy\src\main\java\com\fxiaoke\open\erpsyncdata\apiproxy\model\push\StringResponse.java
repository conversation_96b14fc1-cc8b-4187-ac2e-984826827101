package com.fxiaoke.open.erpsyncdata.apiproxy.model.push;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * 返回结果
 */
@Data
@Accessors(chain = true)
public class StringResponse {
    private int status = 200;
    private Map<String, List<String>> headers;
    private String body;
    /**
     * 需要处理的数据，可为空
     *
     * @deprecated 后面修改为中间态处理，不能直接返回
     */
    @Deprecated
    private Map<EventTypeEnum, List<StandardData>> dataMap;

    public ResponseEntity<?> toResponse() {
        ResponseEntity.BodyBuilder bodyBuilder = ResponseEntity.status(status);
        HttpHeaders headers = new HttpHeaders();
        if (this.headers != null) {
            headers.putAll(this.headers);
        }
        if (headers.getContentType() == null) {
            //连接器实现未设置contentType时，将默认使用application/json;charset=UTF-8
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        }
        bodyBuilder.headers(headers);
        if (body == null) {
            //body为空时，会设置默认的body
            return bodyBuilder.body(Result.newSuccess());
        } else {
            return bodyBuilder.body(body);
        }
    }
}
