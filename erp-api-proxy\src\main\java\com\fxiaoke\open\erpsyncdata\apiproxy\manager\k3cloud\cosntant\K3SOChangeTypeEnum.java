package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/1/8
 */
@Getter
@AllArgsConstructor
public enum K3SOChangeTypeEnum {

    ENABLE_SO_CHANGE_BILL("A", "启用订单变更单"),
    DIRECT_CHANGE_SO("B", "直接订单变更"),
    ENABLE_SO_NEW_CHANGE_BILL("C", "启用订单新变更单"),
    ;

    private final String type;

    private final String name;

    public static K3SOChangeTypeEnum getByType(String type){
        for (K3SOChangeTypeEnum value : K3SOChangeTypeEnum.values()) {
            if (value.getType().equals(type)){
                return value;
            }
        }
        return null;
    }
}
