package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service("manualSync")
public class ManualSyncServiceImpl implements CustomFunctionCommonService {

    @ReloadableProperty("dss.u8.middle.url")
    private String u8MiddleServerUrl;

    @ReloadableProperty("dss.manualSyncTimeQuantum")
    private Integer manualSyncTimeQuantum=31;

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {

        if (arg.getParams() == null || arg.getParams().isEmpty()) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        String tenantId=arg.getTenantId();
        JSONObject jsonObject = JSONObject.parseObject(arg.getParams());
        String crmObjApiName = jsonObject.getString("crmObjApiName");
        String erpObjApiName =  jsonObject.getString("erpObjApiName");
        String dataCenterId = jsonObject.getString("dataCenterId");
        String startTime =  jsonObject.getString("startTime");
        String endTime = jsonObject.getString("endTime");

        if (crmObjApiName == null || erpObjApiName == null || startTime == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        if (endTime == null) {
            endTime = System.currentTimeMillis() + "";
        }

        try {
            return syncObjByManual(tenantId.toString(), crmObjApiName, erpObjApiName, dataCenterId, Long.valueOf(startTime), Long
              .valueOf(endTime));
        } catch (Exception e) {
            log.info("执行手动同步异常:",e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    private Result<String> syncObjByManual(String tenantId,
                                          String crmObjApiName,
                                          String erpObjApiName,
                                          String dataCenterId,
                                          Long startTime,
                                          Long endTime) {

        if (Long.valueOf(endTime) - Long.valueOf(startTime) > 1000L * 60 * 60 * 24 * manualSyncTimeQuantum) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(),
                    I18NStringEnum.s27,
                    manualSyncTimeQuantum+"");
        }
        ErpConnectInfoEntity arg = new ErpConnectInfoEntity();
        arg.setTenantId(tenantId);
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        Optional<ErpConnectInfoEntity> first = erpConnectInfoEntities.stream().filter(t->t.getChannel().equals(ErpChannelEnum.ERP_U8)).findFirst();
        if (!first.isPresent()){
            return Result.newError(ResultCodeEnum.THE_ENTERPRISE_CON_NOT_EXIST);
        }
        ErpChannelEnum channel = first.get().getChannel();
        switch (channel){
            case ERP_U8:
                return syncU8ObjByManual(tenantId,crmObjApiName,erpObjApiName,dataCenterId,startTime,endTime);
            default:
                return Result.newError(ResultCodeEnum.UNSUPPORTED_CHANNEL);
        }
    }

    private Result<String> syncU8ObjByManual(String tenantId,
                                             String crmObjApiName,
                                             String erpObjApiName,
                                             String dataCenterId,
                                             Long startTime,
                                             Long endTime){
        Map bodyMap =new HashMap<String,String>();
        bodyMap.put("tenantId",tenantId);
        bodyMap.put("objApiName",erpObjApiName);
        bodyMap.put("startTime",startTime);
        bodyMap.put("endTime",endTime);
        bodyMap.put("dsSequence",dataCenterId);
        proxyHttpClient.postUrl(
          u8MiddleServerUrl + "/yongyou/sync/obj/syncObjByManual", bodyMap, Collections.emptyMap(), (Long)null);
        return Result.newSuccess("success");
    }


}
