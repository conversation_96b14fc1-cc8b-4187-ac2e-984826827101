package com.fxiaoke.open.erpsyncdata.dbproxy.redis;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/7
 */
@Service
public class RedisCacheManager {
    @Autowired
    private RedisDataSource redisDataSource;


    public boolean setCache(String key, String value, Long secondsToExpire) {
        return setCache(key, value, secondsToExpire, getCallerClassName());
    }

    public boolean setCache(String key, String value, Long secondsToExpire, String redisVisitorName) {
        SetParams setParams = SetParams.setParams().ex(secondsToExpire);
        return setCache(key, value, setParams, redisVisitorName);
    }

    public boolean setCache(String key, String value, SetParams setParams) {
        return setCache(key, value, setParams, getCallerClassName());
    }

    public boolean setCache(String key, String value, SetParams setParams, String monitorName) {
        String set = redisDataSource.get(monitorName).set(key, value, setParams);
        return "OK".equals(set);
    }

    /**
     * redis key 统一放到{@link CommonConstant}
     *
     * @param key
     * @return
     */
    public String getCache(String key, String redisVisitorName) {
        return redisDataSource.get(redisVisitorName).get(key);
    }

    public String getCache(String key) {
        return getCache(key, getCallerClassName());
    }

    /**
     * 获取并删除
     *
     * @param key
     * @return
     */
    public String getDelCache(String key, String redisVisitorName) {
        String s = redisDataSource.get(redisVisitorName).get(key);
        if (StringUtils.isNotBlank(s)) {
            redisDataSource.get(redisVisitorName).del(key);
        }
        return s;
    }


    /**
     * 获取并删除
     *
     * @param key
     * @return
     */
    public String getDelCache(String key) {
        return getDelCache(key, getCallerClassName());
    }

    /**
     * 获取并删除
     *
     * @param key
     * @return
     */
    public Long delCache(String key) {
        return delCache(key, getCallerClassName());
    }

    public Long delCache(String key, String redisVisitorName) {
        return redisDataSource.get(redisVisitorName).del(key);
    }

    /**
     * 获取调用方的类名
     *
     * @return 调用方类名，如果获取失败则返回"Unknown"
     */
    private String getCallerClassName() {
        String className = "Unknown";
        try {
            // 获取调用方法的类名，需要使用索引2
            // 索引0是getStackTrace方法本身
            // 索引1是当前方法getCallerClassName
            // 索引2才是实际的调用方法
            className = new Throwable().getStackTrace()[2].getClassName();
        } catch (Throwable ignore) {
        }
        return className;
    }
}