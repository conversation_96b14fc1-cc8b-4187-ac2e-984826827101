package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
public class ExecuteArg<T> {
    /**
     * 执行类型
     */
    private ExecuteType executeType;
    /**
     * 引用Id，同一个复合请求内，不可重复
     */
    private String referenceId = "__default__";
    /**
     * 参数统一使用Dict接收
     */
    private T body;

    public static ExecuteArg<PreparingSql> query() {
        return new ExecuteArg<PreparingSql>()
                .setExecuteType(ExecuteType.sql_query);
    }

    public static ExecuteArg<PreparingSql> queryOne() {
        return new ExecuteArg<PreparingSql>()
                .setExecuteType(ExecuteType.sql_query_one);
    }

    public static ExecuteArg<PreparingSql> queryString() {
        return new ExecuteArg<PreparingSql>().setExecuteType(ExecuteType.sql_query_string);
    }

    public static ExecuteArg<PreparingSql> count() {
        return new ExecuteArg<PreparingSql>().setExecuteType(ExecuteType.sql_count);
    }

    public static ExecuteArg<PreparingPageSql> queryPage() {
        return new ExecuteArg<PreparingPageSql>().setExecuteType(ExecuteType.sql_query_page);
    }

    public static ExecuteArg<PreparingSql> execute() {
        return new ExecuteArg<PreparingSql>().setExecuteType(ExecuteType.sql_execute);
    }

    public static ExecuteArg<PreparingSql> executeGetId() {
        return new ExecuteArg<PreparingSql>().setExecuteType(ExecuteType.sql_execute_get_id);
    }

    public String toJsonStr(){
        return JacksonUtil.toJson(this);
    }
}
