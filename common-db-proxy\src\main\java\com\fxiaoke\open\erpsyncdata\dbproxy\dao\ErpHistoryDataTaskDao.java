package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:27 2021/8/12
 * @Desc:
 */
@Repository
public interface ErpHistoryDataTaskDao extends ErpBaseDao<ErpHistoryDataTaskEntity>, ITenant<ErpHistoryDataTaskDao> {
    int updateRemarkAndStopById(@Param("id")String id,@Param("remark")String remark ,@Param("needStop")Boolean needStop,@Param("newStatus")int newStatus);
    int updateStatusByIdsAndOldStatus(@Param("taskId")String taskId, @Param("oldStatus")int oldStatus, @Param("newStatus")int newStatus, @Param("updateTime")Long updateTime);
    List<ErpHistoryDataTaskEntity> listByStatus(@Param("tenantId")String tenantId, @Param("status")List<Integer> status);
    int countByStatus(@Param("tenantId")String tenantId, @Param("status")List<Integer> status);
    int viewAllCountByDcIdAndActualObjStatus(@Param("tenantId") String tenantId, @Param("dataCenterIds") List<String> dataCenterIds, @Param("erpObjApiNames") List<String> erpObjApiNames, @Param("taskName") String taskName, @Param("status") List<Integer> status);
    List<ErpHistoryDataTaskEntity> viewAllListByDcIdAndActualObjStatus(@Param("tenantId") String tenantId, @Param("dataCenterIds") List<String> dataCenterIds, @Param("erpObjApiNames") List<String> erpObjApiNames, @Param("taskName") String taskName, @Param("status") List<Integer> status, @Param("limit") Integer limit, @Param("offset") Integer offset,@Param("sequence") Integer sequence);
    List<ErpHistoryDataTaskEntity> listByRealObj(@Param("tenantId") String tenantId,@Param("dataCenterId") String dataCenterId, @Param("erpRealObjApiName") String erpRealObjApiName);

    List<ErpHistoryDataTaskEntity> listBySplitObj(@Param("tenantId") String tenantId, @Param("erpSplitObjApiNames") List<String> erpSplitObjApiNames);
    List<ErpHistoryDataTaskEntity> listByIds(@Param("tenantId") String tenantId, @Param("taskIds") List<String> taskIds);

    List<ErpHistoryDataTaskEntity> listByDataCenterId(@Param("tenantId") String tenantId, @Param("dataCenterId") String dataCenterId);

    int deleteByDataCenterId(@Param("tenantId") String tenantId, @Param("dataCenterId") String dataCenterId);

    int stopTask(@Param("tenantId") String tenantId,@Param("id")String id, @Param("needStop")Boolean needStop);

    int updateById(@Param("updated")ErpHistoryDataTaskEntity updated);

    int updateEntityById(@Param("updated")ErpHistoryDataTaskEntity updated);

    int restartEntity(@Param("updated")ErpHistoryDataTaskEntity updated);

    int updateTaskDataCenterId(@Param("tenantId") String tenantId, @Param("dataCenterId") String dataCenterId,@Param("relatedPloyDetailId") ListStringData relatedPloyDetailId,  @Param("erpSplitObjApiName")String erpSplitObjApiName);

    int countByDcIdAndUpdateTime(@Param("tenantId") String tenantId, @Param("dataCenterId") String dataCenterId, @Param("deadLineTime") Long deadLineTime);
}