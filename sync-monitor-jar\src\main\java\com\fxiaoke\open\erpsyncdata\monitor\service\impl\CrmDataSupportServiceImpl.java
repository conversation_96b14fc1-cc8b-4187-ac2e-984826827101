package com.fxiaoke.open.erpsyncdata.monitor.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.helper.DataSupport;
import com.fxiaoke.open.erpsyncdata.monitor.manager.DataSourceInfoManager;
import com.fxiaoke.open.erpsyncdata.monitor.model.CompareData;
import com.fxiaoke.open.erpsyncdata.monitor.model.CrmDataSourceInfo;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByIdArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByIdResult;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataSupportService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/30
 */
@Slf4j
@Service
@DataSupport(DataSourceType.FS_CRM)
public class CrmDataSupportServiceImpl implements DataSupportService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private DataSourceInfoManager dataSourceInfoManager;

    @Override
    public Result<ListDataByIdResult> listDataById(ListDataByIdArg arg) {
        CrmDataSourceInfo crmDataSourceInfo = dataSourceInfoManager.getCrmConnectInfo(arg.getTenantId(), arg.getObjApiName());
        String tenantId = arg.getTenantId();
        Integer tenantIdInt = Integer.parseInt(tenantId);
        HeaderObj headers = HeaderObj.newInstance(tenantIdInt, -10000);
        //分组
        List<String> dataIds = arg.getDataIds();
        ListDataByIdResult resultData = new ListDataByIdResult();
        if (dataIds.isEmpty()) {
            return Result.newSuccess(resultData);
        }
        List<List<String>> dataIdPartition = ListUtil.partition(dataIds, 2000);
        String dataIdField = crmDataSourceInfo.getDataIdField();
        String dataVersionField = crmDataSourceInfo.getDataVersionField();
        for (List<String> dataIdPart : dataIdPartition) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(2000);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.addFilter(dataIdField, dataIdPart, FilterOperatorEnum.IN);
            String queryInfoStr = JSON.toJSONString(searchTemplateQuery);
            FindV3Arg findV3Arg = new FindV3Arg();
            findV3Arg.setDescribeApiName(arg.getObjApiName());
            findV3Arg.setSearchQueryInfo(queryInfoStr);
            findV3Arg.setSelectFields(Lists.newArrayList(dataIdField, dataVersionField));
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> listResult = objectDataServiceV3.queryList(headers, findV3Arg);
            log.info("query crm data,arg:{},result:{}", findV3Arg, listResult);
            boolean success = listResult != null
                    && listResult.isSuccess()
                    && listResult.getData() != null
                    && listResult.getData().getQueryResult() != null
                    && listResult.getData().getQueryResult().getDataList() != null;
            if (!success) {
                //任意一次失败,直接返回失败
                return Result.newSystemError(I18NStringEnum.s35);
            }
            for (ObjectData objectData : listResult.getData().getQueryResult().getDataList()) {
                String id = objectData.getString(dataIdField);
                if (StrUtil.isEmpty(id)) {
                    //如果id为null,可能是id字段错了查了所有数据
                    continue;
                }
                resultData.getCompareDataList().add(CompareData.of(id, objectData.getString(dataVersionField)));
            }
        }
        return Result.newSuccess(resultData);
    }
}
