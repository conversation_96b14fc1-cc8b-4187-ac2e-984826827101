package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/1/19 17:28:19
 */
@Component
@Aspect
@Slf4j
public class DBErrorMonitorAspect extends InvokeErrorMonitorAspect {
    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.*Dao.*(..))")
    public Object pgErrorMonitor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        return invokeErrorMonitor(proceedingJoinPoint, InvokeErrorMonitorAspect.InvokeErrorType.PG);
    }

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao.*(..))")
    public Object mongoErrorMonitor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        return invokeErrorMonitor(proceedingJoinPoint, InvokeErrorMonitorAspect.InvokeErrorType.MONGO);
    }
}
