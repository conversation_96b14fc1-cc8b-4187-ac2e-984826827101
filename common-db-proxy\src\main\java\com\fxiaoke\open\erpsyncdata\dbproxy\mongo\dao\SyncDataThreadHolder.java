package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.util.ObjectUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 缓存工具
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/5/6
 */
@Repository
public class SyncDataThreadHolder {
    /**
     * 同步过程，插入数据时记录id，同步结束后，从缓存移除并存入数据库。
     */
    private static ThreadLocal<Map<String,SyncDataEntity>> syncDataHold = new ThreadLocal<>();

    public Map<String,SyncDataEntity> getAndRemove(){
        Map<String, SyncDataEntity> cache = getCache();
        syncDataHold.remove();
        return cache;
    }

    public void put(SyncDataEntity syncData) {
        Map<String, SyncDataEntity> cache = getCache();
        cache.put(syncData.getId(), syncData);
    }
    public void put(String id,SyncDataEntity syncData) {
        Map<String, SyncDataEntity> cache = getCache();
        cache.put(id, syncData);
    }

    private Map<String, SyncDataEntity> getCache() {
        Map<String, SyncDataEntity> cache = syncDataHold.get();
        if (cache ==null){
            cache = new HashMap<>();
            syncDataHold.set(cache);
        }
        return cache;
    }
    public Map<String, SyncDataEntity> getTenantSyncDataCache() {
        Map<String, SyncDataEntity> cache = syncDataHold.get();
        return cache;
    }

    /**
     * 获取原始值
     * @param tenantId
     * @param id
     * @return
     */
    public SyncDataEntity getRaw(String tenantId, String id) {
        SyncDataEntity entity = getCache().get(id);
        if (entity != null && ObjectUtil.equals(tenantId, entity.getTenantId())) {
            return entity;
        }
        return null;
    }


    public boolean contains(String tenantId, String id) {
        return getCache().containsKey(id);
    }

    /**
     * 获取副本
     * @param tenantId
     * @param id
     * @return
     */
    public SyncDataEntity get(String tenantId, String id) {
        return BeanUtil.deepCopy(getRaw(tenantId, id), SyncDataEntity.class);
    }

    /**
     * 减少deepCopy导致的序列化消耗
     */
    public SyncDataEntity getSimple(String tenantId, String id) {
        SyncDataEntity newObj = new SyncDataEntity();
        SyncDataEntity oldObj = getRaw(tenantId, id);
        if (oldObj == null) {
            return null;
        }
        BeanUtils.copyProperties(oldObj, newObj, "sourceData", "destData", "sourceDetailSyncDataIds");
        newObj.setSourceDetailSyncDataIds(BeanUtil.deepCopy(oldObj.getSourceDetailSyncDataIds(), MapListStringData.class));
        return newObj;
    }

    /**
     * 批量获取，并且移除能获取到的id
     *
     * @param tenantId
     * @param ids
     * @return
     */
    public List<SyncDataEntity> batchGetAndRemoveId(String tenantId, Set<String> ids) {
        List<SyncDataEntity> res = new ArrayList<>();
        ids.removeIf(id -> {
            SyncDataEntity entity = get(tenantId, id);
            if (entity != null) {
                res.add(entity);
                return true;
            }
            return false;
        });
        return res;
    }
}
