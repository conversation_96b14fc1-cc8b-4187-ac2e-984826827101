package com.fxiaoke.open.erpsyncdata.monitor.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@AllArgsConstructor
public enum CompareDataErrorType {

    NOT_FOUND("目标系统未找到数据", I18NStringEnum.s967.getI18nKey()),
    VERSION_NOT_MATCH("目标系统数据版本不正确", I18NStringEnum.s968.getI18nKey()),
    ;

    /**
     * 描述
     */
    public final String description;
    public final String i18nKey;
}
