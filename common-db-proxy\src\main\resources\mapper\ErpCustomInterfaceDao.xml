<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpCustomInterfaceDao">
    <select id="findData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpCustomInterfaceEntity">
        select * from erp_custom_interface
        where tenant_id=#{tenantId}
        and data_center_id=#{dataCenterId}
        and obj_api_name=#{objApiName}
        and interface_type=#{interfaceType} limit 1
    </select>
    <select id="findByTenantIdAndId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpCustomInterfaceEntity">
        select * from erp_custom_interface
        where tenant_id=#{tenantId}
          and id = #{id}
    </select>
</mapper>