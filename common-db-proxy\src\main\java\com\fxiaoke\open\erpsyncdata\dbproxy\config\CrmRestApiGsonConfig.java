package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.gson.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Type;
import java.math.BigDecimal;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/19
 */
@Configuration
public class CrmRestApiGsonConfig {

    @Bean("crmRestApiGson")
    public Gson crmRestApiGson() {
        //ObjectData会序列化null
        Gson customGson = new GsonBuilder()
                .registerTypeAdapterFactory(new ValueNullableAdapterFactory(ObjectData.class))
                // .registerTypeAdapter(Double.class, new JsonSerializer<Double>() {
                //     @Override
                //     public JsonElement serialize(Double src, Type typeOfSrc, JsonSerializationContext context) {
                //         if (src == null) {
                //             return JsonNull.INSTANCE;
                //         } else {
                //             BigDecimal plainExpressionBigDecimal = new BigDecimal(src) {
                //                 @Override
                //                 public String toString() {
                //                     return super.toPlainString();
                //                 }
                //             };
                //             return new JsonPrimitive(plainExpressionBigDecimal);
                //         }
                //     }
                // })
                // https://github.com/google/gson/pull/1290
                .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
                .create();
        return customGson;
    }
}
