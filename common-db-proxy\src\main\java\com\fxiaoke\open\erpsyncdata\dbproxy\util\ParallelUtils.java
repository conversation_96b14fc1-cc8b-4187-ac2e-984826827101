package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.github.autoconf.ConfigFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;

public class ParallelUtils {
    private static final Logger LOG = LoggerFactory.getLogger(ParallelUtils.class);
    private static NamedThreadPoolExecutor executorService;
    private static NamedThreadPoolExecutor backgroundExecutorService;
    /**
     * 用于执行轮询的线程池
     */
    private static NamedThreadPoolExecutor erpRollingDataExecutorService;

    /**
     * 用于执行从mongo获取erp数据的线程池
     */
    private static NamedThreadPoolExecutor rollingErpDataFromMongoExecutorService;

    /**
     * 用于执行获取erp历史数据的线程池
     */
    private static NamedThreadPoolExecutor rollingErpHistoryDataExecutorService;

    /**
     * 大屏线程池
     */
    private static NamedThreadPoolExecutor dataScreenExecutorService;

    public static final int MAX_PARALLEL_NUM = 5000;

    public static  int taskCoreThreadPoolSize = 10;
    public static  int taskMaxThreadPoolSize = 200;
    static {

        ConfigFactory.getInstance().getConfig("erp-sync-data-all", config -> {
            taskCoreThreadPoolSize = Integer.valueOf(config.get("taskCoreThreadPoolSize","10"));
            taskMaxThreadPoolSize = Integer.valueOf(config.get("taskMaxThreadPoolSize","200"));
        });
        LOG.info("trace ParallelUtils coreThreadPoolSize:{}, maxThreadPoolSize:{}", taskCoreThreadPoolSize, taskMaxThreadPoolSize);
        executorService = new NamedThreadPoolExecutor("ParallelUtils",true,taskCoreThreadPoolSize, taskMaxThreadPoolSize, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(50));
        dataScreenExecutorService = new NamedThreadPoolExecutor("ParallelUtils",true,taskCoreThreadPoolSize, taskCoreThreadPoolSize, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(50));

        backgroundExecutorService = new NamedThreadPoolExecutor("ParallelUtils-Background",true,taskCoreThreadPoolSize, taskMaxThreadPoolSize, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(5000));

        erpRollingDataExecutorService = new NamedThreadPoolExecutor("ErpRollingDataTask",true,taskCoreThreadPoolSize, taskMaxThreadPoolSize, 60 * 1000, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(5000));

        rollingErpDataFromMongoExecutorService = new NamedThreadPoolExecutor("RollingErpDataFromMongoTask",true,taskCoreThreadPoolSize, taskMaxThreadPoolSize, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(5000));

        rollingErpHistoryDataExecutorService = new NamedThreadPoolExecutor("RollingErpHistoryDataTask",true,taskCoreThreadPoolSize, taskMaxThreadPoolSize, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(5000));
    }

    public static ParallelTask createParallelTask() {
        return new ParallelTaskImpl(executorService);
    }

    public static ParallelTask createBackgroundTask() {
        return new ParallelTaskImpl(backgroundExecutorService);
    }
    public static ParallelTask createErpRollingDataTask() {
        return new ParallelTaskImpl(erpRollingDataExecutorService);
    }

    public static ParallelTask createRollingErpDataFromMongoTask() {
        return new ParallelTaskImpl(rollingErpDataFromMongoExecutorService);
    }
    public static ParallelTask createRollingErpHistoryData() {
        return new ParallelTaskImpl(rollingErpHistoryDataExecutorService);
    }

    public static ParallelTask createDataScreenExecutor() {
        return new ParallelTaskImpl(dataScreenExecutorService);
    }

    public interface ParallelTask {

        ParallelTask submit(Runnable runnable);

        boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException;

        void run();
    }

    /**
     * 执行后台异步任务
     */
    public static <T> CompletableFuture<T> supplyAsyncBackgroundTask(Supplier<T> supplier) {
        CompletableFuture<T> tCompletableFuture = CompletableFuture.supplyAsync(supplier, backgroundExecutorService);
        return tCompletableFuture;
    }


    /**
     * 执行后台异步任务
     */
    public static CompletableFuture<Void> runAsyncBackgroundTask(Runnable supplier) {
        return CompletableFuture.runAsync(supplier, backgroundExecutorService);
    }

    private static class ParallelTaskImpl implements ParallelTask {

        private List<Runnable> runnableList = new ArrayList<>(MAX_PARALLEL_NUM);

        private ExecutorService executor;

        public ParallelTaskImpl(ExecutorService executor) {
            this.executor = executor;
        }

        @Override
        public ParallelTask submit(Runnable runnable) {
            if (runnable != null) {
                if (runnableList.size() <= MAX_PARALLEL_NUM) {
                    runnableList.add(runnable);
                } else {
                    throw new RuntimeException("Max Parallel Task Number:" + MAX_PARALLEL_NUM);
                }
            }

            return this;
        }

        @Override
        public boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException {

            final AtomicBoolean ret = new AtomicBoolean(true);

            if (runnableList.isEmpty()) {
                return true;
            }
            CountDownLatch countDownLatch = new CountDownLatch(runnableList.size());

            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            runnable.run();
                        } catch (Exception e) {
                            ret.compareAndSet(true, false);
                            LOG.error("execute task error", e);
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
                } catch (Exception e) {
                    LOG.error("submit task error!", e);
                    throw e;
                }
            }

            try {
                boolean finished = countDownLatch.await(timeout, timeUnit);
                if (!finished) {
                    throw new TimeoutException("execute task timeout");
                }
            } catch (InterruptedException e) {
                throw new TimeoutException("execute task interrupted");
            }

            return ret.get();
        }


        @Override
        public void run() {
            if (runnableList.isEmpty()) {
                return;
            }
            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            runnable.run();
                        } catch (Exception e) {
                            LOG.error("execute task error", e);
                        }
                    });
                } catch (Exception e) {
                    LOG.error("submit task error!", e);
                }
            }
        }
    }
}