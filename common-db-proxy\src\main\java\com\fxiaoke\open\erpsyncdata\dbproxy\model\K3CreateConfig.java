package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/14 14:30:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class K3CreateConfig {
    /**
     * 以哪个接口返回的状态更新中间表
     * A:创建 B:提交 C:审核
     * @see com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3DocumentStatusEnum#status
     */
    private String returnStepStatus = "ALL";
    /**
     * k3提交失败后 是否删除源单据
     */
    private boolean deleteBySubmitFail = true;

    /**
     * k3审核失败后 是否删除源单据
     */
    private boolean deleteByAuditFail = true;

    public static final K3CreateConfig DefaultCreateConfig = new K3CreateConfig("C", true, true);

    public static final K3CreateConfig DefaultUpdateConfig = new K3CreateConfig("C", false, false);
}
