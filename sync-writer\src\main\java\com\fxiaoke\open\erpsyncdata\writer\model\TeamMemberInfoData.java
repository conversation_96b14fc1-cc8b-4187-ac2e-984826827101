package com.fxiaoke.open.erpsyncdata.writer.model;

import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Builder
public class TeamMemberInfoData implements Serializable {
    private String teamMemberPermissionType;
    private List<String> teamMemberEmployee;
    private String teamMemberRole;
    private String teamMemberType;
    private List<String> teamMemberRoleList;
}
