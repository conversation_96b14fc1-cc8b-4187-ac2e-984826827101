package com.fxiaoke.open.erpsyncdata.monitor.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.RunnableTask;
import com.facishare.converter.EIEAConverter;
import com.facishare.qixin.api.model.message.content.AdvanceText;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MonitorLogModule;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TextInfoUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.monitor.constant.CompareDataErrorType;
import com.fxiaoke.open.erpsyncdata.monitor.constant.CompareResultType;
import com.fxiaoke.open.erpsyncdata.monitor.dao.CompareDao;
import com.fxiaoke.open.erpsyncdata.monitor.helper.DataSupportServiceHelper;
import com.fxiaoke.open.erpsyncdata.monitor.model.CompareData;
import com.fxiaoke.open.erpsyncdata.monitor.model.CompareObjectMapping;
import com.fxiaoke.open.erpsyncdata.monitor.model.CompareStrategy;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.CompareArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByIdArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByTimeArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.CompareResult;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByIdResult;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByTimeResult;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataCompareService;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataSupportService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AlertArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
@Slf4j
@Service
public class DataCompareServiceImpl implements DataCompareService {
    @Autowired
    private CompareDao compareDao;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EIEAConverter eieaConverter;

    @SneakyThrows
    @Override
    public Result<Void> compareAndAlert(String tenantId, String compareName) {
        RLock rLock = redissonClient.getLock(CommonConstant.REDIS_LOCK_COMPARE_DATA + tenantId + compareName);
        RLock rLock2 = redissonClient.getLock(CommonConstant.REDIS_LOCK_COMPARE_DATA2 + tenantId + compareName);
        if (rLock2.tryLock(2, 120, TimeUnit.SECONDS)) {
            //先获取一个锁，用于控制执行时间间隔。再获取执行的锁。
            if (rLock.tryLock()) {
                try {
                    CompareObjectMapping mapping = compareDao.getObjMapping(tenantId, compareName);
                    CompareArg arg = new CompareArg();
                    arg.setMapping(mapping);
                    CompareStrategy compareStrategy = mapping.getCompareStrategy();
                    DateTime compareTime = DateTime.now();
                    DateTime beginTime = DateUtil.offsetMinute(compareTime, -1 * (compareStrategy.getTimeLength() + compareStrategy.getDelay()));
                    DateTime endTime = DateUtil.offsetMinute(compareTime, -1 * compareStrategy.getDelay());
                    CompareArg arg1 = new CompareArg();
                    arg1.setMapping(mapping);
                    arg1.setBeginTime(beginTime.getTime());
                    arg1.setEndTime(endTime.getTime());
                    compareAndAlert(arg1);
                } finally {
                    rLock.unlock();
                }
            } else {
                return Result.newError("try lock failed,this task is running");
            }
        } else {
            return Result.newError("try lock failed,another client running");
        }
        return Result.newSuccess();
    }


    @Override
    public Result<CompareResult> compareAndAlert(CompareArg arg) {
        TimeInterval ti = new TimeInterval();
        ti.start();
        Result<CompareResult> compareResultResult = doCompare(arg);
        long cost = ti.intervalMs();
        if (!compareResultResult.isSuccess()) {
            //代码异常不上报bizlog
            log.error("compare exception,{}", compareResultResult.getErrMsg());
            return compareResultResult;
        }
        CompareResult compareResult = compareResultResult.getData();
        CompareResultType resultType = compareResult.getResultType();
        CompareObjectMapping mapping = arg.getMapping();
        StringJoiner primaryJoiner = new StringJoiner("\n");
        primaryJoiner.add(i18NStringManager.getByEi2(I18NStringEnum.s716.getI18nKey(),
                mapping.getTenantId(),
                String.format(I18NStringEnum.s716.getI18nValue(), mapping.getCompareName(), mapping.getSourceObjApiName(), mapping.getDestObjApiName()),
                Lists.newArrayList(mapping.getCompareName(), mapping.getSourceObjApiName(), mapping.getDestObjApiName())));
        primaryJoiner.add(i18NStringManager.getByEi2(I18NStringEnum.s717.getI18nKey(),
                mapping.getTenantId(),
                String.format(I18NStringEnum.s717.getI18nValue(), DateUtil.date(arg.getBeginTime()), DateUtil.date(arg.getEndTime())),
                Lists.newArrayList(DateUtil.date(arg.getBeginTime())+"", DateUtil.date(arg.getEndTime())+"")));


        primaryJoiner.add(i18NStringManager.getByEi(resultType.i18nKey,arg.getMapping().getTenantId(),resultType.description));
        String primary = primaryJoiner.toString();
        if (resultType != CompareResultType.SUCCESS) {
            //比对不成功
            alert(arg, primary, "");
        }
        sendBizLog(arg, compareResultResult, cost);
        StringJoiner msgJoiner = new StringJoiner("\n");
        if (compareResult.getErrorCount() > 0) {
            //存在错误结果
            compareResult.getDataErrorTypeMap().forEach(((errorType, ids) -> {
                if (ids.size() > 100) {
                    msgJoiner.add(i18NStringManager.getByEi(errorType.i18nKey,arg.getMapping().getTenantId(),errorType.description) + ids.size() + i18NStringManager.getByEi(I18NStringEnum.s718,mapping.getTenantId()) + ids.subList(0, 100));
                } else {
                    msgJoiner.add(i18NStringManager.getByEi(errorType.i18nKey,arg.getMapping().getTenantId(),errorType.description) + ids.size() + i18NStringManager.getByEi(I18NStringEnum.s719,mapping.getTenantId()) + ids);
                }
            }));
            alert(arg, primary, msgJoiner.toString());
        }
        return compareResultResult;
    }

    private void alert(CompareArg arg, String primary, String msg) {
        arg.getMapping().getAlertUserMap().forEach((ea, userIds) -> {
            String ei = eieaConverter.enterpriseAccountToId(ea)+"";
            AdvanceText advanceText = TextInfoUtil.advanceText(
                    TextInfoUtil.text(i18NStringManager.getByEi(I18NStringEnum.s515,ei), "#FF0000", false),
                    TextInfoUtil.text(primary),
                    TextInfoUtil.text(msg)
            );
            AlertArg alertArg = AlertArg.primaryApp()
                    .setEa(ea)
                    .setToUserList(userIds)
                    .setAdvanceText(advanceText);
            Result<Void> voidResult = notificationService.sendAlertMsg(alertArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager,null,ei),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
            log.info("compare data alert ,{},{},alert result:{}", arg, msg, voidResult);
        });
        log.info("compare data alert end,{},{},alert", arg, msg);
    }

    private void sendBizLog(CompareArg arg, Result<CompareResult> result, long cost) {
        int errorCount = 0;
        int totalCount = 0;
        String status = "-1";
        if (result.isSuccess()) {
            CompareResult compareResult = result.getData();
            totalCount = compareResult.getTotalCount().intValue();
            errorCount = compareResult.getErrorCount().intValue();
            status = String.valueOf(compareResult.getResultType().ordinal());
        }
        String tenantId = arg.getMapping().getTenantId();
        ErpSyncMonitorLogDTO dto = MonitorBizLogUtil.builder(MonitorLogModule.compare_data, "compare", tenantId, status)
                .parameters(JacksonUtil.toJson(arg))
                .response(JacksonUtil.toJson(result))
                .cost1(cost)
                .num1(totalCount)
                .num2(errorCount)
                .time1(arg.getBeginTime())
                .time2(arg.getEndTime())
                .build();
        MonitorBizLogUtil.send(dto);
    }

    @Override
    public Result<CompareResult> doCompare(CompareArg arg) {
        CompareObjectMapping mapping = arg.getMapping();
        String tenantId = mapping.getTenantId();
        CompareResult compareResult = new CompareResult();
        DataSupportService srcSupport = DataSupportServiceHelper.get(mapping.getSourceType());
        DataSupportService destSupport = DataSupportServiceHelper.get(mapping.getDestType());
        ListDataByTimeArg listDataByTimeArg = new ListDataByTimeArg();
        listDataByTimeArg.setTenantId(tenantId);
        listDataByTimeArg.setObjApiName(mapping.getSourceObjApiName());
        listDataByTimeArg.setFilterString(mapping.getSourceFilterString());
        listDataByTimeArg.setBeginTime(arg.getBeginTime());
        listDataByTimeArg.setEndTime(arg.getEndTime());
        if (mapping.getLimit() != null && mapping.getLimit() != 0) {
            listDataByTimeArg.setLimit(mapping.getLimit());
        }
        ListDataByIdArg listDataByIdArg = new ListDataByIdArg();
        listDataByIdArg.setTenantId(tenantId);
        listDataByIdArg.setObjApiName(mapping.getDestObjApiName());
        for (int i = 0; i < 10000; i++) {
            //1000万数据
            Result<ListDataByTimeResult> srcResult = srcSupport.listDataByTime(listDataByTimeArg);
            if (!srcResult.isSuccess()) {
                //一旦接口异常直接终止
                log.info("list data by Time error,{}", srcResult);
                return Result.newSuccess(compareResult.setResultType(CompareResultType.GET_SRC_DATA_FAILED));
            }
            ListDataByTimeResult data = srcResult.getData();
            List<CompareData> srcCompareData = data.getCompareDataList();
            if (!srcCompareData.isEmpty()) {
                compareResult.setTotalCount(compareResult.getTotalCount() + (long) srcCompareData.size());
                List<String> dataIds = srcCompareData.stream().map(v -> v.getDataId()).collect(Collectors.toList());
                listDataByIdArg.setDataIds(dataIds);
                Result<ListDataByIdResult> destResult = destSupport.listDataById(listDataByIdArg);
                if (!destResult.isSuccess()) {
                    //一旦接口异常直接终止
                    log.info("list data by id error,{}", destResult);
                    return Result.newSuccess(compareResult.setResultType(CompareResultType.GET_DEST_DATA_FAILED));
                }
                //比对数据
                ListDataByIdResult destData = destResult.getData();
                List<CompareData> destDataCompareData = destData.getCompareDataList();
                Map<String, String> destVerMap = destDataCompareData.stream().collect(Collectors.toMap(v -> StrUtil.nullToEmpty(v.getDataId()), u -> StrUtil.nullToEmpty(u.getVersion()), (v, u) -> v));
                for (CompareData srcCompareDatum : srcCompareData) {
                    String dataId = srcCompareDatum.getDataId();
                    if (!destVerMap.containsKey(dataId)) {
                        compareResult.addError(dataId, CompareDataErrorType.NOT_FOUND);
                        continue;
                    }
                    if (!ObjectUtil.equal(destVerMap.get(dataId), srcCompareDatum.getVersion())) {
                        compareResult.addError(dataId, CompareDataErrorType.VERSION_NOT_MATCH);
                    }
                }
                log.info("comparing,total:{},error:{}", compareResult.getTotalCount(), compareResult.getErrorCount());
            }
            if (data.getNextBeginTime() == null || data.getNextBeginTime() == 0L) {
                log.info("compare end,total:{},error:{}", compareResult.getTotalCount(), compareResult.getErrorCount());
                break;
            }
            if (StrUtil.equals(listDataByTimeArg.getBeginId(), data.getNextBeginId()) && data.getNextBeginTime() < listDataByTimeArg.getBeginTime()) {
                //下一个查询时间在当前查询时间之前，直接结束
                log.warn("compare end because nextBeginTime is earlier than this,total:{},error:{}", compareResult.getTotalCount(), compareResult.getErrorCount());
                break;
            }
            listDataByTimeArg.setBeginId(data.getNextBeginId());
            listDataByTimeArg.setBeginTime(data.getNextBeginTime());
        }
        return Result.newSuccess(compareResult);
    }

    @Override
    public Result<Void> refreshTask(String tenantIdOrNull) {
        List<CompareObjectMapping> compareObjectMappings = compareDao.listObjMappingAndRefreshCache(tenantIdOrNull);
        for (CompareObjectMapping compareObjectMapping : compareObjectMappings) {
            String cron = compareObjectMapping.getCompareStrategy().getCron();
            String tenantId = compareObjectMapping.getTenantId();
            String compareName = compareObjectMapping.getCompareName();
            String id = tenantId + compareName;
            if (StringUtil.isBlank(cron)) {
                log.info("compare task no start for {}", id);
                continue;
            }
            boolean remove = CronUtil.remove(id);
            log.info("compare task remove {},{}", id, remove);
            CronUtil.schedule(id, cron, new RunnableTask(() -> {
                TraceUtil.initTraceWithFormat(tenantId);
                TraceUtil.addChildTrace(compareName);
                log.info("compare task begin {}", id);
                compareAndAlert(tenantId, compareName);
                log.info("compare task end {}", id);
            }));
        }
        if (!CronUtil.getScheduler().isStarted()) {
            CronUtil.start();
            log.info("compare task start");
        }
        return Result.newSuccess();
    }
}
