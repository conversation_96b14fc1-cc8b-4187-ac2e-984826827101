package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminStreamQuery;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseLogMongoStore;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.DistinctIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class AdminStreamDao extends BaseLogMongoStore<StreamSimpleInfo> {

    private final String TENANT_ID= "1";

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                                                      .register(StreamSimpleInfo.class)
                                                      .automatic(true).build()));
    }

    protected AdminStreamDao() {
        super(AdminStreamDao.SingleCodecHolder.codecRegistry);
    }

    @Override
    public String getCollName(String tenantId) {
        return "stream_temp";
    }


    public Date getMongoLastSyncTime() {
        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        StreamSimpleInfo first = collection.find()
                .projection(Projections.include(StreamSimpleInfo.Fields.mongoLastSyncTime))
                .sort(Sorts.descending(StreamSimpleInfo.Fields.mongoLastSyncTime))
                .limit(1).first();
        if (first != null) {
            return first.getMongoLastSyncTime();
        }
        return null;
    }

    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> indexModels = new ArrayList<>();
        indexModels.add(new IndexModel(Indexes.ascending(StreamSimpleInfo.Fields.enterpriseAccount, StreamSimpleInfo.Fields.connectorKey, StreamSimpleInfo.Fields.streamId), new IndexOptions().background(true)));
        indexModels.add(new IndexModel(Indexes.ascending(StreamSimpleInfo.Fields.crmObjApiName, StreamSimpleInfo.Fields.status, StreamSimpleInfo.Fields.streamId), new IndexOptions().background(true)));
        indexModels.add(new IndexModel(Indexes.ascending(StreamSimpleInfo.Fields.mongoLastSyncTime),
                                       new IndexOptions().expireAfter(7L, TimeUnit.DAYS).background(true)));
        return indexModels;
    }

    public void batchUpsert(List<StreamSimpleInfo> infos) {
        if (CollectionUtils.isEmpty(infos)) {
            return;
        }
        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        List<UpdateOneModel<StreamSimpleInfo>> bulkOps = new ArrayList<>();
        for (StreamSimpleInfo info : infos) {
            // 收集更新字段形成updates
            List<Bson> updates = CollUtil.newArrayList(Updates.currentDate(StreamSimpleInfo.Fields.mongoLastSyncTime));
            info.setStreamLastUpdateTime(null);
            Map<String, Object> infoMap = BeanUtil.beanToMap(info, false, true);
            infoMap.forEach((k, v) -> {
                updates.add(Updates.set(k, v));
            });
            // 指定操作为upsert
            bulkOps.add(new UpdateOneModel<>(Filters.eq(StreamSimpleInfo.Fields.streamId, info.getStreamId()),
                                             Updates.combine(updates), new UpdateOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = collection.bulkWrite(bulkOps);
        return;
    }

    public void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        collection.deleteMany(Filters.in(StreamSimpleInfo.Fields.streamId, ids));
        log.info("AdminStreamDao: batchDelete " + ids);
    }

    public List<StreamSimpleInfo> pageInfo(AdminStreamQuery query) {
        int perPage = query.getPerPage();
        int page = query.getPage();
        int skipped = (page-1) * perPage;
        List<Bson> filters = obtainFilters(query);
        Bson sort = Sorts.ascending(StreamSimpleInfo.Fields.streamId);

        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        List<StreamSimpleInfo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(filters)) {
            collection.find().sort(sort).skip(skipped).limit(perPage).into(result);
        } else {
            collection.find(Filters.and(filters)).sort(sort).skip(skipped).limit(perPage).into(result);
        }
        return result;
    }

    public List<StreamSimpleInfo> getAllInfo() {
        Bson sort = Sorts.ascending(StreamSimpleInfo.Fields.streamId);

        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        List<StreamSimpleInfo> result = new ArrayList<>();
        collection.find().sort(sort).into(result);
        return result;
    }

//    public List<StreamSimpleInfo> getAllInfo(boolean test) {
//        List<StreamSimpleInfo> result = new ArrayList<>();
//        for (int i = 0; i < 60000; i++) {
//            StreamSimpleInfo info = new StreamSimpleInfo();
//            info.setStreamId(UUID.randomUUID().toString());
//            info.setTenantId("fktest"+i)
//                    .setEnterpriseAccount("测试用数据"+i)
//                    .setIntegrationStreamName("测试用集成流"+i)
//                    .setStatus(1)
//                    .setErpDcId(UUID.randomUUID().toString())
//                    .setSourceTenantType(RandomUtil.randomInt(1, 3))
//                    .setCrmObjApiName("crmObjApiName_" + RandomUtil.randomString(15))
//                    .setErpObjApiName("erpObjApiName_" + RandomUtil.randomString(15))
//                    .setConnectorKey(RandomUtil.randomEle(ErpChannelEnum.values()).name())
//                    .setStreamLastUpdateTime(RandomUtil.randomLong(1625068980000L, 1720454400000L))
//                    .setMongoLastSyncTime(new Date(RandomUtil.randomLong(1625068980000L, 1720454400000L)));
//            result.add(info);
//        }
//        return result;
//    }

    public int filteredCount(AdminStreamQuery query) {
        List<Bson> filters = obtainFilters(query);
        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        int count;
        if (CollectionUtils.isEmpty(filters)) {
            count = (int) collection.countDocuments();
        } else {
            count = (int) collection.countDocuments(Filters.and(filters));
        }
        return count;
    }

    private List<Bson> obtainFilters(AdminStreamQuery query) {
        if (Objects.isNull(query)) {
            return new ArrayList<>();
        }
        String eaFilter = query.getEa();
        Set<String> eaFilters = null;
        if (StrUtil.isNotEmpty(eaFilter)) {
            eaFilters = new HashSet<>(StrUtil.split(eaFilter, ","));
        }
        String connectorKeyFilter = query.getConnectorKey();
        Set<String> connectorKeyFilters = null;
        if (StrUtil.isNotEmpty(connectorKeyFilter)) {
            connectorKeyFilters = new HashSet<>(StrUtil.split(connectorKeyFilter, ","));
        }
        String apiNameFilter = query.getCrmObjApiName();
        Set<String> apiNameFilters = null;
        if (StrUtil.isNotEmpty(apiNameFilter)) {
            apiNameFilters = new HashSet<>(StrUtil.split(apiNameFilter, ","));
        }
        String statusFilter = query.getStatus();
        Set<Integer> statusFilters = null;
        if (StrUtil.isNotEmpty(statusFilter)) {
            statusFilters = StrUtil.split(statusFilter, ",").stream()
                    .map(v -> Opt.ofTry(() -> Integer.parseInt(v)).get())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        List<Bson> filters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(eaFilters)) {
            filters.add(Filters.in(StreamSimpleInfo.Fields.enterpriseAccount, eaFilters));
        }
        if (CollectionUtils.isNotEmpty(connectorKeyFilters)) {
            filters.add(Filters.in(StreamSimpleInfo.Fields.connectorKey, connectorKeyFilters));
        }
        if (CollectionUtils.isNotEmpty(apiNameFilters)) {
            filters.add(Filters.in(StreamSimpleInfo.Fields.crmObjApiName, apiNameFilters));
        }
        if (CollectionUtils.isNotEmpty(statusFilters)) {
            filters.add(Filters.in(StreamSimpleInfo.Fields.status, statusFilters));
        }
        return filters;
    }

    /**
     * 因为要全量查询id，一次IO可能压力太大？先用着吧
     * @return
     */
    public List<String> getAllStreamIds() {
        MongoCollection<StreamSimpleInfo> collection = getOrCreateCollection(TENANT_ID);
        DistinctIterable<String> ids = collection.distinct(StreamSimpleInfo.Fields.streamId, String.class);
        List<String> result = Lists.newArrayList(ids);
        log.info("AdminStreamDao: getAllStreamIds" + result.size());
        return result;
    }

}
