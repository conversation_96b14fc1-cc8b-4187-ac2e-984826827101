package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity.Fields;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 * 告警聚合表
 *
 * <AUTHOR>
 * @date 2023.07.30
 */
@Repository
@Slf4j
public class AlertAggregationDao {

    @Qualifier("erpSyncDataLogMongoStore")
    @Autowired
    private DatastoreExt store;
    private String DATABASE;

    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
        createIndex();
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<AlertAggregationEntity> collection = getCollection();

        // TTL过期索引
        Bson expireIdx = Indexes.ascending(Fields.createTime);

        Bson idx = Indexes.ascending(Fields.tenantId,
                Fields.dataCenterId,
                Fields.ployDetailId,
                Fields.alertAggregationType,
                Fields.alarmLevel);


        try {
            //告警数据有效期24小时
            collection.createIndex(expireIdx, new IndexOptions()
                    .expireAfter(24L, TimeUnit.HOURS));

            collection.createIndex(idx, new IndexOptions()
                    .unique(true));
        } catch (Exception e) {
            log.info("AlertAggregationDao.createIndex,failed,exception={}", e.getMessage());
        }
    }


    private MongoCollection<AlertAggregationEntity> getCollection() {
        MongoCollection<AlertAggregationEntity> collection = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("alert_aggregation", AlertAggregationEntity.class);
        return collection;
    }

    public List<AlertAggregationEntity> getDataList(String tenantId, int limit) {
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId));

        return getDataList(filters, limit);
    }

    public List<AlertAggregationEntity> getDataList(String tenantId, String dcId, int limit) {
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId));

        return getDataList(filters, limit);
    }

    public List<AlertAggregationEntity> getDataList(String tenantId,
                                                    String dcId,
                                                    String ployDetailId,
                                                    AlertAggregationType alertAggregationType,
                                                    int limit) {
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));

        return getDataList(filters, limit);
    }

    public Map<String, List<AlertAggregationEntity>> getPollingErpAlertRecoverDataList(int limit) {
        Bson filters = and(
                Filters.eq(Fields.alertRecover, true),
                Filters.eq(Fields.alertAggregationType, AlertAggregationType.POLLING_ERP_ALERT.name()));

        List<AlertAggregationEntity> dataList = getDataList(filters, limit);
        return getTenantDcDataMap(dataList);
    }

    /**
     * 获取告警聚合数据列表
     *
     * @param aggregationTime 告警聚合时间，单位  ms
     * @param limit
     * @return
     */
    public Map<String, List<AlertAggregationEntity>> getAlertAggregationDataList(AlertAggregationType alertAggregationType,
                                                                                 long aggregationTime,
                                                                                 int limit) {
        Bson filters = and(
                Filters.eq(Fields.alertRecover, false),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()),
                Filters.lte(Fields.lastAlertTime, System.currentTimeMillis() - aggregationTime),
                Filters.gt(Fields.count, 0));

        List<AlertAggregationEntity> dataList = getDataList(filters, limit);
        return getTenantDcDataMap(dataList);
    }

    /**
     * 获取告警中的数据列表，按企业进行聚合
     *
     * @param startTime 开始时间，单位  ms
     * @param limit
     * @return
     */
    public Map<String, List<AlertAggregationEntity>> getAlertDataList(long startTime,
                                                                      int limit) {
        Bson filters = and(
                Filters.eq(Fields.alertRecover, false),
                Filters.gte(Fields.createTime, new Date(startTime)),
                Filters.lt(Fields.createTime, new Date(System.currentTimeMillis())));

        List<AlertAggregationEntity> dataList = getDataList(filters, limit);
        return getTenantDataMap(dataList);
    }

    /**
     * 对告警数据按告警级别进行聚合
     *
     * @param entityList
     * @return
     */
    public Map<AlarmLevel, List<AlertAggregationEntity>> getAlertDataMap(List<AlertAggregationEntity> entityList) {
        Map<AlarmLevel, List<AlertAggregationEntity>> map = new HashMap<>();
        if(CollectionUtils.isEmpty(entityList)) return map;

        for(AlertAggregationEntity entity : entityList) {
            if(!map.containsKey(entity.getAlarmLevel())) {
                map.put(entity.getAlarmLevel(), Lists.newArrayList(entity));
            } else {
                map.get(entity.getAlarmLevel()).add(entity);
            }
        }
        return map;
    }

    private Map<String, List<AlertAggregationEntity>> getTenantDataMap(List<AlertAggregationEntity> dataList) {
        Map<String, List<AlertAggregationEntity>> dataMap = new HashMap<>();
        if(CollectionUtils.isEmpty(dataList)) return dataMap;

        for (AlertAggregationEntity dataEntity : dataList) {
            String key = dataEntity.getTenantId();
            if (!dataMap.containsKey(key)) {
                List<AlertAggregationEntity> list = new ArrayList<>();
                list.add(dataEntity);
                dataMap.put(key, list);
            } else {
                dataMap.get(key).add(dataEntity);
            }
        }

        return dataMap;
    }

    private Map<String, List<AlertAggregationEntity>> getTenantDcDataMap(List<AlertAggregationEntity> dataList) {
        Map<String, List<AlertAggregationEntity>> dataMap = new HashMap<>();
        if(CollectionUtils.isEmpty(dataList)) return dataMap;

        for (AlertAggregationEntity dataEntity : dataList) {
            String key = dataEntity.getTenantId() + "-" + dataEntity.getDataCenterId();
            if (!dataMap.containsKey(key)) {
                List<AlertAggregationEntity> list = new ArrayList<>();
                list.add(dataEntity);
                dataMap.put(key, list);
            } else {
                dataMap.get(key).add(dataEntity);
            }
        }

        return dataMap;
    }

    public Map<String, List<AlertAggregationEntity>> getTenantDcPloyDetailDataMap(List<AlertAggregationEntity> dataList) {
        Map<String, List<AlertAggregationEntity>> dataMap = new HashMap<>();
        if(CollectionUtils.isEmpty(dataList)) return dataMap;

        for (AlertAggregationEntity dataEntity : dataList) {
            String key = dataEntity.getTenantId() + "-" + dataEntity.getDataCenterId() + "-" + dataEntity.getPloyDetailId();
            if (!dataMap.containsKey(key)) {
                List<AlertAggregationEntity> list = new ArrayList<>();
                list.add(dataEntity);
                dataMap.put(key, list);
            } else {
                dataMap.get(key).add(dataEntity);
            }
        }

        return dataMap;
    }

    private List<AlertAggregationEntity> getDataList(Bson filters, int limit) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        List<AlertAggregationEntity> totalDataList = new ArrayList<>();
        int offset = 0;
        while (true) {
            List<AlertAggregationEntity> dataList = new ArrayList<>();
            collection.find(filters).skip(offset).limit(limit).into(dataList);
            if (CollectionUtils.isEmpty(dataList)) break;
            totalDataList.addAll(dataList);
            if (dataList.size() == limit) {
                offset += limit;
            } else {
                break;
            }
        }

        return totalDataList;
    }

    public Map<String, AlertAggregationEntity> getTenantDcPloyDetailDataMap() {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = Filters.exists(Fields.tenantId);

        MongoCursor<AlertAggregationEntity> iterator = collection.find(filters).iterator();
        Map<String, AlertAggregationEntity> map = new HashMap<>();
        while (iterator.hasNext()) {
            AlertAggregationEntity entity = iterator.tryNext();
            if (entity == null) continue;
            String key = entity.getTenantId() + "-" + entity.getDataCenterId() + "-" + entity.getPloyDetailId();
            map.putIfAbsent(key, entity);
        }
        return map;
    }

    public AlertAggregationEntity getData(String tenantId, String dcId, String ployDetailId, AlertAggregationType alertAggregationType) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));
        if (StringUtils.isNotEmpty(ployDetailId)) {
            filters = and(filters,
                    Filters.eq(Fields.ployDetailId, ployDetailId));
        }
        AlertAggregationEntity first = collection.find(filters).first();
        return first;
    }

    public void insert(String tenantId,
                       String dcId,
                       String ployDetailId,
                       AlertAggregationType alertAggregationType,
                       AlarmLevel alarmLevel,
                       Long lastAlertTime,
                       Integer count,
                       Boolean alertRecover,
                       String errCode,
                       String errMsg,
                       String traceMsg) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();

        AlertAggregationEntity entity = new AlertAggregationEntity();
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dcId);
        entity.setPloyDetailId(ployDetailId);
        entity.setAlertAggregationType(alertAggregationType);
        entity.setAlarmLevel(alarmLevel);
        entity.setLastAlertTime(lastAlertTime);
        entity.setCount(count);
        entity.setAlertRecover(alertRecover);
        entity.setErrCode(errCode);
        entity.setErrMsg(errMsg);
        entity.setTraceMsg(StringUtils.isEmpty(traceMsg) ? TraceUtil.get() : traceMsg);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        collection.insertOne(entity);
        log.info("AlertAggregationDao.insert,entity={}", entity);
    }

    public void replace(AlertAggregationEntity entity) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, entity.getTenantId()),
                Filters.eq(Fields.dataCenterId, entity.getDataCenterId()),
                Filters.eq(Fields.ployDetailId, entity.getPloyDetailId()),
                Filters.eq(Fields.alertAggregationType, entity.getAlertAggregationType().name()));

        if (StringUtils.isEmpty(entity.getTraceMsg())) {
            entity.setTraceMsg(TraceUtil.get());
        }

        UpdateResult updateResult = collection.replaceOne(filters, entity);
        log.info("AlertAggregationDao.replace,entity={},updateResult={}", entity, updateResult);
    }

    public long batchUpdateLastAlertTime(String tenantId,
                                         String dcId,
                                         List<String> ployDetailIdList,
                                         AlertAggregationType alertAggregationType,
                                         Long lastAlertTime) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.in(Fields.ployDetailId, ployDetailIdList),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));

        List<Bson> updates = new ArrayList<>();
        updates.add(set(Fields.lastAlertTime, lastAlertTime));
        updates.add(set(Fields.updateTime, new Date(lastAlertTime)));
        updates.add(set(Fields.count, 0));

        Bson update = combine(updates);

        UpdateResult updateResult = collection.updateMany(filters, update, new UpdateOptions().upsert(false));
        log.info("AlertAggregationDao.batchUpdateLastAlertTime,filters={},update={},updateResult={}", filters, update, updateResult);
        return updateResult.getModifiedCount();
    }

    public void insertOrUpdate(String tenantId,
                               String dcId,
                               String ployDetailId,
                               AlertAggregationType alertAggregationType,
                               AlarmLevel alarmLevel,
                               Long lastAlertTime,
                               Integer count,
                               Boolean alertRecover,
                               String errCode,
                               String errMsg,
                               String traceMsg) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));

        AlertAggregationEntity entity = collection.find(filters).first();
        if (entity != null) {
            entity.setLastAlertTime(lastAlertTime);
            entity.setCount(count);
            entity.setAlertRecover(alertRecover);
            entity.setErrCode(errCode);
            entity.setErrMsg(errMsg);
            entity.setTraceMsg(StringUtils.isEmpty(traceMsg) ? TraceUtil.get() : traceMsg);
            UpdateResult updateResult = collection.replaceOne(filters, entity);
            log.info("AlertAggregationDao.replace,entity={},updateResult={}", entity, updateResult);
        } else {
            insert(tenantId,
                    dcId,
                    ployDetailId,
                    alertAggregationType,
                    alarmLevel,
                    lastAlertTime,
                    count,
                    alertRecover,
                    errCode,
                    errMsg,
                    traceMsg);
        }
    }

    public long delete(String tenantId, String dcId, String ployDetailId, AlertAggregationType alertAggregationType) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));

        DeleteResult result = collection.deleteOne(filters);
        log.info("AlertAggregationDao.delete,tenantId={},dcId={},ployDetailId={},aggregationAlertType={},result={}", tenantId,
                dcId,
                ployDetailId,
                alertAggregationType,
                result);
        return result.getDeletedCount();
    }

    public long deleteMany(String tenantId) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId));

        DeleteResult result = collection.deleteMany(filters);
        log.info("AlertAggregationDao.deleteMany,tenantId={},result={}", tenantId,
                result);
        return result.getDeletedCount();
    }

    public long deleteMany(String tenantId, String dcId, AlertAggregationType alertAggregationType) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));

        DeleteResult result = collection.deleteMany(filters);
        log.info("AlertAggregationDao.deleteMany,tenantId={},dcId={},aggregationAlertType={},result={}", tenantId,
                dcId,
                alertAggregationType,
                result);
        return result.getDeletedCount();
    }

    public long deleteMany(AlertAggregationType alertAggregationType, boolean alertRecover) {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.eq(Fields.alertRecover, alertRecover),
                Filters.eq(Fields.alertAggregationType, alertAggregationType.name()));

        DeleteResult result = collection.deleteMany(filters);
        log.info("AlertAggregationDao.deleteMany,aggregationAlertType={},alertRecover={},result={}", alertAggregationType,
                alertRecover,
                result);
        return result.getDeletedCount();
    }

    /**
     * 删除所有告警聚合数据
     * @return
     */
    public long deleteAll() {
        MongoCollection<AlertAggregationEntity> collection = getCollection();
        Bson filters = and(
                Filters.ne(Fields.tenantId, null));

        DeleteResult result = collection.deleteMany(filters);
        log.info("AlertAggregationDao.deleteAll,result={}", result);
        return result.getDeletedCount();
    }
}
