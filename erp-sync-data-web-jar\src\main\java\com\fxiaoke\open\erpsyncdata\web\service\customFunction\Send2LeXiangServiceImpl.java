package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.Send2LeXiangArg;
import com.fxiaoke.open.erpsyncdata.admin.service.LeXiangService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 腾讯乐享自定义函数相关接口封装
 * <AUTHOR>
 * @date 2023.05.25
 */
@Slf4j
@Service("send2LeXiang")
public class Send2LeXiangServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private LeXiangService leXiangService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("Send2LeXiangServiceImpl.executeLogic,arg={}", arg);
        Send2LeXiangArg send2LeXiangArg = JSONObject.parseObject(arg.getParams(), Send2LeXiangArg.class);
        if(send2LeXiangArg==null
                || StringUtils.isEmpty(send2LeXiangArg.getTitle())
                || StringUtils.isEmpty(send2LeXiangArg.getHtml()))
            return Result.newError(ResultCodeEnum.PARAM_ERROR);

        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(arg.getTenantId()));
        log.info("Send2LeXiangServiceImpl.executeLogic,ea={}", ea);

        String traceId = TraceUtil.get();
        Thread thread = new Thread(()->{
            TraceUtil.initTrace(traceId);
            Result<JSONObject> result = leXiangService.sendHtmlWorkOrder(ConfigCenter.lexiangAppId,
                    ConfigCenter.lexiangAppSecret,
                    ea,
                    send2LeXiangArg.getTitle(),
                    send2LeXiangArg.getHtml(),
                    send2LeXiangArg.getNPathList());
            log.info("Send2LeXiangServiceImpl.executeLogic,sendHtmlWorkOrder,result={}", result);
        },"send2LeXiang");
        thread.start();

        return Result.newSuccess();
    }
}
