package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import cn.hutool.core.util.EnumUtil;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

/**
 * <AUTHOR> (^_−)☆
 */
public class SafeEnumCodec<T extends Enum<T>> implements Codec<T> {
    private final Class<T> clazz;
    private final T defaultValue;

    private SafeEnumCodec(final Class<T> clazz, final T defaultValue) {
        this.clazz = clazz;
        this.defaultValue = defaultValue;
    }

    public static <E extends Enum<E>> SafeEnumCodec<E> of(final Class<E> clazz, final E defaultValue) {
        return new SafeEnumCodec<>(clazz, defaultValue);
    }

    public static <E extends Enum<E>> SafeEnumCodec<E> of(final Class<E> clazz) {
        return new SafeEnumCodec<>(clazz, null);
    }

    @Override
    public void encode(final BsonWriter writer, final T value, final EncoderContext encoderContext) {
        writer.writeString(value.name());
    }

    @Override
    public Class<T> getEncoderClass() {
        return clazz;
    }

    @Override
    public T decode(final BsonReader reader, final DecoderContext decoderContext) {
        return EnumUtil.fromString(clazz, reader.readString(), defaultValue);
    }
}
