package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/6 16:09 链路日志请求的接口
 * @Version 1.0
 */
public interface BaseLinkWalkingService {
    /**
     * 根据类型返回节点日志
     * @param tenantId
     * @param streamLogQueryArg
     * @return
     */
    Result<?> queryListLogByType(String tenantId, StreamLogQueryArg streamLogQueryArg,String lang);


    Result<?> queryListByLogIds(String tenantId, final String realObjApiName, List<String> logIds, final StreamLogQueryArg streamLogQueryArg,String lang);
    /**
     * 根据id查询数据
     */
    default Result<?> queryLogDataById(String tenantId, StreamLogQueryArg.SyncLogDataArg syncLogDataArg, String lang, SyncLog syncLog){
         return Result.newSuccess();
     }
}
