package com.fxiaoke.open.erpsyncdata.web.service.customFunction;


import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncByCrmDataIdArg;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.AdminSyncDataMappingServiceImpl;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 10:15 2021/3/18
 * @Desc:
 */
@Service("manualSyncErpDataByCrmDataId")
@Slf4j
public class ManualSyncErpDataByCrmDataIdServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private AdminSyncDataMappingServiceImpl adminSyncDataMappingService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        SyncByCrmDataIdArg arg = JsonUtil.fromJson(commonArg.getParams(), SyncByCrmDataIdArg.class);
        if (arg == null || StringUtils.isBlank(arg.getCrmDataId()) || StringUtils.isBlank(arg.getCrmObjectApiName()) || StringUtils.isBlank(arg.getErpObjectApiName())) {
            log.info("executeLogic params error commonArg={} arg={}", commonArg, arg);
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        SyncDataMappingsEntity destDataMapping = syncDataMappingsDao.setTenantId(commonArg.getTenantId()).getByDestData(commonArg.getTenantId(), arg.getErpObjectApiName(), arg.getCrmObjectApiName(), arg.getCrmDataId());
        if (destDataMapping == null) {
            return Result.newError("-1", I18NStringEnum.s24, arg.getCrmDataId());
        }
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setObjAPIName(arg.getErpObjectApiName());
        erpIdArg.setDataId(destDataMapping.getSourceDataId());
        erpIdArg.setTenantId(commonArg.getTenantId());
        Result<Void> sendResult = adminSyncDataMappingService.syncSingletonData(erpIdArg, arg.getCrmObjectApiName(), DataReceiveTypeEnum.FUNCTION_TRIGGER);
        if (!sendResult.isSuccess()) {
            return Result.copy(sendResult);
        }
        return Result.newSuccessByI18N(null,I18NStringEnum.s25.getI18nKey(),null);
    }
}
