package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * 异步任务
 * s306243521 为执行中
 *
 * <AUTHOR> (^_−)☆
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class AsyncTaskResult<T> extends Result<T> {
    /**
     * 任务key，用于获取任务结果
     */
    private String taskKey;
    /**
     * 任务名称
     */
    private String taskName;

    public AsyncTaskResult<T> success(T data) {
        this.setErrCode(ResultCodeEnum.SUCCESS.getErrCode());
        this.setErrMsg(ResultCodeEnum.SUCCESS.getText());
        this.setI18nKey(null);
        this.setData(data);
        return this;
    }

    public AsyncTaskResult<T> error(String errMsg) {
        this.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
        this.setErrMsg(errMsg);
        return this;
    }

    public AsyncTaskResult<T> error(String errCode, String errMsg) {
        this.setErrCode(errCode);
        this.setErrMsg(errMsg);
        return this;
    }

    public AsyncTaskResult<T> error(ResultCodeEnum resultCodeEnum) {
        this.setErrCode(resultCodeEnum.getErrCode());
        this.setErrMsg(resultCodeEnum.getText());
        return this;
    }

    public AsyncTaskResult<T> finish() {
        //防止未正常完成
        if (Objects.equals(this.getErrCode(), ResultCodeEnum.ASYNC_TASK_IS_RUNNING.getErrCode())) {
            this.setErrCode(ResultCodeEnum.SUCCESS.getErrCode());
            this.setErrMsg(ResultCodeEnum.SUCCESS.getText());
        }
        return this;
    }

    public AsyncTaskResult<T> copyFrom(AsyncTaskResult<?> result) {
        super.copyFrom(result);
        this.setTaskName(result.getTaskName());
        this.setTaskKey(result.getTaskKey());
        return this;
    }

    public static <T> AsyncTaskResult<T> create(String taskKey, String taskName) {
        AsyncTaskResult<T> task = new AsyncTaskResult<>();
        task.setTaskKey(taskKey);
        task.setTaskName(taskName);
        task.setErrCode(ResultCodeEnum.ASYNC_TASK_IS_RUNNING.getErrCode());
        task.setErrMsg(ResultCodeEnum.ASYNC_TASK_IS_RUNNING.getText());
        return task;
    }


    public static <T> AsyncTaskResult<T> copyError(BaseResult result) {
        AsyncTaskResult<T> task = new AsyncTaskResult<>();
        task.copyFrom(result);
        return task;
    }
}
