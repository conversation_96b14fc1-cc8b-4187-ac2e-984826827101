package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.model.MultiRequest;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MultiDomain;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.open.erpsyncdata.web.interceptor.SuperAdminTokenUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Request;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 多云支持
 * 混合云可以直接API访问修改
 * 私有云规划使用文件补丁，目前不支持。
 *
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/multicloud")
@Slf4j
public class MulticloudSupportController extends BaseController {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;


    @GetMapping("listMultiDomains")
    Result<List<MultiDomain>> listMultiDomains() {
        return Result.newSuccess(getMutiDomainListCopy());
    }

    /**
     * 从纷享获取发起支持的云的请求
     *
     * @param multiRequest
     * @return
     */
    @PostMapping("multiRequestFromFs")
    Result<Map<String, Result<?>>> multiRequestFromFs(@RequestBody MultiRequest multiRequest) {
        return Result.newSuccess(executeMultiRequest(multiRequest));
    }


    /**
     * 跨云请求，返回list
     *
     * @param multiRequest
     * @return
     */
    @PostMapping("multiRequestFromFsList")
    Result<Amis.Crud<Dict>> multiRequestFromFsList(@RequestBody MultiRequest multiRequest) {
        Map<String, Result<?>> multiResponse = executeMultiRequest(multiRequest);
        List<Dict> result = new ArrayList<>();
        multiResponse.forEach((k, v) -> {
            Dict dict = new Dict();
            dict.put("success", v.isSuccess());
            dict.put("msg", v.getErrMsg());
            dict.put("domain", k);
            if (!v.isSuccess()) {
                //失败
                result.add(dict);
            } else {
                if (v.getData() instanceof List) {
                    ((List<?>) v.getData()).forEach(it -> {
                        if (it instanceof Map) {
                            //每条一个
                            Dict dict2 = dict.clone();
                            dict2.parseBean(it);
                            result.add(dict2);
                        }
                    });
                } else {
                    if (v.getData() instanceof Map) {
                        dict.parseBean(v.getData());
                    }
                    result.add(dict);
                }
            }
        });
        Amis.Crud<Dict> dictCrud = Amis.Crud.of(result);
        return Result.newSuccess(dictCrud);
    }

    private @NotNull Map<String, Result<?>> executeMultiRequest(MultiRequest multiRequest) {
        //构建token
        UserVo superAdmin = getUserVo();
        if (superAdmin == null) {
            throw new ErpSyncDataException("not superAdmin");
        }
        List<MultiDomain> multiDomains = getMutiDomainListCopy();
        if (CollUtil.isNotEmpty(multiRequest.getDomains())) {
            multiDomains.removeIf(v -> !multiRequest.getDomains().contains(v.getDomain()));
        }
        String token = SuperAdminTokenUtil.generateToken(superAdmin);
        final Map<String, String> headers = Opt.ofNullable(multiRequest.getHeaderMap()).orElseGet(HashMap::new);
        headers.put("Cookie", "ESAToken=" + token);
        Map<String, Result<?>> resultMap = new ConcurrentHashMap<>();
        //仅获取纷享云和混合云
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        for (MultiDomain multiDomain : multiDomains) {
            //多线程处理
            parallelTask.submit(() -> {
                Headers headers1 = Headers.of(headers);
                String domain = multiDomain.getDomain();
                if (multiDomain.isAbleCallFromFs()) {
                    Result<?> thisRes;
                    //鉴权
                    String url = "https://" + domain + multiRequest.getPath();
                    String method = multiRequest.getMethod();
                    if (StrUtil.equalsIgnoreCase(method, "get")) {
                        thisRes = get(url, headers1);
                    } else if (StrUtil.equalsIgnoreCase(method, "post")) {
                        thisRes = post(url, headers1, multiRequest.getRequestBody());
                    } else {
                        thisRes = Result.newError("not support method：" + method);
                    }
                    resultMap.put(domain, thisRes);
                } else {
                    resultMap.put(domain, Result.newError("not support call from fs"));
                }
            });
        }
        try {
            boolean await = parallelTask.await(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.warn("parallel task timeout");
        }
        return resultMap;
    }

    private List<MultiDomain> getMutiDomainListCopy() {
        List<MultiDomain> multiDomains = plusTenantConfigManager.getGlobalObjConfig(TenantConfigurationTypeEnum.MULTI_CLOUD_DOMAINS, new TypeReference<List<MultiDomain>>() {
        });
        if (multiDomains == null) {
            throw new ErpSyncDataException("config not exist");
        }
        multiDomains = CollUtil.newArrayList(multiDomains);
        for (MultiDomain multiDomain : multiDomains) {
            multiDomain.setFullName(String.format("%s(%s)", multiDomain.getName(), multiDomain.getDomain()));
        }
        return multiDomains;
    }

    private Result<?> get(String url, Headers headers) {
        Request.Builder builder = new Request.Builder().url(url).headers(headers).get();
        return request(builder);
    }


    private Result<?> post(String url, Headers headers, Object resBody) {
        Request.Builder builder = new Request.Builder().url(url).headers(headers).post(createRequestBody(resBody));
        return request(builder);
    }

    private Result<?> request(Request.Builder resBuilder) {
        try {
            String result = proxyHttpClient.simpleRequest(resBuilder, 10);
            Result<?> result1 = JacksonUtil.fromJson(result, Result.class);
            return result1;
        } catch (Exception e) {
            return Result.wrapException(e);
        }
    }


    private okhttp3.RequestBody createRequestBody(Object body) {
        okhttp3.RequestBody requestBody;
        final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
        if (body instanceof String) {
            String bodyStr = (String) body;
            if (JSONUtil.isJson(bodyStr)) {
                requestBody = okhttp3.RequestBody.create(body.toString(), JSON_TYPE);
            } else {
                final MediaType TEXT_TYPE = MediaType.parse("text/plain");
                requestBody = okhttp3.RequestBody.create(body.toString(), TEXT_TYPE);
            }
        } else {
            requestBody = okhttp3.RequestBody.create(JacksonUtil.toJson(body), JSON_TYPE);
        }
        return requestBody;
    }
}
