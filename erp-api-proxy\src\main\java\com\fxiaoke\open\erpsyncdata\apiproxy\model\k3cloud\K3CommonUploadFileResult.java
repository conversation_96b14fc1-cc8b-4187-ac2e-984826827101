package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/10
 */
@Data
public class K3CommonUploadFileResult implements Serializable {
    @JsonProperty("Result") // 添加注解来匹配JSON中的字段名
    private CommonUploadFileResult result;

    @Data
    public static class CommonUploadFileResult implements Serializable {
        @JsonProperty("FileId")
        private String fileId;
        @JsonProperty("Message")
        private String message;
        @JsonProperty("ResponseStatus")
        private ResponseStatus responseStatus;

        @Data
        public static class ResponseStatus implements Serializable {
            @JsonProperty("IsSuccess") // 添加注解来匹配JSON中的字段名
            private Boolean isSuccess;
            @JsonProperty("ErrorCode")
            private Integer errorCode;
            @JsonProperty("MsgCode")
            private Integer msgCode;
            @JsonProperty("Errors")
            private Object[] errors;
            @JsonProperty("SuccessEntitys")
            private Object[] successEntitys;
            @JsonProperty("SuccessMessages")
            private Object[] successMessages;
        }
    }
}
