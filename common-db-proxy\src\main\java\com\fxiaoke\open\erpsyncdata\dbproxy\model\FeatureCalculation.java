package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 特征计算方法
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/5/20
 */
@Data
public class FeatureCalculation {

    private CalType calType = CalType.PART_FIELDS;

    /**
     * 字段筛选
     */
    private FieldConfig fieldConfig;

    /**
     * 默认情况下不启用多线程
     */
    private Boolean enableParallel = false;

    public enum CalType {
        /**
         * 筛选部分字段
         */
        PART_FIELDS
    }

    @Data
    public static class FieldConfig {
        /**
         * 主对象字段
         */
        private List<String> masterObjFields = new ArrayList<>();
        /**
         * 明细字段
         */
        private Map<String, List<String>> detailObjFields = new HashMap<>();
    }

}
