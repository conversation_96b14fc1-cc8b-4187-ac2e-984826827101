package com.fxiaoke.open.erpsyncdata.apiproxy.manager.jdy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ProductCategory;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.errors.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.*;


/**
 * @Author: yancd
 * @Date: 2021/12/28 10:24
 */
@Component
@Slf4j
public class KingdeeCloudUtil {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    private static final String[] DEFAULT_SIGNHEADERS = new String[]{"X-Api-Nonce", "X-Api-TimeStamp"};
    private final Random rand = new SecureRandom();




    public Object executeGetToken(String clientId, String clientSecret, String domain, String interfacePath, Map<String, Object> paramMap) {
        try {
            log.info("executeGetToken host:{}, interfacePath:{},  paramMap:{}", domain, interfacePath, paramMap);
            String reqUrl = domain + interfacePath;
            Map<String, String> headerMap = getHeader(clientId, clientSecret, "GET", interfacePath, paramMap);

            Iterator params = paramMap.entrySet().iterator();

            while(params.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry)params.next();
                String key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.toString());
                String value = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.toString());
                paramMap.put(key, value);
            }
            if (MapUtils.isNotEmpty(paramMap)) {
                String path = Joiner.on("&").withKeyValueSeparator("=").join(paramMap);
                reqUrl = Joiner.on("?").join(reqUrl, path);
            }
//            Object responseMessage = proxyHttpClient.getUrl(reqUrl, headerMap, new TypeReference<Map>() {
//            });


            return null;
        } catch (Exception e) {
            log.error("kingdee executeGet error, e:{}", e);
            return null;
        }
    }



    public Map<String, String> getHeader(String clientId, String clientSecret, String method, String path, Map<String, Object> params) {
        Map<String, String> headerMap = Maps.newHashMap();

        headerMap.put("X-Api-ClientID", clientId);
        headerMap.put("X-Api-Auth-Version", "2.0");
        headerMap.put("X-Api-TimeStamp", "1697511963732");
        headerMap.put("X-Api-Nonce", "1697511159389");
        headerMap.put("X-Api-SignHeaders", "X-Api-Nonce,X-Api-TimeStamp");
        headerMap.put("X-Api-Signature",
                getSignature(method, getPathEncode(path), getQueryEncode(params), DEFAULT_SIGNHEADERS, headerMap, clientSecret));

        return headerMap;
    }

    @SneakyThrows
    private String getQueryEncode(Map<String, Object> querys) {
        if (Objects.isNull(querys)) {
            return "";
        } else {
            List<String> list = new ArrayList(querys.size());
            Iterator var3 = querys.entrySet().iterator();

            while(var3.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry)var3.next();
                String key = URLEncoder.encode((String)entry.getKey(), StandardCharsets.UTF_8.toString());
                String value = URLEncoder.encode((String)entry.getValue(), StandardCharsets.UTF_8.toString());
                list.add(key + "=" + value);
            }

            Collections.sort(list);
            String rawQueryString = String.join("&", list);
            String[] queryStrings = rawQueryString.split("&");
            list.clear();
            String[] var15 = queryStrings;
            int var16 = queryStrings.length;

            for(int var7 = 0; var7 < var16; ++var7) {
                String param = var15[var7];
                int index = param.indexOf("=");
                if (index >= 1) {
                    String key = URLEncoder.encode(param.substring(0, index), StandardCharsets.UTF_8.toString());
                    String value = URLEncoder.encode(param.substring(index + 1), StandardCharsets.UTF_8.toString());
                    list.add(key + "=" + value);
                }
            }

            return String.join("&", list);
        }
    }


    private String getPathEncode(String path) {
        try {
            return URLEncoder.encode(path, StandardCharsets.UTF_8.toString());
        } catch (Exception var3) {
            throw new ApiException(var3);
        }
    }


    private String getTimestamp() {
        return Long.toString(System.currentTimeMillis());
    }

    public String getNonce(int len) {
        StringBuilder rs = new StringBuilder();

        for (int i = 0; i < len; ++i) {
            rs.append(this.rand.nextInt(10));
        }

        return rs.toString();
    }

    public String getSignature(String method, String path, String query, String[] signatureHeaders, Map<String, String> headers, String clientSecret) {
        StringBuilder b = new StringBuilder();
        b.append(method);
        b.append("\n");
        b.append(path);
        b.append("\n");
        b.append(query);
        b.append("\n");
        String[] var7 = signatureHeaders;
        int var8 = signatureHeaders.length;

        for (int var9 = 0; var9 < var8; ++var9) {
            String x = var7[var9];
            b.append(x.toLowerCase());
            b.append(":");
            b.append((String) headers.get(x));
            b.append("\n");
        }

        String s = SHAUtil.SHA256HMAC(b.toString(), clientSecret);
        return s != null ? Base64.encodeBase64String(s.getBytes()) : "";
    }
    public Object executePushToken(String clientId, String clientSecret, String domain, String interfacePath,
                                              Map<String, Object> headerArgMap, Map<String, Object> paramMap) {
        try {
            log.info("executePushToken host:{}, interfacePath:{},  paramMap:{}", domain, interfacePath, headerArgMap);
            String reqUrl = domain + interfacePath;
            Map<String, String> headerMap = getHeader(clientId, clientSecret, "POST", interfacePath, headerArgMap);


        return null;
//            log.info("executeGet interfacePath:{}, paramMap:{}, responseMessage:{}", interfacePath, paramMap, responseMessage);
//
//            return parseHttpResult(responseMessage);
        } catch (Exception e) {
            log.error("kingdee executeGet error, e:{}", e);
            return null;
        }
    }
    public static void main(String[] args) {

        Map<String, Object> param = Maps.newHashMap();
        KingdeeCloudUtil kingdeeCloudUtil=new KingdeeCloudUtil();
        param.put("app_signature","ZTQ0MzdiNDExYWVkZDgzY2Q3NTcwYTNiOWFjYTA5N2U3MDViM2VjM2U1MGFiZWFmZDA0NmJhYzQwODhkMTIwNw==");
        param.put("app_key","3qBt3ABo");
        kingdeeCloudUtil.getHeader("242159","46d02b40c25a0784bba9708fa9b474ec","GET","/jdyconnector/app_management/kingdee_auth_token",
                param);
        String hmac = SHAUtil.SHA256HMAC("3qBt3ABo", "1e0557c54bb8a7bb6813ccebd395c6cd08a0b599");
        String app_signature = Base64.encodeBase64String(hmac.getBytes());
//        Map<String, Object> param = Maps.newHashMap();
        param.put("app_key", "3qBt3ABo");
        param.put("app_signature",app_signature);
        kingdeeCloudUtil.executeGetToken("242159","46d02b40c25a0784bba9708fa9b474ec",
                "https://api.kingdee.com","/jdyconnector/app_management/kingdee_auth_token",param);
    }
}
