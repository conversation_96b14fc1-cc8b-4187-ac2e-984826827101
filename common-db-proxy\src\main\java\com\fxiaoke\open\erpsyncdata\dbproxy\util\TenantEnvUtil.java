package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;

import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 */
public class TenantEnvUtil {

    public static ErpSyncDataBackStageEnvironmentEnum getTenantAllModelEnv(String tenantId) {
        //好丽友环境
        Set<String> haoliyouSet = ConfigCenter.HAOLIYOU_TENANTS;
        if (haoliyouSet.contains(tenantId)) {
            return ErpSyncDataBackStageEnvironmentEnum.HAOLIYOU;
        }
        //vip环境
        Set<String> vipSet = ConfigCenter.VIP_ENVIROMENT_TENANT;
        if (vipSet.contains(tenantId)) {
            return ErpSyncDataBackStageEnvironmentEnum.VIP;
        }
        //灰度环境
        Set<String> GRAY_TENANTS = ConfigCenter.GRAY_TENANTS;
        if (GRAY_TENANTS.contains(tenantId)) {
            return ErpSyncDataBackStageEnvironmentEnum.GRAY;
        }
        //jacoco环境
        Set<String> JACOCO_TENANTS = ConfigCenter.JACOCO_TENANTS;
        if (JACOCO_TENANTS.contains(tenantId)) {
            return ErpSyncDataBackStageEnvironmentEnum.JACOCO;
        }
        return ErpSyncDataBackStageEnvironmentEnum.NORMAL;
    }
}
