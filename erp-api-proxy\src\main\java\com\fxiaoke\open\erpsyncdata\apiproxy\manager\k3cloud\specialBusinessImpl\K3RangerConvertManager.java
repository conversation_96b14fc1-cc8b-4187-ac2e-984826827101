package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.ValueTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConvertFactory;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldDataMappingManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/17 15:50 范围类型适用条件，需要前置根据源数据的数据条件转换成目标条件
 *
 * @Version 1.0
 */
@Component
@Slf4j
public class K3RangerConvertManager {
    @Autowired
    private FieldConvertFactory fieldConvertFactory;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager;
    public ObjectData convertRangeData(String tenantId,String dataCenterId, ObjectData sourceData, Integer destEventType, String destTenantId, String destObjectApiName, List< FieldMappingData > fieldMappingDataList, Integer destTenantType, Integer sourceTenantType){

            ObjectData destData = new ObjectData();
            destData.putApiName(destObjectApiName);
            destData.putTenantId(destTenantId);
            for (FieldMappingData fieldMappingData : fieldMappingDataList) {
                if (EventTypeEnum.UPDATE.getType() == destEventType && BooleanUtils.isTrue(fieldMappingData.getNotUpdateField())) {
                    continue;
                }
                Object value = doConvertValue(tenantId,dataCenterId, sourceData, destEventType, destTenantId, fieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
                if (value == null && ValueTypeEnum.DEFAULTVALUE.getType().equals(fieldMappingData.getValueType())) {
                    //默认值逻辑
                    if ("null".equals(fieldMappingData.getDefaultValue())) {//如果默认值为null,设置为空串
                        if (fieldMappingData.getDestType().equals(FieldType.EMPLOYEE)) {
                            destData.put(fieldMappingData.getDestApiName(), Lists.newArrayList());
                        } else if (fieldMappingData.getDestType().equals(FieldType.DEPARTMENT)) {
                            destData.put(fieldMappingData.getDestApiName(), Lists.newArrayList());
                        } else if (fieldMappingData.getDestType().equals(FieldType.SELECT_MANY)) {
                            destData.put(fieldMappingData.getDestApiName(), Lists.newArrayList());
                        } else {
                            destData.put(fieldMappingData.getDestApiName(), "");
                        }
                    } else {
                        FieldMappingData newFieldMappingData = BeanUtil2.deepCopy(fieldMappingData, FieldMappingData.class);
                        newFieldMappingData.setMappingType(FieldMappingTypeEnum.FIXED_VAULE.getType());//固定值类型
                        newFieldMappingData.setValue(fieldMappingData.getDefaultValue());
                        newFieldMappingData.setDestType(fieldMappingData.getDestType());
                        Object newValue = doConvertValue(tenantId, dataCenterId,sourceData, destEventType, destTenantId, newFieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
                        destData.put(fieldMappingData.getDestApiName(), newValue);
                    }
                } else {
                    //对ERP的源数据，当转换值为null，且源数据不存在该字段时，不赋值到destData,即不修改目标数据的值。
                    boolean skipSetDestValue = value == null
                            && TenantType.ERP.equals(sourceTenantType)
                            && !sourceData.containsKey(fieldMappingData.getSourceApiName());
                    if (skipSetDestValue) {
                        continue;
                    }
                    destData.put(fieldMappingData.getDestApiName(), value);
                }
            }
            return destData;

    }

    private Object doConvertValue(String tenantId, String dataCenterId,ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        if (FieldMappingTypeEnum.DEPARTMENT_EMPLOYEE_FROM_MAPPING_TABLE.getType() == fieldMappingData.getMappingType()
                && StringUtils.equalsIgnoreCase(fieldMappingData.getSourceType(), ErpFieldTypeEnum.object_reference_many.name())) {
            fieldMappingData.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
        }
        //部门人员特殊处理
        if(fieldMappingData.getSourceType().equals("department")||fieldMappingData.getSourceType().equals("employee")){
            ErpConnectInfoEntity tenantInfoEntity = erpConnectInfoDao.setTenantId(tenantId).getByIdAndTenantId(tenantId, dataCenterId);
            Object oldValue=sourceData.get(fieldMappingData.getSourceApiName());
            if(ObjectUtils.isEmpty(oldValue)){
                return null;
            }
           Object convertValue= convertDepartmentEmp(tenantInfoEntity,oldValue,ErpFieldTypeEnum.getFieldType(fieldMappingData.getSourceType()),fieldMappingData.getSourceApiName(),
                    sourceData,false);
           return convertValue;
        }
        FieldConverter fieldConverter = fieldConvertFactory.getConverter(fieldMappingData.getMappingType());
        try {
            return fieldConverter.convert(tenantId, sourceData, destEventType, destTenantId, fieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
        } catch (RuntimeException e) {
            log.warn("sourceData={},fieldMappingData={}", sourceData, fieldMappingData);
            throw e;
        }
    }

    private Object convertDepartmentEmp(ErpConnectInfoEntity connectInfo, Object oldValue, ErpFieldTypeEnum fieldType,
                                      String fieldKey,
                                      ObjectData objectData, boolean crm2erp){
        if (oldValue instanceof List) {
            List<Object>list= (List) oldValue;
            List<String> oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
            List<String> newList = Lists.newArrayList();
            if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                for (String oldsStr : oldList) {
                    oldsStr = dealK3CloudOperatorErp2Crm(oldsStr, connectInfo);
                    String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp);
                    newList.add(newStr);
                }
            } else {
                for (String oldsStr : oldList) {
                    String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp);
                    newList.add(newStr);
                }
            }

            objectData.put(fieldKey, newList);
            return newList;
        } else {
            //string->list
            String old = oldValue.toString();
            if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                old = dealK3CloudOperatorErp2Crm(old, connectInfo);
            }
            String newStr = getConvertedFieldValue(connectInfo, fieldType, old, crm2erp);
            objectData.put(fieldKey, Lists.newArrayList(newStr));
            return newStr;
        }
    }

    private String dealK3CloudOperatorErp2Crm(String value, ErpConnectInfoEntity connectInfo) {
        FilterData filterData2 = FilterData.builder().fieldApiName("FNumber").fieldValue(Arrays.asList(value)).operate("IN").build();
        List<FilterData> filterDataList = Lists.newArrayList();
        filterDataList.add(filterData2);
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FStaffId.FNumber,FNumber");
        queryArg.setFormId(ObjectApiNameEnum.K3CLOUD_OPERATOR.getObjApiName());
        queryArg.addAndFilters(filterDataList);
        Result<List<K3Model>> result = k3DataManager.queryK3ObjData(connectInfo.getTenantId(), connectInfo.getId(), queryArg);
        if (result.getData() != null && result.getData().size() > 0) {
            return String.valueOf(result.getData().get(0).get("FStaffId.FNumber"));
        }
        return value;
    }

    /**
     * 获取转换后的字段值，如果找到多个，取第一个(最新的一个)
     *
     * @param connectInfo
     * @param fieldType
     * @param fieldValue
     * @param crm2erp
     * @return
     */
    public String getConvertedFieldValue(ErpConnectInfoEntity connectInfo, ErpFieldTypeEnum fieldType, String fieldValue, boolean crm2erp) {
        if (fieldType==null){
            //按道理都进不来
            return null;
        }
        if (connectInfo == null || connectInfo.getTenantId() == null || fieldValue == null) {
            return fieldType.defaultValue(null, false, null,true);
        }
        if (crm2erp) {//crm转erp
            List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                    erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                            connectInfo.getId(), fieldType, fieldValue, null);
            if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getErpDataId())) {
                return erpFieldDataMappingEntities.get(0).getErpDataId();//如果找到多个，取第一个(最新的一个)
            }
        } else {//erp转crm
            List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                    erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                            connectInfo.getId(), fieldType, null, fieldValue);
            if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getFsDataId())) {
                return erpFieldDataMappingEntities.get(0).getFsDataId();
            }
        }
        return fieldType.defaultValue(fieldValue, crm2erp, connectInfo.getChannel(),true);
    }
}
