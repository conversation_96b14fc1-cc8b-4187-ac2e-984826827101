package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023.07.30
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
public class AlertAggregationEntity {
    @BsonId
    private ObjectId id;
    private String tenantId;
    private String dataCenterId;
    private String ployDetailId;

    private AlertAggregationType alertAggregationType;

    //告警级别
    private AlarmLevel alarmLevel;

    //上次触发告警时间
    private Long lastAlertTime;
    //触发告警次数
    private Integer count;
    //轮询ERP告警恢复，轮询正常
    private Boolean alertRecover;

    //轮询ERP错误码
    private String errCode;
    //轮询ERP错误信息
    private String errMsg;
    //轮询ERP trace信息
    private String traceMsg;

    private Date createTime;
    private Date updateTime;
}
