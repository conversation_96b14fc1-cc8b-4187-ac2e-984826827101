package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.common.http.handler.AsyncCallback;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/6/17
 */
@Slf4j
public class FsHttpClient4K3 {

    private static final MediaType CONTENT_TYPE = MediaType.parse("text/plain; charset=UTF-8");
    private static final MediaType JSON_CONTENT_TYPE = MediaType.parse("application/json; charset=UTF-8");
    private static final MediaType FORM_CONTENT_TYPE = MediaType.parse("application/octet-stream;charset=UTF-8");

    private static final String SESSION_KEY = "kdservice-sessionid";
    private static final String ASP_SESSION_KEY = "ASP.NET_SessionId";
    @Getter
    private OkHttpSupport client;

    private RequestBody createRequestBody(Object params) {
        if (params instanceof byte[]) {
            return RequestBody.create(FORM_CONTENT_TYPE, JSONObject.toJSONString(params));
        }
        return params instanceof String ? RequestBody.create(JSON_CONTENT_TYPE, params.toString()) : RequestBody.create(JSON_CONTENT_TYPE, JSONObject.toJSONString(params));
    }

    private FsHttpClient4K3() {
        HttpSupportFactoryBean factory = new HttpSupportFactoryBean();
        //写死了配置文件，如果拷贝这段代码注意
        factory.setConfigName("erp-sync-data-all");
        factory.init();
        try {
            client = factory.getObject();
        } catch (Exception e) {
            log.error("init http support fail, ", e);
        }
    }

    public static FsHttpClient4K3 defaultClient() {
        return FsHttpClient4K3.LazyHolder.INSTANCE;
    }

    private static class LazyHolder {
        private static final FsHttpClient4K3 INSTANCE = new FsHttpClient4K3();
    }

    public HttpRspLimitLenUtil.ResponseBodyModel  get(String url) {
        String rspString = client.getString(url);
        HttpRspLimitLenUtil.ResponseBodyModel result = HttpRspLimitLenUtil.ResponseBodyModel.builder().body(rspString).reachLengthLimit(false).build();
        return result;
    }

    public  Result<StoneFileUploadResponse> sendOkHttp3GetFile(String url, Map<String, String> headers, Function<Response, Result<StoneFileUploadResponse>> uploadFunction) {


        Request.Builder get = new Request.Builder();
        buildHeader(get, headers);
        Request request = get.url(url).build();
        Result<StoneFileUploadResponse> result = (Result<StoneFileUploadResponse>) client.syncExecute(request, new SyncCallback() {
            @Override
            public Result<StoneFileUploadResponse> response(Response response) throws Exception {
                Result<StoneFileUploadResponse> apply = uploadFunction.apply(response);
                return apply;
            }
        });
        return result;
    }

    public int downloadFile(String url, File localFile, boolean report404) {
        return client.downloadFile(url, localFile, report404);
    }

    public void downloadFile(String url, File localFile) {
        downloadFile(url, localFile, true);
    }

    public HttpRspLimitLenUtil.ResponseBodyModel  post(String url, Object body) {
        String rspString =  doPost(url, body, CONTENT_TYPE);
        HttpRspLimitLenUtil.ResponseBodyModel result = HttpRspLimitLenUtil.ResponseBodyModel.builder().body(rspString).reachLengthLimit(false).build();
        return result;
    }

    public HttpRspLimitLenUtil.ResponseBodyModel  postUseCookie(String url, String body, Map<String, String> headerMap,boolean useNewResponseReader) {
        Headers headersB = Headers.of(headerMap);
        Request req = new Request.Builder()
                .url(url)
                .headers(headersB)
                .post(RequestBody.create(JSON_CONTENT_TYPE, body))
                .build();
        return (HttpRspLimitLenUtil.ResponseBodyModel) client.syncExecute(req, new SyncCallback() {
            @Override
            public HttpRspLimitLenUtil.ResponseBodyModel response(Response response) throws Exception {
                HttpRspLimitLenUtil.ResponseBodyModel result = null;
                List<String> headers1 = response.headers("Set-Cookie");
                for (String s : headers1) {
                    if (s.startsWith(SESSION_KEY)) {
                        String s1 = StringUtils.substringBefore(s, ";");
                        String sessionId = StringUtils.substringAfter(s1, "=");
                        if (StringUtils.isNotBlank(sessionId)) {
                            headerMap.put(SESSION_KEY, sessionId);
                        }
                    }
                    if (s.startsWith(ASP_SESSION_KEY)) {
                        String s1 = StringUtils.substringBefore(s, ";");
                        String sessionId = StringUtils.substringAfter(s1, "=");
                        if (StringUtils.isNotBlank(sessionId)) {
                            headerMap.put(ASP_SESSION_KEY, sessionId);
                        }
                    }
                }
                String path=response.request().url().url().getPath();
                HttpRspLimitLenUtil.ResponseBodyModel model;
                if (useNewResponseReader){
                    model = HttpRspLimitLenUtil.getLimitLengthRspString2(response, ConfigCenter.CONTENT_LENGTH_LIMIT.intValue());
                }else{
                    model = HttpRspLimitLenUtil.getLimitLengthRspString(response,ConfigCenter.CONTENT_LENGTH_LIMIT);
                }
                Objects.requireNonNull(model);
                if(path!=null&&path.endsWith("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc")){
                    long contentLength=model.getBody().length();
                    log.info("FsHttpClient4K3.postUseCookie,response contentLength={},ConfigCenter.CONTENT_LENGTH_LIMIT={}",
                            contentLength,
                            ConfigCenter.CONTENT_LENGTH_LIMIT);
                    //上面读过了，真走到这里，应该是读不了了。
                    if(contentLength >= ConfigCenter.CONTENT_LENGTH_LIMIT){
                        ////如果是view接口，并且formI为允许超出16M返回的对象名单,比如库存和销售出库单。 这些对象会在后面的处理步骤中把数据大小降下来。
                        if(url.endsWith("View.common.kdsvc")){
                            Map map = JSONObject.parseObject(body, Map.class);
                            String parameters = map.get("parameters").toString();
                            List<String> paramsList = JSONArray.parseArray(parameters, String.class);
                            String formId = paramsList.get(0);
                            if(ConfigCenter.OVER_OBJ_NAMES.contains(formId)){
                                String rspString = response.body().string();
                                result = HttpRspLimitLenUtil.ResponseBodyModel.builder().body(rspString).reachLengthLimit(false).build();
                                return result;
                            }
                        }
                        //return "返回内容过大，Content-Length大小："+contentLength+",超过："+ConfigCenter.CONTENT_LENGTH_LIMIT+",cmd url:"+url;
                        String rspString = Objects.requireNonNull(model).getBody();
                        result = HttpRspLimitLenUtil.ResponseBodyModel.builder().body(rspString).reachLengthLimit(true).build();
                        return result;
                    }
                }
                return model;
            }
        });
    }


/**
    public static void main(String args[]) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            BufferedSource bf = Okio.buffer(Okio.source(new File("D:\\test.txt")));
            long totallen = readHttpContentByPage(bos, bf);
            System.out.println("totallen: "+totallen+ ", str: " + bos.toString());
        }catch (Exception e){}
    }
**/
    public String postJson(String url, String body) {
        return doPost(url, body, JSON_CONTENT_TYPE);
    }

    public void asyncPostJson(String url, Object body, Consumer<Object> consumer) {
        doPostAsync(url, body, JSON_CONTENT_TYPE, consumer);
    }

    public HttpRspLimitLenUtil.ResponseBodyModel  postUrl(String url, Object params, Map<String, String> headerMap) {
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/octet-stream;charset=UTF-8"), (byte[]) params);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = client.syncExecute(request, new SyncCallback() {
            public Object response(Response response) throws IOException {
                return response.body().string();
            }
        });
        String rspString = result.toString();
        return HttpRspLimitLenUtil.ResponseBodyModel.builder().body(rspString).reachLengthLimit(false).build();
    }

    public HttpRspLimitLenUtil.ResponseBodyModel postUrl2(String url, Object params, Map<String, String> headerMap) {
        String paramsJson = params instanceof String ? params.toString() : JSONObject.toJSONString(params);
        RequestBody requestBody = RequestBody.create(paramsJson, MediaType.parse("application/json; charset=utf-8"));
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = client.syncExecute(request, new SyncCallback() {
            public Object response(Response response) throws IOException {
                return response.body().string();
            }
        });
        String rspString = result.toString();
        return HttpRspLimitLenUtil.ResponseBodyModel.builder().body(rspString).reachLengthLimit(false).build();
    }

    private String doPost(String url, Object body, MediaType mediaType) {
        Request req;
        if (body instanceof Map) {
            FormBody.Builder form = new FormBody.Builder();
            for (Object obj : ((Map) body).entrySet()) {
                Map.Entry kv = (Map.Entry) obj;
                form.addEncoded(kv.getKey().toString(), kv.getValue().toString());
            }
            req = new Request.Builder().url(url).post(form.build()).build();
        } else {
            req = new Request.Builder().url(url).post(RequestBody.create(mediaType, (String) body)).build();
        }
        return (String) client.syncExecute(req, new SyncCallback() {
            @Override
            public String response(Response response) throws Exception {
                return Objects.requireNonNull(response.body()).string();
            }
        });
    }

    private void doPostAsync(String url, Object body, MediaType mediaType, Consumer<Object> consumer) {
        Request req;
        if (body instanceof Map) {
            FormBody.Builder form = new FormBody.Builder();
            for (Object obj : ((Map) body).entrySet()) {
                Map.Entry kv = (Map.Entry) obj;
                form.addEncoded(kv.getKey().toString(), kv.getValue().toString());
            }
            req = new Request.Builder().url(url).post(form.build()).build();
        } else {
            req = new Request.Builder().url(url).post(RequestBody.create(mediaType, (String) body)).build();
        }
        client.asyncExecute(req, new AsyncCallback() {
            @Override
            public void response(Response response) {
                consumer.accept(response);
            }

            @Override
            public void onFailure(Call call, IOException e) {
                consumer.accept(e);
            }
        });
    }

    /**
     * 构造Header
     *
     * @param builder
     * @param headers
     */
    private static void buildHeader(Request.Builder builder, Map<String, String> headers) {
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String key = entry.getKey();
                Object val = entry.getValue();
                builder.addHeader(key, val.toString());
            }
        }
    }

    @Data
    private static class ResponseBodyModel {
        private long contentLength;
        private String body;
    }
}
