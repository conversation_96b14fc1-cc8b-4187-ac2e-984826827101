package com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23 11:50:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddRecordLog implements Serializable {
    private List<Map<String, Object>> beforeData;
    private List<Map<String, Object>> afterData;
    private String actionType;
}
