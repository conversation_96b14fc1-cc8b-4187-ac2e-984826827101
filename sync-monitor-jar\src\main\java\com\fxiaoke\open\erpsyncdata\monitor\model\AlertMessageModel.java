package com.fxiaoke.open.erpsyncdata.monitor.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/9 11:36
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("消息通知")
public class AlertMessageModel  implements Serializable {

    private static final long serialVersionUID = -7080410060793334990L;
    @ApiModelProperty("企业ei，标准接口不传")
    private String tenantId;
    @ApiModelProperty("数据中心")
    private String dataCenterId;
    @ApiModelProperty("集成流ID")
    private String ployDetailId;
    @ApiModelProperty("集成流名字")
    private String streamName;
    @ApiModelProperty("报错信息")
    private String errorMessage;
    @ApiModelProperty("连接器名称")
    private String streamInfo;
    @ApiModelProperty("企业名称")
    private String tenantInfo;
    @ApiModelProperty("企业名称")
    private String title;
}
