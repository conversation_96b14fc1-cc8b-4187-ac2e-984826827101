package com.fxiaoke.open.erpsyncdata.monitor.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fxiaoke.common.http.handler.AsyncCallback;
import com.fxiaoke.common.http.handler.TimeoutSettings;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.ServerStatusLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.OuterConnectorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.CheckStatusUrl;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.monitor.util.MonitorConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 服务状态监测 当前仅内部
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Component
@JobHander(value = "serverStatusMonitorHandler")
public class ServerStatusMonitorHandler extends IJobHandler {
    @Autowired
    private OkHttpSupport okHttpSupport;
    @Autowired
    private OuterConnectorManager outerConnectorManager;


    @Override
    public ReturnT<?> execute(TriggerParam triggerParam) throws Exception {
        //从参数取
        List<CheckStatusUrl> checkStatusUrls = new ArrayList<>(MonitorConfigCenter.checkStatusUrls);
        fillHubUrls(checkStatusUrls);
        //开始探测
        checkStatus(checkStatusUrls);

        log.info("ServerStatusMonitorHandler check result,{}", checkStatusUrls);

        Map<String, Boolean> statusMap = checkStatusUrls.stream().collect(Collectors.toMap(v -> v.getUrl(), u -> u.isOk(), (u, v) -> u));
        return ReturnT.SUCCESS;
    }


    private void fillHubUrls(List<CheckStatusUrl> checkStatusUrls) {
        List<HubInfo> hubInfoList = outerConnectorManager.getHubInfoList();
        if (CollUtil.isEmpty(hubInfoList)) {
            return;
        }
        CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(hubInfoList.size());
        for (HubInfo hubInfo : hubInfoList) {
            if ( hubInfo.getBaseUrl() == null) {
                //本地执行的不用检查
                countDownLatch.countDown();
            }
            checkStatusUrls.add(CheckStatusUrl.builder().type("hub").url(hubInfo.getBaseUrl() + "/hub/check").name(hubInfo.getName()).build());
        }
    }

    private void checkStatus(List<CheckStatusUrl> checkStatusUrls) {
        CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(checkStatusUrls.size());
        Set<CheckStatusUrl> hasResponseSet = new ConcurrentHashSet<>();
        for (CheckStatusUrl checkStatusUrl : checkStatusUrls) {
            try {
                asyncGetStatus(checkStatusUrl, hasResponseSet, countDownLatch);
            } catch (Exception e) {
                log.info("checkStatus asyncGetStatus exception", e);
                //请求异常
                checkStatusUrl.setStatus(598);
                countDownLatch.countDown();
            }
        }
        //仅等待3s
        try {
            boolean await = countDownLatch.await(3L, TimeUnit.SECONDS);
            log.info("checkHub countDownLatch.await {}", await);
        } catch (InterruptedException ignored) {
            log.info("checkHub countDownLatch.await InterruptedException");
        }
        for (CheckStatusUrl v : checkStatusUrls) {
            ServerStatusLog bizLog = ServerStatusLog.builder().type(v.getType()).url(v.getUrl()).name(v.getName()).statusCode(ObjectUtil.defaultIfNull(v.getStatus(), 599)).build();
            MonitorBizLogUtil.sendGeneric(bizLog);
        }
    }

    /**
     * 检查是否能访问并返回2xx
     */
    private void asyncGetStatus(CheckStatusUrl checkStatusUrl, Set<CheckStatusUrl> hasResponseSet, CountDownLatch countDownLatch) {
        String url = checkStatusUrl.getUrl();
        Request request = new Request.Builder().url(url).get()
                .tag(TimeoutSettings.class, TimeoutSettings.builder().connect(2).read(2).write(2).build())
                .build();
        okHttpSupport.asyncExecute(request, new AsyncCallback() {
            @Override
            public void response(Response response) throws IOException {
                checkStatusUrl.setStatus(response.code());
                hasResponseSet.add(checkStatusUrl);
                countDownLatch.countDown();
            }
        });
    }
}
