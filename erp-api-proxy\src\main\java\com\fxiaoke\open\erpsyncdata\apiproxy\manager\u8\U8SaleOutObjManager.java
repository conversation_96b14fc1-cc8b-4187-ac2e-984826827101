package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("saleout")
public class U8SaleOutObjManager extends U8DefaultMananger {
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Override
    protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                             String tenantId,
                                             String dataCenterId,
                                             String snapshotId) {
        StandardListData standardListData = standardListDataResult.getData();
        if (standardListData == null) {
            return;
        }
        for (StandardData standardData : standardListData.getDataList()) {
            getRealData(standardData,tenantId,dataCenterId,snapshotId);
        }
    }

    /**
     * 获取接口
     */
    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        String connectParam=connectInfo.getConnectParams();
        U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);
        String queryPath = getLoadUrl(erpIdArg.getObjAPIName());
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectInfo.getId(),
                erpIdArg.getObjAPIName(),
                ErpObjInterfaceUrlEnum.queryMasterById.name());

        Map<String, String> params = new HashMap<>();
        params.put("id",erpIdArg.getDataId());
        //发送请求
        Result<StandardData> result = get(U8ConnectParam, queryPath, params, interfaceMonitorData);
        result.setData(getRealData(result.getData(),erpIdArg.getTenantId(),connectInfo.getId(),erpIdArg.getSyncPloyDetailSnapshotId()));
        return result;
    }

    private StandardData getRealData(StandardData standardData, String tenantId, String dataCenterId, String snapshotId) {
        if (standardData == null) {
            return null;
        }

        ObjectData masterObj = standardData.getMasterFieldVal();
        String saleoutId=masterObj.getString("code");//出库单号
        masterObj.put("saleoutId",saleoutId);
        List<ObjectData> detailObjectDataList = standardData.getDetailFieldVals().get("entry");
        for (ObjectData objectData : detailObjectDataList) {
            objectData.put("saleoutId", saleoutId);
            String rowno = objectData.getString("rowno");//来源订单行号
            String iorderseq = objectData.getString("iorderseq");
            if (StringUtils.isEmpty(iorderseq)) {
                iorderseq = rowno;
            }

            String serial = objectData.getString("serial");//批号？序列号？
            String orderCode = objectData.getString("ordercode");//来源订单号
            String inventoryCode = objectData.getString("inventorycode");//存货编码
            String warehouseCode = masterObj.getString("warehousecode");//仓库编码

            String oldEntryId = saleoutId + "#" + orderCode + "#"+ inventoryCode + "#" + rowno;
            log.info("getRealData,oldEntryId={}",oldEntryId);

            SyncPloyDetailSnapshotEntity entryBySnapshotId = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId,
                    snapshotId);

            ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId,
                    dataCenterId,
                    ErpChannelEnum.ERP_U8.name(),
                    TenantConfigurationTypeEnum.U8_SALE_OUT_DETAIL_SUPPORT_SERIAL.name());
            log.info("getRealData,configuration={}",configuration);
            if(entryBySnapshotId!=null && configuration!=null) {
                DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData = entryBySnapshotId.getSyncPloyDetailData()
                        .getDetailObjectMappings().get(0);
                log.info("getRealData,detailObjectMappingData.sourceObjectApiName={},destObjectApiName={}",
                        detailObjectMappingData.getSourceObjectApiName(),detailObjectMappingData.getDestObjectApiName());

                Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mappingFirstBySource = syncDataMappingManager.getMappingFirstBySource(tenantId,
                        detailObjectMappingData.getSourceObjectApiName(),
                        oldEntryId,
                        detailObjectMappingData.getDestObjectApiName());
                log.info("getRealData,mappingFirstBySource={}",mappingFirstBySource);
                //已经同步成功的历史数据，不改变数据的entryId，继续走老逻辑
                if(mappingFirstBySource!=null
                        && mappingFirstBySource.getLeft()!=null
                        && mappingFirstBySource.getLeft().getIsCreated()
                        && !mappingFirstBySource.getLeft().getIsDeleted()) {
                    //如果没有配置，默认走老逻辑
                    objectData.put("entryId", oldEntryId);//虚拟主键
                } else {
                    //新单据，走新逻辑
                    objectData.put("entryId", saleoutId + "#" + orderCode + "#"+ inventoryCode + "#" + rowno + "#" + serial);//虚拟主键
                }
            } else {
                //如果没有配置，默认走老逻辑
                objectData.put("entryId", oldEntryId);//虚拟主键
            }

            objectData.put("orderInvertoryCode", orderCode + "#" + inventoryCode + "#" + iorderseq);//订单产品编码

            if (StringUtils.isNotEmpty(serial)) {
                objectData.put("currentStockId", inventoryCode + "#" + warehouseCode + "#" + serial);//库存id
            } else {
                objectData.put("currentStockId", inventoryCode + "#" + warehouseCode);//库存id
            }
        }
        return standardData;
    }


}
