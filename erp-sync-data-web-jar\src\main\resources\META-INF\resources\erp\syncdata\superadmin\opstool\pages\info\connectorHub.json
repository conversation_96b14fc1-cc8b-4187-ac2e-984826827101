{"type": "page", "title": "连接器Hub", "remark": null, "name": "connectorHub", "data": {"baseUrl": ".."}, "body": [{"type": "button", "label": "修改配置", "level": "primary", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "drawer", "drawer": {"type": "drawer", "title": "修改配置", "body": [{"type": "form", "reload": "hubs,connectors", "body": [{"type": "editor", "label": "配置", "name": "hubInfoStr", "id": "u:7f594d5d082d", "minRows": 3, "maxRows": 100, "language": "json"}], "id": "u:32922b8f5ce0", "actions": [{"type": "submit", "label": "提交", "primary": true}], "feat": "Edit", "dsType": "api", "initApi": {"method": "get", "url": "${baseUrl}/hub/getHubInfoStr"}, "api": {"url": "${baseUrl}/hub/updateHubInfoStr", "method": "post"}}], "id": "u:2260cc3cd2a2", "size": "xl"}}]}}}, {"type": "button", "label": "刷新", "level": "primary", "actionType": "reload", "target": "hubs,connectors", "style": {"margin": 5}}, {"type": "divider"}, {"type": "grid", "name": "grid1", "columns": [{"md": 3, "body": {"type": "crud", "name": "hubs", "api": "../hub/getHubInfoList", "loadDataOnce": true, "autoFillHeight": true, "defaultParams": {"perPage": 50}, "primaryField": "name", "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据，请刷新缓存", "combineNum": 0, "itemAction": {"actionType": "reload", "target": "connectors?hubName=${name}"}}}, {"md": 9, "body": {"type": "crud", "name": "connectors", "api": "../hub/getConnectorList?hubName=${hubName}", "loadDataOnce": true, "autoFillHeight": true, "defaultParams": {"perPage": 50}, "primaryField": "name", "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据，请刷新缓存", "combineNum": 0}}]}]}