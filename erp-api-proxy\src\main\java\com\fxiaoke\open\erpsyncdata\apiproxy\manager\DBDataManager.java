package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class DBDataManager {
    @Autowired
    private ErpPushDataDao erpPushDataDao;

    Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg) {
        Result<StandardListData> result = new Result<>();
        StandardListData standardListData = new StandardListData();
        result.setData(standardListData);
        List<Integer> operationTypes;
        switch (timeFilterArg.getOperationType()) {
            case 1:
            case 2:
                //新增更新通过预处理是否存在映射判断，这里全数据返回
                operationTypes = ImmutableList.of(1, 2);
                break;
            case 3:
                //作废
                operationTypes = ImmutableList.of(3);
                break;
            default:
                //暂时支持新增，更新，作废数据类型
                return result;
        }
        List<String> jsonStrList =
                erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(timeFilterArg.getTenantId())).listErpObjDataByTime(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName(), timeFilterArg
                        .getStartTime(), timeFilterArg.getEndTime(), operationTypes, timeFilterArg.getOffset(), timeFilterArg.getLimit());

        for (String jsonStr : jsonStrList) {
            StandardData standardData = JSON.parseObject(jsonStr, StandardData.class);
            standardListData.getDataList().add(standardData);
        }

        return result;
    }

    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, String dataCenterId) {
        ErpPushDataEntity pushDataEntity =
                erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId())).findByTenantObjectId(erpIdArg.getTenantId(), erpIdArg.getObjAPIName(), erpIdArg.getDataId(), dataCenterId);

        if (pushDataEntity == null) return Result.newError(ResultCodeEnum.PUSH_DATA_HAVE_NOT_FOUND);
        StandardData standardData = JSON.parseObject(pushDataEntity.getStandardFormat(), StandardData.class);
        return new Result<>(standardData);
    }


}
