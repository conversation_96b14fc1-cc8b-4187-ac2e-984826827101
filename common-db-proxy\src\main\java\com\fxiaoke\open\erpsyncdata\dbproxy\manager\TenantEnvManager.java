package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TenantEnvUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 19:59 2022/5/12
 * @Desc:企业环境管理者
 */
@Component
@Slf4j
public class TenantEnvManager {

    @Autowired
    private ConfigCenterManager configCenterManager;

    /**
     * 获取web环境，前端应用
     * return <web环境，前端应用>
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 60, localLimit = 1000)
    @LogLevel
    public Pair<String, String> getTenantWebEnvironment(String tenantId) {
        ErpSyncDataBackStageEnvironmentEnum tenantAllModelEnv = getTenantAllModelEnv(tenantId);
        String tenantWebModelEnv = ConfigCenter.ALL_ENV_TO_WEB_ENV.get(tenantAllModelEnv.getEnvironment());
        String tenantWebApp = ConfigCenter.WEB_ENV_TO_WEB_APP_ENV.get(tenantWebModelEnv);
        return Pair.of(tenantWebModelEnv, tenantWebApp);
    }

    /**
     * 获取企业all模块环境
     */
    @LogLevel
    public ErpSyncDataBackStageEnvironmentEnum getTenantAllModelEnv(String tenantId) {
        return TenantEnvUtil.getTenantAllModelEnv(tenantId);
    }

    public void changeTenantEnv(String tenantId, ErpSyncDataBackStageEnvironmentEnum newEnv) {
        configCenterManager.batchChangeTenantInfo(Lists.newArrayList(tenantId),newEnv);
    }
    public Result<String> batchChangeTenantInfo(List<String> tenantIds, ErpSyncDataBackStageEnvironmentEnum newEnv) {
        return configCenterManager.batchChangeTenantInfo(tenantIds,newEnv);
    }

}
