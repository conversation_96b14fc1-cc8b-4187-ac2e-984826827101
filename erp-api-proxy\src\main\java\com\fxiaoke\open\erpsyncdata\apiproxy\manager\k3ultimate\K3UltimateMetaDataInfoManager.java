package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate;


import com.fxiaoke.open.erpsyncdata.apiproxy.model.ErpObjectDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ErpObjectDescribe.SplitObjDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseByT;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 11:59 2025/2/6
 * @Desc:
 */
@Component
@Slf4j
public class K3UltimateMetaDataInfoManager {
    @Autowired
    private K3UltimateApiService k3UltimateApiService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;


    /**
     * @param tenantId
     * @param dataCenterId
     * @param formId
     * @return
     */
    public Result<ErpObjectDescribe> getErpObjectDescribe(String tenantId, String dataCenterId, String formId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        Result<K3UltimateResponseByT<String>> queryBusinessInfoResultResult = k3UltimateApiService.getFormIdMetaData(tenantId, dataCenterId, formId, ConnectParamUtil.parseK3Ultimate(connectInfo.getConnectParams()));
        if (!queryBusinessInfoResultResult.isSuccess()) {
            Result result= Result.copy(queryBusinessInfoResultResult);
            if(queryBusinessInfoResultResult.getData()!=null&&StringUtils.isNotBlank(queryBusinessInfoResultResult.getData().getData())){
                result.setErrMsg(queryBusinessInfoResultResult.getData().getData());
            }
            return result;
        }
        K3UltimateResponseByT<String> k3UltimateResponseByQuery = queryBusinessInfoResultResult.getData();
        if (!k3UltimateResponseByQuery.isStatus()) {
            return Result.newError(k3UltimateResponseByQuery.getErrorCode(), k3UltimateResponseByQuery.getMessage());
        }
        String xmlData = k3UltimateResponseByQuery.getData();
        ErpObjectDescribe erpObjectDescribe = this.analysisK3UltimateMetaDataInfo(formId, xmlData);
        if (erpObjectDescribe == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), I18NStringEnum.s44);
        }
        return Result.newSuccess(erpObjectDescribe);
    }


    /**
     * 解析K3Ultimate元数据信息
     *
     * @return
     */
    public ErpObjectDescribe analysisK3UltimateMetaDataInfo(String formId, String xmlData) {
        JSONObject jsonObject = XML.toJSONObject(xmlData);
        String realObjName = jsonObject.getJSONObject("EntityMetadata").getString("Name");
        ErpObjectDescribe erpObjectDescribe = new ErpObjectDescribe();
        ErpObjectEntity realObj = new ErpObjectEntity();
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = new LinkedHashMap<>();
        erpObjectDescribe.setRealObj(realObj);
        erpObjectDescribe.setSplitObjDescribes(splitObjDescribes);
        //真实对象信息
        realObj.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        realObj.setErpObjectApiName(formId);
        realObj.setErpObjectName(realObjName);
        realObj.setErpObjectExtendValue("DETAIL2DETAIL_SPLIT");
        JSONObject items = jsonObject.getJSONObject("EntityMetadata").getJSONObject("Items");
        //主对象
        String splitMainObjApiName=formId+".BillHead";
        Map<String, String> id2ObjApiName = Maps.newHashMap();
        id2ObjApiName.put("mainObj", splitMainObjApiName);
        analysisMainObj(formId,splitMainObjApiName,realObjName, splitObjDescribes);
        if(!items.isNull("EntryEntity")){
            //从对象信息
            List<JSONObject> details=Lists.newArrayList();
            if (items.get("EntryEntity") instanceof JSONObject) {
                JSONObject detail = items.getJSONObject("EntryEntity");
                details.add(detail);
            } else {
                for (int j = 0; j < items.getJSONArray("EntryEntity").length(); j++) {
                    JSONObject detail = items.getJSONArray("EntryEntity").getJSONObject(j);
                    details.add(detail);
                }
            }
            for (int i = 0; i < details.size(); i++) {
                JSONObject detail = details.get(i);
                analysisDetailObj(detail, splitObjDescribes, formId,splitMainObjApiName,realObjName,id2ObjApiName);
            }
        }
        for (String key : items.keySet()) {
            if ("BaseEntity".equals(key) || "EntryEntity".equals(key)) {
                continue;
            }
            List<JSONObject> fields = Lists.newArrayList();
            if (items.get(key) instanceof JSONObject) {
                JSONObject detail = items.getJSONObject(key);
                fields.add(detail);
            } else if (items.get(key) instanceof JSONArray) {
                for (int j = 0; j < items.getJSONArray(key).length(); j++) {
                    JSONObject detail = items.getJSONArray(key).getJSONObject(j);
                    fields.add(detail);
                }
            }
            switch (key) {
                case "TextField":
                case "MuliLangTextField":
                case "MasterIdField":
                case "GroupField":
                case "AdminDivisionField":
                case "TelephoneField":
                case "AssistantField":
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.text);
                    break;
                case "BillTypeField":
                case "OrgField":
                case "UnitField":
                case "MaterielField":
                case "BasedataPropField":
                case "BasedataField"://Basedata没有查找关联对象信息，先预置为文本
                    analysisNumberSuffixField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.text);
                    break;
                case "BillStatusField":
                    analysisStatusField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.select_one);
                    break;
                case "CreaterField":
                case "ModifierField":
                case "UserField":
                    analysisNumberSuffixField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.employee);
                    break;
                case "ComboField":
                    analysisComboField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.select_one);
                    break;
                case "BigIntField":
                case "IntegerField":
                case "QtyField":
                case "PriceField":
                case "DecimalField":
                case "AmountField":
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.number);
                    break;
                case "CreateDateField":
                case "ModifyDateField":
                case "DateTimeField":
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.date_time);
                    break;
                case "PictureField":
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.image);
                    break;
                case "DateField":
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.date);
                    break;
                case "MulComboField":
                    analysisComboField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.select_many);
                    break;
                case "CheckBoxField":
                    analysisCheckBoxField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.true_or_false);
                    break;
                case "EmailField":
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.email);
                    break;
                default:
                    analysisDefaultField(fields, erpObjectDescribe, id2ObjApiName, ErpFieldTypeEnum.text);//默认为文本
            }

        }
        return erpObjectDescribe;
    }

    private void analysisNumberSuffixField(List<JSONObject> fields, ErpObjectDescribe erpObjectDescribe, Map<String, String> id2ObjApiName, ErpFieldTypeEnum type) {
        String nameSuffix=I18NStringEnum.s1198.getText();
        String numberSuffix=I18NStringEnum.s1199.getText();
        for (int i = 0; i < fields.size(); i++) {
            try {
                JSONObject field = fields.get(i);
                addField(erpObjectDescribe, id2ObjApiName, type, field,"number",numberSuffix);
                addField(erpObjectDescribe, id2ObjApiName, type, field,"id",null);
                addField(erpObjectDescribe, id2ObjApiName, type, field,"name",nameSuffix);
            }catch (Exception e){
                log.warn("analysisNumberSuffixField e",e);
            }
        }
    }

    private void analysisCheckBoxField(List<JSONObject> fields, ErpObjectDescribe erpObjectDescribe, Map<String, String> id2ObjApiName, ErpFieldTypeEnum type) {
        for (int i = 0; i < fields.size(); i++) {
            try {
                JSONObject field = fields.get(i);
                ErpObjectFieldEntity fieldEntity = addField(erpObjectDescribe, id2ObjApiName, type, field,null,null);
                if(fieldEntity==null){
                    continue;
                }
                List<Option> options = Lists.newArrayList();
                Option option = new Option();
                option.setLabel("TRUE");
                option.setValue(true);
                options.add(option);
                Option option1 = new Option();
                option1.setLabel("FALSE");
                option1.setValue(false);
                options.add(option1);
                fieldEntity.setFieldExtendValue(JacksonUtil.toJson(options));
            }catch (Exception e){
                log.warn("analysisCheckBoxField e",e);
            }

        }
    }

    private void analysisComboField(List<JSONObject> fields, ErpObjectDescribe erpObjectDescribe, Map<String, String> id2ObjApiName, ErpFieldTypeEnum type) {
        for (int i = 0; i < fields.size(); i++) {
            try {
                JSONObject field = fields.get(i);
                ErpObjectFieldEntity fieldEntity = addField(erpObjectDescribe, id2ObjApiName, type, field,null,null);
                if (fieldEntity != null){
                    List<Option> options = Lists.newArrayList();
                    if (!field.isNull("Items")) {
                        JSONObject statusItem = field.getJSONObject("Items");
                        if(statusItem.get("ComboItem") instanceof JSONArray){
                            JSONArray statusItems = statusItem.getJSONArray("ComboItem");
                            for (int j = 0; j < statusItems.length(); j++) {
                                JSONObject status = statusItems.getJSONObject(j);
                                Option option = new Option();
                                if(status.isNull("Caption") || status.isNull("Value")){
                                    continue;
                                }
                                option.setLabel(status.get("Caption"));
                                option.setValue(status.get("Value"));
                                options.add(option);
                            }
                        }else if(statusItem.get("ComboItem") instanceof JSONObject){
                            JSONObject status = statusItem.getJSONObject("ComboItem");
                            Option option = new Option();
                            if(status.isNull("Caption") || status.isNull("Value")){
                                continue;
                            }
                            option.setLabel(status.get("Caption"));
                            option.setValue(status.get("Value"));
                            options.add(option);
                        }

                    }
                    fieldEntity.setFieldExtendValue(JacksonUtil.toJson(options));
                }
            }catch (Exception e){
                log.warn("analysisComboField e",e);
            }

        }
    }

    private void analysisStatusField(List<JSONObject> fields, ErpObjectDescribe erpObjectDescribe, Map<String, String> id2ObjApiName, ErpFieldTypeEnum type) {
        for (int i = 0; i < fields.size(); i++) {
            try {
                JSONObject field = fields.get(i);
                ErpObjectFieldEntity fieldEntity = addField(erpObjectDescribe, id2ObjApiName, type, field,null,null);
                if (fieldEntity != null){
                    List<Option> options = Lists.newArrayList();
                    if (!field.isNull("StatusItems")) {
                        JSONObject statusItem = field.getJSONObject("StatusItems");
                        if(statusItem.get("StatusItem") instanceof JSONArray){
                            JSONArray statusItems = statusItem.getJSONArray("StatusItem");
                            for (int j = 0; j < statusItems.length(); j++) {
                                JSONObject status = statusItems.getJSONObject(j);
                                Option option = new Option();
                                if(status.isNull("statusname") || status.isNull("statuskey")){
                                    continue;
                                }
                                option.setLabel(status.get("statusname"));
                                option.setValue(status.get("statuskey"));
                                options.add(option);
                            }
                        }else if(statusItem.get("StatusItem") instanceof JSONObject){
                            JSONObject status = statusItem.getJSONObject("StatusItem");
                            Option option = new Option();
                            if(status.isNull("statusname") || status.isNull("statuskey")){
                                continue;
                            }
                            option.setLabel(status.get("statusname"));
                            option.setValue(status.get("statuskey"));
                            options.add(option);
                        }

                    }
                    fieldEntity.setFieldExtendValue(JacksonUtil.toJson(options));
                }
            }catch (Exception e){
                log.warn("analysisStatusField e",e);
            }

        }
    }

    private void analysisDefaultField(List<JSONObject> fields, ErpObjectDescribe erpObjectDescribe, Map<String, String> id2ObjApiName, ErpFieldTypeEnum type) {

        for (int i = 0; i < fields.size(); i++) {
            try {
                JSONObject field = fields.get(i);
                ErpObjectFieldEntity fieldEntity = addField(erpObjectDescribe, id2ObjApiName, type, field,null,null);
            }catch (Exception e){
                log.warn("analysisDefaultField e",e);
            }
        }
    }

    private ErpObjectFieldEntity addField(ErpObjectDescribe erpObjectDescribe, Map<String, String> id2ObjApiName, ErpFieldTypeEnum type, JSONObject field,
                                          String fieldApiNameSuffix,String fieldNameSuffix) {
        String splitApiName;
        if (field.isNull("ParentId")) {
            splitApiName = id2ObjApiName.get("mainObj");
        } else {
            splitApiName = id2ObjApiName.get(field.getString("ParentId"));
        }
        if (splitApiName == null) {
            return null;
        }
        if(field.isNull("Key") || field.isNull("Name")){
            return null;
        }
        String fieldApiName = field.getString("Key");
        if(StringUtils.isNotBlank(fieldApiNameSuffix)){
            fieldApiName = fieldApiName+"_"+fieldApiNameSuffix;
        }
        String name = field.getString("Name");
        if(StringUtils.isNotBlank(fieldNameSuffix)){
            name = name+"_"+fieldNameSuffix;
        }
        boolean required = field.isNull("MustInput") ? false : field.getBoolean("MustInput");
        ErpObjectFieldEntity nField = new ErpObjectFieldEntity();
        nField.setRequired(false);
        nField.setErpObjectApiName(splitApiName);
        nField.setFieldApiName(fieldApiName);
        nField.setFieldLabel(name);
        nField.setRequired(required);
        nField.setFieldDefineType(type);
        if (erpObjectDescribe.getSplitObjDescribes() != null && erpObjectDescribe.getSplitObjDescribes().containsKey(splitApiName)) {
            if (erpObjectDescribe.getSplitObjDescribes().get(splitApiName).getFields() == null) {
                erpObjectDescribe.getSplitObjDescribes().get(splitApiName).setFields(Maps.newLinkedHashMap());
            }
            erpObjectDescribe.getSplitObjDescribes().get(splitApiName).getFields().put(fieldApiName, nField);
        }
        return nField;
    }

    private void analysisMainObj(String realApiName,String splitApiName,String realObjName, LinkedHashMap<String, SplitObjDescribe> splitObjDescribes) {
        SplitObjDescribe splitObjDescribe = new SplitObjDescribe();
        //拆分对象
        ErpObjectEntity splitObj = new ErpObjectEntity();
        //对象关系
        ErpObjectRelationshipEntity relation = new ErpObjectRelationshipEntity();
        splitObjDescribe.setSplitObj(splitObj);
        splitObjDescribe.setObjRelation(relation);
        splitObjDescribe.setMain(true);
        splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        splitObj.setErpObjectApiName(splitApiName);
        splitObj.setErpObjectName(realObjName);
        splitObj.setErpObjectExtendValue("");
        relation.setSplitSeq(1);
        relation.setErpRealObjectApiname(realApiName);
        relation.setErpSplitObjectApiname(splitApiName);
        relation.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
        splitObjDescribes.put(splitApiName, splitObjDescribe);
        //主对象增加id字段
        fillIdField(splitApiName, splitObjDescribe);
    }

    private void analysisDetailObj(JSONObject splitObject, LinkedHashMap<String, SplitObjDescribe> splitObjDescribes, String mainRealApiName, String mainSplitApiName,String realObjName,Map<String, String> id2ObjApiName) {
        SplitObjDescribe splitObjDescribe = new SplitObjDescribe();
        String detailApiName = splitObject.getString("Key");
        String splitApiName = getDetailSplitApiName(mainRealApiName,detailApiName);
        id2ObjApiName.put(splitObject.getString("Id"), splitApiName);
        String realName = realObjName+"-"+splitObject.getString("Name");
        //拆分对象
        ErpObjectEntity splitObj = new ErpObjectEntity();
        //对象关系
        ErpObjectRelationshipEntity relation = new ErpObjectRelationshipEntity();
        splitObjDescribe.setSplitObj(splitObj);
        splitObjDescribe.setObjRelation(relation);
        splitObjDescribe.setParentObjApiName(mainSplitApiName);
        splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        splitObj.setErpObjectApiName(splitApiName);
        splitObj.setErpObjectName(realName);
        splitObj.setErpObjectExtendValue(detailApiName);
        relation.setSplitSeq(1);
        relation.setErpRealObjectApiname(mainRealApiName);
        relation.setErpSplitObjectApiname(splitApiName);
        relation.setSplitType(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT);
        splitObjDescribes.put(splitApiName, splitObjDescribe);
        //增加明细id字段
        fillDetailIdField(mainSplitApiName, splitApiName, splitObjDescribe);
    }

    private String getDetailSplitApiName(String mainSplitApiName, String detailApiName) {
        return mainSplitApiName+"."+detailApiName;
    }


    private void fillIdField(String realObjApiName, SplitObjDescribe splitObjDescribe) {
        ErpObjectFieldEntity nField = new ErpObjectFieldEntity();
        nField.setErpObjectApiName(realObjApiName);
        nField.setFieldApiName("id");
        nField.setFieldLabel("ID");
        nField.setFieldDefineType(ErpFieldTypeEnum.id);
        splitObjDescribe.getFields().put(nField.getFieldApiName(), nField);

    }

    private void fillDetailIdField(String mainSplitApiName, String splitApiName, SplitObjDescribe splitObjDescribe) {
        ErpObjectFieldEntity detailIdField = new ErpObjectFieldEntity();
        detailIdField.setErpObjectApiName(splitApiName);
        detailIdField.setFieldApiName("id");
        detailIdField.setFieldLabel(I18NStringEnum.s1183.getI18nValue());
        detailIdField.setFieldDefineType(ErpFieldTypeEnum.id);
        ErpObjectFieldEntity masterDetailField = new ErpObjectFieldEntity();
        masterDetailField.setErpObjectApiName(splitApiName);
        masterDetailField.setFieldApiName("fake_master_detail");
        masterDetailField.setFieldLabel(I18NStringEnum.s1174.getI18nValue());
        masterDetailField.setFieldDefineType(ErpFieldTypeEnum.master_detail);
        masterDetailField.setFieldExtendValue(mainSplitApiName);
        splitObjDescribe.getFields().put(detailIdField.getFieldApiName(), detailIdField);
        splitObjDescribe.getFields().put(masterDetailField.getFieldApiName(), masterDetailField);
    }

    @Data
    @EqualsAndHashCode
    public static class Option implements Serializable {
        private Object value;
        private Object label;
    }

}
