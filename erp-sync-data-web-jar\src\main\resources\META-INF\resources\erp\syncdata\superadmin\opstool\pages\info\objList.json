{"type": "page", "title": "ERP对象", "remark": null, "name": "objList", "toolbar": [], "body": [{"type": "crud", "name": "objs", "api": {"url": "../listObjInfos", "sendOn": "tenantId != null"}, "loadDataOnce": true, "defaultParams": {"perPage": 100}, "headerToolbar": ["export-csv", "reload", "bulkActions"], "bulkActions": [], "filter": {"mode": "horizontal", "body": [{"size": "sm", "label": "tenantId", "type": "input-text", "name": "tenantId", "onEvent": {"blur": {"actions": [{"actionType": "reload", "componentId": "dcIdSelect"}]}}}, {"id": "dcIdSelect", "name": "dcId", "label": "数据中心", "type": "select", "size": "lg", "labelField": "dataCenterName", "valueField": "id", "selectFirst": true, "clearable": true, "source": {"method": "get", "url": "../listDcInfos?tenantId=${tenantId}", "sendOn": "tenantId != null", "autoRefresh": false}, "searchable": true}]}, "columns": [{"name": "realObjApiName", "label": "realObjApiName"}, {"name": "splitObjApiName", "label": "splitObjApiName"}, {"name": "objName", "label": "objName"}, {"name": "splitType", "label": "splitType"}, {"name": "extentValue", "label": "extentValue"}, {"name": "splitSeq", "label": "splitSeq"}, {"name": "missingObject", "label": "missingObject", "type": "status"}, {"name": "missingRelation", "label": "missingRelation", "type": "status"}, {"type": "operation", "label": "操作", "fixed": "right", "buttons": [{"type": "button", "label": "字段", "level": "link", "actionType": "link", "link": "./fieldList?perPage=100&page=1&tenantId=${tenantId}&dcId=${dcId}&objApiName=${splitObjApiName}"}, {"type": "button", "level": "link", "label": "字段扩展", "actionType": "link", "link": "./fieldExtendList?perPage=100&page=1&tenantId=${tenantId}&dcId=${dcId}&objApiName=${realObjApiName}"}]}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "combineNum": 0, "bodyClassName": "panel-default"}]}