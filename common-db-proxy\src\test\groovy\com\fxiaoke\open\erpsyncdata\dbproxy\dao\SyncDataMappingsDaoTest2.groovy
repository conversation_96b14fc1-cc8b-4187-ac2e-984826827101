package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import ch.qos.logback.classic.Level
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil
import org.apache.ibatis.io.Resources
import org.apache.ibatis.mapping.Environment
import org.apache.ibatis.session.SqlSessionFactory
import org.apache.ibatis.session.SqlSessionFactoryBuilder
import org.apache.ibatis.session.defaults.DefaultSqlSessionFactory
import spock.lang.Specification
import org.apache.ibatis.session.Configuration

import javax.sql.DataSource

class SyncDataMappingsDaoTest2 extends Specification {

    def namespace = "com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao"

    def "get sql"() {
        given:
        SqlSessionFactory factory = new SqlSessionFactoryBuilder().build(
                // 该配置只涉及mybatis的映射，不启动Spring或任何数据库
                Resources.getResourceAsStream("mybatisTestConfiguration/mybatis-config.xml"));
        def conf = factory.getConfiguration()
        conf.setEnvironment(GroovyMock(Environment.class) {
            getDataSource() >> GroovyMock(DataSource) {
                // Do nothing
            }
        })

        when:
        def maps = conf.getMappedStatements()
        maps.forEach({ it -> println(it.getId()) })
        def sql = conf.getMappedStatement(namespace+".truncateTable").getBoundSql("88783").getSql()
        then:
        noExceptionThrown()
//        println(sql)
    }
}
