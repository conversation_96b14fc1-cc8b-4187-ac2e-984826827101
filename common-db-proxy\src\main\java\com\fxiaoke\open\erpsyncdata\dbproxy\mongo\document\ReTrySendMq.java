package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RetryDataEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.DataVerificationTaskStatusEnum;
import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 15:41 2024/4/7
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class ReTrySendMq implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 重试的数据类型
     */
    private String retryDataEnum;

    /**
     * crm-erp
     */
    private boolean crm2erp;

    /**
     * mq 详情
     */
    private String mqMsg;
    /**
     * 重试状态, 0:待重试，1：已重试，2：重试失败
     * 觉得先不及时删除。避免刷数据的逻辑有问题，还可以重刷
     */
    private Integer status;
    /**
     * 发送结果详情
     */
    private String resultMsg;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *更新时间
     */
    private Date updateTime;

}
