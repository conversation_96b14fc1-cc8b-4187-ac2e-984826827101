package com.fxiaoke.open.erpsyncdata.dbproxy.dao.custom;

import com.fxiaoke.open.erpsyncdata.common.ibatis.TenantBaseMapper;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjIdNumberMappingEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * erp_obj_id_number_mapping表对应的dao类
 * <AUTHOR>
 * @date 20230313
 */
@Repository
public interface ErpObjIdNumberMappingDao extends TenantBaseMapper<ErpObjIdNumberMappingEntity>, ITenant<ErpObjIdNumberMappingDao> {
    int updateDataNumber(@Param("tenantId") String tenantId,
                     @Param("dcId") String dcId,
                     @Param("objApiName") String objApiName,
                     @Param("dataId") String dataId,
                     @Param("dataNumber") String dataNumber);

    ErpObjIdNumberMappingEntity queryByDataId(@Param("tenantId") String tenantId,
                                              @Param("dcId") String dcId,
                                              @Param("objApiName") String objApiName,
                                              @Param("dataId") String dataId);

    List<ErpObjIdNumberMappingEntity> queryByDataNumber(@Param("tenantId") String tenantId,
                                                        @Param("dcId") String dcId,
                                                        @Param("objApiName") String objApiName,
                                                        @Param("dataNumber") String dataNumber);
}
