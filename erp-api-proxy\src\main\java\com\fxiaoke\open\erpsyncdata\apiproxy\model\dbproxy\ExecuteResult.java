package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
@SuppressWarnings("rawtypes")
@Data
public class ExecuteResult{
    private String referenceId;
    private Object body;

    public ObjectData toObjectData() {
        if (body instanceof Map) {
            return ObjectData.convert((Map) body);
        }
        return new ObjectData();
    }


    public List<ObjectData> toObjectDataList() {
        if (body instanceof List) {
            List<ObjectData> result = ((List<?>) body).stream().map(o -> {
                if (o instanceof Map) {
                    return ObjectData.convert((Map) o);
                }
                return new ObjectData();
            }).collect(Collectors.toList());
            return result;
        }
        return new ArrayList<>();
    }
}
