package com.fxiaoke.open.erpsyncdata.apiproxy.model.push;

import cn.hutool.core.map.multi.ListValueMap;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
public class StringRequest {
    private ListValueMap<String, String> headers;
    /**
     * 旧push未传这个，别用。
     */
    private LinkedHashMap<String, String> params;
    private String body;
}
