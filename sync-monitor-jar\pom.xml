<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-erp-sync-data</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>sync-monitor-jar</artifactId>
    <packaging>jar</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>sync-monitor-jar</name>

    <dependencies>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-preprocess-data-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-i18n</artifactId>
            <version>${com.fxiaoke.syncdata.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>common-db-proxy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-common</artifactId>
            <version>${com.fxiaoke.syncdata.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-jwt</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-cron</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <!-- 定时任务-->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-job-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--com.alibaba.rocketmq 升级为 org.apache.rocketmq 后,我们的代码里面已经不再使用com.alibaba的mq了。
       alibaba的mq还不能去掉, 因为我们引入的fs-uc-cache-no-dubbo.xml文件里面用到了。 -->
        <!--        <dependency>-->
        <!--            <artifactId>rocketmq-client</artifactId>-->
        <!--            <groupId>com.alibaba.rocketmq</groupId>-->
        <!--            <version>3.2.6</version>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>com.github.phantomthief</groupId>
            <artifactId>more-lambdas</artifactId>
            <version>0.1.55</version>
        </dependency>

        <!-- 添加 Mockito 依赖 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
