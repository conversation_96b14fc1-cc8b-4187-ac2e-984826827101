package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class IntegrationStreamNodesData implements Serializable{

    /**
     * 回写组件
     */
    private ReverseWriteNode reverseWriteNode;

    /**
     * 通知组件
     */
    private NotifyComplementNode notifyComplementNode;
    /**
     * 检查中间表节点
     */
    private CheckSyncDataMappingNode checkSyncDataMappingNode;

    /**
     * 通过源数据查询crm节点
     */
    private QueryCrmObject2DestNode queryCrmObject2DestNodeBySource;
    /**
     * 通过转换后数据查询crm节点
     */
    private QueryCrmObject2DestNode queryCrmObject2DestNodeByDest;
    /**
     * 数据范围-查询crm
     */
    private SyncConditionsQueryDataNode syncConditionsQueryDataNode;
    /**
     * 不更新字段ApiName,同步更新到字段映射的notUpdateField字段
     */
    private Map<String,List<String>> objApiName2NotUpdateFieldApiName;
    /**
     * 需要返回字段ApiName，
     */
    private Map<String,List<String>> objApiName2NeedReturnFieldApiName;
    /**
     * 错误数据重试
     */
    private ReSyncErrorDataNode reSyncErrorDataNode;

    @Data
    public static class SyncConditionsQueryDataNode implements Serializable{
        private List<QueryObjectMappingData> queryObjectMappingData; //查询crm
        //不支持明细
        //同步条件
        private Integer syncCondition; //同步条件，1：查到同步，2：查不到同步
    }

    @Data
    public static class CheckSyncDataMappingNode implements Serializable{
        private QueryObjectMappingData queryObjectMappingData; //查询crm
        private DetailObjectIdFieldMappingsData source2SyncDataMapping;//集成流源对象字段映射到中间表、查出来的目标对象字段映射到中间表
        private DetailQueryObject2SyncDataMappingsData detailCheckSyncDataMappingData;//明细
    }

    @Data
    public static class QueryCrmObject2DestNode implements Serializable{
        private List<QueryObjectToDestObjectData> queryObjectToDestObject;
        private DetailQueryObjectMappingsData detailQueryData2DestDataMapping;//明细
    }

    @Data
    public static class ReverseWriteNode implements Serializable{
        /** 源从对象apiName */
        private String sourceObjectApiName;
        /** 目标从对象apiName */
        private String destObjectApiName;
        private FieldMappingsData fieldMappings;
        private DetailObjectMappingsData detailObjectMappings = new DetailObjectMappingsData();
    }

    @Data
    public static class NotifyComplementNode implements Serializable{
        /** 通知类型 */
        private List<NotifyType> notifyType;
        /** 是否需要通知回写crm失败 */
        private Boolean needNotifyReverseWrite2CrmFailed;
       /** 是否需要通知同步后函数执行失败 */
        private Boolean needNotifyAfterFuncFailed;
        /** 通知状态条件 */
        private List<Integer> notifyStatus;
        /**
         * 状态详情 通过  包含  xx关键字来筛选
         */
        private List<NotifyConditionFilter> notifyConditionFilters;
        /**
         * 通知人员的列表
         */
        private List<Integer> notifyEmployees;
        /**
         * 通知人员的角色
         */
        private List<String> notifyRoles;
        /**
         * 通知的数据相关变量 数据负责人、数据创建人
         */
        private List<String> dataRelatedOwner;

        /**
         * 生成的表达式语言
         */
        private String notifyConditionAviator;
    }

    @Data
    public static class NotifyConditionFilter implements Serializable{
        /**
         * 判断字段 先默认remark
         */
        private String fieldApiName="remark";

        /** 条件value */
        private String fieldValue;
        /**
         * 状态详情 通过  包含  xx关键字来筛选
         */
        private String operator="LIKE";

    }

    @Data
    public static class ReSyncErrorDataNode implements Serializable{
        private String reSyncCondition;//未使用
        private Integer reSyncTimeInterval;//单位固定分钟
        private Boolean reSyncRightNow;
        private Integer reSyncTopLimit;

    }

    public static final List<String> GET_CRM_DATA_VARIABLES =Lists.newArrayList("owner","last_modified_by","created_by");


}
