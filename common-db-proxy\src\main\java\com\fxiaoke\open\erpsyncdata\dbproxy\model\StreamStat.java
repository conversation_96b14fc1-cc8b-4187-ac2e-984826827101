package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Date;
import java.util.List;

/**
 * 集成流统计信息
 *
 * <AUTHOR> (^_−)☆
 */
@Data
@Builder
@FieldNameConstants
public class StreamStat {
    private String tenantId;
    /**
     * 下游企业Id
     */
    private String downstreamId;
    /**
     * 数据中心Id
     */
    private Object dcId;
    /**
     * 集成流Id
     */
    private String streamId;
    /**
     * 任务traceId
     */
    private String traceId;
    /**
     * {@link com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum}
     */
    private Integer status;
    /**
     * 集成流告警状态
     */
    private StreamStatus streamStatus;
    /**
     * {@link TenantTypeEnum}
     */
    private TenantTypeEnum destTenantType;
    private ObjMappingStat mainMapping;
    private List<ObjMappingStat> detailMappings;
    /**
     * 最近告警时间
     */
    private long lastAlertTime;
    /**
     * 最近同步事件
     */
    private long lastSyncTime;

    private long mappingTotal;
    private long mappingFailedTotal;
    /**
     * 创建时间，储存时赋值
     */
    private Date createTime;

    @Data
    @Builder
    @FieldNameConstants
    public static class ObjMappingStat {
        private TenantTypeEnum destTenantType;
        private ObjMappingType objMappingType;
        private String crmObjApiName;
        private String erpObjApiName;
        /**
         * 暂不使用，无计算
         */
        private long mappingTotal;
        private long mappingFailedTotal;

        public ObjMappingStat setObjApiNames(TenantTypeEnum destTenantType, String sourceObjApiName, String destObjApiName){
            this.destTenantType = destTenantType;
            if (destTenantType == TenantTypeEnum.CRM) {
                erpObjApiName = sourceObjApiName;
                crmObjApiName = destObjApiName;
            }else {
                erpObjApiName = destObjApiName;
                crmObjApiName = sourceObjApiName;
            }
            return this;
        }

        public String getSourceObjApiName(){
            return destTenantType == TenantTypeEnum.CRM ? erpObjApiName : crmObjApiName;
        }

        public String getDestObjApiName(){
            return destTenantType == TenantTypeEnum.ERP ? erpObjApiName : crmObjApiName;
        }
    }

    public enum ObjMappingType {
        main, detail
    }

    public enum StreamStatus {
        normal, alerting
    }
}
