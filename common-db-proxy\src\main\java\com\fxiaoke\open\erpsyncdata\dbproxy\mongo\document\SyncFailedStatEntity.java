package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/3
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
public class SyncFailedStatEntity {
    @BsonId
    private ObjectId id;
    private String tenantId;
    private String dataCenterId;
    private String streamId;
    private Date createTime;
    private Date updateTime;

    /**
     * 上次熔断时间
     */
    private Long lastBreakTime = 0L;
    /**
     * 上次统计时间
     */
    private Long lastCountTime = 0L;
    /**
     * 上次统计数量
     */
    private int lastCount;

}
