package com.fxiaoke.open.erpsyncdata.apiproxy.manager.jdy;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialWayDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.HeaderManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.factory.SpecialObjHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpListArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.JdyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_JDY)
public class JDYDataManager extends BaseErpDataManager {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SpecialObjHandlerFactory specialObjHandlerFactory;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SpecialWayDataService specialWayDataService;
    @Autowired
    private HeaderManager headerManager;
    //crm对象与云星辰对象的映射。
    private String ProductObj="material";//产品
    private String AccountObj="customer";
    private String WarehouseObj="store";
    private String StockObj="inventory";
    private String SalesOrderObj="sal_order";
    private String DeliveryNoteObj="sal_out_bound";
    private String PaymentObj="ar_credit";
    private String clientId="";
    private String clientSecret="";
    private String appSecret="";
    private String appKey="";
    public static final String KINGDEE_AUTH_TOKEN = "https://api.kingdee.com/jdyconnector/app_management/kingdee_auth_token";
    //TODO 先固定写函数
    private String JDY_APL_FUNC_NAME="JDYManagerErpAPL__c";
    @Autowired
    private AplManager aplManager;
    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {

        JdyConnectParam jdyConnectParam = connectInfo.getChannel().getAndCheckConnectParam(erpIdArg.getTenantId(),connectInfo.getConnectParams());
        List<Object> requestBody=Lists.newArrayList();
        requestBody.add(erpIdArg);
        requestBody.add(connectInfo);
        String viewPath = jdyConnectParam.getServicePath().getView();

       Result<StandardData> standardDataResult=aplManager.executeAplMethod(erpIdArg.getTenantId(), connectInfo.getId(), erpIdArg.getObjAPIName(), ErpObjInterfaceUrlEnum.queryMasterById, getAPLapiNameByDataCenterId(connectInfo), requestBody, null,
                null,
               new TypeReference<StandardData>(){});

        return standardDataResult;
    }






    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<List<StandardData>> resultData=null;
        Result<StandardData> erpObjData = this.getErpObjData(erpIdArg, connectInfo);
        resultData=new Result<>(erpObjData.getErrCode(),erpObjData.getErrMsg(),null);
        resultData.setErrMsg(erpObjData.getErrMsg());
        if(erpObjData.isSuccess()&&ObjectUtils.isNotEmpty(erpObjData.getData())){
            resultData.setData(Lists.newArrayList(erpObjData.getData()));
        }
        return resultData;
    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        JdyConnectParam jdyConnectParam = connectInfo.getChannel().getAndCheckConnectParam(timeFilterArg.getTenantId(),connectInfo.getConnectParams());
        final ErpObjInterfaceUrlEnum interEnum;
        String tenantId = timeFilterArg.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objAPIName = timeFilterArg.getObjAPIName();
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            interEnum = ErpObjInterfaceUrlEnum.queryInvalid;
        } else {
            interEnum = ErpObjInterfaceUrlEnum.queryMasterBatch;
        }
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objAPIName,
                interEnum,
                getAPLapiNameByDataCenterId(connectInfo),
                ListUtil.of(timeFilterArg, connectInfo),
                null,
                timeFilterArg,
                new TypeReference<StandardListData>() {
                }
        );
    }



    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        String connectParam = connectInfo.getConnectParams();
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardData.getMasterFieldVal().getApiName();
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.create;
        //创建时取出Id字段
        standardData.removeId();
       return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
               getAPLapiNameByDataCenterId(connectInfo),
                ListUtil.of(standardData, connectInfo),
                null,
                null,
                new TypeReference<ErpIdResult>() {
                }
        );

    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {

        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        String connectParam = connectInfo.getConnectParams();
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardInvalidData.getMasterFieldVal().getApiName();
        String dataId = standardInvalidData.getMasterFieldVal().getId();
        String dataName = standardInvalidData.getMasterFieldVal().getName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.invalid;
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                getAPLapiNameByDataCenterId(connectInfo),
                ListUtil.of(standardInvalidData, connectInfo),
                null,
                null,
                new TypeReference<String>() {
                }
        );

    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return null;
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        final String objApiName = standardData.getMasterFieldVal().getApiName();
        standardData.getMasterFieldVal().remove("_id");
        JdyConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId,connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.update;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                getAPLapiNameByDataCenterId(connectInfo),
                ListUtil.of(standardData, connectInfo),
                null,
                null,
                new TypeReference<ErpIdResult>() {
                }
        );

    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {

        return null;
    }

    public Result<StandardListData> listErpObjFieldsByUrl(ErpListArg arg, ErpConnectInfoEntity connectInfo) {
        final ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.queryList;
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objAPIName;
        String url = arg.getUrl();
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
            objAPIName = url.substring(lastSlashIndex + 1);
        } else {
            return Result.newSystemError(I18NStringEnum.s3676);
        }
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                                           dataCenterId,
                                           objAPIName,
                                           interEnum,
                                           getAPLapiNameByDataCenterId(connectInfo),
                                           ListUtil.of(arg, connectInfo),
                                           null,
                                           null,
                                           new TypeReference<StandardListData>() {
                                           }
        );
    }

    public Result<String> executeToken(String tenantId,String dataCenterId,String functionApiName){

        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.executeGetToken;
        //使用apl类 bd_materialgroup aplmanager会判断apiname。默认先用bd_materialgroup
        Result<String> tokenResult = aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "bd_materialgroup",
                interEnum,
                functionApiName,
                Lists.newArrayList(),
                null,
                null,
                new TypeReference<String>() {
                }
        );
        return tokenResult;
    }

    public String getAPLapiNameByDataCenterId(ErpConnectInfoEntity erpConnectInfoEntity){
        if(ErpChannelEnum.ERP_JDY.name().equals(erpConnectInfoEntity.getChannel().name())){
            JdyConnectParam jdyConnectParam= JSONObject.parseObject(erpConnectInfoEntity.getConnectParams(),JdyConnectParam.class);
            if(StringUtils.isNotEmpty(jdyConnectParam.getAplClassApiName())){
                return jdyConnectParam.getAplClassApiName();
            }
        }
        return JDY_APL_FUNC_NAME;

    }


}
