package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class BeanUtil {
    public static <T> List<T> copyList(List<?> source, Class<T> cls) {
        if (source == null) {
            return new ArrayList<>(0);
        }
        List<T> targetList = new ArrayList<>(source.size());
        for (Object obj : source) {
            targetList.add(copy(obj, cls));
        }
        return targetList;
    }

    public static <T, V> List<V> deepCopyList(List<T> srcList, Class<V> tarClass) {
        if (srcList == null) {
            return new ArrayList<>(0);
        }
        List<V> result = new ArrayList<>(srcList.size());
        for (T t : srcList) {
            V v = deepCopy(t, tarClass);
            result.add(v);
        }
        return result;
    }

    public static <T> T copy(Object obj, Class<T> cls) {
        try {
            T t = cls.newInstance();
            BeanUtils.copyProperties(obj,t);
            return t;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T, V> V deepCopy(T srcBean, Type tarType) {
        if (srcBean == null) {
            return null;
        }
        //这个会将null值也序列化
        String json = JSON.toJSONString(srcBean, SerializerFeature.WriteMapNullValue);
        return JSON.parseObject(json, tarType);
    }

    public static <V> V parse(String srcJson, Type tarType) {
        if (srcJson == null) {
            return null;
        }
        return JSON.parseObject(srcJson, tarType);
    }
}
