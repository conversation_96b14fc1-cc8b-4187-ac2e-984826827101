<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:beans="http://www.springframework.org/schema/beans"
       xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context-3.2.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
	                       http://www.springframework.org/schema/mvc
	                       http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">


    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata">
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
    </context:component-scan>



    <aop:aspectj-autoproxy/>

    <mvc:default-servlet-handler/>

    <mvc:annotation-driven>
        <mvc:message-converters>
            <!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
            <beans:bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <beans:constructor-arg value="UTF-8"/>
            </beans:bean>
            <!--不使用fsConverter是因为无法反序列化带泛型的类型-->
            <beans:bean class="com.fxiaoke.open.erpsyncdata.web.interceptor.CepGsonConverter">
                <beans:property name="supportedMediaTypes">
                    <beans:list>
                        <beans:value>application/json</beans:value>
                    </beans:list>
                </beans:property>
            </beans:bean>
            <bean class="com.facishare.cep.plugin.converter.ProtobufConverter" id="protobufConverter"/>
            <bean class="com.facishare.cep.plugin.converter.SimpleJsonConverter" id="simpleJsonConverter"/>
        </mvc:message-converters>
        <!--异步请求超时配置-->
        <mvc:async-support default-timeout="15000" task-executor="taskExecutor"/>
        <mvc:argument-resolvers>
            <bean class="com.facishare.cep.plugin.resolver.RequestAnnotationResolver"/>
        </mvc:argument-resolvers>
        <mvc:return-value-handlers>
            <bean class="com.facishare.cep.plugin.idempotent.IdempotentReturnValueHandler"/>
        </mvc:return-value-handlers>
    </mvc:annotation-driven>
    <!-- 拦截器配置 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/**/anonymousQuery" />
            <mvc:exclude-mapping path="/api/v1/**" />
            <mvc:exclude-mapping path="/monitor/**" />
            <bean class="com.fxiaoke.open.erpsyncdata.web.interceptor.UserInterceptors"/>
        </mvc:interceptor>
    </mvc:interceptors>
    <!-- 重要！配置swagger资源不被拦截 -->
    <mvc:resources mapping="swagger-ui.html" location="classpath:/META-INF/resources/" />
    <mvc:resources mapping="/webjars/**" location="classpath:/META-INF/resources/webjars/" />
    <!-- 重要！将你的Swagger2Config配置类注入 -->
    <bean id="swagger2Config" class="com.fxiaoke.open.erpsyncdata.web.config.Swagger2Config"/>

    <beans:import resource="classpath*:spring/erp-sync-data-whole-war-applicationContext.xml" />
</beans>
