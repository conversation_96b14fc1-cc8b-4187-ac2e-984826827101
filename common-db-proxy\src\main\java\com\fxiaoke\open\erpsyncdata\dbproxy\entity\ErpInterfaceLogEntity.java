package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import java.util.Date;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * erp接口日志
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DataId("dataId")
@ObjApiName("objectApiName")
@Table(name = "erp_interface_log")
public class ErpInterfaceLogEntity {
    /**
    * 主键
    */
    @Id
    private String id;

    private String traceId;

    /**
    * 企业ei
    */
    @TenantID
    private String tenantId;

    /**
    * 对象apiName
    */
    private String objectApiName;

    /**
    * 数据Id
    */
    private String dataId;

    /**
    * 数据主属性
    */
    private String name;

    /**
    * 操作类型
    */
    private Integer operationType;

    /**
    * 请求参数
    */
    private String request;

    /**
    * 返回结果
    */
    private String response;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;
}