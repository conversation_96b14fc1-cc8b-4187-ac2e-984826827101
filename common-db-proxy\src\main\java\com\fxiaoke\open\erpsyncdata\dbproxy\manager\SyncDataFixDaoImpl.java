package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.lang.Pair;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.SpeedLimiter;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DeleteWithLogicBySourceData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataThreadHolder;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ObjectIdTimeUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/5/6
 */
@Repository
@Primary
@Slf4j
public class SyncDataFixDaoImpl implements SyncDataFixDao {
    @Autowired
    private SyncDataThreadHolder syncDataThreadHolder;
    @Autowired
    private CHSyncDataManager chSyncDataManager;
    @Resource(name = "syncDataExecutor")
    private Executor syncDataExecutor;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private DataSyncNotifyManager dataSyncNotifyManager;

    @Autowired
    private SyncDataFixDaoImpl syncDataFixDao;
    @Autowired
    private MonitorReportManager monitorReportManager;


    /**
     * 仅保留到缓存中，会在同步后，再统一插入到数据库
     *
     * @param t
     */
    @Override
    public void insertCache(SyncDataEntity t) {
        syncDataThreadHolder.put(t);
    }

    /**
     * 从线程变量移除并落库
     *
     * @param tenantId
     */
    @Override
    public void removeCacheAndInsertDb(String tenantId) {
        Map<String, SyncDataEntity> cache = syncDataThreadHolder.getAndRemove();

        final Collection<SyncDataEntity> dataList = new ArrayList<>();
        //清理数据
        cache.forEach((k, v) -> {
            if (v == null) {
                //值为空，异常，打印错误日志方便定位
                log.warn("sync data value is null,{}", k);
                return;
            }
            dataList.add(v);
        });
        if (dataList.isEmpty()) {
            return;
        }
        syncDataExecutor.execute(() -> {
            syncDataFixDao.insertSyncData(tenantId, dataList);
//            addErpErrorMonitor(tenantId, dataList);
            pushDataToNotify(tenantId, dataList);
            monitorReportManager.send2SyncDataErrorMonitor(dataList);
        });
    }

    @Override
    public Map<String, SyncDataEntity> getTenantSyncDataCache(String tenantId) {
        return syncDataThreadHolder.getTenantSyncDataCache();
    }

    @SpeedLimiter(value = TenantConfigurationTypeEnum.WRITE_SYNC_DATA_LIMIT, countKey = "WRITE_SYNC_DATA", resetFields = {"#data.sourceData", "#data.destData"}, replaceI18nKey = I18NStringEnum.s3640, type = ObjectData.class)
    public void insertSyncData(String tenantId, Collection<SyncDataEntity> values) {
        Boolean notSaveSyncData = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NOT_SAVE_SYNC_DATA_TENANTS);
        if (BooleanUtils.isTrue(notSaveSyncData)) {
            //不保存syncData
            return;
        }
        if (CollectionUtils.isEmpty(values)) {
            log.info("batch upsert syncData empty");
            return;
        }

        try {
            List<String> bulkWriteResult = chSyncDataManager.batchReplace(tenantId, values);
            log.debug("batch upsert syncData,result:{}", bulkWriteResult);
            //检查当前数据syncData数量，大于两条则删除1条。
            //先按对象分组,srcObjApiName,destObjApiName,srcDataId
            Map<Pair<String, String>, List<String>> objGroup = new HashMap<>();
            for (SyncDataEntity v : values) {
                String sourceDataId = v.getSourceDataId();
                objGroup.computeIfAbsent(Pair.of(v.getSourceObjectApiName(), v.getDestObjectApiName()),
                        k -> new ArrayList<>()).add(sourceDataId);
            }
            objGroup.forEach((pair, ids) -> {
                chSyncDataManager.limitGroupByObj(tenantId, pair, ids);
            });
        } catch (Exception e) {
            log.warn("batch upsert syncData throw exception", e);
        }
    }

//    private void addErpErrorMonitor(final String tenantId, final Collection<SyncDataEntity> cache) {
//    }

    /**
     * 同步后的状态通知发送到localDispatcher
     *
     * @param tenantId
     * @param syncDataEntityMap
     */
    private void pushDataToNotify(String tenantId, Collection<SyncDataEntity> syncDataEntityMap) {
        if (CollectionUtils.isEmpty(syncDataEntityMap)) {
            log.info("pushDataToNotify  syncData empty");
            return;
        }
        dataSyncNotifyManager.pushDataToNotify(tenantId, syncDataEntityMap);
    }


    @Override
    public List<SyncDataEntity> listByIds(String tenantId, Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        org.apache.commons.lang3.tuple.Pair<Long, Long> objectStrIdsTime = ObjectIdTimeUtil.getObjectStrIdsTime(Lists.newArrayList(ids));
        //直接从数据库取。
        List<SyncDataEntity> mongoRes = chSyncDataManager.listByIds(tenantId, ids,objectStrIdsTime.getLeft(),objectStrIdsTime.getRight());
        return mongoRes;
    }

    @Override
    public List<SyncDataEntity> listByIdsCachePri(String tenantId, Collection<String> ids) {
        HashSet<String> ids1 = new HashSet<>(ids);
        List<SyncDataEntity> syncDataEntities = syncDataThreadHolder.batchGetAndRemoveId(tenantId, ids1);
        if (!ids1.isEmpty()) {
            List<SyncDataEntity> fromDb = listByIds(tenantId, ids);
            syncDataEntities.addAll(fromDb);
        }
        return syncDataEntities;
    }


    @Override
    public List<SyncDataEntity> listSimpleByIds(String tenantId, Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        org.apache.commons.lang3.tuple.Pair<Long, Long> objectStrIdsTime = ObjectIdTimeUtil.getObjectStrIdsTime(Lists.newArrayList(ids));
        //直接从数据库取。
        List<SyncDataEntity> mongoRes = chSyncDataManager.listSimpleByIds(tenantId, ids,objectStrIdsTime.getLeft(),objectStrIdsTime.getRight());
        return mongoRes;
    }

    @Override
    public List<SyncDataEntity> listBySourceData(String tenantId, String sourceTenantId, List<String> sourceObjectApiName, List<String> sourceDataIds,
                                                 String destTenantId, List<String> destObjectApiName, Long startLogTime, Long endLogTime) {
        List<SyncDataEntity> syncDataEntities = chSyncDataManager.listBySourceData(tenantId, sourceTenantId, sourceObjectApiName, sourceDataIds,
                destTenantId, destObjectApiName,startLogTime,endLogTime);
        return syncDataEntities;
    }

    @Override
    public int updateDestDataById(String tenantId, String id, ObjectData destData) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setDestData(ObjectData.convert(destData));
        return 1;
    }


    @Override
    public int updateSourceDataById(String tenantId, String id, ObjectData sourceData, Integer destEventType) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setSourceData(ObjectData.convert(sourceData));
        entity.setDestEventType(destEventType);
        return 1;
    }

    @Override
    public int updateRemarkAndStatus(String tenantId, String id, String remark) {
        return chSyncDataManager.updateRemark(tenantId, id, remark);
    }

    @Override
    public int updateWaitingStatus(String tenantId, String id, Integer status, String remark) {
        return chSyncDataManager.updateStatus(tenantId, id, status, remark);
    }

    @Override
    public List<SyncDataEntity> listByStatusListAndEndUpdateTime(String tenantId, List<Integer> statusList, Long startUpdateTime, Long endUpdateTime, Integer offset, Integer limit) {
        return chSyncDataManager.listByStatusListAndEndUpdateTime(tenantId, statusList, startUpdateTime, endUpdateTime, offset, limit);
    }

    @Override
    public int deleteSingleSyncData(String tenantId, Long updateTime, String id) {
        return chSyncDataManager.deleteSingleSyncData(tenantId, updateTime, id);
    }

    @Override
    public Integer deleteWithLogicBySourceDatas(String tenantId, List<DeleteWithLogicBySourceData> deleteWithLogicBySourceDataList) {
        if (CollectionUtils.isEmpty(deleteWithLogicBySourceDataList)) {
            return 0;
        }
        //聚类,sourceObjectApiName,destObjectApiName,sourceDataId,
        Map<String, Map<String, List<String>>> srcMap = deleteWithLogicBySourceDataList.stream().collect(
                Collectors.groupingBy(v -> v.getSourceObjectApiName(),
                        Collectors.groupingBy(u -> u.getDestObjectApiName(),
                                Collectors.mapping(i -> i.getSourceDataId(), Collectors.toList()))));
        Integer count = 0;
        for (Map.Entry<String, Map<String, List<String>>> entry : srcMap.entrySet()) {
            String srcObjApiName = entry.getKey();
            Map<String, List<String>> map2 = entry.getValue();
            for (Map.Entry<String, List<String>> e : map2.entrySet()) {
                String dstObjApiName = e.getKey();
                List<String> srcIds = e.getValue();
                count += chSyncDataManager.deleteWithLogicBySourceDatas(tenantId, srcObjApiName, dstObjApiName, srcIds);
            }
        }
        return count;
    }

    @Override
    public int deleteSyncDatas(String tenantId, String sourceObjApiName, String destObjApiName) {
        return chSyncDataManager.deleteSyncDatas(tenantId, sourceObjApiName, destObjApiName);
    }


    @Override
    public SyncDataEntity getFromThreadLocal(String tenantId, String id) {
        SyncDataEntity raw = syncDataThreadHolder.getRaw(tenantId, id);
        if (raw != null) {
            return raw;
        }
        return getByIdFromDb(tenantId, id);
    }


    @Override
    public SyncDataEntity getById(String tenantId, String id, String... returnFields) {
        SyncDataEntity entity = syncDataThreadHolder.get(tenantId, id);
        if (entity != null) {
            return entity;
        }
        return getByIdFromDb(tenantId, id,returnFields);
    }

    @Override
    public SyncDataEntity getByIdFromDb(String tenantId, String id, String... returnFields) {
        SyncDataEntity entity;
        entity = chSyncDataManager.getById(tenantId, id, returnFields);
        return entity;
    }

    @Override
    public SyncDataEntity getSimple(String tenantId, String id) {
        SyncDataEntity entity = syncDataThreadHolder.getSimple(tenantId, id);
        if (entity != null) {
            return entity;
        }
        return getSimpleFromDb(tenantId, id);
    }

    @Override
    public SyncDataEntity getSimpleFromDb(String tenantId, String id) {
        SyncDataEntity entity;
        entity = chSyncDataManager.getById(tenantId, id);
        return entity;
    }


    /**
     * 可能返回空
     */
    private SyncDataEntity getFromDbIfAbsent(String tenantId, String id) {
        boolean contains = syncDataThreadHolder.contains(tenantId, id);
        if (contains) {
            return syncDataThreadHolder.getRaw(tenantId, id);
        }
        SyncDataEntity byIdFromDb = getByIdFromDb(tenantId, id);
        //byIdFromDb为null也放进去
        syncDataThreadHolder.put(id, byIdFromDb);
        return syncDataThreadHolder.getRaw(tenantId, id);
    }

    @Override
    public int updateStatusAndDestDataIdBySuccess(String tenantId, String id, String destDataId, Integer newStatus, String remark) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setStatus(newStatus);
        entity.setRemark(remark);
        entity.setDestDataId(destDataId);
        return 1;
    }

    @Override
    public int updateDestEventTypeAndDestDataIdAndStatus(String tenantId, String id, Integer destEventType, String destDataId, Integer oldStatus, Integer newStatus, Long updateTime) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setDestEventType(destEventType);
        entity.setStatus(newStatus);
        entity.setDestDataId(destDataId);
        return 1;
    }

    /**
     * 实际上可能并不更新，下面同理。
     */
    @Override
    public int updateStatus(String tenantId, String id, Integer newStatus, String remark, String errorCode) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setStatus(newStatus);
        entity.setErrorCode(errorCode);
        entity.setRemark(remark);
        return 1;
    }

    @Override
    public int updateStatusByIds(String tenantId, List<String> ids, Integer newStatus, String remark, String errorCode) {
        Map<String, SyncDataEntity> tenantSyncDataCache = getTenantSyncDataCache(tenantId);
        Integer dataSize = 0;
        for (String id : ids) {
            SyncDataEntity syncDataEntity = tenantSyncDataCache.get(id);
            if (syncDataEntity == null) {
                continue;
            }
            dataSize++;
            syncDataEntity.setStatus(newStatus);
            syncDataEntity.setRemark(remark);
            syncDataEntity.setErrorCode(errorCode);
        }
        return dataSize;
    }

    @Override
    public void updateNodeMsg(String tenantId,String sourceObjApiName,String lastNodeName,String lastNodeStatus, String dataVersion,Boolean reverseWrite2CrmFailed,
                              String reverseWrite2CrmFailedRemark,Boolean afterFuncFailed,String afterFuncFailedRemark) {
        Map<String, SyncDataEntity> tenantSyncDataCache = getTenantSyncDataCache(tenantId);
        for (String id : tenantSyncDataCache.keySet()) {
            SyncDataEntity syncDataEntity = tenantSyncDataCache.get(id);
            if (syncDataEntity == null) {
                continue;
            }
            if(StringUtils.isNotBlank(sourceObjApiName) &&!sourceObjApiName.equals(syncDataEntity.getSourceObjectApiName())){
                continue;
            }
            setNodeMsg(lastNodeName, lastNodeStatus, dataVersion, syncDataEntity,reverseWrite2CrmFailed,reverseWrite2CrmFailedRemark,afterFuncFailed,afterFuncFailedRemark);
        }
    }

    @Override
    public void updateNodeMsgBySyncDataId(String tenantId, String syncDataId, String lastNodeName, String lastNodeStatus, String dataVersion) {
        SyncDataEntity syncDataEntity = getTenantSyncDataCache(tenantId).get(syncDataId);
        if (syncDataEntity != null) {
            setNodeMsg(lastNodeName, lastNodeStatus, dataVersion, syncDataEntity,null,null,null,null);
        }
    }

    private static void setNodeMsg(String lastNodeName, String lastNodeStatus, String dataVersion, SyncDataEntity syncDataEntity,Boolean reverseWrite2CrmFailed,
                                   String reverseWrite2CrmFailedRemark,Boolean afterFuncFailed,String afterFuncFailedRemark) {
        if (syncDataEntity.getData() == null) {
            syncDataEntity.setData(new SyncDataEntity.NodeMsg());
        }
        if (dataVersion != null) {
            syncDataEntity.getData().setDataVersion(dataVersion);
        }
        if (lastNodeName != null) {
            syncDataEntity.getData().setLastNodeName(lastNodeName);
        }
        if (lastNodeStatus != null) {
            syncDataEntity.getData().setLastNodeStatusMsg(lastNodeStatus);
        }
        if (reverseWrite2CrmFailed != null) {
            syncDataEntity.getData().setReverseWrite2CrmFailed(reverseWrite2CrmFailed);
        }
        if (reverseWrite2CrmFailedRemark != null) {
            syncDataEntity.getData().setReverseWrite2CrmFailedRemark(reverseWrite2CrmFailedRemark);
        }
        if (afterFuncFailed != null) {
            syncDataEntity.getData().setAfterFuncFailed(afterFuncFailed);
        }
        if (afterFuncFailedRemark != null) {
            syncDataEntity.getData().setAfterFuncFailedRemark(afterFuncFailedRemark);
        }
    }

    @Override
    public int updateDestData(String tenantId, String id, String destDataId, ObjectData destData, Integer oldStatus, Integer newStatus, Long updateTime) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setDestDataId(destDataId);
        entity.setDestData(ObjectData.convert(destData));
        entity.setStatus(newStatus);
        return 1;
    }

    @Override
    public int updateNeedReturnData(String tenantId, String id, ObjectData needReturnData, Long updateTime) {
        SyncDataEntity entity = getFromDbIfAbsent(tenantId, id);
        if (entity == null) {
            return 0;
        }
        entity.setNeedReturnDestObjectData(ObjectData.convert(needReturnData));
        return 1;
    }

    @Override
    public long countByTenantId(String tenantId) {
        return chSyncDataManager.countByTenantId(tenantId);
    }
}
