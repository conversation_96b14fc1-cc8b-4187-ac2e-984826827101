package com.fxiaoke.open.erpsyncdata.monitor.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
@Data
public class ProxyDataSourceInfo {
    private String url;
    /**
     * 用于生成鉴权token,rsa私钥
     */
    @JSONField(serialize = false)
    @JsonIgnore
    private String secretKey;
    private String statusField = "errorCode";
    private String msgField = "errorMessage";
    private String dataField = "data";
    private String successStatus = "0";
}
