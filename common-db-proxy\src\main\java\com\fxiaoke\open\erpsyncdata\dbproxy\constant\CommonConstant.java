package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
public interface CommonConstant {

    String ERP_SYNC_DATA_BUSINESS = "ERP_SYNC_DATA";

    int SUPER_ADMIN_USER = -10000;

    /**
     * redis key,ERPSYNCDATA:PROBE:SET:{tenantId},值为objApiName
     */
    String PROBE_DATA_SET_REDIS_KEY = "ERPSYNCDATA:PROBE:SET:%S";

    /**
     * redis key,ERPSYNCTEMPDATA:PROBE:SET:{tenantId},值为objApiName
     */
    String PROBE_TEMP_DATA_SET_REDIS_KEY = "ERPSYNCTEMPDATA:PROBE:SET:%S";

    String ONE = "1";

    /**
     * 用于储存开启专表企业的全局设置的id
     */
    String GLOBAL_DISABLE_DYNAMIC_TENANTS_CONFIG_ID = "99990001";


    /**
     * redis key 轮询持续失败次数
     */
    String REDIS_KEY_POLLING_CONTINUE_FAILED_COUNT = "erpSyncData:pollingFailed:%s";

    /**
     * redis key 同步失败增量
     */
    String REDIS_KEY_FAILED_SYNC_INCREMENT = "erpSyncData:fsIncrement:%s";
    /**
     * redis key 同步失败增量
     */
    String REDIS_KEY_FAILED_SYNC_ID_SET = "erpSyncData:fsIdSet:%s";
    /**
     * redis key 策略异常缓存信息 :tenantId:ployDetailId
     */
    String REDIS_KEY_EXCEPTION_PLOY_DETAIL = "erpSyncData:ployDetailException:%s:%s";
    /**
     * redis key 熔断的策略集合,:tenantId
     */
    String REDIS_KEY_PLOY_BREAK_SET = "erpSyncData:breakPloySet:%s";

    /**
     * redis key Crm循环同步检查，:tenantId:ployDetailId
     */
    String REDIS_KEY_CRM_CYCLE_SYNC_CHECK = "erpSyncData:crmCycleSyncCheck:%s:%s";


    /**
     * header的crm_request_id
     */
    String REQUEST_ID_KEY = "crm_request_id";

    /**
     * 停用策略时间,tenantId-srcObjApiName。使用对象是因为相同对象数据同步时，只能对所有策略一起触发。
     */
    String REDIS_KEY_STOP_PLOY_TIME = "erpSyncData:stopPloyTime:%s-%s";

    /**
     * 同步失败统计信息修改时加锁的key
     */
    String REDIS_KEY_SYNC_FAILED_STAT_LOCK = "erpSyncData:lock:sfStat:%s";

    /**
     * redis锁，轮询ERP,对象级别
     */
    String REDIS_LOCK_PROBE_ERP = "erpSyncData:lock:probeErp:%s:%s";
    /**
     * redis信号量，轮询ERP，企业级别
     */
    String REDIS_SEMAPHORE_PROBE_ERP = "erpSyncData:semaphore:probeErp:%s";
    /**
     * redis lock 本地任务
     */
    String REDIS_LOCK_LOCAL_TASK = "erpSyncData:lock:localTask:";
    /**
     * redis lock 处理轮询ERP结果MQ锁
     */
    String REDIS_LOCK_HANDLE_POLLING_ERP_MQ = "erpSyncData:lock:handlePollingErpMQ:";
    /**
     * redis lock 处理同步失败数据锁
     */
    String REDIS_LOCK_HANDLE_SYNC_FAILED_DATA_MQ = "erpSyncData:lock:handleSyncFailedDataMQ:";
    /**
     * 轮询临时库锁前缀，后面拼接tenantId
     */
    String lockMongoPrefix = "redis_lockprobemongo_";

    /**
     * ErpTenantConfiguration不区分的信息（tenantId,dcId,channel），统一使用标识符
     */
    String configUniformIdentifier = "0";

    List<String> crmCommonField = Lists.newArrayList("tenant_id", "modifiedTime", "object_describe_api_name", "_id", "version");

    /**
     * 配额超出的key，value为应该停用集成流的日期
     */
    String REDIS_CACHE_QUOTA_OUT = "erpSyncData:cache:quotaOut:%s";

    /**
     * 检查配额的结果
     */
    String REDIS_CACHE_QUOTA_CHECK_RESULT = "erpSyncData:cache:quotaCheck";

    String REDIS_LOCK_COMPARE_DATA = "erpSyncData:lock:compareData:";
    String REDIS_LOCK_COMPARE_DATA2 = "erpSyncData:lock:compareData2:";
    String BUSINESS_SYMBOL = "FS_ERP_SYNC";
    String REDIS_LOCK_DATA_NODE_TIME_OUT = "erpSyncData:lock:dataNodeTimeOut";
    String REDIS_LOCK_SYNC_FAILED_DATA = "erpSyncData:lock:syncFailedData";
    String REDIS_LOCK_ALERT_AGGREGATION_DATA = "erpSyncData:lock:alertAggregationData";
    String REDIS_LOCK_DAILY_ALARM_STATISTICS = "erpSyncData:lock:dailyAlarmStatistics";

    /**
     * 异步轮询任务结果
     */
    String REDIS_CACHE_ANALYSE_K3_OBJ = "erpSyncData:cache:analyseK3Obj:%s:%s";
    String REDIS_CACHE_PARSE_OBJ = "erpSyncData:cache:parseObj:%s:%s";

    /**
     * db连接器查询条件占位符
     */
    String DB_QUERY_CRITERIA = "query_criteria_place_holder";
    /**
     * db连接器轮询时间字段分隔符
     */
    String DB_QUERY_TIME_FIELD_SPLIT = ";";

    String REDIS_LOCK_HISTORY_TASK = "erpSyncData:lock:historyTask:";

    Integer REMARK_SIZE_LIMIT=2000;

    /**
     * 系统信息缓存
     */
    String REDIS_CACHE_SYSTEM_INFO_HOLDER = "erpSyncData:cache:systemInfoHolder";
    /**
     * JDY系统函数的前缀
     */
    String JDY_SYSTEM_FUNC = "JDYManagerErpAPL";
    /**
     * 计算mapping表的锁
     */
    String REDIS_LOCK_COUNT_MAPPING = "erpSyncData:lock:countMapping:";
    /**
     * 计算下游集成流信息
     */
    String REDIS_LOCK_DOWN_STREAM_STAT = "erpSyncData:lock:downstreamStat:";
    /**
     * 计算下游集成流TraceId
     */
    String REDIS_CACHE_DOWN_STREAM_STAT_TRACE = "erpSyncData:cache:downstreamStatTrace:";
    /**
     * 重试下游
     */
    String REDIS_LOCK_DOWN_STREAM_RETRY = "erpSyncData:lock:downstreamRetry:";
    /**
     * 从mongo重试mq锁
     */
    String lockMongoReTryMq = "lock_mongo_ReTryMq";

    String READ_OPERATE_TYPE="read";

    String WRITE_OPERATE_TYPE="write";

    String FUNC_OPERATE_TYPE="func";
    //操作中间表
    String OPERATE_DATA_MAPPING="mapping";
    //告警集成流
    String ALERT_INTEGRATION_STREAM="alert";
    //列表导入的data_source
    String dataSource_import="import";
    //批量工具的data_source
    String dataSource_optool_batch="optool_batch";
    //默认过滤的data_source
    List<String> default_paas_dataSource=Lists.newArrayList(dataSource_import,dataSource_optool_batch);

    String REDIS_LOCK_ADD_OUTER_CONNECTOR = "erpSyncData:lock:addOuterConnector";
    /**
     * 历史任务创建数量缓存，日
     */
    String REDIS_COUNT_DAILY_HISTORY_TASK_CREATE = "erpSyncData:cache:historyTaskCreate:";

    String LIFE_STATUS_INVALID = "invalid";

    String REDIS_ASYNC_TASK_CACHE = "erpSyncData:cache:asyncTask:";
}
