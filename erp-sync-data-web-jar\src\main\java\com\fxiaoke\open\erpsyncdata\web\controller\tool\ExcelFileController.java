package com.fxiaoke.open.erpsyncdata.web.controller.tool;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.MultipartFileArg;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CrmSpecialFieldExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ExportSyncDataMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.service.DataVerificationService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/31
 */
@Slf4j
@Api(tags = "excel文件导入导出接口")
@RestController()
@RequestMapping("erp/syncdata/setUp/excelFile")
public class ExcelFileController extends BaseController {
    @Autowired
    private FileService fileService;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private DataVerificationService dataVerificationService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "获取CRM地区字段信息，excel")
    @RequestMapping(value = "/getCrmDistrictExcel", method = RequestMethod.GET)
    public Result<Void> getCrmDistrictExcel(HttpServletResponse response,
                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        Integer tenantId = getIntLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<List<CrmSpecialFieldExcelVo>> crmDistrictExcel = fileService.getCrmDistrictExcel(tenantId, userId,lang);
        if (!crmDistrictExcel.isSuccess()){
            return Result.copy(crmDistrictExcel);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String sheetName = i18NStringManager.get(I18NStringEnum.s220,lang,tenantId+"");
            String fileName = URLEncoder.encode(sheetName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), CrmSpecialFieldExcelVo.class)
                    .autoCloseStream(Boolean.FALSE).sheet(sheetName)
                    .doWrite(crmDistrictExcel.getData());
            return Result.newSuccess();
        } catch (Exception e) {
            return Result.copy(crmDistrictExcel);
        }
    }
    @ApiOperation(value = "获取CRM部门信息，excel")
    @RequestMapping(value = "/getCrmDeptExcel", method = RequestMethod.GET)
    public Result<Void> getCrmDeptExcel(HttpServletResponse response,
                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        Integer tenantId = getIntLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<List<CrmSpecialFieldExcelVo>> crmDistrictExcel = fileService.getCrmDeptExcel(tenantId, userId,lang);
        if (!crmDistrictExcel.isSuccess()){
            return Result.copy(crmDistrictExcel);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String sheetName = i18NStringManager.get(I18NStringEnum.s222,lang,tenantId+"");
            String fileName = URLEncoder.encode(sheetName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), CrmSpecialFieldExcelVo.class)
                    .autoCloseStream(Boolean.FALSE).sheet(sheetName)
                    .doWrite(crmDistrictExcel.getData());
            return Result.newSuccess();
        } catch (Exception e) {
            return Result.copy(crmDistrictExcel);
        }
    }

    @ApiOperation(value = "导入数据")
    @RequestMapping(value = "/importExcelData", method = RequestMethod.POST)
    public DeferredResult<Result<ImportExcelFile.Result>> asyncImportExcelData(ImportExcelFile.FieldDataMappingArg arg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) throws IOException {

        arg.setLang(lang);
        ImportExcelFile.Result timeOutResult = new ImportExcelFile.Result();
        timeOutResult.setPrintMsg(i18NStringManager.get(I18NStringEnum.s1088.getI18nKey(),lang,getLoginUserTenantId(),I18NStringEnum.s1088.getI18nValue()));
        DeferredResult<Result<ImportExcelFile.Result>> result = new DeferredResult<>(ConfigCenter.IMPORT_TIME_OUT, Result.newSuccess(timeOutResult));
        String loginUserTenantId = getLoginUserTenantId();
        arg.setTenantId(loginUserTenantId);
        String dataCenterId = getUserVo().getDataCenterId();
        if (StringUtils.isBlank(dataCenterId)){
            result.setResult(Result.newError(ResultCodeEnum.NOT_RECEIVE_DCID));
            return result;
        }
        arg.setDataCenterId(dataCenterId);
        Integer loginUserId = getLoginUserId();
        arg.setUserId(loginUserId);
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                Result<ImportExcelFile.Result> resultResult = fileService.importExcelFile(arg, dataCenterId,lang);
                if (!result.setResult(resultResult)) {
                    sendImportResult(loginUserTenantId, dataCenterId, null, loginUserId, resultResult,lang);
                }
            } catch (IOException e) {
                log.error("importObjectDataMapping get error", e);
            }
        }).run();
        return result;
    }

    @ApiOperation(value = "导入对象数据映射，异步请求处理")
    @RequestMapping(value = "/importObjectDataMapping", method = RequestMethod.POST)
    public DeferredResult<Result<ImportExcelFile.Result>> asyncImportObjectDataMapping(ImportExcelFile.ObjectDataMappingArg arg,
                                                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) throws IOException {
        String tenantId = getLoginUserTenantId();
        arg.setTenantId(tenantId);
        Integer loginUserId = getLoginUserId();
        arg.setUserId(loginUserId);
        ImportExcelFile.Result timeOutResult = new ImportExcelFile.Result();
        timeOutResult.setPrintMsg(i18NStringManager.get(I18NStringEnum.s1088.getI18nKey(),lang,getLoginUserTenantId(),I18NStringEnum.s1088.getI18nValue()));
        DeferredResult<Result<ImportExcelFile.Result>> result =
                new DeferredResult<>(ConfigCenter.IMPORT_TIME_OUT, Result.newSuccess(timeOutResult));
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                Result<ImportExcelFile.Result> resultResult = fileService.importObjectDataMapping(arg,lang);
                log.info("ExcelFileController.asyncImportObjectDataMapping,resultResult={}", JSONObject.toJSONString(resultResult));
                if (!result.setResult(resultResult)) {
                    log.info("ExcelFileController.asyncImportObjectDataMapping,setResult failed");
                    sendImportResult(tenantId, getDcId(),arg.getPloyDetailId(), loginUserId, resultResult,lang);
                    log.info("ExcelFileController.asyncImportObjectDataMapping,sendImportResult success");
                }
            } catch (IOException e) {
                log.error("importObjectDataMapping get error", e);
            }
        }).run();
        return result;
    }


    @ApiOperation(value = "导入对象字段数据")
    @RequestMapping(value = "/importObjectFieldData", method = RequestMethod.POST)
    public DeferredResult<Result<ImportExcelFile.FieldImportResult>> importObjectFieldData(ImportExcelFile.FieldImportArg arg,
                                                                                           @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer loginUserId = getLoginUserId();
        String dataCenterId = getDcId();

        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        arg.setUserId(loginUserId);

        ExportSyncDataMapping.Result timeOutResult = new ExportSyncDataMapping.Result();

        DeferredResult<Result<ImportExcelFile.FieldImportResult>> result = new DeferredResult<>(ConfigCenter.EXPORT_TIME_OUT, Result.newSuccess(timeOutResult));

        if(StringUtils.isEmpty(tenantId) || loginUserId==null || StringUtils.isEmpty(dataCenterId)) {
            result.setErrorResult(Result.newError(ResultCodeEnum.PARAM_ERROR));
            return result;
        }

        result.onTimeout(()->{
            //发送企信消息
            SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
            sendTextNoticeArg.setTenantId(tenantId);
            sendTextNoticeArg.setDataCenterId(dataCenterId);
            sendTextNoticeArg.setReceivers(Collections.singletonList(loginUserId));
            sendTextNoticeArg.setMsg(i18NStringManager.get(I18NStringEnum.s223,lang,tenantId));
            sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s224,lang,tenantId));
            notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager,lang,tenantId),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
        });

        ParallelUtils.createBackgroundTask().submit(() -> {
            try (final InputStream inputStream = arg.getFile().getInputStream()) {
                arg.setFileStream(inputStream);
                Result<ImportExcelFile.FieldImportResult> resultResult = erpObjectFieldsService.batchImportErpObjectFields(arg,lang);
                if (!resultResult.isSuccess()){
                    result.setErrorResult(resultResult);
                }else {
                    result.setResult(resultResult);
                }
            } catch (Exception e) {
                log.error("ExcelFileController.importObjectFieldData,exception={}", e.getMessage());
                result.setErrorResult(Result.newError(ResultCodeEnum.SYSTEM_ERROR,e.getMessage()));
            }
        }).run();

        return result;
    }

    private void sendImportResult(String tenantId, String dataCenterId, String ployDetailId, Integer userId, Result<ImportExcelFile.Result> importResult,String lang) {
        String msg = importResult.getErrMsg();
        if (importResult.isSuccess()) {
            msg = importResult.getData().getPrintMsg();
        }
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(dataCenterId);
        if(StringUtils.isNotEmpty(ployDetailId)) {
            sendTextNoticeArg.setPloyDetailId(ployDetailId);
        }
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(i18NStringManager.get2(I18NStringEnum.s225, lang, tenantId, LocalDateTime.now().toString()));
        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,lang,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }

    @ApiOperation(value = "上传数据核对id文件")
    @RequestMapping(value = "/uploadDataVerificationIdField", method = RequestMethod.POST)
    public Result<String> uploadDataVerificationIdField(MultipartFileArg arg,
                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) throws IOException {
        String tenantId = getLoginUserTenantId();
        Integer loginUserId = getLoginUserId();
        return dataVerificationService.uploadDataVerificationIdField(tenantId,loginUserId,arg,lang);
    }

}
