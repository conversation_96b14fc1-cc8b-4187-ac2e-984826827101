<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <import resource="whole-war-common.xml"/>
    <!--all start-->
    <bean id="overrideOuterService" class="com.fxiaoke.open.erpsyncdata.preprocess.impl.ErpOverrideOuterServiceImpl"/>
    <!--mq生产者配置-->
    <import resource="whole-all-mq-producer.xml"/>
    <!--Mq消费者-->
    <import resource="classpath*:spring/fseventtrigger-mq.xml"/>
    <!--db连接信息-->
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/syncmain-context.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
    <!--admin-context.xml 在web引入了 -->
    <import resource="classpath*:spring/dispatcher-context.xml"/>
    <!--all end-->

    <!--web start-->
    <!--接入CEP 参考原来fs-cep-plugin.xml，自定义了序列化类-->
    <import resource="classpath:spring/whole-web-cep-plugin.xml"/>
    <import resource="classpath*:spring/whole-web-mq-producer.xml"/>
    <import resource="classpath*:spring/whole-web-dubbo-consumer.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <!--  迁移服务  -->
    <import resource="classpath:spring/whole-web-transfer-sharding-mongo.xml"/>
    <!--web end-->

    <!--task start-->
    <!--ea和ei互转-->
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/whole-task-fs-paas-mq.xml"/>
    <import resource="classpath:spring/whole-task-xxl-job.xml"/>
    <!--task end-->

    <!--file start-->
    <import resource="classpath*:spring/whole-file-fileserver.xml"/>
    <!--file end-->

    <!--monitor start-->
    <!--xxl-job合并到task的xml:whole-task-xxl-job.xml-->
    <!--monitor end-->

</beans>