package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum
import spock.lang.*


/**
 * <AUTHOR> 
 * @date 2024/10/23 20:34:24
 */
class ErpObjManagerSpec extends Specification {
    ErpObjectDao erpObjectDao = Mock()
    ErpObjectRelationshipDao erpObjectRelationshipDao = Mock()
    I18NStringManager i18NStringManager = Mock()

    ErpObjManager erpObjManager = new ErpObjManager(
            erpObjectDao: erpObjectDao,
            erpObjectRelationshipDao: erpObjectRelationshipDao,
            i18NStringManager: i18NStringManager
    )

    void setup() {
        erpObjManager.erpObjManager = erpObjManager
        erpObjectDao.setTenantId(*_) >> erpObjectDao
        erpObjectRelationshipDao.setTenantId(*_) >> erpObjectRelationshipDao
    }

    def "test get Erp Obj Name"() {
        given:
        erpObjectDao.getByObjApiName(*_) >> new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)
        erpObjManager.getErpObj(*_) >> new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)

        when:
        String result = erpObjManager.getErpObjName("tenantId", "objApiName")

        then:
        result == "erpObjectName"
    }

    def "test get Erp Obj Name 2"() {
        given:
        erpObjectDao.getByObjApiName(*_) >> new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)
        erpObjManager.getErpObj(*_) >> new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)

        when:
        String result = erpObjManager.getErpObjName("tenantId", "dcId", "objApiName")

        then:
        result == "erpObjectName"
    }

    def "test get Split Obj Relations"() {
        given:
        erpObjectRelationshipDao.getSplitObjApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]


        when:
        List<ErpObjectRelationshipEntity> result = erpObjManager.getSplitObjRelations("tenantId", "actualApiName")

        then:
        result == [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
    }

    def "test get Erp Obj"() {
        given:
        erpObjectDao.getByObjApiName(*_) >> new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)

        when:
        ErpObjectEntity result = erpObjManager.getErpObj("tenantId", "objApiName")

        then:
        result == new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)
    }

    def "test get Erp Obj 2"() {
        given:
        erpObjectDao.getByObjApiName(*_) >> new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)

        when:
        ErpObjectEntity result = erpObjManager.getErpObj("tenantId", "dcId", "objApiName")

        then:
        result == new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)
    }

    def "test get Relation"() {
        given:
        erpObjectRelationshipDao.findBySplit(*_) >> new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"

        when:
        ErpObjectRelationshipEntity result = erpObjManager.getRelation("tenantId", "objApiName")

        then:
        result == new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)
    }

    def "test get Real Obj Api Name"() {
        given:
        erpObjectRelationshipDao.findBySplit(*_) >> new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)
        erpObjManager.getRelation(*_) >> new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"

        when:
        String result = erpObjManager.getRealObjApiName("tenantId", "objApiName")

        then:
        result == "erpRealObjectApiname"
    }

    def "test list Relations By Real Api Name"() {
        given:
        erpObjectRelationshipDao.findByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]


        when:
        List<ErpObjectRelationshipEntity> result = erpObjManager.listRelationsByRealApiName("tenantId", "dcId", "realObjApiName")

        then:
        result == [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
    }

    def "test list Master Relations By Real Api Name"() {
        given:
        erpObjectRelationshipDao.findMasterByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]


        when:
        List<ErpObjectRelationshipEntity> result = erpObjManager.listMasterRelationsByRealApiName("tenantId", "dcId", "realObjApiName")

        then:
        result == [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
    }

    def "test get Master Split Obj Api Name"() {
        given:
        erpObjectRelationshipDao.findMasterByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]

        erpObjManager.listMasterRelationsByRealApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]

        when:
        String result = erpObjManager.getMasterSplitObjApiName("tenantId", "dcId", "realObjApiName")

        then:
        result == "erpSplitObjectApiname"
    }

    def "test get Real Main Split Obj Api Names Map"() {
        given:
        erpObjectRelationshipDao.findNotSplit(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]


        when:
        Map<String, Set<String>> result = erpObjManager.getRealMainSplitObjApiNamesMap("tenantId", "dcId")

        then:
        result == [erpRealObjectApiname:['erpSplitObjectApiname'] as Set<String>]
    }

    def "test get Details Need Send Alone"() {
        given:
        erpObjectRelationshipDao.findBySplit(*_) >> new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)
        erpObjectRelationshipDao.findByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
        erpObjManager.getRelation(*_) >> new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"

        when:
        Set<String> result = erpObjManager.getDetailsNeedSendAlone("tenantId", "mainObjApiName")

        then:
        result == [] as Set<String>
    }

    def "test get Main Seq Map"() {
        given:
        erpObjectRelationshipDao.listByTenantId(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]

        when:
        Map<String, Integer> result = erpObjManager.getMainSeqMap("tenantId")

        then:
        result == ["erpSplitObjectApiname": 0]
    }

    def "test query By Erp Obj Api Names List"() {
        given:
        erpObjectDao.queryByApiNames2(*_) >> [new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)]


        when:
        List<ErpObjectEntity> result = erpObjManager.queryByErpObjApiNamesList("tenantId", "dcId", ["erpObjectApiNameCollection"])

        then:
        result == [new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)]
    }

    def "test get Detail Erp Obj Entity List"() {
        given:
        def entity = new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)
        erpObjectDao.queryByApiNames2(*_) >> [entity]
        erpObjectRelationshipDao.findByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]

        erpObjManager.listRelationsByRealApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
        erpObjManager.queryByErpObjApiNamesList(*_) >> [entity]

        when:
        List<ErpObjectEntity> result = erpObjManager.getDetailErpObjEntityList("tenantId", "dataCenterId", "mainObjApiName")

        then:
        result == []
    }

    def "test get Detail Entity"() {
        given:
        def entity = new ErpObjectEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpObjectTypeEnum.SPLIT_OBJECT, "erpObjectApiName", "erpObjectName", "erpObjectExtendValue", Boolean.TRUE, 1l, 1l)
        erpObjectDao.queryByApiNames2(*_) >> [entity]
        erpObjectRelationshipDao.findByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
        erpObjManager.listRelationsByRealApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
        erpObjManager.queryByErpObjApiNamesList(*_) >> [entity]
        erpObjManager.getDetailErpObjEntityList(*_) >> [entity]

        when:
        ErpObjectEntity result = erpObjManager.getDetailEntity("tenantId", "dataCenterId", "mainObjApiName", "detailObjApiName")

        then:
        result == null
    }
}
