package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;

@Data
@FieldNameConstants
public class AdminConnectorInfo implements Serializable {

    private static final long serialVersionUID = -8970733030699157874L;

    @ApiModelProperty("连接器id")
    private String connectorId;
    private String tenantId;
    @ApiModelProperty("ea")
    private String enterpriseAccount;
    @ApiModelProperty("企业类型")
    private String enterpriseType;
    @ApiModelProperty("企业名称")
    private String enterpriseName;
//    @ApiModelProperty("环境")
//    private ErpSyncDataBackStageEnvironmentEnum tenantEnv;
    @ApiModelProperty("连接器类型")
    private String connectorKey;
    @ApiModelProperty("系统名称")
    private String systemName;
    @ApiModelProperty("连接器创建时间")
    private Long connectorCreateTime;
    @ApiModelProperty("连接状态")
    private int status = -1;
    @ApiModelProperty("最后同步时间")
    private Date mongoLastSyncTime;
}
