package com.fxiaoke.open.erpsyncdata.dbproxy.util;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 11:32 2022/4/20
 * @Desc:
 */
@Slf4j
@Service
public class DataBaseBatchIndexUtil {
    public static String notTenantId="-10001";//代表需要获取所有企业的数据，如果有多个数据源，必须特殊处理，目前默认为configurationDataBaseBatchIndex
    private static String configurationDataBaseBatchIndex="-10001";


    public static String getDataBaseBatchIndex(String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            log.error("DataBaseBatchIndexUtil getDataBaseBatchIndex tenantId is null");
            throw new ErpSyncDataException(I18NStringEnum.s3644, tenantId);
        }
        return configurationDataBaseBatchIndex;
    }

}
