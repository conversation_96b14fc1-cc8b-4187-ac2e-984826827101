<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.custom.ErpObjIdNumberMappingDao">
    <update id="updateDataNumber">
        update erp_obj_id_number_mapping
        set data_number = #{dataNumber}
        where tenant_id = #{tenantId} and dc_id = #{dcId} and obj_api_name = #{objApiName} and data_id = #{dataId}
    </update>

    <select id="queryByDataId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjIdNumberMappingEntity">
        select *
        from erp_obj_id_number_mapping
        where tenant_id = #{tenantId} and dc_id = #{dcId} and obj_api_name = #{objApiName} and data_id = #{dataId} limit 1
    </select>

    <select id="queryByDataNumber" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjIdNumberMappingEntity">
        select *
        from erp_obj_id_number_mapping
        where tenant_id = #{tenantId} and dc_id = #{dcId} and obj_api_name = #{objApiName} and data_number = #{dataNumber}
    </select>
</mapper>