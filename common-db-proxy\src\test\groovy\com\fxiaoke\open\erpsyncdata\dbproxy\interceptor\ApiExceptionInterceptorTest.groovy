package com.fxiaoke.open.erpsyncdata.dbproxy.interceptor

import cn.hutool.core.util.ClassUtil
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchCreateObjectResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class ApiExceptionInterceptorTest extends Specification {

    def "testClass-#returnType"(Class<?> returnType, boolean res) {
        when:

        then:
        expect:
        res == ClassUtil.isAssignable(returnType, Result2.class)

        where:
        returnType              | res
        Object                  | true
        Result                  | true
        BatchCreateObjectResult | false
        Void                    | false
        String                  | false
        null                    | false
    }
}
