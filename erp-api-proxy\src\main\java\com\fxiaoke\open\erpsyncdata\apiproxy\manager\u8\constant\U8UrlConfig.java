package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant;

import lombok.Data;

public enum U8UrlConfig {

  //收款单
  ACCEPT("accept",null,"acceptlist",null,null,null),
 //应收单
 OUGHT_RECEIVE("oughtreceive",null,"oughtreceivelist",null,null,null),
 //销售订单
 SALE_ORDER("saleorder",null,"saleorderlist",null,null,null),
  //销售出库单
  SALE_OUT("saleout",null,"saleoutlist",null,null,null),

  ;

  private String apiName;
  private String loadApiName;
  private String queryApiName;
  private String createApiName;
  private String updateApiName;
  /**2021/04/26 新增审核逻辑*/
  private String verifyApiName;

  U8UrlConfig(String apiName, String loadApiName, String queryApiName, String createApiName, String updateApiName, String verifyApiName) {
    this.apiName = apiName;
    this.loadApiName = loadApiName == null ? apiName : loadApiName;
    this.queryApiName = queryApiName == null ? apiName : queryApiName;
    this.createApiName = createApiName == null ? apiName : createApiName;
    this.updateApiName = updateApiName == null ? apiName : updateApiName;
    this.verifyApiName = verifyApiName == null ? apiName : verifyApiName;
  }

  public String getLoadApiName() {
    return loadApiName;
  }

  public String getQueryApiName() {
    return queryApiName;
  }

  public String getCreateApiName() {
    return createApiName;
  }

  public String getUpdateApiName() {
    return updateApiName;
  }

  public String getVerifyApiName(){
      return verifyApiName;
  }

  public static String getApiName(String apiName, String status) {
    for (U8UrlConfig u8UrlConfig : U8UrlConfig.values()) {
      if (u8UrlConfig.apiName.equals(apiName)) {
        if ("load".equals(status)) {
          return u8UrlConfig.getLoadApiName();
        }
        if ("query".equals(status)) {
          return u8UrlConfig.getQueryApiName();
        }
        if ("create".equals(status)) {
          return u8UrlConfig.getCreateApiName();
        }
        if ("update".equals(status)) {
          return u8UrlConfig.getUpdateApiName();
        }
        if("verify".equals(status)){
            return u8UrlConfig.getVerifyApiName();
        }
      }
    }
    return apiName;
  }


}
