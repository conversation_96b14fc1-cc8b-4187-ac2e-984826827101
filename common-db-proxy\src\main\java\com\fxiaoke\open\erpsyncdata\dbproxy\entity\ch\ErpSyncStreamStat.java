package com.fxiaoke.open.erpsyncdata.dbproxy.entity.ch;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ErpSyncStreamStat implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业Id
     */
    private Object tenantId;

    /**
     * 企业ea
     */
    private Object ea;

    /**
     * 连接器Id
     */
    private Object dcId;

    /**
     * 上游企业Id（如果有）
     */
    private Object upTenantId;

    /**
     * 集成流Id
     */
    private Object streamId;

    /**
     * 目标类型
     */
    private Object destTenantType;

    /**
     * 对象映射类型
     */
    private Object objMappingType;

    /**
     * 外部对象ApiName
     */
    private Object erpObjApiName;

    /**
     * crm对象ApiName
     */
    private Object crmObjApiName;

    /**
     * 集成流状态
     */
    private Object streamStatus;

    /**
     * 上次告警时间
     */
    private Date lastAlertTime;

    /**
     * 上次同步时间
     */
    private Date lastSyncTime;

    /**
     * 中间表总数
     */
    private Integer mappingTotal;

    /**
     * 中间表失败总数
     */
    private Integer mappingFailedTotal;

    /**
     * 统计任务traceId
     */
    private String traceId;

    /**
     * 此数据统计时间
     */
    private Date createTime;

    /**
     * 统计操作耗时,毫秒
     */
    private Integer statCost;

    /**
     * 计划删除时间
     */
    private Date deleteTime;
}