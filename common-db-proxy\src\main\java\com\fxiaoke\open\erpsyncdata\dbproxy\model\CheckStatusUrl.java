package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckStatusUrl {
    private String type;
    private String url;
    private String name;

    private Integer status;

    public boolean isOk() {
        return status != null && status >= 200 && status < 300;
    }
}
