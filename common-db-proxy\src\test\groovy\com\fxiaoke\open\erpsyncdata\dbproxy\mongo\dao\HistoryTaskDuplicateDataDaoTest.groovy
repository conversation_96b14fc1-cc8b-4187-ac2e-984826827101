package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.HistoryTaskDuplicateDataEntity
import com.github.mongo.support.DatastoreExt
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/1/11 17:04:32
 */
// TODO: Spring
@Ignore
//@ContextConfiguration(["classpath:spring-test.xml"])
class HistoryTaskDuplicateDataDaoTest extends Specification {

//    static {
//        System.setProperty("process.profile", "fstest");
//    }

    HistoryTaskDuplicateDataDao historyTaskDuplicateDataDao;

    def setup() {
        // TODO: Mock一下依赖的bean
        historyTaskDuplicateDataDao = new HistoryTaskDuplicateDataDao()
    }

    def "测试sql"() {
        when:
        def taskNum = String.valueOf(System.currentTimeMillis())
        def test = "test"

        historyTaskDuplicateDataDao.incDuplicateTime(test, taskNum, test, 2)
        historyTaskDuplicateDataDao.incDuplicateTime(test, taskNum, test, 3)

        historyTaskDuplicateDataDao.incTotalDataSize(test, taskNum, 100)
        historyTaskDuplicateDataDao.incTotalDataSize(test, taskNum, 100)

        def time = historyTaskDuplicateDataDao.getByDataId(test, taskNum, test)
        def total = historyTaskDuplicateDataDao.getByDataId(test, taskNum, HistoryTaskDuplicateDataEntity.totalSizeDataNum)

        then:
        time == 6
        total == 200
    }
}
