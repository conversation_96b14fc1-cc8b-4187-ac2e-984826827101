package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Objects;

@Component
@Aspect
@Slf4j
public class DubboSetContextEiAspect {

    @Pointcut("execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.ErpReSyncDataService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.ErpSyncService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.ScanSyncWarnningService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.UpLoadFileService.*(..)) " +
            "|| execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.AttachmentsService.*(..))")
    public void pointCut() {
    }
    @Around("pointCut()")
    public Object doSetAndRemoveContextEi(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        return setAndRemoveContextEi(proceedingJoinPoint);
    }
    public static Object setAndRemoveContextEi(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        String oldTenantId= TraceContext.get().getEi();
        boolean isSetTenantId=false;
        try{
            String newTenantId=getTenantIdByArg(proceedingJoinPoint);
            if(StringUtils.isNotBlank(newTenantId)){
                TraceContext.get().setEi(newTenantId);
                isSetTenantId=true;
            }
        }catch (Exception e){
           log.info("setContextEi exception e={}",e);
        }
        try {
            return proceedingJoinPoint.proceed();
        }finally {
            if(isSetTenantId){
                TraceContext.get().setEi(oldTenantId);//为了防止修改这个对后续产生影响，改回来旧值
            }
        }

    }

    private static String getTenantIdByArg(ProceedingJoinPoint proceedingJoinPoint) {
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = signature.getMethod();
        final Object[] args = proceedingJoinPoint.getArgs();
        final Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            final Parameter parameter = parameters[i];
            final ContextEi annotation = parameter.getAnnotation(ContextEi.class);
            if (Objects.nonNull(annotation)) {
                String ei = null;
                if (StringUtils.isEmpty(annotation.value())) {
                    ei = (String) args[i];
                }else {
                    ei = AspectSpelUtil.getSpelValueByArg(annotation.value(), args[i]);
                }
                if(StringUtils.isBlank(ei)){
                    log.warn("{}.{} arg={} getTenantIdByArg newTenantId is null",method.getDeclaringClass(),method.getName(),args[i]);
                }
                return ei;
            }
        }
        return null;
    }

}
