package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.facishare.function.biz.api.service.FunctionService;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFuncArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFunctionResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.admin.FunctionInfo;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.standard.SyncResult;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 调用自定义函数的统一入口
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class AplManager {
    @Autowired
    private FunctionService functionService;
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private SfaApiManager sfaApiManager;


    /**
     * 执行apl方法
     */
    public <T> Result<T> executeAplMethod(final String tenantId,
                                          final String dataCenterId,
                                          final String objApiName,
                                          final ErpObjInterfaceUrlEnum interfaceUrl,
                                          final String funcApiName,
                                          final List<Object> params,
                                          TypeReference<T> resType) {
        return executeAplMethod(tenantId, dataCenterId, objApiName, interfaceUrl, funcApiName, params, null, null, resType);
    }

    /**
     * 执行apl方法
     */
    public <T> Result<T> executeAplMethod(final String tenantId,
                                          final String dataCenterId,
                                          final String objApiName,
                                          final ErpObjInterfaceUrlEnum interfaceUrl,
                                          final String funcApiName,
                                          final List<Object> params,
                                          final ObjectData objectData,
                                          final TimeFilterArg timeFilterArg,
                                          TypeReference<T> resType) {
        try {
            String resStr = executeAplMethod(tenantId, dataCenterId, objApiName, interfaceUrl, funcApiName, params, objectData, timeFilterArg);
            SyncResult syncResult = SyncResult.parseFromStr(resStr);
            if (syncResult.isSuccess()) {
                return Result.newSuccess(syncResult.parseData(resType));
            } else {
                if (syncResult.getMessage() == null) {
                    //格式不正确
                    return new Result<>(ResultCodeEnum.FUNC_EXECUTE_EXCEPTION2, String.format("%s#%s", funcApiName, interfaceUrl.getMethodName()));
                }
                return Result.newError(ResultCodeEnum.FUNC_EXECUTE_EXCEPTION4, syncResult.getMessage());
            }
        } catch (JSONException e) {
            log.info("json exception", e);
            return new Result<>(ResultCodeEnum.FUNC_EXECUTE_EXCEPTION3, String.format("%s#%s:%s", funcApiName, interfaceUrl.getMethodName(), e.getMessage()));
        } catch (Exception e) {
            log.info("executeAplMethod exception", e);
            throw ErpSyncDataException.wrap(e);
        }
    }


    /**
     * 执行APL方法
     *
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param interfaceUrl
     * @param funcApiName
     * @param params
     * @param objectData    可以为空
     * @param timeFilterArg 不可为空
     * @return
     */
    protected String executeAplMethod(final String tenantId,
                                      final String dataCenterId,
                                      final String objApiName,
                                      final ErpObjInterfaceUrlEnum interfaceUrl,
                                      final String funcApiName,
                                      final List<Object> params,
                                      final ObjectData objectData,
                                      final TimeFilterArg timeFilterArg) {
        //校验，这类异常不会记录到日志。
        if (interfaceUrl == null) {
            throw new ErpSyncDataException(I18NStringEnum.s1081, tenantId);
        }
        if (objApiName == null) {
            throw new ErpSyncDataException(I18NStringEnum.s1082, tenantId);
        }
        if (StrUtil.isBlank(funcApiName)) {
            throw new ErpSyncDataException(I18NStringEnum.s1083, tenantId);
        }
        if (params == null) {
            throw new ErpSyncDataException(I18NStringEnum.s1084, tenantId);
        }

        long callTime = System.currentTimeMillis();
        String res = "";
        boolean success = true;
        try {
            List<String> paramStrs = params.stream().map(v -> JSON.toJSONString(v, JSONWriter.Feature.WriteNulls)).collect(Collectors.toList());
            res = functionService.executeFuncMethod(tenantId, "-10000", funcApiName, interfaceUrl.getMethodName(), paramStrs, Maps.newHashMap(), 20);
            return res;
        } catch (Exception e) {
            success = false;
            //新国际化改造后，异常不支持自定义msg，只能打印一下了。
            //不返回太难受了，还是改一改吧。。。
            log.info("executeFuncMethod exception", e);
            res = e.getMessage();
            String msg = String.format(ResultCodeEnum.FUNC_EXECUTE_EXCEPTION.getText(), e.getMessage());
            throw new ErpSyncDataException(e, ResultCodeEnum.FUNC_EXECUTE_EXCEPTION.getErrCode(), msg);
        } finally {
            long returnTime = System.currentTimeMillis();
            long costTime = returnTime - callTime;
            interfaceMonitorManager.saveErpInterfaceMonitor(tenantId,
                    dataCenterId,
                    objApiName,
                    interfaceUrl.name(),
                    params,
                    res,
                    success ? 1 : 2,
                    callTime,
                    returnTime,
                    "apl",
                    TraceUtil.get(),
                    costTime,
                    timeFilterArg);
        }
    }

    public Result<FunctionServiceFindResult> findFunction(HeaderObj headerObj, FunctionServiceFindArg arg) {
        return sfaApiManager.findFunction(headerObj, arg);
    }

    public Result<QueryFunctionResult> queryRegularFunction(HeaderObj headerObj, QueryFuncArg arg) {
        return sfaApiManager.queryRegularFunction(headerObj, arg);
    }

    public Result<FunctionServiceFindResult> createFunction(HeaderObj headerObj, FunctionInfo functionInfo) {
        return sfaApiManager.createFunction(headerObj, functionInfo);
    }

    public Result<FunctionServiceFindResult> editFunction(HeaderObj headerObj, FunctionInfo functionInfo) {
        return sfaApiManager.editFunction(headerObj, functionInfo);
    }
}
