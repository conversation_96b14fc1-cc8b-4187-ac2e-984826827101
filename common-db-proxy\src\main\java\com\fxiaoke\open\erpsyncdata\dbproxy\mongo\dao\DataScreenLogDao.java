package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.arg.QueryTaskArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataScreenLog;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存大屏的数据u
 */
@Slf4j
@Repository
@DependsOn("erpSyncDataMongoStore")
public class DataScreenLogDao {

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    private DatastoreExt store;
    private String DATABASE = "erp_sync_data";
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();
    private final static String ERP_DATA_SCREEN_LOG = "data_screen_log";
    private final static String id="_id";


    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<DataScreenLog> coll = getColl();
        IndexOptions indexOptions = new IndexOptions().unique(true);
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
        //过期自动清理时间,30天
        Bson idxExpire = Indexes.descending(DataScreenLog.Fields.createTime);
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(30L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending(DataScreenLog.Fields.tenantId)
                , Indexes.ascending(DataScreenLog.Fields.screenMd5));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("data_filters").background(true)));

        List<String> created = coll.createIndexes(toBeCreate);
    }


    private MongoCollection<DataScreenLog> getColl() {
        MongoCollection<DataScreenLog> coll = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(ERP_DATA_SCREEN_LOG, DataScreenLog.class);
        if(!collectionCache.contains(ERP_DATA_SCREEN_LOG)){
            return coll;
        }
        createIndex();
        return coll;
    }

    public void batchInsert(String tenantId, List<DataScreenLog> DataScreenLogs) {
        UpdateOneModel<DataScreenLog> updateOneModel;
        List<WriteModel<DataScreenLog>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (DataScreenLog syncLog : DataScreenLogs) {
            if(syncLog.getId()==null){
                syncLog.setId(ObjectId.get());
            }
            updateOneModel = new UpdateOneModel<>(filterIdDoc(tenantId,syncLog), upsert(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<DataScreenLog> collection = getColl();
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }


    public Bson filterIdDoc(String tenantId,DataScreenLog dataScreenLog) {
        Document result = new Document();
        result.put(DataScreenLog.Fields.tenantId,tenantId);
        result.put(DataScreenLog.Fields.screenMd5, dataScreenLog.getScreenMd5());
        return result;
    }

    public DataScreenLog queryLogByScreen(String tenantId,String screenMd5) {
        List<DataScreenLog> result = new ArrayList<>();
        MongoCollection<DataScreenLog> collection = getColl();
        Bson filter = buildFilter(tenantId,screenMd5);
        DataScreenLog screenLog = collection.find(filter)
                .sort(Sorts.descending("updateTime"))
                .first();
        return screenLog;
    }


    private Bson buildFilter(String tenantId,String dataMd5) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(DataScreenLog.Fields.tenantId, tenantId),Filters.eq(DataScreenLog.Fields.screenMd5, dataMd5));
        Bson filter = Filters.and(filters);
        return filter;
    }

    public Bson upsert(DataScreenLog dataScreenLog){
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        //更新
        if(dataScreenLog.getUpdateTime()!=null){
            updateDoc.append(DataScreenLog.Fields.updateTime, dataScreenLog.getUpdateTime());
        }else{
            updateDoc.append(DataScreenLog.Fields.updateTime, new Date());
        }

        //不为空,才更新
        if(dataScreenLog.getScreenMd5()!=null){
            updateDoc.append(DataScreenLog.Fields.screenMd5, dataScreenLog.getScreenMd5());
        }
        //不为空,才更新
        if(dataScreenLog.getDashboardEnum()!=null){
            updateDoc.append(DataScreenLog.Fields.dashboardEnum, dataScreenLog.getDashboardEnum());
        }
        if(dataScreenLog.getDateRangeEnum()!=null){
            updateDoc.append(DataScreenLog.Fields.dateRangeEnum, dataScreenLog.getDateRangeEnum());
        }
        if (ObjectUtils.isNotEmpty(dataScreenLog.getDateRangeEnum())) {
            updateDoc.append(DataScreenLog.Fields.biDataResult, dataScreenLog.getBiDataResult());
        }
        //插入
        setOnInsertDoc
                .append(DataScreenLog.Fields.tenantId, dataScreenLog.getTenantId())
                .append(id,dataScreenLog.getId())
                .append(DataScreenLog.Fields.createTime,new Date());
        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);

        return doc;
    }

}
