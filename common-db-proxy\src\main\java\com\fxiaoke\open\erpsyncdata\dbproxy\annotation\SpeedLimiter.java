package com.fxiaoke.open.erpsyncdata.dbproxy.annotation;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/2/20 09:47:25
 */

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface SpeedLimiter {

    /**
     * 保存一分钟限额的Key
     * 包括企业级和全局
     */
    TenantConfigurationTypeEnum value();

    /**
     * 企业id字段名,spel表达式
     * 默认为第一个String类型的参数
     */
    String tenantId() default "";

    /**
     * 限制的数据字段名,需为Collection类型
     * 默认为第一个Controller的参数
     */
    String limitData() default "";

    /**
     * 计算次数的key
     * 不填默认为类名
     */
    String countKey() default "";

    /**
     * 降级的字段,spel表达式,参数值为data
     * 没填表示丢弃数据,不做处理
     */
    String[] resetFields() default {};

    /**
     * 符合条件的才降级,spel表达式,参数值为data
     */
    String condition() default "true";

    I18NStringEnum replaceI18nKey() default I18NStringEnum.s3638;

    Class<?> type() default String.class;
}
