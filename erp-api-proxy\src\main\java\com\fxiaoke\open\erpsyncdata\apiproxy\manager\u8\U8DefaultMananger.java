package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant.U8ObjIdFieldConfig;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("u8default")
public class U8DefaultMananger extends U8DataManagerAbstracta {

  @Override
  protected StandardListData transferU8ToStandardListData(String objApiName, Map<String, Object> map) {
    StandardListData standardListData = new StandardListData();
    List<StandardData> dataList = new ArrayList<>();
    standardListData.setTotalNum(Integer.valueOf(map.get("row_count").toString()));
    standardListData.setDataList(dataList);
    List<Map<String, Object>> datas = Collections.emptyList();
    for (Map.Entry<String, Object> entry : map.entrySet()) {
      if (entry.getValue() instanceof JSONArray) {
        datas = (List<Map<String, Object>>) entry.getValue();
        break;
      }
    }

    for (Map<String, Object> data : datas) {
      //处理每条数据
      dataList.add(transferU8ToStandardData(objApiName,data));
    }

    return standardListData;
  }

  @Override
  protected StandardData transferU8ToStandardData(String objApiName, Map<String, Object> data) {
    StandardData standardData = new StandardData();
    Iterator<Map.Entry<String, Object>> iterator = data.entrySet().iterator();
    Map<String, List<ObjectData>> detailFieldVals = new HashMap<>();
    while (iterator.hasNext()) {
      Map.Entry<String, Object> entry = iterator.next();

      if (entry.getValue() instanceof JSONArray) {
        //从对象数据
        JSONArray jsonArray = (JSONArray) entry.getValue();
        List<ObjectData> objectDatabs = new ArrayList<>();
        for (Object o : jsonArray) {
          JSONObject jsonObject = (JSONObject) o;
          ObjectData objectData = ObjectData.convert(jsonObject);
          objectDatabs.add(objectData);
        }
        detailFieldVals.put(entry.getKey(), objectDatabs);
        iterator.remove();//移除对象
      }
    }
    ObjectData objectData = ObjectData.convert(data);
    standardData.setMasterFieldVal(objectData);
    standardData.setDetailFieldVals(detailFieldVals);
    standardData.setObjAPIName(objApiName);
    generatorId(standardData);
    return standardData;
  }

  @Override
  protected Map transferStandardToU8(String objApiName, StandardData standardData) {
    Map<String, Object> objectDataMap = new HashMap<>();
    objectDataMap.put(objApiName, standardData.getMasterFieldVal());
    standardData.getMasterFieldVal().putAll(standardData.getDetailFieldVals());
    return objectDataMap;
  }

  @Override
  protected void generatorId(StandardData standardData){

    //获取主对象数据主键
    String apiName=standardData.getObjAPIName();
    String masterCodes=U8ObjIdFieldConfig.getApiName(apiName);
    String masterId=null;
    String[] masterIdCodes = masterCodes.split(",");
    for (String idCode : masterIdCodes) {
      if (masterId==null){
        masterId=standardData.getMasterFieldVal().getString(idCode);
      }else {
        masterId=masterId+"#"+standardData.getMasterFieldVal().get(idCode);
      }
    }
    standardData.getMasterFieldVal().put("masterId",masterId);

    //获取明细对象数据主键
    for (Map.Entry<String, List<ObjectData>> entry : standardData.getDetailFieldVals().entrySet()) {
      String entryObjApiName = entry.getKey();
      String entryCodes=U8ObjIdFieldConfig.getApiName(apiName+"."+entryObjApiName);

      String[] entryIdCodes = entryCodes.split(",");
      for (ObjectData objectData : entry.getValue()) {
        String detailId=masterId;
        for (String idCode : entryIdCodes) {
          detailId=detailId+"#"+objectData.get(idCode);
        }
        objectData.put("detailId",detailId);
      }
    }

  }

}
