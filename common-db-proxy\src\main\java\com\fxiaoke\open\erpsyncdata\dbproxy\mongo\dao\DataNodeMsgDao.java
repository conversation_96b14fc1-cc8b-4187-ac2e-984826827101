package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.mongodb.client.model.Filters.and;

/**
 * <AUTHOR>
 * @Date: 19:01 2023/1/12
 * @Desc:
 */
@Slf4j
@Component
@DependsOn("erpSyncDataLogMongoStore")
public class DataNodeMsgDao {
    private static final String f_id = "_id";
    private static final String f_nodeTimes = "nodeTimes";
    private static final String f_tenantId = "tenantId";
    private static final String f_uniqueKey = "uniqueKey";
    private static final String f_objApiName = "objApiName";
    private static final String f_dataId = "dataId";
    private static final String f_version = "version";
    private static final String f_streamId = "streamId";
    private static final String f_nodeTypes = "nodeTypes";
    private static final String f_nodeNames = "nodeNames";
    private static final String f_time = "time";
    private static final String f_nodeRemarks = "nodeRemarks";
    private static final String f_traceId = "traceId";

    private static final String f_createTime = "createTime";
    private static final String f_updateTime = "updateTime";
    private String DATABASE;
    private DatastoreExt store;

    @Getter
    private final Set<String> dataNodeMsgCollectionCache = Sets.newConcurrentHashSet();
    private final static String collectionName = "data_node_msg";

    DataNodeMsgDao() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    public List<DataNodeMsgDoc> getByObjectIds(List<ObjectId> objectIds) {
        if (CollectionUtils.isEmpty(objectIds)) {
            return Lists.newArrayList();
        }
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.in(f_id, objectIds));
        Bson filter = and(filters);
        MongoCollection<DataNodeMsgDoc> collectionList = this.getOrCreateDataNodeMsgCollection();
        FindIterable<DataNodeMsgDoc> dataNodeMsgDocs = collectionList.find(filter).limit(objectIds.size());
        return Lists.newArrayList(dataNodeMsgDocs);
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(DataNodeMsgDoc.class)
                        .automatic(true).build()));
    }

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    public void setStore(DatastoreExt store) {
        this.store = store;
        store.getMongo().getDatabase(DATABASE)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.equals(collectionName)) {
                        dataNodeMsgCollectionCache.add(v);
                    }
                });
    }

    public void deleteInVersionNodesMsg(String tenantId, String objApiName, String dataId, String streamId, List<Long> versionList) {
        MongoCollection<DataNodeMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_objApiName, objApiName));
        filters.add(Filters.eq(f_dataId, dataId));
        filters.add(Filters.eq(f_streamId, streamId));
        filters.add(Filters.in(f_version, versionList));
        mongoCollection.deleteMany(Filters.and(filters));
    }

    public void deleteLtVersionEnterTempDataNodesMsg(String tenantId, String objApiName, String dataId, String streamId, Long version) {
        MongoCollection<DataNodeMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_objApiName, objApiName));
        filters.add(Filters.eq(f_dataId, dataId));
        filters.add(Filters.eq(f_streamId, streamId));
        filters.add(Filters.in(f_nodeNames, Lists.newArrayList(DataNodeNameEnum.EnterTempData.name())));
        filters.add(Filters.size(f_nodeNames, 1));
        filters.add(Filters.lt(f_version, version));
        mongoCollection.deleteMany(Filters.and(filters));
    }
    public void deleteLteVersionNodesMsg(String tenantId, String objApiName, String dataId, String streamId, Long version) {
        MongoCollection<DataNodeMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_objApiName, objApiName));
        filters.add(Filters.eq(f_dataId, dataId));
        filters.add(Filters.eq(f_streamId, streamId));
        filters.add(Filters.lte(f_version, version));
        mongoCollection.deleteMany(Filters.and(filters));
    }

    public void deleteByIds(List<ObjectId> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        MongoCollection<DataNodeMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_id, idList));

        mongoCollection.deleteMany(Filters.and(filters));
    }

    public List<DataNodeMsgDoc> getLtTimeNodeMsgList(String tenantId, List<String> notDefaultTenantIds, Long time, Integer limit) {
        MongoCollection<DataNodeMsgDoc> collectionList = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.lt(f_time, time));
        if (StringUtils.isNotBlank(tenantId)) {//按企业
            filters.add(Filters.eq(f_tenantId, tenantId));
        } else {
            if (CollectionUtils.isNotEmpty(notDefaultTenantIds)) {
                filters.add(Filters.nin(f_tenantId, notDefaultTenantIds));
            }
        }
        Bson filter = and(filters);
        FindIterable<DataNodeMsgDoc> dataList = collectionList.find(filter).limit(limit);
        return Lists.newArrayList(dataList);
    }



    public void batchUpsertDataNodeMsgDoc(List<DataNodeMsgDoc> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        int size = dataList.size();
        StopWatch sw = new StopWatch("batchUpsert-" + collectionName + '[' + size + ']');
        List<WriteModel<DataNodeMsgDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (int i = 0; i < dataList.size(); i++) {
            DataNodeMsgDoc dataNodeMsgDoc = dataList.get(i);
            UpdateOneModel<DataNodeMsgDoc> updateOneModel = new UpdateOneModel<>(this.updateBy(dataNodeMsgDoc), this.upsert(dataNodeMsgDoc), updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        sw.start("bulkWrite-" + collectionName + '[' + size + ']');
        MongoCollection<DataNodeMsgDoc> collection = this.getOrCreateDataNodeMsgCollection();
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
            sw.stop();
            long cost = sw.getTotalTimeMillis();
            if (cost > 5000) {
                log.warn("bulkWrite collection: {}, cost: {}ms, items: {}", collectionName, cost, size);
                log.warn(sw.prettyPrint());
            }
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
    }

    private Bson updateBy(DataNodeMsgDoc message) {
        Document result = new Document();
        result.put(f_uniqueKey, message.getUniqueKey());
        return result;
    }

    private Bson upsert(DataNodeMsgDoc message) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();
        Document addToSet = new Document();

        //更新
        updateDoc.append(f_updateTime, new Date());
        if (StringUtils.isNotBlank(message.getTraceId())) {
            updateDoc.append(f_traceId, message.getTraceId());
        }

        //插入
        setOnInsertDoc
                .append(f_tenantId, Objects.requireNonNull(message.getTenantId(), "tenantId cannot be null"))
                .append(f_objApiName, Objects.requireNonNull(message.getObjApiName(), "objApiName cannot be null"))
                .append(f_dataId, Objects.requireNonNull(message.getDataId(), "dataId cannot be null"))
                .append(f_version, Objects.requireNonNull(message.getVersion(), "version cannot be null"))
                .append(f_streamId, Objects.requireNonNull(message.getStreamId(), "streamId cannot be null"))
                .append(f_uniqueKey, Objects.requireNonNull(message.getUniqueKey(), "uniqueKey cannot be null"))
                .append(f_time, Objects.requireNonNull(message.getTime(), "uniqueKey cannot be null"))
                .append(f_createTime, new Date());

        //添加到集合
        if (CollectionUtils.isNotEmpty(message.getNodeTypes())) {
            addToSet.put(f_nodeTypes, new Document("$each", message.getNodeTypes()));
        }
        if (CollectionUtils.isNotEmpty(message.getNodeTypes())) {
            addToSet.put(f_nodeNames, new Document("$each", message.getNodeNames()));
        }
        if (CollectionUtils.isNotEmpty(message.getNodeTypes())) {
            addToSet.put(f_nodeRemarks, new Document("$each", message.getNodeRemarks()));
        }
        if (CollectionUtils.isNotEmpty(message.getNodeTimes())) {
            addToSet.put(f_nodeTimes, new Document("$each", message.getNodeTimes()));
        }

        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        if (!addToSet.isEmpty()) {
            doc.put("$addToSet", addToSet);
        }
        return doc;
    }

    public List<DataNodeMsgDoc> getByUniqueKey(String tenantId, List<String> uniqueKeys) {
        if (CollectionUtils.isEmpty(uniqueKeys)) {
            return Lists.newArrayList();
        }
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.in(f_uniqueKey, uniqueKeys));
        Bson filter = and(filters);
        MongoCollection<DataNodeMsgDoc> collectionList = this.getOrCreateDataNodeMsgCollection();
        FindIterable<DataNodeMsgDoc> dataNodeMsgDocs = collectionList.find(filter).limit(uniqueKeys.size() + 10);
        return Lists.newArrayList(dataNodeMsgDocs);
    }

    public MongoCollection<DataNodeMsgDoc> getOrCreateDataNodeMsgCollection() {
        MongoCollection<DataNodeMsgDoc> collectionList = store.getMongo().getDatabase(DATABASE).withCodecRegistry(DataNodeMsgDao.SingleCodecHolder.codecRegistry).getCollection(collectionName, DataNodeMsgDoc.class);
        if (!dataNodeMsgCollectionCache.add(collectionName)) {
            return collectionList;
        }
        List<String> exists = Lists.newArrayList();
        collectionList.listIndexes().iterator().forEachRemaining(doc -> {
            String name = doc.getString("name");
            if (!"_id_".equals(name)) {
                exists.add(name);
            }
        });
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending(f_createTime);
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(3L, TimeUnit.DAYS).background(true)));
        }
        key = "index_dataId_ver_ei_obj_streamId";
        if (!exists.remove(key)) {
            Bson index_ei_obj_dataId_ver_streamId = Indexes.compoundIndex(Indexes.ascending(f_dataId), Indexes.ascending(f_version),
                    Indexes.ascending(f_tenantId), Indexes.ascending(f_objApiName), Indexes.ascending(f_streamId));
            toBeCreate.add(new IndexModel(index_ei_obj_dataId_ver_streamId, new IndexOptions().name(key).background(true)));
        }
        key = "index_uniqueKey";
        if (!exists.remove(key)) {
            Bson index_uniqueKey = Indexes.compoundIndex(Indexes.ascending(f_uniqueKey));
            toBeCreate.add(new IndexModel(index_uniqueKey, new IndexOptions().name(key).background(true)));
        }
        key = "index_time_ei";
        if (!exists.remove(key)) {
            Bson index_uniqueKey = Indexes.compoundIndex(Indexes.ascending(f_time), Indexes.ascending(f_tenantId));
            toBeCreate.add(new IndexModel(index_uniqueKey, new IndexOptions().name(key).background(true)));
        }

        if (!toBeCreate.isEmpty()) {
            List<String> created = collectionList.createIndexes(toBeCreate);
            log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        }
        return collectionList;
    }

}
