package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.DeleteDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperationTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("deleteDataMapping")
public class DeleteDataMappingServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;

    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private AdminSyncDataMappingManager adminSyncDataMappingManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        DeleteDataMappingArg deleteDataMappingArg = JsonUtil.fromJson(commonArg.getParams(), DeleteDataMappingArg.class);
        if (deleteDataMappingArg == null || deleteDataMappingArg.getSourceObjectApiName() == null || deleteDataMappingArg.getDestObjectApiName() == null ||
                deleteDataMappingArg.getSyncDirection() == null||
                (CollectionUtils.isEmpty(deleteDataMappingArg.getSourceDataIds())&&CollectionUtils.isEmpty(deleteDataMappingArg.getDestDataIds()))) {
            log.info("executeLogic params error commonArg={} arg={}", commonArg, deleteDataMappingArg);
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        TenantTypeEnum sourceTenantType = TenantTypeEnum.convertTenantEnum(deleteDataMappingArg.getSyncDirection());

        Result<SyncPloyDetailResult> syncPloyDetailAndSourceDestApiName = adminSyncPloyDetailService.getSyncPloyDetailAndSourceDestApiName(commonArg.getTenantId(), deleteDataMappingArg.getSourceObjectApiName(),
                deleteDataMappingArg.getDestObjectApiName(), sourceTenantType.getType());
        if(ObjectUtils.isEmpty(syncPloyDetailAndSourceDestApiName)||ObjectUtils.isEmpty(syncPloyDetailAndSourceDestApiName.getData())){
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }
        SyncPloyDetailResult syncPloyDetailResult = syncPloyDetailAndSourceDestApiName.getData();
        String dataCenterId=sourceTenantType.name().equals("CRM")?syncPloyDetailResult.getDestDataCenterId():syncPloyDetailResult.getSourceDataCenterId();
        List<SyncObjectAndTenantMappingData> objectApiNameMappingDatas = new ArrayList<>();
        for (SyncPloyDetailResult.ObjectMappingInfo detailObjectMapping : syncPloyDetailResult.getDetailObjectMappings()) {
            objectApiNameMappingDatas.add(SyncObjectAndTenantMappingData.newInstance(String.valueOf(tenantId),
                    detailObjectMapping.getSourceObjectApiName(),
                    String.valueOf(tenantId),
                    detailObjectMapping.getDestObjectApiName()));
        }
        List<String> needDeleteIds= Lists.newArrayList();
        List<String> deleteSourceDataIds= Lists.newArrayList();
        List<SyncDataMappingsEntity> syncDataMappingsEntities=Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(deleteDataMappingArg.getSourceDataIds())){
            //根据源destDataIds删除数据
             syncDataMappingsEntities = syncDataMappingsDao.setTenantId(commonArg.getTenantId()).queryBySourceDataIdList(tenantId, deleteDataMappingArg.getSourceObjectApiName(),
                    deleteDataMappingArg.getSourceDataIds(),  deleteDataMappingArg.getDestObjectApiName());
        }else {
             syncDataMappingsEntities = syncDataMappingsDao.setTenantId(commonArg.getTenantId()).queryByDestDataIdList(tenantId, deleteDataMappingArg.getSourceObjectApiName(),
                    deleteDataMappingArg.getDestDataIds(),  deleteDataMappingArg.getDestObjectApiName());
        }
        for (SyncDataMappingsEntity syncDataMappingsEntity : syncDataMappingsEntities) {
            needDeleteIds.add(syncDataMappingsEntity.getId());
            deleteSourceDataIds.add(syncDataMappingsEntity.getSourceDataId());
            if(syncDataMappingsEntity.getSourceObjectApiName().equals(syncPloyDetailResult.getSourceObjectApiName())&&CollectionUtils.isNotEmpty(objectApiNameMappingDatas)){
                //删除主对象，需要联合删除从对象
                List<SyncDataMappingsEntity> deleteDetailMappingIds = syncDataMappingsDao.setTenantId(commonArg.getTenantId()).listByMasterDataId(tenantId, objectApiNameMappingDatas, syncDataMappingsEntity.getSourceDataId());
                List<String> detailMappingIds = deleteDetailMappingIds.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
                needDeleteIds.addAll(detailMappingIds);
            }
        }
        List<List<String>> partition = Lists.partition(needDeleteIds, 1000);//一次只删除1000条
        for (List<String> partitionDeleteIds : partition) {
            adminSyncDataMappingManager.deleteBySyncDataMappingIds(tenantId, dataCenterId, syncPloyDetailResult.getId(), partitionDeleteIds);
        }
        //在页面记录下删除的日志
        StringBuilder executeFunctionDelete=new StringBuilder();
        executeFunctionDelete.append(i18NStringManager.getByEi(I18NStringEnum.s1142,tenantId))
                .append(i18NStringManager.getByEi(I18NStringEnum.s1143,tenantId))
                .append(JSONObject.toJSONString(commonArg))
                .append("\n")
                .append(i18NStringManager.getByEi(I18NStringEnum.s1144,tenantId))
                .append(JSONObject.toJSONString(deleteSourceDataIds));
        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(String.valueOf(tenantId),dataCenterId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                syncPloyDetailResult.getId(),-10000, UserOperationTypeEnum.DELETE_MAPPING.name(),executeFunctionDelete.toString(),null));

        return Result.newSuccess();
    }

}
