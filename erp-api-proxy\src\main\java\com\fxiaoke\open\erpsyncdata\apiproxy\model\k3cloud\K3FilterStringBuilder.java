package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fxiaoke.open.erpsyncdata.apiproxy.utils.TimeUtils;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.Operate;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/4/19 10:14:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class K3FilterStringBuilder {
    private List<List<FilterData>> filterList = Lists.newArrayList();

    @ApiModelProperty("最小时间")
    private Long startTime;

    @ApiModelProperty("最大时间")
    private Long endTime;

    public String build() {
        FillFilter();
        checkFilter();

        // 部分K3c不支持相同的and参数放在多个or里,兼容这部分逻辑
        return checkSameAndFilter() ? getFilterStringBySameAnd() : getFilterStringByDiffAnd();
    }

    private String getFilterStringBySameAnd() {
        if (CollectionUtils.isEmpty(filterList)) {
            return "";
        }

        final List<List<FilterData>> orFilter = filterList.stream().map(list -> list.stream().filter(this::isTimeFilter).collect(Collectors.toList())).collect(Collectors.toList());
        final List<FilterData> andFilter = filterList.get(0).stream().filter(filterData -> !isTimeFilter(filterData)).collect(Collectors.toList());

        String filterString = getFilterString(orFilter);
        if (StringUtils.isNotBlank(filterString)) {
            filterString = "(" + filterString + ")";
        }

        final String andFilterString = getAndFilterString(andFilter);

        return Stream.of(filterString, andFilterString).filter(StringUtils::isNotBlank).collect(Collectors.joining(" and "));
    }

    private boolean checkSameAndFilter() {
        // 如果and条件都相同,将and放到外面
        if (filterList.size() < 2) {
            return false;
        }

        final List<Set<FilterData>> collect = filterList.stream()
                .map(list -> list.stream().filter(filterData -> !isTimeFilter(filterData)).collect(Collectors.toSet()))
                .collect(Collectors.toList());

        final Set<FilterData> filter = collect.get(0);
        for (Set<FilterData> filterData : collect) {
            if (!Objects.equals(filterData, filter)) {
                return false;
            }
        }

        return true;
    }

    private boolean isTimeFilter(final FilterData filterData) {
        return BooleanUtils.isTrue(filterData.getIsVariableBetween());
    }

    private String getFilterStringByDiffAnd() {
        return getFilterString(filterList);
    }

    private static String getFilterString(final List<List<FilterData>> list) {
        return list.stream()
                .map(K3FilterStringBuilder::getAndFilterString)
                .filter(StringUtils::isNotBlank)
                .map(s -> "(" + s + ")")
                .collect(Collectors.joining(" or "));
    }

    private static String getAndFilterString(final List<FilterData> andFilter) {
        return andFilter.stream().map(K3FilterStringBuilder::transFilter).filter(StringUtils::isNotBlank).collect(Collectors.joining(" and "));
    }

    private void FillFilter() {
        if (CollectionUtils.isEmpty(filterList)) {
            return;
        }

        if (filterList.stream().flatMap(Collection::stream).noneMatch(FilterData::getIsVariableBetween)) {
            return;
        }

        String startTime = TimeUtils.getDateTime(this.startTime);
        String endTime = TimeUtils.getDateTime(this.endTime);

        filterList.stream()
                .flatMap(Collection::stream)
                .filter(FilterData::getIsVariableBetween)
                .forEach(filterData -> filterData.setFieldValue(Lists.newArrayList(startTime, endTime)));
    }

    private void checkFilter() {
        // 物料分组默认不使用时间筛选条件，每次全量查。

        // 空List allMatch 结果一定为true
        // if (!filterList.stream().allMatch(list -> list.stream().anyMatch(FilterData::getIsVariableBetween))) {
        //     throw new RuntimeException("每一组AND的筛选字段里，必须使用日期或日期时间字段。" + JSON.toJSONString(filterList));
        // }
    }

    public static String transFilter(FilterData filterData) {
        if (Operate.CUSTOM.equals(filterData.getOperate())) {
            //自定义筛选条件，加括号防止里面有or
            return "(" + filterData.getFieldValue().get(0) + ")";
        }
        StringBuilder result = new StringBuilder();
        result.append(filterData.getFieldApiName());
        // 旧的配置为 IS/ISN
        /**
         * 前端组件配置
         * EQ：'等于’
         * N:"不等于’
         * GT:’大于’，
         * GTE:'大于等于’，
         * LT: ’小于"，
         * LTE:'小于等于’，
         * LIKE:"包含’，
         * NLIKE：'不包含’，
         * IS:‘为空’，
         * ISN:'不为空’，
         * LT：‘早于’，
         * GT：‘晚于’，
         * IN:‘属于’，
         * NTN: ，不属于，
         * TimeProid:时间段，
         */
        switch (filterData.getOperate()) {
            case Operate.IN:
                String join = Joiner.on("','").skipNulls().join(filterData.getFieldValue());
                if(join!=null&&join.contains(";")){//旧版本数据范围组件，输入字符串；分隔没有被分隔成list
                    join = join.replace(";","','");
                }
                result.append(" in ('").append(join).append("')");
                return result.toString();
            case Operate.IS:
            case Operate.EQ:
                result.append("='").append(filterData.getFieldValue().get(0)).append("'");
                return result.toString();
            case Operate.IS_NOT:
            case Operate.N:
                //使用标准sql的<>
                result.append("<>'").append(filterData.getFieldValue().get(0)).append("'");
                return result.toString();
            case Operate.LIKE:
                result.append(" like '").append(filterData.getFieldValue().get(0)).append("'");
                return result.toString();
            case Operate.BETWEEN:
                if (filterData.getFieldValue() == null || filterData.getFieldValue().size() != 2) {
                    return null;
                }
                StringBuilder between = new StringBuilder();//加括号，以防作为or的一部分
                if (FieldType.DATE.equals(filterData.getFieldType()) || FieldType.DATE_TIME.equals(filterData.getFieldType())) {
                    between.append("(").append(filterData.getFieldApiName()).append("> {ts'").append(filterData.getFieldValue().get(0))
                            .append("'} and ").append(filterData.getFieldApiName()).append("<= {ts'").append(filterData.getFieldValue().get(1)).append("'} )");
                } else {
                    between.append("(").append(filterData.getFieldApiName()).append(">").append(filterData.getFieldValue().get(0))
                            .append(" and ").append(filterData.getFieldApiName()).append("<=").append(filterData.getFieldValue().get(1)).append(")");
                }
                return between.toString();
            default:
                return null;
        }
    }
}
