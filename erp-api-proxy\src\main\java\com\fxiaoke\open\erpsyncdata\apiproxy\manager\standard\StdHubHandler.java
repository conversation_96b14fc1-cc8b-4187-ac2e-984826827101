package com.fxiaoke.open.erpsyncdata.apiproxy.manager.standard;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorConfigHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorMetaDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.PushData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.PushResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringResponse;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.standard.SyncResult;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.OuterConnectorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.EmployeeData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.trace.TraceContext;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * apl类连接器
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.HUB, channel = ErpChannelEnum.STANDARD_CHANNEL)
public class StdHubHandler implements ConnectorDataHandler, ConnectorMetaDataHandler, ConnectorConfigHandler {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private OuterConnectorManager outerConnectorManager;


    private static final String X_FS_ENTERPRISE_ID = "x-fs-enterprise-id";
    private static final String X_FS_EI = "x-fs-ei";
    private static final String X_TENANT_ID = "x-tenant-id";
    private static final String X_FS_LOCALE = "x-fs-locale";
    private static final String X_FS_RPC_ID = "x-fs-rpc-Id";
    private static final String X_FS_PEER_NAME = "x-fs-peer-name";
    private static final String X_FS_TRACE_ID = "x-fs-trace-id";
    private static final String X_FS_DC_ID = "x-fs-dc-id";
    private static final String X_FS_API_NAME = "x-fs-api-name";
    private static final MediaType JSONT = MediaType.get("application/json;charset=utf-8");

    private static final ImmutableMap<String,ResultCodeEnum> codeMap = ImmutableMap.of(
            com.fxiaoke.erpdss.connector.core.constant.ResultCodeEnum.UNSUPPORTED_METHOD.getCode().toString(),ResultCodeEnum.HUB_UNSUPPORTED,
            com.fxiaoke.erpdss.connector.core.constant.ResultCodeEnum.UNSUPPORTED_CONNECTOR.getCode().toString(),ResultCodeEnum.HUB_UNSUPPORTED
    );

    /**
     * 执行apl方法
     */
    public <T> Result<T> execute(final String tenantId,
                                 final String dataCenterId,
                                 final String objApiName,
                                 final ErpObjInterfaceUrlEnum interfaceUrl,
                                 final String connectorApiName,
                                 final Object requestBody,
                                 TypeReference<T> resType) {
        return execute(tenantId, dataCenterId, objApiName, interfaceUrl, connectorApiName, requestBody, null, null, resType);
    }

    /**
     * 执行apl方法
     */
    public <T> Result<T> execute(final String tenantId,
                                 final String dataCenterId,
                                 final String objApiName,
                                 final ErpObjInterfaceUrlEnum interfaceUrl,
                                 final String connectorApiName,
                                 final Object requestBody,
                                 final Integer timeoutSecond,
                                 final TimeFilterArg timeFilterArg,
                                 TypeReference<T> resType) {
        String res = "";
        Result<String> apiResult = null;
        long callTime = System.currentTimeMillis();
        try {
            String requestStr = requestBody == null ? "{}" : JacksonUtil.toJson(requestBody);
            HubInfo hubInfo = outerConnectorManager.getHubInfo(tenantId, connectorApiName);
            //远程执行
            apiResult = executeRemote(tenantId, dataCenterId, connectorApiName, hubInfo.getBaseUrl(), interfaceUrl, requestStr, timeoutSecond);
            if (!apiResult.isSuccess()) {
                res = apiResult.getErrMsg();
                return Result.copy(apiResult);
            }
            res = apiResult.getData();
            SyncResult syncResult = SyncResult.parseFromStr(apiResult.getData());
            if (!syncResult.isSuccess()) {
                //需要映射一些错误码
                final ResultCodeEnum resultCode = codeMap.getOrDefault(syncResult.getCode(), ResultCodeEnum.HUB_EXECUTE_EXCEPTION);
                //noinspection DataFlowIssue
                return Result.newError(resultCode, syncResult.getMessage());
            }
            T resultData = syncResult.parseData(resType);
            return Result.newSuccess(resultData);
        } finally {
            long returnTime = System.currentTimeMillis();
            long costTime = returnTime - callTime;
            interfaceMonitorManager.saveErpInterfaceMonitor(tenantId,
                    dataCenterId,
                    objApiName,
                    interfaceUrl.name(),
                    requestBody,
                    res,
                    apiResult != null && apiResult.isSuccess() ? 1 : 2,
                    callTime,
                    returnTime,
                    "hub",
                    TraceUtil.get(),
                    costTime,
                    timeFilterArg);
        }
    }


    /**
     * 当前环境执行
     */
    private Result<String> executeLocal(String tenantId,
                                        String dataCenterId,
                                        String connectorApiName,
                                        ErpObjInterfaceUrlEnum interfaceUrl,
                                        String requestStr,
                                        Integer timeoutSecond) {
        return Result.newError("todo");
    }

    private Result<String> executeRemote(String tenantId, String dataCenterId, String connectorApiName, String hubBaseUrl, ErpObjInterfaceUrlEnum interfaceUrl, String requestStr, Integer timeoutSecond) {
        Request.Builder builder = new Request.Builder();
        builder.url(hubBaseUrl + "/hub/connector/" + connectorApiName + "/" + interfaceUrl.getMethodName())
                .header(X_FS_EI, tenantId)
                .header(X_TENANT_ID, tenantId)
                .header(X_FS_ENTERPRISE_ID, tenantId)
                .header(X_TENANT_ID, tenantId)
                .header(X_FS_LOCALE, I18nUtil.getLocaleFromTrace())
                .header(X_FS_RPC_ID, TraceContext.get().getRpcId())
                .header(X_FS_TRACE_ID, TraceUtil.get())
                .header(X_FS_PEER_NAME, CommonConstant.ERP_SYNC_DATA_BUSINESS)
                .header(X_FS_DC_ID, dataCenterId)
                .header(X_FS_API_NAME, connectorApiName)
                .post(RequestBody.create(requestStr, JSONT));
        Result<String> apiResult = proxyHttpClient.requestWrapException(builder, timeoutSecond);
        return apiResult;
    }

    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(erpIdArg.getTenantId(), connectInfo.getConnectParams());
        String tenantId = erpIdArg.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objAPIName = erpIdArg.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.queryMasterById;

        return execute(tenantId,
                dataCenterId,
                objAPIName,
                interEnum,
                standardConnectParam.getApiName(),
                erpIdArg,
                new TypeReference<StandardData>() {
                }
        );

    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(timeFilterArg.getTenantId(), connectInfo.getConnectParams());
        final ErpObjInterfaceUrlEnum interEnum;
        String tenantId = timeFilterArg.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objAPIName = timeFilterArg.getObjAPIName();
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            interEnum = ErpObjInterfaceUrlEnum.queryInvalid;
        } else {
            interEnum = ErpObjInterfaceUrlEnum.queryMasterBatch;
        }

        return execute(tenantId,
                dataCenterId,
                objAPIName,
                interEnum,
                standardConnectParam.getApiName(),
                timeFilterArg,
                null,
                timeFilterArg,
                new TypeReference<StandardListData>() {
                }
        );
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        final String objApiName = standardData.getMasterFieldVal().getApiName();
        //创建时取出Id字段
        standardData.removeId();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.create;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                standardData,
                new TypeReference<ErpIdResult>() {
                }
        );
    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData 不使用
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardDetailData.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.create;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(), standardDetailData,
                new TypeReference<StandardDetailId>() {
                }
        );
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, true);
    }

    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardDeleteData.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.delete;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(), standardDeleteData,
                new TypeReference<String>() {
                }
        );
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, false);
    }

    private Result<String> comInvalid(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo, boolean isMaster) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        String objApiName1 = standardInvalidData.getObjAPIName();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = isMaster ? ErpObjInterfaceUrlEnum.invalid : ErpObjInterfaceUrlEnum.invalidDetail;
        Optional<Map.Entry<String, String>> detail = standardInvalidData.getDetailFieldVals().entrySet().stream().findAny();
        if (detail.isPresent()) {
            objApiName1 += objApiName1 + "==" + detail.get().getKey();
        }
        final String objApiName = objApiName1;

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                standardInvalidData,
                new TypeReference<String>() {
                }
        );
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardRecoverData.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.recover;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(), standardRecoverData,
                new TypeReference<String>() {
                }
        );
    }

    @Override
    public Result<List<ErpObjTreeNode>> listObjects(ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.listObjects;

        return execute(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                null,
                new TypeReference<List<ErpObjTreeNode>>() {
                }
        );
    }

    @Override
    public Result<ErpObjTreeNode> getObjectTree(ErpConnectInfoEntity connectInfo, ObjectDescArg arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getObjectTree;

        return execute(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(), arg,
                new TypeReference<ErpObjTreeNode>() {
                }
        );
    }

    @Override
    public Result<ErpObjTreeNode> getObjectDesc(ErpConnectInfoEntity connectInfo, ObjectDescArg.ParseObjField arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getObjectDesc;

        return execute(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(), arg,
                new TypeReference<ErpObjTreeNode>() {
                }
        );
    }

    @Override
    public Result<List<ConnectorAuthType>> getAuthTypeList(ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getAuthTypeList;
        return execute(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                null,
                new TypeReference<List<ConnectorAuthType>>() {
                }
        );
    }

    public Result<ConnectorIntro> getConnectorIntro(ErpConnectInfoEntity connectInfo, GetConnectorIntroArg arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getConnectorIntro;

        return execute(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                arg,
                new TypeReference<ConnectorIntro>() {
                }
        );
    }


    public Result<SystemParams> processUserInputSystemParams(ErpConnectInfoEntity connectInfo, SystemParams systemParams) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.processUserInputSystemParams;
        String objApiName = "all";

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(), systemParams,
                new TypeReference<SystemParams>() {
                }
        );
    }


    /**
     * 获取OAuth2 获取授权码URL
     *
     * @param connectInfo
     * @param systemParams
     * @return
     */
    public Result<String> getOAuth2AuthUrl(ErpConnectInfoEntity connectInfo, SystemParams systemParams) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getOAuth2AuthUrl;
        String objApiName = "all";

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(), systemParams,
                new TypeReference<String>() {
                }
        );
    }


    /**
     * 检查授权状态
     */
    public Result<Void> checkAuthStatus(ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.checkAuthStatus;
        String objApiName = "all";

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                null,
                new TypeReference<Void>() {
                }
        );
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        final String objApiName = standardData.getMasterFieldVal().getApiName();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.update;

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                standardData,
                new TypeReference<ErpIdResult>() {
                }
        );

    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardDetailData.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.updateDetail;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                standardDetailData,
                new TypeReference<StandardDetailId>() {
                }
        );
    }

    @Override
    public Result<Void> checkPushAuth(CheckAuthArg checkAuth, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.checkPushAuth;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                checkAuth,
                new TypeReference<Void>() {
                }
        );
    }

    @Override
    public Result<List<StandardData>> processPushData(PushDataProcessArg pushDataProcessArg, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = pushDataProcessArg.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.processPushData;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                pushDataProcessArg,
                new TypeReference<List<StandardData>>() {
                }
        );
    }

    public Result<List<ErpObjTreeNode>> getObjsNeedPreset(ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getObjsNeedPreset;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());

        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                null,
                new TypeReference<List<ErpObjTreeNode>>() {
                }
        );
    }

    @Override
    public Result<String> getStreamInfoNeedPreset(ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getStreamInfoNeedPreset;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                null,
                new TypeReference<String>() {
                }
        );
    }

    @Override
    public Result<Void> checkEnableStream(ErpConnectInfoEntity connectInfo, CheckStreamEnableArg arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = arg.getErpSplitObjApiName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.checkEnableStream;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                arg,
                new TypeReference<Void>() {
                }
        );
    }

    @Override
    public Result<StringResponse> webhook(StringRequest arg, ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.webhook;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                arg,
                new TypeReference<StringResponse>() {
                }
        );
    }

    @Override
    public Result<PushData> webhookProcessRequest(StringRequest request, ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.webhookProcessRequest;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        Result<PushData> execute = execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                request,
                new TypeReference<PushData>() {
                }
        );
        return execute;
    }

    @Override
    public Result<StringResponse> webhookProcessResponse(PushResult result, ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.webhookProcessResponse;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                result,
                new TypeReference<StringResponse>() {
                }
        );
    }

    @Override
    public Result<Void> processChangeEmployeeMapping(SimpleEmployeeMapping simpleEmployeeMapping, ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.processChangeEmployeeMapping;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                simpleEmployeeMapping,
                new TypeReference<Void>() {
                }
        );
    }


    @Override
    public Result<List<EmployeeData>> queryAllEmployee(ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.queryAllEmployee;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                null,
                new TypeReference<List<EmployeeData>>() {
                }
        );
    }

    @Override
    public Result<List<OutFile>> convertCrmFile2Out(ConvertFile.Crm2OutArg arg, ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.convertCrmFile2Out;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                arg,
                new TypeReference<List<OutFile>>() {
                }
        );
    }

    @Override
    public Result<List<CrmFileModel>> convertOutFile2Crm(ConvertFile.Out2CrmArg arg, ErpConnectInfoEntity connectInfo) {
        String dataCenterId = connectInfo.getId();
        String objApiName = "all";
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.convertOutFile2Crm;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        return execute(connectInfo.getTenantId(),
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                arg,
                new TypeReference<List<CrmFileModel>>() {
                }
        );
    }

}
