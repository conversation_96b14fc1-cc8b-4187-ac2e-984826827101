package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/7/15 20:22
 * @Version 1.0
 */
@Data
public class OaSyncLogDaoArg implements Serializable {
    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 数据id
     */
    private String dataId;

    /**
     * 数据主属性
     */
    private String dataName;

    /**
     * 业务对象名
     */
    private String objectName;
    /**
     * 数据
     */

    private String dataJson;

    /**
     * 状态
     */

    private String status;
    /**
     * 接收人
     */

    private String receiverId;
    /**
     * 对象信息
     */

    private String objApiName;

    /**
     * 事件类型
     */

    private String eventType;
}
