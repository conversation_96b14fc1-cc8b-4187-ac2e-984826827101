package com.fxiaoke.open.erpsyncdata.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ErpTempDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BsonTimeUtil;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.helper.DataSupport;
import com.fxiaoke.open.erpsyncdata.monitor.manager.DataSourceInfoManager;
import com.fxiaoke.open.erpsyncdata.monitor.model.*;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByTimeArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByTimeResult;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataSupportService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/30
 */
@Service
@DataSupport(DataSourceType.TEMP)
public class TempDataSupportServiceImpl implements DataSupportService {
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private DataSourceInfoManager dataSourceInfoManager;

    @Override
    public Result<ListDataByTimeResult> listDataByTime(ListDataByTimeArg arg) {
        TempDataSourceInfo tempDataSourceInfo = dataSourceInfoManager.getTempConnectInfo(arg.getTenantId(), arg.getObjApiName());
        QueryTempTimeFilterArg tempTimeFilterArg = new QueryTempTimeFilterArg();
        tempTimeFilterArg.setTenantId(arg.getTenantId());
        tempTimeFilterArg.setStartTime(arg.getBeginTime());
        tempTimeFilterArg.setEndTime(arg.getEndTime());
        tempTimeFilterArg.setLimit(arg.getLimit());
        tempTimeFilterArg.setObjAPIName(arg.getObjApiName());
        tempTimeFilterArg.setLastErpTempId(arg.getBeginId());
        tempTimeFilterArg.setOperationType(2);
        List<Document> docs = erpTempDataDao.listErpObjDataFromMongo(tempTimeFilterArg, tempDataSourceInfo.getDatacenterId());
        ListDataByTimeResult result = new ListDataByTimeResult();
        if (CollectionUtils.isNotEmpty(docs)) {
            //有数据时
            boolean complete = docs.size() < arg.getLimit();
            List<CompareData> dataList = Lists.newArrayList();
            for (Document document : docs) {
                if (ErpTempDataStatusEnum.STATUS_NOT_READY.getStatus().equals(document.getInteger("status"))){
                    //未准备好的数据不返回
                    continue;
                }
                String dataId = document.getString("data_id");
                JSONObject standardData = JSONObject.parseObject(document.getString("data_body"));
                String dataVersion = standardData.getJSONObject("masterFieldVal").getString(tempDataSourceInfo.getDataVersionField());
                dataList.add(CompareData.of(dataId,dataVersion));
            }
            result.setCompareDataList(dataList);
            //填充最后一条数据信息
            Document lastDoc = docs.get(docs.size()-1);
            String erpTempId = lastDoc.getObjectId("_id").toString();
            Long lastSyncTime = BsonTimeUtil.bsonTime2Long(lastDoc.get("new_last_sync_time"));
            //这个会用来判断是否存在数据
            if (!complete){
                result.setNextBeginTime(lastSyncTime);
                result.setNextBeginId(erpTempId);
            }
        }
        return Result.newSuccess(result);
    }
}
