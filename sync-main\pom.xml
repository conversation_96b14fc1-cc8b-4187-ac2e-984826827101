<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-erp-sync-data</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sync-main</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>dispatcher-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-other-rest-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-writer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-converter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
        </dependency>

        <!-- Reids -->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>
        <!-- End Reids -->


        <!-- mybatis -->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rpc-trace</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-auth-rest-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-aviator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>common-db-proxy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-sync-data-admin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-sandbox-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-change-set-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>