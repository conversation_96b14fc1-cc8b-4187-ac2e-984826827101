package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;


/**
 * <AUTHOR>
 * @Date: 19:01 2023/1/12
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class ReSyncDataNodeMsg {

    /**
     * ei+streamId+sourceDataId
     */
    private String uniqueKey;

    /**
     * id
     */
    @BsonId
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * 数据源对象
     */
    private String sourceObjApiName;
    /**
     * 数据目标对象
     */
    private String destObjApiName;
    /**
     * 源数据唯一标识
     */
    private String sourceDataId;
    /**
     * 集成流id
     */
    private String streamId;
    /**
     * 重试时间间隔（分钟）
     */
    private Integer reSyncTimeInterval;
    /**
     * 下一次重试时间
     */
    private Long nextReSyncTime;
    /**
     * 重试次数
     */
    private Integer tries;
    /**
     * 重试次数限制
     */
    private Integer tryLimit;
    /**
     * 重试数据
     */
    private SyncDataEntity syncDataEntity;
    /**
     * 是否需要检查mapping是否存在
     */
    private Boolean needCheckMapping;
    /**
     * traceId
     */
    private String traceId;
    /**
     * 多语
     */
    private String locale;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public static ReSyncDataNodeMsg createBySyncDataEntity(String tenantId, SyncPloyDetailSnapshotData2 snapshot, SyncDataEntity syncDataEntity,
                                                           Boolean needCheckMapping, String locale,Boolean reSyncRightNow) {
        IntegrationStreamNodesData.ReSyncErrorDataNode node = snapshot.getSyncPloyDetailData().getIntegrationStreamNodes().getReSyncErrorDataNode();
        Long nextReSyncTime=null;
        if(reSyncRightNow!=null&&reSyncRightNow){//时间调大，不扫描重试
            nextReSyncTime=System.currentTimeMillis()*2;
        }
        return create(tenantId, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getDestObjectApiName(),
                syncDataEntity.getSourceDataId(), snapshot.getSyncPloyDetailId(), node.getReSyncTimeInterval(), nextReSyncTime, 0, node.getReSyncTopLimit(),
                syncDataEntity, needCheckMapping, TraceUtil.get(), locale);
    }

    public static ReSyncDataNodeMsg create(String tenantId, String sourceObjApiName, String destObjApiName, String sourceDataId, String streamId,
                                           Integer reSyncTimeInterval, Long nextReSyncTime, Integer tries, Integer tryLimit, SyncDataEntity syncDataEntity,
                                           Boolean needCheckMapping, String traceId, String locale) {
        Date createTime = new Date();
        if (nextReSyncTime == null && reSyncTimeInterval != null) {
            nextReSyncTime = System.currentTimeMillis() + reSyncTimeInterval * 60 * 1000L;
        }
        if(reSyncTimeInterval==null){
            reSyncTimeInterval=0;
        }
        String uniqueKey = getMsgUniqueKey(tenantId, streamId, sourceDataId);
        ReSyncDataNodeMsg reSyncDataNodeMsg = ReSyncDataNodeMsg.builder()
                .uniqueKey(uniqueKey)
                .tenantId(tenantId)
                .sourceObjApiName(sourceObjApiName)
                .destObjApiName(destObjApiName)
                .sourceDataId(sourceDataId)
                .streamId(streamId)
                .reSyncTimeInterval(reSyncTimeInterval)
                .nextReSyncTime(nextReSyncTime)
                .tries(tries)
                .tryLimit(tryLimit)
                .syncDataEntity(syncDataEntity)
                .needCheckMapping(needCheckMapping)
                .traceId(traceId)
                .locale(locale)
                .createTime(createTime)
                .updateTime(createTime)
                .build();
        return reSyncDataNodeMsg;
    }

    public static String getMsgUniqueKey(String tenantId, String streamId, String sourceDataId) {
        StringBuffer uniqueKey = new StringBuffer();
        uniqueKey.append(tenantId).append("_").append(streamId).append("_").append(sourceDataId);
        return uniqueKey.toString();
    }
}
