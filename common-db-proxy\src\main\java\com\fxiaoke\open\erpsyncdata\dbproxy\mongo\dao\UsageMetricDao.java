package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.lang.Opt;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UsageMetricDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UsageMetricDoc.Fields;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */
@Slf4j
@Component
@DependsOn("erpSyncDataMongoStore")
public class UsageMetricDao {

    @Qualifier("erpSyncDataMongoStore")
    @Autowired
    private DatastoreExt store;

    private static final String dbName = "erp_sync_data2";


    @PostConstruct
    void init() {
        createIndex();
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private MongoCollection<UsageMetricDoc> getColl() {
        MongoCollection<UsageMetricDoc> coll = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("usage_metric", UsageMetricDoc.class);
        return coll;
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<UsageMetricDoc> coll = getColl();
        coll.createIndex(Indexes.ascending(Fields.tenantId, Fields.uniKey), new IndexOptions().unique(true).background(true));
    }

    public void incGlobal(String uniKey, String metricKey, Integer incNum, List<String> addTenantIds, List<String> addStreamIds) {
        Bson filters = Filters.and(Filters.eq(Fields.tenantId, null), Filters.eq(Fields.uniKey, uniKey));
        MongoCollection<UsageMetricDoc> coll = getColl();
        Bson inc = Updates.combine(
                Updates.inc(Fields.metrics + "." + metricKey, incNum),
                Updates.addEachToSet(Fields.linkTenantIds, addTenantIds),
                Updates.addEachToSet(Fields.linkStreamIds, addStreamIds),
                Updates.setOnInsert(Fields.uniKey, uniKey)
        );
        UpdateResult updateResult = coll.updateOne(filters, inc, new UpdateOptions().upsert(true));
        log.info("inc metric,{},{},{},{},{},{}", uniKey, metricKey, incNum, addStreamIds, addStreamIds, updateResult);
    }

    public void inc(String tenantId, String uniKey, String metricKey, Integer incNum) {
        Bson filters = Filters.and(Filters.eq(Fields.tenantId, tenantId), Filters.eq(Fields.uniKey, uniKey));
        MongoCollection<UsageMetricDoc> coll = getColl();
        Bson inc = Updates.combine(
                Updates.inc(Fields.metrics + "." + metricKey, incNum)
        );
        coll.updateOne(filters, inc);
    }

    public UsageMetricDoc find(String tenantId, String uniKey) {
        Bson filters = Filters.and(Filters.eq(Fields.tenantId, tenantId), Filters.eq(Fields.uniKey, uniKey));
        UsageMetricDoc result = getColl().find(filters)
                .projection(Projections.exclude(Fields.linkTenantIds, Fields.linkStreamIds))
                .limit(1).first();
        return result;
    }

    public Double GetMetric(String tenantId, String uniKey, String metricKey) {
        UsageMetricDoc usageMetricDoc = find(tenantId, uniKey);
        Double metric = safeGetMetricFromDoc(usageMetricDoc, metricKey);
        return metric;
    }

    private static Double safeGetMetricFromDoc(UsageMetricDoc usageMetricDoc, String metricKey) {
        Double metric = Opt.ofNullable(usageMetricDoc)
                .map(v -> v.getMetrics())
                .map(v -> v.get(metricKey))
                .orElse(0.0);
        return metric;
    }

    public Double getGlobalMetric(String uniKey, String metricKey) {
        return GetMetric(null, uniKey, metricKey);
    }
}
