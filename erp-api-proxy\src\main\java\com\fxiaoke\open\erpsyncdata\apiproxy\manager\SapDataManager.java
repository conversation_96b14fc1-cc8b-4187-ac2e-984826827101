package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.Pair;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.HeaderManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.factory.SpecialObjHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.sap.SAPTodoData;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HeaderScriptUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushIdentifyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HeaderFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SapConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.net.SocketTimeoutException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_SAP)
public class SapDataManager extends BaseErpDataManager {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SpecialObjHandlerFactory specialObjHandlerFactory;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpPushIdentifyDao erpPushIdentifyDao;
    @Autowired
    private HeaderManager headerManager;
    @Autowired
    private ComplexDataConvertUtils complexDataConvertUtils;
    /**
     * 构建参数，发送请求，并保存日志，解析返回结果转换为标准格式
     *
     *
     * @param tenantId
     * @param connectParam 连接参数
     * @param servicePath  服务地址
     * @param params       参数
     * @param interfaceMonitorData    接口日志
     * @return 标准结果
     */
    @LogLevel()
    private <T> Result<T> post(String tenantId,String dataCenterId,SapConnectParam connectParam,
                               String servicePath, Object params,Map<String, String> header,
                               InterfaceMonitorData interfaceMonitorData, TypeReference<T> typeReference,
                               Long rspReadLimitLenByte) {
        Boolean serializeNull = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.SERIALIZE_NULL_TENANTS);
        String url = connectParam.getBaseUrl() + "/" + servicePath;

        Map<String, String> headerMap=Maps.newHashMap();
        ErpPushIdentifyEntity token = erpPushIdentifyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTokenByTenantId(tenantId);
        if (token != null && StringUtils.isNotBlank(token.getToken())) {
            headerMap.put("erp_dss_token", token.getToken());
        }
        if(ObjectUtils.isNotEmpty(connectParam.getHeaderFunctionName())){
            //前端header函数
            HeaderFunctionArg headerFunctionArg= HeaderFunctionArg.builder().tenantId(tenantId).dataCenterId(dataCenterId).functionName(connectParam.getHeaderFunctionName()).interfaceUrl(ErpObjInterfaceUrlEnum.valueOf(interfaceMonitorData.getType()))
                    .params(params).build();
            Result<Map<String, String>> headerMapByFunction = headerManager.getHeaderMapByFunction(headerFunctionArg);
            if(!headerMapByFunction.isSuccess()){
                saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(), interfaceMonitorData.getObjApiName(),interfaceMonitorData.getType(),
                        new ProxyRequest(url, headerMap, params),headerMapByFunction.getErrMsg(),2,System.currentTimeMillis(), System.currentTimeMillis(),
                        "", TraceUtil.get(),0L, null);
               throw new ErpSyncDataException(I18NStringEnum.s153,tenantId);
            }
            headerMap=headerMapByFunction.getData();
        }else{
            headerMap = HeaderScriptUtil.getHeaderMap(tenantId,connectParam.getHeaderScript(), url);
        }
        if(header!=null){
            headerMap.putAll(header);
        }
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        String rspStr = "";
        StopWatch stopWatch = new StopWatch();
        Long callTime=System.currentTimeMillis();
        stopWatch.start();
        Integer status=1;
        try {
            if (serializeNull){
                response = proxyHttpClient.postUrlSerialNull(url, params, headerMap,rspReadLimitLenByte);
            }else {
                response = proxyHttpClient.postUrl(url, params, headerMap,rspReadLimitLenByte);
            }
            if(response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}",tenantId);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            rspStr = response.getBody();
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            SapConnectParam.ResultFormat resultFormat = connectParam.getResultFormat();
            String resultCode = resultObj.getString(resultFormat.getCodeName());
            String resultMsg = resultObj.getString(resultFormat.getMsgName());
            //防止resultCode找不到
            if (resultFormat.getSuccessCode().equals(resultCode)) {
                //调用接口成功
                String dataJson = resultObj.getString(resultFormat.getDataName());
                dataJson = (dataJson == null) ? "" : dataJson;
                T standardData = null;
                dataJson = dataJson.trim();
                if (dataJson.startsWith("{") && dataJson.endsWith("}")) {
                    standardData = JSON.parseObject(dataJson, typeReference);
                } else {
                    standardData = (T) dataJson;
                }
                Result<T> result = new Result<>(standardData);
                result.setErrMsg(resultMsg);
                return result;
            } else {
                Result<T> result=new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
                String dataJson = resultObj.getString(resultFormat.getDataName());
                if(StringUtils.isNotBlank(dataJson)){//如果不为空失败也返回数据
                    try{
                        T standardData = null;
                        dataJson = dataJson.trim();
                        if (dataJson.startsWith("{") && dataJson.endsWith("}")) {
                            standardData = JSON.parseObject(dataJson, typeReference);
                        } else {
                            standardData = (T) dataJson;
                        }
                        result.setData(standardData);
                    }catch (Exception e){
                        log.warn("parseObject dataJson:{}", dataJson);
                    }
                }
                status=2;
                //请求失败
                log.warn("post failed,url:{},param:{},header:{},response:{}", url, params, headerMap, response);
                return result;
            }
        } catch (Exception e) {
            status=2;
            log.debug("post error,connectParam:{},url:{},headerMap:{},params:{}", connectParam, url, headerMap, params, e);
            rspStr = e + " " + rspStr;
            if(e.getCause() instanceof SocketTimeoutException){//超时
                return new Result<>(ResultCodeEnum.SOCKETTIMEOUT, e.getMessage());
            }
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            stopWatch.stop();
            saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(),
                    interfaceMonitorData.getObjApiName(),interfaceMonitorData.getType(),
                    new ProxyRequest(url, headerMap, params),rspStr,status,callTime, System.currentTimeMillis(),
                    "", TraceUtil.get(),stopWatch.getTotalTimeMillis(), null);
        }
    }

    /**
     * 构建参数，发送请求，并保存日志，解析返回结果转换为标准格式
     *
     *
     * @param connectParam  连接参数
     * @param servicePath   服务地址
     * @param interfaceMonitorData     接口日志
     * @param type
     * @return 标准结果
     */
    @LogLevel()
    private <R> Result<R> get(String tenantId,String dataCenterId,SapConnectParam connectParam, String servicePath,
                              InterfaceMonitorData interfaceMonitorData,TypeReference<R> type,
                              Function<JSONObject, R> function, Long rspReadLimitLenByte) {
        Boolean serializeNull = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.SERIALIZE_NULL_TENANTS);
        String url = connectParam.getBaseUrl() + "/" + servicePath;
//        Map<String, String> headerMap;
//        headerMap = HeaderScriptUtil.getHeaderMap(connectParam.getHeaderScript(), url);
        Map<String, String> headerMap=Maps.newHashMap();
        ErpPushIdentifyEntity token = erpPushIdentifyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTokenByTenantId(tenantId);
        if (token != null && StringUtils.isNotBlank(token.getToken())) {
            headerMap.put("erp_dss_token", token.getToken());
        }
        if(ObjectUtils.isNotEmpty(connectParam.getHeaderFunctionName())){
            //前端header函数
            HeaderFunctionArg headerFunctionArg= HeaderFunctionArg.builder().tenantId(tenantId).dataCenterId(dataCenterId).functionName(connectParam.getHeaderFunctionName()).interfaceUrl(ErpObjInterfaceUrlEnum.valueOf(interfaceMonitorData.getType()))
                    .params(null).build();
            Result<Map<String, String>> headerMapByFunction = headerManager.getHeaderMapByFunction(headerFunctionArg);
            if(!headerMapByFunction.isSuccess()){
                saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(), interfaceMonitorData.getObjApiName(),interfaceMonitorData.getType(),new ProxyRequest(url, headerMap, null),headerMapByFunction.getErrMsg(),2,System.currentTimeMillis(), System.currentTimeMillis(),
                        "", TraceUtil.get(),0L, null);
                throw new ErpSyncDataException(I18NStringEnum.s153,tenantId);
            }
            headerMap=headerMapByFunction.getData();

        }else{
            headerMap = HeaderScriptUtil.getHeaderMap(tenantId,connectParam.getHeaderScript(), url);
        }
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        String rspStr = "";
        StopWatch stopWatch = new StopWatch();
        Long callTime=System.currentTimeMillis();
        stopWatch.start();
        Integer status=1;
        try {
            response = proxyHttpClient.getUrl(url, headerMap,rspReadLimitLenByte);
            if(response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}",tenantId);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            rspStr = response.getBody();
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            SapConnectParam.ResultFormat resultFormat = connectParam.getResultFormat();
            String resultCode = resultObj.getString(resultFormat.getCodeName());
            String resultMsg = resultObj.getString(resultFormat.getMsgName());
            if (StringUtils.equalsIgnoreCase(resultCode, resultFormat.getSuccessCode())) {
                R res;
                if (serializeNull) {
                    //防止丢失Map里面的null
                    JSONObject dataJson = resultObj.getJSONObject(resultFormat.getDataName());
                    res = function.apply(dataJson);
                } else {
                    //会丢掉null
                    res = JSON.parseObject(resultObj.getString(resultFormat.getDataName()), type);
                }
                Result<R> result = new Result<>(res);
                result.setErrMsg(resultMsg);
                return result;
            } else {
                status=2;
                //请求失败
                log.warn("get failed,url:{},headerMap:{},response:{}", url, headerMap, response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
            }
        } catch (Exception e) {
            status=2;
            log.debug("get error,connectParam:{},url:{},headerMap:{}", connectParam, url, headerMap, e);
            rspStr = e + " " + rspStr;
            if(e.getCause() instanceof SocketTimeoutException){//超时
                return new Result<>(ResultCodeEnum.SOCKETTIMEOUT, e.getMessage());
            }
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            stopWatch.stop();
            saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(),
                    interfaceMonitorData.getObjApiName(),interfaceMonitorData.getType(),
                    new ProxyRequest(url, headerMap, null),rspStr,status,callTime, System.currentTimeMillis(),
                    "", TraceUtil.get(),stopWatch.getTotalTimeMillis(),interfaceMonitorData.getTimeFilterArg());
        }
    }


    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String viewPath = sapConnectParam.getServicePath().getView();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectInfo.getId(),
                erpIdArg.getObjAPIName(),
                ErpObjInterfaceUrlEnum.queryMasterById.name());
        //构建param
        viewPath = addRequestParam(viewPath, ImmutableList.of(
                Pair.build("objAPIName", erpIdArg.getObjAPIName()),
                Pair.build("dataId", erpIdArg.getDataId()),
                Pair.build("includeDetail", erpIdArg.isIncludeDetail())));
        //发送请求
        Result<StandardData> result = get(connectInfo.getTenantId(),connectInfo.getId(),sapConnectParam,
                viewPath, interfaceMonitorData, new TypeReference<StandardData>(){}, FormatConvertUtil::json2Std, ConfigCenter.CONTENT_LENGTH_LIMIT);
        if (result.isSuccess()) {
            result.getData().setObjAPIName(erpIdArg.getObjAPIName());
        }
        return result;
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if (erpObjDataResult != null) {
            Result<List<StandardData>> dataListResult = Result.newSuccess();
            if (!erpObjDataResult.isSuccess()) {
                dataListResult.setErrCode(erpObjDataResult.getErrCode());
                dataListResult.setErrMsg(erpObjDataResult.getErrMsg());
                dataListResult.setData(Lists.newArrayList());
                return dataListResult;
            } else {
                //特殊处理
                List<StandardData> dataList = Lists.newArrayList();
                dataList.add(erpObjDataResult.getData());
                List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(erpIdArg.getTenantId(), connectInfo.getId(), erpIdArg.getObjAPIName(), false);
                if (CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)) {
                    for (String apiName : destObjApiNameByErpRealApiName) {
                        SpecialObjHandler objHandler = specialObjHandlerFactory.getObjHandler(apiName);
                        if (objHandler != null) {
                            objHandler.afterReSyncDataById(erpIdArg, dataList, connectInfo.getId());
                        }
                    }
                }
                dataListResult.setData(dataList);
                return dataListResult;
            }
        }
        return null;
    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String queryPath;
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            queryPath = sapConnectParam.getServicePath().getQueryInvalidByTime();
            type=ErpObjInterfaceUrlEnum.queryInvalid;
        } else {
            queryPath = sapConnectParam.getServicePath().getQueryByTime();
        }
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(timeFilterArg.getTenantId(),
                connectInfo.getId(),
                timeFilterArg.getObjAPIName(),
                type.name());
        interfaceMonitorData.setTimeFilterArg(timeFilterArg);
        //构建param
        List<Pair<String, Object>> pairList = Lists.newArrayList(
                Pair.build("objAPIName", timeFilterArg.getObjAPIName()),
                Pair.build("startTime", timeFilterArg.getStartTime()),
                Pair.build("endTime", timeFilterArg.getEndTime()),
                Pair.build("offset", timeFilterArg.getOffset()),
                Pair.build("limit", timeFilterArg.getLimit()),
                Pair.build("includeDetail", timeFilterArg.isIncludeDetail()),
                Pair.build("operationType", timeFilterArg.getOperationType()));
        //仅仅支持EQ,不校验操作符，统一变成等于
        ListUtils.emptyIfNull(timeFilterArg.getFilters()).stream()
                .flatMap(Collection::stream)
                .distinct()
                .forEach(filter -> {
                    Pair pair = Pair.build(filter.getFieldApiName(), CollectionUtils.isEmpty(filter.getFieldValue()) ? "" : filter.getFieldValue().get(0));
                    pairList.add(pair);
                });
        List<Pair<String, Object>> pairs = ImmutableList.copyOf(pairList);
        queryPath = addRequestParam(queryPath, pairs);
        //发送请求，使用get请求
        Result<StandardListData> result = get(connectInfo.getTenantId(),connectInfo.getId(),sapConnectParam, queryPath,
                interfaceMonitorData, new TypeReference<StandardListData>(){}, FormatConvertUtil::json2StdList, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        if (result == null || !result.isSuccess()) {
            return result;
        }
        //特殊处理
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(timeFilterArg.getTenantId(), connectInfo.getId(), timeFilterArg.getObjAPIName(), false);
        if (CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)) {
            for (String apiName : destObjApiNameByErpRealApiName) {
                SpecialObjHandler objHandler = specialObjHandlerFactory.getObjHandler(apiName);
                if (objHandler != null) {
                    objHandler.afterQueryListData(timeFilterArg, result.getData().getDataList(), connectInfo.getId());
                }
            }
        }
        //支持sap拼接主键
        //根据dbid+erp_realapiname查询对应的拓展
        complexDataConvertUtils.complexId(result.getData(),timeFilterArg,connectInfo.getId());
        return result;
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getMasterFieldVal().getApiName();
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        //创建时取出Id字段
        standardData.removeId();
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String savePath = sapConnectParam.getServicePath().getCreate();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.create.name());
        //特殊处理
        SpecialObjHandler objHandler = null;
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(tenantId, connectInfo.getId(), standardData.getObjAPIName(), true);
        if (CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)) {
            for (String apiName : destObjApiNameByErpRealApiName) {
                objHandler = specialObjHandlerFactory.getObjHandler(apiName);
            }
        }
        if (objHandler != null) {//前动作
            objHandler.beforeCreateErpObjData(tenantId, standardData, connectInfo.getId());
        }

//        LinkedStandardData linkedStandardData = LinkedStandardData.convert(standardData,
//                erpObjectFieldDao,
//                adminSyncDataDao,
//                tenantId,
//                connectInfo.getId());

        Map<String, String> header= Maps.newHashMap();
        if(standardData.getMasterFieldVal().get(CommonConstant.REQUEST_ID_KEY)!=null){
            header.put(CommonConstant.REQUEST_ID_KEY,standardData.getMasterFieldVal().getString(CommonConstant.REQUEST_ID_KEY));
            standardData.getMasterFieldVal().remove(CommonConstant.REQUEST_ID_KEY);
        }
        //发送请求
        Result<ErpIdResult> result = post(tenantId, connectInfo.getId(),sapConnectParam, savePath, standardData,
                header, interfaceMonitorData,
                new TypeReference<ErpIdResult>() {
                },null);
        if (result == null || !result.isSuccess()) {
            return result;
        }
        if (objHandler != null) {//后动作
            objHandler.afterCreateErpObjData(tenantId, standardData, result, connectInfo.getId());
        }
        return result;
    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        //StandardDetailData standardData = FormatConvertUtil.crm2StdDetailErp(doWriteMqData);
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardDetailData.getObjAPIName();
        String dataId = standardDetailData.getMasterDataId();
        String dataName = standardDetailData.getMasterDataName();
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String savePath = sapConnectParam.getServicePath().getCreateDetail();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.create.name());

//        LinkedStandardDetailData linkedStandardDetailData = LinkedStandardDetailData.convert(standardDetailData,
//                erpObjectFieldDao,
//                adminSyncDataDao,
//                doWriteMqData.getDestTenantId(),
//                connectInfo.getId());

        Map<String, String> header= Maps.newHashMap();
        //发送请求
        Result<StandardDetailId> result = post(tenantId, connectInfo.getId(),sapConnectParam, savePath,
                standardDetailData, header, interfaceMonitorData,
                new TypeReference<StandardDetailId>() {
                },null);
        return result;
    }


    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, true);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardDeleteData.getObjAPIName();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.delete.name());
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        //获取删除接口的path
        String deletePath = sapConnectParam.getServicePath().getDelete();
        Map<String, String> header= Maps.newHashMap();
        //发送请求
        Result<String> result = post(tenantId, connectInfo.getId(),sapConnectParam, deletePath, standardDeleteData, header,
                interfaceMonitorData, new TypeReference<String>() {}, null);
        if (result == null || !result.isSuccess()) {
            return result;
        }
        return result;
    }


    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, false);
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    private Result<String> comInvalid(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo, boolean isMaster) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardInvalidData.getObjAPIName();
        String dataId = standardInvalidData.getMasterFieldVal().getId();
        Optional<Map.Entry<String, String>> detail = standardInvalidData.getDetailFieldVals().entrySet().stream().findAny();
        if (detail.isPresent()) {
            objApiName += objApiName + "==" + detail.get().getKey();
            dataId = dataId + "==" + detail.get().getValue();
        }
        String dataName = "";  //作废这个字段没有值
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.invalid.name());
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        //获取作废接口的path
        String invalidPath = isMaster ? sapConnectParam.getServicePath().getInvalid() : sapConnectParam.getServicePath().getInvalidDetail();
        Map<String, String> header= Maps.newHashMap();
        //发送作废请求
        Result<String> result = post(tenantId,connectInfo.getId(), sapConnectParam, invalidPath, standardInvalidData,
                header, interfaceMonitorData, new TypeReference<String>() {}, null);
        log.info("trace invalidErpObjData isMaster:{},  standardData:{}, result:{} ", isMaster, standardInvalidData, result);
        return result;
    }


    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getMasterFieldVal().getApiName();
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String updatePath = sapConnectParam.getServicePath().getUpdate();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.update.name());
        //特殊处理
        SpecialObjHandler objHandler = null;
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(tenantId, connectInfo.getId(), standardData.getObjAPIName(), true);
        if (CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)) {
            for (String apiName : destObjApiNameByErpRealApiName) {
                objHandler = specialObjHandlerFactory.getObjHandler(apiName);
            }
        }
        if (objHandler != null) {//前动作
            objHandler.beforeUpdateErpObjData(tenantId, standardData, connectInfo.getId());
        }

//        LinkedStandardData linkedStandardData = LinkedStandardData.convert(standardData,
//                erpObjectFieldDao,
//                adminSyncDataDao,
//                tenantId,
//                connectInfo.getId());

        Map<String, String> header= Maps.newHashMap();
        if(standardData.getMasterFieldVal().get(CommonConstant.REQUEST_ID_KEY)!=null){
            header.put(CommonConstant.REQUEST_ID_KEY,standardData.getMasterFieldVal().getString(CommonConstant.REQUEST_ID_KEY));
            standardData.getMasterFieldVal().remove(CommonConstant.REQUEST_ID_KEY);
        }
        //发送请求
        Result<ErpIdResult> result = post(tenantId, connectInfo.getId(),sapConnectParam, updatePath, standardData, header,
                interfaceMonitorData, new TypeReference<ErpIdResult>() {}, null);
        if (result == null || !result.isSuccess()) {
            return result;
        }
        if (objHandler != null) {//后动作
            objHandler.afterUpdateErpObjData(tenantId, standardData, result, connectInfo.getId());
        }
        return result;
    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        //StandardDetailData standardData = FormatConvertUtil.crm2StdDetailErp(doWriteMqData);
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardDetailData.getObjAPIName();
        String dataId = standardDetailData.getMasterDataId();
        String dataName = standardDetailData.getMasterDataName();
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String updatePath = sapConnectParam.getServicePath().getUpdateDetail();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.update.name());

//        LinkedStandardDetailData linkedStandardDetailData = LinkedStandardDetailData.convert(standardDetailData,
//                erpObjectFieldDao,
//                adminSyncDataDao,
//                doWriteMqData.getDestTenantId(),
//                connectInfo.getId());

        Map<String, String> header= Maps.newHashMap();
        //发送请求
        Result<StandardDetailId> result = post(tenantId, connectInfo.getId(),sapConnectParam, updatePath, standardDetailData,
                header, interfaceMonitorData, new TypeReference<StandardDetailId>() {}, null);
        return result;
    }

    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "'ProcessObj'", action = ActionEnum.CREATE)
    public Result<String> sendExternalTodo(SAPTodoData sapTodoData, ErpConnectInfoEntity connectInfo) {
        if (Objects.isNull(sapTodoData)) {
            log.info("SAPTodoData is null");
            return Result.newSuccess();
        }
        SapConnectParam sapConnectParam = ConnectParamUtil.parseSap(connectInfo.getConnectParams());
        String path = "create";
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(connectInfo.getTenantId(),
                connectInfo.getId(),
                "ProcessObj",
                ErpObjInterfaceUrlEnum.create.name());
        Map<String, String> header= Maps.newHashMap();
        // 发送给多个接收人
        Result<String> result = post(connectInfo.getTenantId(), connectInfo.getId(),sapConnectParam, path, sapTodoData, header,
                interfaceMonitorData,  new TypeReference<String>() {}, null);
        log.info("sendExternalTodo result={}", result);
        return result;
    }


    private String addRequestParam(String url, List<Pair<String, Object>> pairs) {
        String param = pairs.stream().map(v -> v.first + "=" + v.second).collect(Collectors.joining("&"));
        url = url + "?" + param;
        return url;
    }

    /**
     * 通过erp真时apiName获取相关的策略的crm对象apiName
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    private List<String> getCrmObjApiNameByErpRealApiName(String tenantId, String dataCenterId, String objApiName, Boolean crmIsSource) {
        ErpObjectRelationshipEntity erpObjectRelationshipEntity = ErpObjectRelationshipEntity.builder()
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .erpRealObjectApiname(objApiName)
                .build();
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryList(erpObjectRelationshipEntity);
        if (CollectionUtils.isNotEmpty(erpObjectRelationshipEntityList)) {
            List<String> splitApiNames = erpObjectRelationshipEntityList.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
            List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByTenantIdAndStatus(tenantId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            if (CollectionUtils.isNotEmpty(syncPloyDetailEntities)) {
                if (!crmIsSource) {
                    List<String> destApiNames = syncPloyDetailEntities.stream().filter(entity -> splitApiNames.contains(entity.getSourceObjectApiName())).map(SyncPloyDetailEntity::getDestObjectApiName).collect(Collectors.toList());
                    return destApiNames;
                } else {
                    List<String> destApiNames = syncPloyDetailEntities.stream().filter(entity -> splitApiNames.contains(entity.getDestObjectApiName())).map(SyncPloyDetailEntity::getSourceObjectApiName).collect(Collectors.toList());
                    return destApiNames;
                }

            }
        }

        return Lists.newArrayList();
    }
}
