package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/27
 */

@Data
@Builder
public class SyncCostLogDTO {
    private String tenantId; // 企业id
    private String ea; //企业账号
    private String srcObjApiName; //源对象
    private String sourceDataId; //源数据id
    private String syncDataId; //同步记录id
    private Long firstSendDispatchTime; //首次发送聚合mq
    private Long firstParseTime; //首次接收解析聚合mq
    private Long dispatchMqWaitingTime; //首次聚合mq等待时间
    private Long listenTime; //分发时间
    private Long dispatchWaitingTime; //分发等待时间
    private Long triggerCost; //触发耗时
    private Long cplTriggerCost; //触发耗时
    private Long processCost; //处理耗时
    private Long cplProcessCost; //完成处理耗时
    private Long writeTimeCost; //写耗时
    private Long cplWriteCost; //完成写耗时
    private Long finishTime; //完成时间

}
