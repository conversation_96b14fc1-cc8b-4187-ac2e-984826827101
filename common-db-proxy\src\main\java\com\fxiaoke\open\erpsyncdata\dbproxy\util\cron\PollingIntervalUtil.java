package com.fxiaoke.open.erpsyncdata.dbproxy.util.cron;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PollingIntervalDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.CronPatternBuilder;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.Part;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.matcher.PartMatcher;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.matcher.PatternMatcher;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.parser.PatternParser;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DayLimitType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IntervalTimeUnitEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/9/9
 */
@Slf4j
public class PollingIntervalUtil {

    public static boolean checkCron(String cron) {
        if (StringUtils.isBlank(cron)) {
            return true;
        }
        try {
            List<String> split = StrSplitter.split(cron, ' ', true, true);
            if (split.size() != 5) {
                return false;
            }
            List<PatternMatcher> matchers = PatternParser.parse(cron);
            int minMatchCount = 0;
            for (int i = 0; i < 60; i++) {
                //任意一个匹配到都算
                for (PatternMatcher matcher : matchers) {
                    PartMatcher minMatcher = matcher.get(Part.MINUTE);
                    if (minMatcher.match(i)) {
                        minMatchCount++;
                        break;
                    }
                }
            }
            //一个小时内只能匹配10次
            return minMatchCount <= 10;
        } catch (Exception e) {
            log.warn("check error", e);
            return false;
        }
    }


    public static void fillCron(String tenantId, PollingIntervalApiDto pollingInterval, int cronBeginMinute) {
        try {
            String cron = getCronFromApiDto(tenantId,pollingInterval, cronBeginMinute);
            pollingInterval.setCronExpression(cron);
        } catch (ErpSyncDataException e) {
            log.warn("set cron failed,{}", e.getErrMsg());
        } catch (Exception e) {
            log.error("setCron expression error,{}", pollingInterval, e);
        }
    }

    //不会转换cron
    public static PollingIntervalApiDto dto2ApiDto(PollingIntervalDto pollingIntervalDto) {
        PollingIntervalApiDto apiDto = new PollingIntervalApiDto();
        BeanUtils.copyProperties(pollingIntervalDto, apiDto);
        if (DayLimitType.EVERY_DAY == pollingIntervalDto.getDayLimitType()) {
            //每分钟转换成时和天
            Integer minutes = pollingIntervalDto.getMinutes();
            long d2m = TimeUnit.DAYS.toMinutes(1L);
            long h2m = TimeUnit.HOURS.toMinutes(1L);
            long year2m = TimeUnit.DAYS.toMinutes(366L);
            if (minutes == null || minutes > year2m) {
                //每天执行一次
                apiDto.setTimeUnit(IntervalTimeUnitEnum.once);
            } else if (minutes >= d2m) {
                apiDto.setTimeUnit(IntervalTimeUnitEnum.days);
                apiDto.setIntervalQuantity(Long.valueOf(minutes / d2m).intValue());
            } else if (minutes >= h2m) {
                apiDto.setTimeUnit(IntervalTimeUnitEnum.hours);
                apiDto.setIntervalQuantity(Long.valueOf(minutes / h2m).intValue());
            } else {
                apiDto.setTimeUnit(IntervalTimeUnitEnum.minutes);
                apiDto.setIntervalQuantity(minutes);
            }
        }
        return apiDto;
    }


    //兼容一些异常的旧数据，如60分钟，24小时
    public static void fixOldInterval(PollingIntervalApiDto apiDto) {
        if (DayLimitType.EVERY_DAY == apiDto.getDayLimitType()) {
            if (apiDto.getTimeUnit() == IntervalTimeUnitEnum.minutes && apiDto.getIntervalQuantity() >= 60) {
                //转换为时
                apiDto.setTimeUnit(IntervalTimeUnitEnum.hours);
                apiDto.setIntervalQuantity(Long.valueOf(apiDto.getIntervalQuantity() / 60).intValue());
            }
            if (apiDto.getTimeUnit() == IntervalTimeUnitEnum.hours && apiDto.getIntervalQuantity() >= 24) {
                //转换为天，实际上在后面会抛出业务异常。
                apiDto.setTimeUnit(IntervalTimeUnitEnum.days);
                apiDto.setIntervalQuantity(Long.valueOf(apiDto.getIntervalQuantity() / 24).intValue());
            }
        }
    }

    public static String getCronFromApiDto(String tenantId, PollingIntervalApiDto pollingInterval, int cronBeginMinute) {
        if (pollingInterval == null) {
            return cronBeginMinute + "/6 * * * *";
        }
        if (DayLimitType.CRON.equals(pollingInterval.getDayLimitType())) {
            return pollingInterval.getCronExpression();
        }
        int startHour = 0, startMin = 0, endHour = 23;
        try {
            if (StrUtil.isNotBlank(pollingInterval.getStartDataTime())) {
                DateTime hm = DateUtil.date(DateUtil.parseByPatterns(pollingInterval.getStartDataTime(), "HH:mm"));
                startHour = hm.hour(true);
                startMin = hm.minute();
            }
            if (StrUtil.isNotBlank(pollingInterval.getEndDataTime())) {
                DateTime hm = DateUtil.date(DateUtil.parseByPatterns(pollingInterval.getEndDataTime(), "HH:mm"));
                endHour = hm.hour(true);
                if (hm.minute() < 30) {
                    //当原来配置的分钟起始在30分之前，大部分为00.将时往前调一小时
                    endHour--;
                }
            }
        } catch (Exception ignore) {
        }
        CronPatternBuilder patternBuilder = CronPatternBuilder.of();
        if (startHour > endHour) {
            //不允许跨天
            endHour = Part.HOUR.getMax();
            pollingInterval.setEndDataTime("23:59");
        }
        if (pollingInterval.getTimeUnit() == null) {
            pollingInterval.setTimeUnit(IntervalTimeUnitEnum.once);
        }
        //兼容旧的异常interval
        fixOldInterval(pollingInterval);
        Integer intervalQuantity = pollingInterval.getIntervalQuantity();
        switch (pollingInterval.getTimeUnit()) {
            case once:
                //固定时分点执行
                patternBuilder.setValues(Part.HOUR, startHour);
                patternBuilder.setValues(Part.MINUTE, startMin);
                break;
            case minutes:
                //间隔分钟执行,固定从0开始
                if (intervalQuantity < 6) {
                    //不允许间隔小于6分钟
                    intervalQuantity = 6;
                    pollingInterval.setIntervalQuantity(6);
                }
                if (!(startHour == 0 && endHour == 23)) {
                    patternBuilder.setRange(Part.HOUR, startHour, endHour);
                }
//                添加随机值,防止流量尖刺
                patternBuilder.setLoop(Part.MINUTE, cronBeginMinute, intervalQuantity);
                break;
            case hours:
                //间隔时执行
                patternBuilder.setValues(Part.MINUTE, startMin);
                if (!(startHour == 0 && endHour == 23)) {
                    patternBuilder.setRangeLoop(Part.HOUR, startHour, endHour,intervalQuantity);
                }else {
                    patternBuilder.setLoop(Part.HOUR, startHour, intervalQuantity);
                }
                break;
            case days:
                //原来按天执行的需要刷数据
                throw new ErpSyncDataException(I18NStringEnum.s239,tenantId);
        }
        switch (pollingInterval.getDayLimitType()) {
            //天的限定
            case DAY_OF_MONTH:
                patternBuilder.setValues(Part.DAY_OF_MONTH, pollingInterval.getLimitValues());
                break;
            case DAY_OF_WEEK:
                patternBuilder.setValues(Part.DAY_OF_WEEK, pollingInterval.getLimitValues());
                break;
            default:
                //默认每天都执行
        }
        return patternBuilder.build();
    }

    public static void main(String[] args) {
        System.out.println(checkCron("0/3 *  * * *"));
        System.out.println(checkCron("0/6 *  * * *"));
        System.out.println(checkCron("0/10 *  * * *"));
        System.out.println(checkCron("1,5,9 *  * * *"));
        System.out.println(checkCron("0 0/6 * * * *"));
        System.out.println(checkCron("0/60 9-17 * * *"));
    }
}
