package com.fxiaoke.open.erpsyncdata.monitor.util;

import cn.hutool.core.lang.Opt;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.CheckStatusUrl;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.ImmutableList;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Component
public class MonitorConfigCenter {
    public static ImmutableList<CheckStatusUrl> checkStatusUrls = ImmutableList.of();

    /**
     * 仅启动spring会执行，方便测试
     */
    @PostConstruct
    void init() {
        ConfigFactory.getInstance().getConfig("erp-sync-data-monitor", config -> {
            loadConfig(config);
        });
    }

    private void loadConfig(IConfig config) {
        Opt.ofNullable(config.get("checkStatusUrls"))
                .ifPresent(v -> checkStatusUrls = ImmutableList.copyOf(JacksonUtil.fromJson(v, new TypeReference<List<CheckStatusUrl>>() {
                })));
    }

}
