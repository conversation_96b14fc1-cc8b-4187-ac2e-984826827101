package com.fxiaoke.open.erpsyncdata.common.config;

import com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor;
import com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> (^_−)☆
 */
@Configuration
public class AutoConfConfig {


    @Bean("autoConf")
    public ReloadablePropertySourcesPlaceholderConfigurer autoConf() {
        ReloadablePropertySourcesPlaceholderConfigurer configurer = new ReloadablePropertySourcesPlaceholderConfigurer();
        configurer.setFileEncoding("UTF-8");
        configurer.setIgnoreResourceNotFound(true);
        configurer.setIgnoreUnresolvablePlaceholders(false);
        configurer.setConfigName("erp-sync-data-all");
        return configurer;
    }

    @Bean
    public ReloadablePropertyPostProcessor reloadablePropertyPostProcessor() {
        return new ReloadablePropertyPostProcessor(autoConf());
    }
}
