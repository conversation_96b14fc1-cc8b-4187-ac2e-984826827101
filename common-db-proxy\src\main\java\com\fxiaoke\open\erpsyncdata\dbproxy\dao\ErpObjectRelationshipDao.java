package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheInvalidateContainer;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 注意：如果对象的属性变了需要修改
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
@ManagedTenantReplace
public interface ErpObjectRelationshipDao extends BaseTenantDao<ErpObjectRelationshipEntity, ErpObjectRelationshipDao> {

    @CacheInvalidateContainer({
            @CacheInvalidate(name = "erpMainObjSeqMap.", key = "#tenantId"),
            @CacheInvalidate(name = "realMainSplitObjApiNamesMap.", key = "#tenantId+'.'+#dcId"),
    })
    default void invalidCacheErpObj(String tenantId, String dcId){}

    @InsertProvider(type = CrudProvider.class, method = "insert")
    @Result(javaType = int.class)
    int insert(ErpObjectRelationshipEntity record);

    @InsertProvider(type = BatchProvider.class, method = "batchInsert")
    @Result(javaType = int.class)
    int batchInsert(@Param(BatchProvider.KEY) List<ErpObjectRelationshipEntity> record);

    @Cached(name = "erpObjRelation.", key = "args[0]+'.'+args[1]", expire = 600, cacheType = CacheType.BOTH)
    ErpObjectRelationshipEntity findBySplit(@Param("tenantId") String tenantId,
                                            @Param("erpSplitObjectApiname") String erpSplitObjectApiname);


    List<ErpObjectRelationshipEntity> ListBySplit(@Param("tenantId") String tenantId,@Param("dataCenterId")String dataCenterId,
                                            @Param("erpObjApiNames") List<String> erpObjApiNames);
    List<ErpObjectRelationshipEntity> findByRealObjectApiName(@Param("tenantId")String tenantId,
                                                              @Param("dataCenterId")String dataCenterId,
                                                              @Param("erpRealObjectApiname")String erpRealObjectApiname);

    List<ErpObjectRelationshipEntity> findAllByRealObjectApiName(@Param("tenantId")String tenantId,
                                                              @Param("erpRealObjectApiname")String erpRealObjectApiname);

    List<ErpObjectRelationshipEntity> findAllByTenantId(@Param("tenantId")String tenantId);

    List<ErpObjectRelationshipEntity> listByTenantIdAndDataCenterId(@Param("tenantId")String tenantId,@Param("dataCenterId")String dataCenterId);

    List<String> findAllTenantId();


    List<ErpObjectRelationshipEntity> findMasterByRealObjectApiName(@Param("tenantId")String tenantId,
                                                              @Param("dataCenterId")String dataCenterId,
                                                              @Param("erpRealObjectApiname")String erpRealObjectApiname);

    int deleteByTenantIdAndDcId(@Param("tenantId")String tenantId,@Param("dataCenterId")String dataCenterId);

    int deleteByTenantId(String tenantId);

    List<ErpObjectRelationshipEntity> findNotSplit(@Param("tenantId")String tenantId, @Param("dataCenterId")String dataCenterId);

    List<ErpObjectRelationshipEntity> getSplitObjApiName(@Param("tenantId")String tenantId,
                                             @Param("actualApiName")String actualApiName);

    List<ErpObjectRelationshipEntity> listByTenantId(@Param("tenantId")String tenantId);
}