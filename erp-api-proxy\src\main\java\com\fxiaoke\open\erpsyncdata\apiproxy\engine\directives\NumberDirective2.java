package com.fxiaoke.open.erpsyncdata.apiproxy.engine.directives;

import com.jfinal.template.Env;
import com.jfinal.template.expr.ast.Expr;
import com.jfinal.template.expr.ast.ExprList;
import com.jfinal.template.ext.directive.NumberDirective;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.ParseException;
import com.jfinal.template.stat.Scope;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
public class NumberDirective2 extends NumberDirective {
    private Expr valueExpr;
    @Override
    public void setExprList(ExprList exprList) {
        super.setExprList(exprList);
        int paraNum = exprList.length();
        if (paraNum == 0) {
            throw new ParseException("The parameter of #number directive can not be blank", this.location);
        } else if (paraNum > 2) {
            throw new ParseException("Wrong number parameter of #number directive, two parameters allowed at most", this.location);
        } else {
            this.valueExpr = exprList.getExpr(0);
        }
    }

    @Override
    public void exec(Env env, Scope scope, Writer writer) {
        Object value = this.valueExpr.eval(scope);
        log.info("NumberDirective2.exec,key={},old value={}",valueExpr.toString(),value);
        if(value instanceof Double) {
            Double dValue = (Double) value;
            BigDecimal decimal = BigDecimal.valueOf(dValue);
            String value2 = decimal.toPlainString();
            log.info("NumberDirective2.exec,key={},new value={}",valueExpr.toString(),value2);
            write(writer,value2);
        } else if(value instanceof Long) {
            Long lValue = (Long) value;
            BigDecimal decimal = BigDecimal.valueOf(lValue);
            String value2 = decimal.toPlainString();
            log.info("NumberDirective2.exec,key={},new value={}",valueExpr.toString(),value2);
            write(writer,value2);
        } else {
            super.exec(env, scope, writer);
        }
    }
}
