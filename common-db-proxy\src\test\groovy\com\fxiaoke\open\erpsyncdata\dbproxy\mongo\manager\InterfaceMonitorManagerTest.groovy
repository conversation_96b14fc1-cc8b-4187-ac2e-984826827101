package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import spock.lang.Specification
import spock.lang.Unroll

class InterfaceMonitorManagerTest extends Specification {
    CHInterfaceMonitorManager chInterfaceMonitorManager = Mock()
    SyncLogManager syncLogManager = Mock()
    CHSyncLogManager chSyncLogManager = Mock()
    I18NStringManager i18NStringManager = Mock()

    InterfaceMonitorManager interfaceMonitorManager = new InterfaceMonitorManager(chInterfaceMonitorManager: chInterfaceMonitorManager,
            syncLogManager: syncLogManager,
            chSyncLogManager: chSyncLogManager,
            i18NStringManager: i18NStringManager)

    def setup() {}

    @Unroll
    def "保存ERP接口监控"() {
        given:

        when:
        interfaceMonitorManager.saveErpInterfaceMonitor("tenantId", "dcId", "objApiName", "type", "arg", "result", 1, 1L, 1L, "remark", "traceId", 1L, new TimeFilterArg())

        then:
        1 * chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(*_) >> ["batchUpsertInterfaceMonitorDataResponse"]
        1 * syncLogManager.saveLog(*_)
    }

    @Unroll
    def "批量保存CRM接口监控"() {
        given:
        i18NStringManager.getByEi(*_) >> "getByEiResponse"
        ConfigCenter.NEED_SIMPLIFY_BYTE_LIST_SIZE = 0
        def data1 = new ObjectData(aa: "aaa")
        def data2 = new ObjectData(_id: "id", name: "name")
        List<SyncDataContextEvent> doWriteMqDataList = [new SyncDataContextEvent(sourceData: data1), new SyncDataContextEvent(sourceData: data2)]
        List<ObjectData> dataList = [data1, data2]

        when:
        interfaceMonitorManager.saveBatchWriteCrmInterfaceMonitor("tenantId", "objApiName", "type", ["arg": "arg"], "result", 0, 1L, 1L, "url", doWriteMqDataList, dataList)

        then:
        1 * chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(*_) >> { args ->
            def data = ((List<InterfaceMonitorData>) args[1]).get(0)
            assert data.getArg() == "{\"msg\":\"getByEiResponse\",\"objectDataList\":[{\"aa\":\"aaa\"},{\"_id\":\"id\"}],\"arg\":\"arg\"}"
            return ["Response"]
        }
        1 * chSyncLogManager.batchInsert(*_)
    }

    @Unroll
    def "保存接口日志和同步日志"() {
        given:
        InterfaceMonitorData interfaceMonitorData = new InterfaceMonitorData(null, "tenantId", "dcId", "objApiName", "type", "arg", "result", 0, 1L, 1L, "remark", "traceId", 1L, 1L, new Date(), "syncDataId", "logId", new TimeFilterArg())

        when:
        interfaceMonitorManager.saveInterfaceLogAndSyncLog(interfaceMonitorData)

        then:
        1 * chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(*_) >> ["batchUpsertInterfaceMonitorDataResponse"]
        1 * syncLogManager.saveLog(*_)
    }

    @Unroll
    def "保存并移除等待数据: #description"() {
        given:
        InterfaceMonitorData interfaceMonitorData = new InterfaceMonitorData(null, "tenantId", "dcId", "objApiName", "type", "arg", "result", 0, 1L, 1L, "remark", "traceId", 1L, 1L, new Date(), "syncDataId", "logId", new TimeFilterArg())
        interfaceMonitorManager.WAITING_DATA.set(interfaceMonitorData)

        when:
        interfaceMonitorManager.saveAndRemoveWaitingData(save)

        then:
        if (save) {
            1 * chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(*_) >> ["batchUpsertInterfaceMonitorDataResponse"]
            1 * syncLogManager.saveLog(*_)
        } else {
            0 * chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(*_)
            0 * syncLogManager.saveLog(*_)
        }

        where:
        description  | save
        "保存并移除" | true
        "仅移除"     | false
    }
}
