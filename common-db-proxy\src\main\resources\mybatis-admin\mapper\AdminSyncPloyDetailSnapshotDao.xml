<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao">
  <update id="updateStatusByPloyDetailId">
    update sync_ploy_detail_snapshot
    <set>
        status = #{status}
    </set>
    where sync_ploy_detail_id = #{syncPloyDetailId}
  </update>

  <update id="updateStatusById">
    update sync_ploy_detail_snapshot
    <set>
      status = #{status}
    </set>
    where id = #{id}
  </update>

  <select id="listNewestBySourceTenantIdAndSrouceObjectApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and source_object_api_name = #{sourceObjectApiName} and status = #{status} order by update_time desc
  </select>

  <select id="listEnableSnapshotsBySyncPloyDetailsId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and sync_ploy_detail_id = #{syncPloyDetailsId} and status = #{status} order by update_time desc
  </select>

  <select id="getById" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and id = #{id}
  </select>

  <update id="updateSyncPloyDataDetailById">
    update sync_ploy_detail_snapshot
    set
    "sync_ploy_detail_data" = #{syncPloyDetailData,javaType=com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData,typeHandler = com.fxiaoke.open.erpsyncdata.dbproxy.dao.typehandler.SyncPloyDetailDataTypeHandler}
    where id  =  #{id}
  </update>
  <select id="listBySourceTenantIdAndObjectApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select * from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and source_object_api_name=#{sourceObjectApiName} and status=1
  </select>

  <select id="listNewestByDestTenantIdAndDestObjectApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where dest_tenant_id = #{destTenantId} and dest_object_api_name = #{destObjectApiName} and status = #{status} order by update_time desc
  </select>

  <update id="batchUpdateStatusBySourceTenantIdAndDestTenantIdReverse">
    update sync_ploy_detail_snapshot
    set
    "status" = #{status}
    WHERE (dest_tenant_id = #{destTenantId} AND source_tenant_id = #{sourceTenantId})
    OR
    (dest_tenant_id = #{sourceTenantId} AND source_tenant_id = #{destTenantId})
  </update>

<!--auto generated by MybatisCodeHelper on 2022-03-17-->
  <update id="invalidAllByTenantId">
    update sync_ploy_detail_snapshot
    set "status"=2
    where source_tenant_id=#{sourceTenantId}
  </update>
    <delete id="deleteByTenantId">
        delete from sync_ploy_detail_snapshot
        where dest_tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>
    <select id="listByTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where dest_tenant_id = #{destTenantId}
  </select>
  <select id="listEnableSnapshotOffsetOne"
          resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and sync_ploy_detail_id = #{syncPloyDetailsId} and status = #{status}  order by update_time desc offset 1
  </select>
</mapper>