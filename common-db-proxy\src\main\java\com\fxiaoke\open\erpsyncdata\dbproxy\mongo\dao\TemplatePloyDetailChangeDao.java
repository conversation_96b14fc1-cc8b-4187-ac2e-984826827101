package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplatePloyDetailChangeEntity;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/22 19:44:29
 */
@Component
public class TemplatePloyDetailChangeDao extends BaseDao<TemplatePloyDetailChangeEntity> {

    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }


    public TemplatePloyDetailChangeEntity getById(String id) {
        return findOne("_id", new ObjectId(id));
    }

    public TemplatePloyDetailChangeEntity doFirst() {
        final Query<TemplatePloyDetailChangeEntity> query = createQuery(ImmutableMap.of("status", TemplatePloyDetailChangeEntity.STATUS_INIT));
        query.field("update_time").lessThan(System.currentTimeMillis());
        query.order("update_time");
        query.limit(1);
        final UpdateOperations<TemplatePloyDetailChangeEntity> update = createUpdate(ImmutableMap.of("status", TemplatePloyDetailChangeEntity.STATUS_EXECUTING, "update_time", System.currentTimeMillis()));
        update.inc("tryTime");
        return datastore.findAndModify(query, update);
    }

    public TemplatePloyDetailChangeEntity doExecutingBefore30Min() {
        final Query<TemplatePloyDetailChangeEntity> query = createQuery(ImmutableMap.of("status", TemplatePloyDetailChangeEntity.STATUS_EXECUTING));
        query.field("update_time").lessThan(System.currentTimeMillis() - 30 * 60 * 1000);
        query.order("update_time");
        query.limit(1);
        final UpdateOperations<TemplatePloyDetailChangeEntity> update = createUpdate(ImmutableMap.of("update_time", System.currentTimeMillis()));
        update.inc("tryTime");
        return datastore.findAndModify(query, update);
    }

    public TemplatePloyDetailChangeEntity successful(String id) {
        return updateStatus(id, TemplatePloyDetailChangeEntity.STATUS_SUCCESS, null, null);
    }

    public TemplatePloyDetailChangeEntity retry(String id, String errMsg, Long retryTime) {
        return updateStatus(id, TemplatePloyDetailChangeEntity.STATUS_INIT, errMsg, retryTime);
    }

    public TemplatePloyDetailChangeEntity fail(String id, String errMsg) {
        return updateStatus(id, TemplatePloyDetailChangeEntity.STATUS_FAIL, errMsg, null);
    }

    public TemplatePloyDetailChangeEntity updateStatus(String id, int status, String errMsg, Long retryTime) {
        final Query<TemplatePloyDetailChangeEntity> query = createQuery("_id", id);

        final UpdateOperations<TemplatePloyDetailChangeEntity> update = createUpdate(ImmutableMap.of("status", status));
        Long updateTime = Objects.isNull(retryTime) ? System.currentTimeMillis() : retryTime;
        update.set("update_time", updateTime);
        if (StringUtils.isNotBlank(errMsg)) {
            update.set("err_msg", errMsg);
        }
        return datastore.findAndModify(query, update);
    }
}
