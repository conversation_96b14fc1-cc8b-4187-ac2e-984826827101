package com.fxiaoke.open.erpsyncdata.writer.manager

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.writer.model.BatchDoWriteData
import spock.lang.Specification

class BatchWriteCRMManagerTest extends Specification {

    def "GetNotRepeatedIdDataList"() {
        BatchWriteCRMManager batchWriteCRMManager = new BatchWriteCRMManager();
        when:
        List<BatchDoWriteData> dataList = new ArrayList<>();
        for (int size; size < dataIdList.size(); size++) {
            BatchDoWriteData data = new BatchDoWriteData();
            data.setMainContext(new SyncDataContextEvent());
            data.getMainContext().setDestData(new ObjectData());
            data.getMainContext().getDestData().putId(dataIdList.get(size));
            data.getMainContext().getDestData().put("ff", size);
            dataList.add(data);
        }
        List<List<BatchDoWriteData>> resultList = batchWriteCRMManager.getNotRepeatedIdDataList(dataList)


        then:
        resultList.size() == result
        if (name == "一个重复超2次") {
            for (int i = 0; i < resultList.size() - 1; i++) {//按顺序
                resultList.get(i).get(0).getMainContext().getDestData().get("ff") < resultList.get(i + 1).get(0).getMainContext().getDestData().get("ff")
            }
        }

        where:
        name           || dataIdList                     | result
        "为空"         || []                             | 0
        "不重复"       || ["1", "2", "3"]                | 1
        "一个重复"     || ["1", "2", "3", "1"]           | 2
        "两个重复"     || ["1", "2", "3", "1", "3"]      | 2
        "一个重复超4次" || ["1", "2", "3", "1", "1", "1"] | 4
    }
}
