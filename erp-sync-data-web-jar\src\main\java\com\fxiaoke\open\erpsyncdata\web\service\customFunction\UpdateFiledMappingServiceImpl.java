package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.FieldMappingsArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("updateFiledMappingByCrmId")
public class UpdateFiledMappingServiceImpl  implements CustomFunctionCommonService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    private final List<ErpFieldTypeEnum>supportUpdateFieldType=new ArrayList<ErpFieldTypeEnum>(){{
        add(ErpFieldTypeEnum.employee_oa);
        add(ErpFieldTypeEnum.employee);
        add(ErpFieldTypeEnum.department);
/*      add(ErpFieldTypeEnum.country);
        add(ErpFieldTypeEnum.province);
        add(ErpFieldTypeEnum.city);
        add(ErpFieldTypeEnum.district);
        add(ErpFieldTypeEnum.category);
        add(ErpFieldTypeEnum.town);*/
    }};

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId=commonArg.getTenantId();
        FieldMappingsArg arg = JsonUtil.fromJson(commonArg.getParams(), FieldMappingsArg.class);
        if (arg == null || arg.getDataType()==null ||arg.getDataCenterId()==null|| (StringUtils.isEmpty(arg.getFsDataId()) && StringUtils.isEmpty(arg.getErpDataId()))) {
            log.info("executeLogic params error commonArg={} arg={}", commonArg, arg);
            return Result.newSystemError(I18NStringEnum.s28);
        }

        if (!supportUpdateFieldType.contains(arg.getDataType())){
            return Result.newSystemError(I18NStringEnum.s31);
        }

        List<ErpFieldDataMappingEntity> mappingEntities =
          erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                                .listNoSearch(tenantId, arg.getDataCenterId(), arg.getDataType(), arg.getFsDataId(),null);

        if (CollectionUtils.isEmpty(mappingEntities)){
            return Result.newSystemError(I18NStringEnum.s32);
        }

        ErpFieldDataMappingEntity fieldDataMappingEntity = mappingEntities.get(0);
        fieldDataMappingEntity.setErpDataId(arg.getErpDataId());
        erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(fieldDataMappingEntity);
        return Result.newSuccess();
    }

}