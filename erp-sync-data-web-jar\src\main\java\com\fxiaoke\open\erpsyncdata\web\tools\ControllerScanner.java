package com.fxiaoke.open.erpsyncdata.web.tools;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 15:19:53
 * 扫描controller类，生成csv文件
 */
public class ControllerScanner {
    public static void main(String[] args) throws Exception {
        getControllerClasses();
    }

    public static void getControllerClasses() throws Exception {
        String csvFilePath = "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-sync-data-web/src/test/java/com/fxiaoke/output.csv"; // 指定要输出的CSV文件路径
        List<String> data = new ArrayList<>();

        List<String> errors = Lists.newArrayList();
        String packageName = "com.fxiaoke.open.erpsyncdata.web"; // 指定包名
        List<Class<?>> controllerClasses = getControllerClasses(packageName);
        for (Class<?> clazz : controllerClasses) {
            String url = getUrl(clazz);
            if (url.startsWith("cep") || url.startsWith("erp/syncdata/superadmin") || url.startsWith("/erp/syncdata/superadmin") || url.startsWith("/swagger")) {
                continue;
            }
            Method[] methods;
            try {
                methods = clazz.getDeclaredMethods();
            } catch (Throwable e) {
                errors.add(clazz.getSimpleName() + "," + e.getMessage());
                continue;
            }
            for (Method method : methods) {
                ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
                String methodUrl = getUrl(method);
                if (StringUtils.isBlank(methodUrl)) {
                    continue;
                }
                if (apiOperation != null) { // 如果有ApiOperation注解，则输出注解信息和URL地址
                    data.add(clazz.getSimpleName() + "." + method.getName() + "," + url + "," + methodUrl + "," + apiOperation.value());
                } else {
                    data.add(clazz.getSimpleName() + "." + method.getName() + "," + url + "," + methodUrl + ",");
                }
            }
        }


        errors.forEach(System.out::println);


        try (BufferedWriter writer = new BufferedWriter(new FileWriter(csvFilePath))) {
            writer.write("方法,根url,url,含义\n");
            for (String row : data) {
                writer.write(row + "\n");
            }
            System.out.println("CSV file has been generated successfully!");
        } catch (IOException e) {
            System.err.println("Failed to write CSV file: " + e.getMessage());
        }
    }

    /**
     * 获取指定包下的所有Controller类
     */
    public static List<Class<?>> getControllerClasses(String packageName) throws Exception {
        List<Class<?>> controllerClasses = new ArrayList<>();
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        String path = packageName.replace(".", "/");
        URL resource = classLoader.getResource(path);
        // 创建一个ClassLoader，用于加载class文件
        File directory = new File(resource.toURI());
        final String s = classLoader.getResource("").toURI().getPath();

        Collection<File> files = FileUtils.listFiles(directory, new String[]{"class"}, true);
        for (File file : files) {
            String fileName = file.getAbsolutePath();
            String className = fileName.substring(s.length(), fileName.length() - 6).replace("/", ".");
            Class<?> clazz = classLoader.loadClass(className);
            if (isControllerClass(clazz)) {
                controllerClasses.add(clazz);
            }
        }
        return controllerClasses;
    }

    /**
     * 判断是否为Controller类
     */
    public static boolean isControllerClass(Class<?> clazz) {
        return clazz.isAnnotationPresent(RestController.class) || clazz.isAnnotationPresent(Controller.class);
    }

    /**
     * 获取类上的URL地址
     */
    public static String getUrl(Class<?> clazz) {
        RequestMapping requestMapping = clazz.getAnnotation(RequestMapping.class);
        PostMapping postMapping = clazz.getAnnotation(PostMapping.class);
        if (requestMapping != null) {
            return requestMapping.value()[0];
        } else if (postMapping != null) {
            return postMapping.value()[0];
        } else {
            return "";
        }
    }

    /**
     * 获取方法上的URL地址
     */
    public static String getUrl(Method method) {
        RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
        PostMapping postMapping = method.getAnnotation(PostMapping.class);
        if (requestMapping != null) {
            return requestMapping.value()[0];
        } else if (postMapping != null) {
            return postMapping.value()[0];
        } else {
            return "";
        }
    }
}
