<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.DDLDao">
    <select id="listIndexes" resultType="cn.hutool.core.lang.Dict">
        select indexname, indexdef
        from pg_indexes
        where tablename = #{tableName}
    </select>

    <insert id="createIndexes">
        create index if not exists CONCURRENTLY on #{table} (#{index}) where (#{where});
    </insert>

    <update id="dropIndexes">
        drop index if exists #{index}
    </update>
</mapper>