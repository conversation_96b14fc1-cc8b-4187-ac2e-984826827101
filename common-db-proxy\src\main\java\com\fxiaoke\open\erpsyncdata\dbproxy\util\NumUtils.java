package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.util.IdUtil;
import com.fxiaoke.api.IdGenerator;

/**
 * <AUTHOR>
 * @date 2019/11/6
 **/
public class NumUtils {

    /**
     * 初始化 62 进制数据，索引位置代表字符的数值，比如 a代表10，Z代表61等
     */
    public static String chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final int scale = 62;


    /**
     * 过长的long类型转换成32进制字符串，缩短长度
     *
     * @param num
     * @return
     */
    public static String longTo32Str(Long num) {
        return Long.toUnsignedString(num, 32);
    }

    public static long recLongFrom32Str(String str) {
        return Long.valueOf(str, 32);
    }

    /**
     * 将数字转为62进制
     *
     * @param num    Long 型数字
     * @return 62进制字符串
     */
    public static String longTo62Str(long num) {
        StringBuilder sb = new StringBuilder();
        int remainder = 0;

        while (num > scale - 1) {
            /**
             * 对 scale 进行求余，然后将余数追加至 sb 中，由于是从末位开始追加的，因此最后需要反转（reverse）字符串
             */
            remainder = Long.valueOf(num % scale).intValue();
            sb.append(chars.charAt(remainder));
            num = num / scale;
        }
        sb.append(chars.charAt(Long.valueOf(num).intValue()));
        String value = sb.reverse().toString();
        return value;
    }

    /**
     * 62进制字符串转为数字
     *
     * @param str 编码后的62进制字符串
     * @return 解码后的 10 进制字符串
     */
    public static long recLongFrom62Str(String str) {
        long num = 0;
        int index = 0;
        for (int i = 0; i < str.length(); i++) {
            //查找字符的索引位置
            index = chars.indexOf(str.charAt(i));
            // 索引位置代表字符的数值
            num += (long) (index * (Math.pow(scale, str.length() - i - 1)));
        }

        return num;
    }

    public static void main(String[] args) {
        System.out.println(IdGenerator.get());
        System.out.println(Long.toUnsignedString(786905336470175744L, 32));
        System.out.println(Long.toUnsignedString(786905336470175744L, 48));
        System.out.println(longTo32Str(786905336470175744L));
        System.out.println(recLongFrom32Str("lqt67fu2k000"));
        System.out.println(longTo62Str(786905336470175744L));
        System.out.println(recLongFrom62Str("W829JjhpCw"));
    }

}
