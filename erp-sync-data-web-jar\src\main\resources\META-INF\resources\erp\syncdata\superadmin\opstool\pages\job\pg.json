{"type": "page", "title": "PG管理", "body": [{"type": "tabs", "tabs": [{"title": "索引信息", "body": [{"type": "collapse", "key": "1", "active": true, "header": "筛选条件", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:2d6609d18d84", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}, "required": true}, {"type": "input-text", "label": "表名或表名前缀", "name": "tableNamePrefix", "id": "u:ce3608a27b4a", "value": "sync_data_mappings", "required": true, "validateOnChange": true}, {"type": "input-text", "label": "tenantId", "name": "tenantId", "id": "u:5fedced7eb8a", "size": "sm", "description": ""}, {"type": "textarea", "label": "存在索引", "name": "indexDef", "id": "u:c47fb5606b51", "minRows": 2, "maxRows": 20, "description": "", "hidden": false, "size": "full", "labelRemark": {"icon": "fa fa-question-circle", "trigger": ["click"], "className": "Remark--warning", "placement": "top", "content": "使用右匹配查询输入索引创建语句右边部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false;", "title": "说明", "rootClose": true}, "placeholder": "筛选存在某索引的表"}, {"type": "textarea", "label": "缺少索引", "name": "missIndexDef", "id": "u:084131a53df8", "minRows": 2, "maxRows": 20, "description": "", "hidden": false, "size": "full", "labelRemark": {"icon": "fa fa-question-circle", "trigger": ["click"], "className": "Remark--warning", "placement": "top", "content": "使用右匹配查询输入索引创建语句右边部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false;", "title": "说明", "rootClose": true}, "placeholder": "筛选缺少某索引的表"}], "mode": "horizontal", "id": "u:66d234542aa6", "reload": "", "canAccessSuperData": false, "target": "pgIndexCrud", "debug": false, "submitText": "获取索引列表", "persistData": true, "wrapWithPanel": true, "actions": [{"type": "submit", "label": "获取索引列表", "primary": true, "id": "u:99e4d99efd8e"}], "feat": "Insert", "dsType": "api", "labelAlign": "left"}], "id": "u:58d192519c68", "hidden": false, "collapsable": true}, {"type": "crud", "syncLocation": true, "api": {"method": "get", "url": "${baseUrl}/pg/listIndexInfos", "messages": {}, "requestAdaptor": "", "adaptor": "", "replaceData": false, "data": {"&": "$$", "dbName": "${dbName}"}, "dataType": "json"}, "columns": [{"label": "dbN<PERSON>", "type": "text", "name": "dbN<PERSON>", "id": "u:cae909b89050"}, {"label": "tenantId", "type": "text", "name": "tenantId", "id": "u:d86786dbc099"}, {"label": "tableName", "type": "text", "name": "tableName", "id": "u:95d59547a3d1"}, {"label": "indexName", "type": "text", "name": "indexName", "id": "u:784ae5627ce3"}, {"label": "indexDef", "type": "text", "name": "indexDef", "id": "u:19bc3e06c7af"}, {"label": "indexNamePattern", "type": "text", "name": "indexNamePattern", "id": "u:8be7942378e6", "placeholder": "-", "sortable": false}, {"label": "existConnectInfo", "type": "text", "name": "existConnectInfo", "id": "u:ccc53e9e4e9f", "placeholder": "-", "sortable": false}, {"type": "operation", "label": "操作", "id": "u:a6050922be41", "buttons": [{"type": "button", "label": "删除索引", "onEvent": {"click": {"actions": [{"outputVar": "responseResult", "actionType": "ajax", "args": {"options": {}, "api": {"url": "${baseUrl}/pg/dropIndexByName", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"&": "$$"}}}}, {"componentId": "u:41ac64fe02ab", "actionType": "reload", "args": {"resetPage": true}, "dataMergeMode": "merge"}]}}, "id": "u:4c41e1ea2ca0", "level": "danger", "hiddenOn": "${!indexName}", "confirmText": "确认删除？"}]}], "bulkActions": [], "itemActions": [], "filterSettingSource": ["dbN<PERSON>", "tenantId", "tableName", "indexName", "indexDef", "indexNamePattern", "existConnectInfo"], "features": [], "id": "u:41ac64fe02ab", "messages": {}, "headerToolbar": [{"type": "bulk-actions"}], "filter": null, "perPageAvailable": [10, 50, 100], "initFetch": false, "initFetchOn": "dbName!=0", "autoFillHeight": false, "name": "pgIndexCrud", "perPage": 20, "footerToolbar": [{"type": "statistics"}, {"type": "pagination"}, {"type": "switch-per-page", "tpl": "内容", "wrapperComponent": "", "id": "u:6571f85d9f1c"}]}], "id": "u:4974310aad30"}, {"title": "索引操作", "body": [{"type": "collapse-group", "activeKey": ["1"], "body": [{"type": "collapse", "key": "1", "active": true, "header": "创建索引", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:4c0e8d587b25", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "input-text", "label": "表名前缀", "name": "tableNamePrefix", "id": "u:31a0b7f70cc5", "value": "sync_data_mappings", "required": true, "validateOnChange": true, "readOnly": true, "disabled": true}, {"type": "input-text", "label": "索引名称", "name": "indexNamePattern", "id": "u:171b8db1e7d9", "description": "必须包含{tenantId},执行时会替换为真实tenantId\n。删除索引不使用，"}, {"type": "textarea", "label": "限定企业，逗号分隔", "name": "tenantIdStr", "id": "u:2d1d59b87ae9", "minRows": 3, "maxRows": 20}, {"type": "textarea", "label": "索引语句", "name": "indexDef", "id": "u:247b358d250e", "minRows": 3, "maxRows": 20, "value": "", "description": "索引表名后的部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false"}, {"type": "checkbox", "name": "uniqueIndex", "label": "是否唯一索引(默认否)", "id": "u:861f1ff5f4d7"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:752320a29765", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "mode": "horizontal", "id": "u:604d20b197ce", "reload": "", "canAccessSuperData": false, "target": "", "debug": false, "submitText": "创建索引", "persistData": true, "wrapWithPanel": true, "api": {"url": "${baseUrl}/pg/createIndexes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}, "actions": [{"type": "submit", "label": "创建索引", "primary": true, "id": "u:7a604766067d"}], "feat": "Insert"}], "id": "u:c9d892e071ce", "hidden": false, "collapsable": true, "name": "text"}], "id": "u:a92537a35f3d"}, {"type": "collapse-group", "activeKey": ["1"], "body": [{"type": "collapse", "key": "1", "active": true, "header": "删除索引", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:9507b2a6caad", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "input-text", "label": "表名前缀", "name": "tableNamePrefix", "id": "u:9f22fb43e8d0", "value": "sync_data_mappings", "required": true, "validateOnChange": true, "readOnly": true, "disabled": true}, {"type": "textarea", "label": "限定企业，逗号分隔", "name": "tenantIdStr", "id": "u:0fb0faa3f99c", "minRows": 3, "maxRows": 20}, {"type": "textarea", "label": "索引语句", "name": "indexDef", "id": "u:fbc60bb1fe73", "minRows": 3, "maxRows": 20, "value": "", "description": "索引表名后的部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:46895f6d5a2e", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "mode": "horizontal", "id": "u:25fe01dced46", "reload": "", "canAccessSuperData": false, "target": "", "debug": false, "submitText": "删除索引", "persistData": true, "wrapWithPanel": true, "api": {"url": "${baseUrl}/pg/dropIndexes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}, "actions": [{"type": "submit", "label": "删除索引", "primary": true, "id": "u:65e8f1cd60f8"}], "feat": "Insert"}], "id": "u:97a59cdb0b5a", "hidden": false, "collapsable": true, "name": "text"}], "id": "u:3d7e054d62af"}, {"type": "collapse-group", "activeKey": ["1"], "body": [{"type": "collapse", "key": "1", "active": true, "header": "比较代码创建缺少索引", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:4c07b25e8d58", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "textarea", "label": "限定企业，逗号分隔", "name": "tenantIdStr", "id": "u:2d17ae9d59b8", "minRows": 3, "maxRows": 20}, {"type": "checkbox", "name": "deleteIndex", "label": "是否删除多余索引(默认否)", "id": "u:061f79288488"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:75229765320a", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "mode": "horizontal", "id": "u:604197ced20b", "reload": "", "canAccessSuperData": false, "target": "", "debug": false, "submitText": "比较代码创建缺少索引", "persistData": true, "wrapWithPanel": true, "api": {"url": "${baseUrl}/pg/compareIndexes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}, "actions": [{"type": "submit", "label": "比较代码创建缺少索引", "primary": true, "id": "u:620604d3dc80"}], "feat": "Insert"}], "id": "u:c9ced892e071", "hidden": false, "collapsable": true, "name": "text"}], "id": "u:a935f3d2537a"}], "id": "u:e8df2268cbad"}, {"title": "迁移数据", "body": [{"type": "form", "title": "表单", "body": [{"type": "input-text", "label": "迁移企业，分号隔开", "name": "sourceTenantId", "id": "u:5a60c97f8819"}, {"type": "input-text", "label": "指向目标数据库", "name": "destDataBaseTenantId", "id": "u:5a60c97f9918"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:46895f6d5bbe", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "api": {"url": "${baseUrl}/pg/brushTableData", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}, "id": "u:ef7db9efbaac", "submitText": "执行", "actions": [{"type": "submit", "label": "执行", "primary": true, "id": "u:c38eb1c7f973"}], "feat": "Insert"}], "id": "u:7446761cdee8"}, {"title": "统计数据", "body": [{"type": "link", "value": "http://www.baidu.com/", "id": "u:52bc034f6d98", "href": "https://grafana.firstshare.cn/goto/Z_K2rJDHg?orgId=1", "body": "112 集成平台-表统计", "className": "m"}, {"type": "link", "value": "http://www.baidu.com/", "id": "u:c717315e66cc", "href": "https://grafana.foneshare.cn/goto/zXSU-JvNg?orgId=1", "body": "线上 集成平台-表统计", "className": "m"}, {"type": "divider", "id": "u:113e4ce576d8", "lineStyle": "solid", "direction": "horizontal", "rotate": 0}, {"type": "button", "label": "获取数据库数据量分布预估值", "onEvent": {"click": {"actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "${baseUrl}/pg/getDbDistributeFromPgClass", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {"success": "已获取，请到grafana面板查看数据"}, "silent": false}}]}}, "id": "u:78efb1efa082", "tooltip": "从CONFIG_ROUTE_TENANT读取db数据", "level": "primary"}, {"type": "divider", "id": "u:57683a62201d", "lineStyle": "solid", "direction": "horizontal", "rotate": 0}, {"id": "u:0cbb9d2d4d58", "type": "form", "title": "计算单表数据分布", "mode": "flex", "labelAlign": "top", "dsType": "api", "feat": "Insert", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:6371a0bc814b", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}, "required": true}, {"type": "input-text", "label": "tableName", "name": "tableName", "id": "u:ad98e1850ace", "value": "sync_data_mappings_", "required": true, "validateOnChange": true}, {"type": "input-text", "label": "timeField", "name": "timeField", "id": "u:c27ad4897e0d", "value": "update_time"}, {"type": "textarea", "label": "groupFieldStr", "name": "groupFieldStr", "id": "u:c2868b31c59d", "minRows": 3, "maxRows": 20, "value": "tenant_id,source_object_api_name,dest_object_api_name"}, {"type": "input-number", "label": "interval（单位天）", "name": "intervalDay", "keyboard": true, "id": "u:fc9645716215", "step": 1, "value": 30, "min": 1, "precision": ""}], "api": {"url": "${baseUrl}/pg/calTableDistribute", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}}, "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:0cbb9d2d4d58"}]}}, "level": "primary", "id": "u:d3c87e17ddf3"}], "resetAfterSubmit": true}], "id": "u:234c630b50d2"}, {"title": "批量操作", "body": [{"id": "u:9c3f03aba9ac", "type": "form", "title": "根据条件删除中间表数据", "mode": "horizontal", "labelAlign": "left", "dsType": "api", "feat": "Insert", "body": [{"type": "alert", "body": [{"type": "tpl", "tpl": "高危操作：请先统计数据观察数据量！！！", "wrapperComponent": "", "inline": false, "id": "u:fe638878f5c3"}], "level": "info", "id": "u:0e0f88660fb5"}, {"name": "tenantId", "label": "tenantId", "type": "input-text", "id": "u:5a2fc8fcb327"}, {"name": "sourceObjApiName", "label": "sourceObjApiName", "type": "input-text", "id": "u:13c668139aac"}, {"name": "destObjApiName", "label": "destObjApiName", "type": "input-text", "id": "u:9c94099e3693"}, {"type": "input-datetime-range", "label": "日期范围", "name": "minTime", "id": "u:cf47b492a7d4", "displayFormat": "YYYY-MM-DD HH:mm:ss", "placeholder": "请选择日期时间范围", "valueFormat": "x", "minDate": "", "maxDate": "", "value": "", "shortcuts": [], "utc": false, "extraName": "maxTime"}], "api": {"url": "${baseUrl}/syncDataMapping/deleteMappingByTimeBetween", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}}, "actions": [{"type": "button", "label": "重置", "onEvent": {"click": {"actions": [{"actionType": "reset", "componentId": "u:9c3f03aba9ac"}]}}, "level": "default", "id": "u:206c7f1674e4"}, {"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:9c3f03aba9ac"}]}}, "level": "primary", "id": "u:f5642996af35"}], "resetAfterSubmit": true, "affixFooter": false, "autoFocus": false, "preventEnterSubmit": true}], "id": "u:3b417f779cdf"}], "id": "u:e24a31791685"}], "id": "u:18fe38ed7064", "asideResizor": false, "pullRefresh": {"disabled": true}, "data": {"baseUrl": ".."}, "regions": ["body", "toolbar", "header"], "initApi": {"url": "${baseUrl}/pg/preInitData", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "dataType": "json"}, "style": {}, "name": "pgIndexManager", "themeCss": {"baseControlClassName": {"boxShadow:default": " 0px 0px 0px 0px transparent"}}}