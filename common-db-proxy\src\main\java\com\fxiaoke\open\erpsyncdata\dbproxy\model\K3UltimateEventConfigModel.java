package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.K3UltimateEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
public class K3UltimateEventConfigModel implements Serializable {
    /**
     * 事件类型，供前端使用
     */
    private K3UltimateEventTypeEnum eventType;
    /**
     * 云星空旗舰版事件编码
     */
    private String eventCode;
    /**
     * 事件名称，供前端使用，多语
     */
    private String eventName;
}
