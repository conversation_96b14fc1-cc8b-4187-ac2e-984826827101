package com.fxiaoke.open.erpsyncdata.main.service

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class AllModelDubboServiceImplTest extends Specification {

    AllModelDubboServiceImpl impl
    def tenantId = "123456"

    def setup() {
        def eventTriggerService = Mock(EventTriggerService) {
            batchSendEventData2DispatcherMq(*_) >> Result2.newSuccess()
            batchSendEventData2DispatcherMqByContext(*_) >> Result2.newSuccess()
        }
        def syncMainService = Mock(SyncMainService) {
            syncDataMain(*_) >> Result2.newSuccess()
        }
        def idFieldConvertManager = Mock(IdFieldConvertManager) {
            getRealObjApiName(*_) >> "test"
        }
        impl = new AllModelDubboServiceImpl(eventTriggerService: eventTriggerService,
                syncMainService: syncMainService, idFieldConvertManager: idFieldConvertManager)
    }

    def "test batchSendEventData2DispatcherMq"() {
        expect:
        impl.batchSendEventData2DispatcherMq(new BatchSendEventDataArg([]))
    }

    def "test batchSendEventData2DispatcherMqByContext"() {
        given:
        def event = new SyncDataContextEvent(cleanFields: ["test"], sourceData: new ObjectData())
        expect:
        impl.batchSendEventData2DispatcherMqByContext([event]).isSuccess()

    }

    def "test syncDataMain - #name"() {
        given:
        def e = new SyncDataContextEvent(tenantId: tenantId, syncLogId: "test", sourceData: data)
        expect:
        impl.syncDataMain(e).isSuccess()
        where:
        name    | data
        "正常"   | new ObjectData()
        "异常"   | null
    }
}
