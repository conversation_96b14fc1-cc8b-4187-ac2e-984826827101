package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@ToString
public class ErpHistoryTaskLog {

    @BsonId
    private ObjectId id;

    //企业id
    private String tenantId;

    //数据中心id
    private String dataCenterId;

    private String taskId;

    private String taskNum;

    public String taskName; //

    private String objApiName;

    private String realObjApiName;

    private String dataIds;

    private String filerString;

    private Long startTime;

    private Long lastQueryStartTime;

    private Long endTime;

    private Boolean needStop;

    private Long limit;

    private Long offset;

    private Integer taskStatus;

    private Long totalDataSize;

    private Long totalCostTime;

    private String remark;

    private String traceId;

    private Date createTime;

    private Date updateTime;

    private Date executeTime;

    private List<String> relatedPloyDetailId;

    public  ErpHistoryTaskLog convertTimeEntity(ErpHistoryDataTaskEntity erpHistoryDataTaskEntity){
        if(erpHistoryDataTaskEntity.getExecuteTime()!=null){
            this.setExecuteTime(DateUtil.date(erpHistoryDataTaskEntity.getExecuteTime()));
        }
        if(erpHistoryDataTaskEntity.getCreateTime()!=null){
            this.setCreateTime(DateUtil.date(erpHistoryDataTaskEntity.getCreateTime()));
        }
        if(erpHistoryDataTaskEntity.getUpdateTime()!=null){
            this.setUpdateTime(DateUtil.date(erpHistoryDataTaskEntity.getUpdateTime()));
        }

        return this;
    }


}
