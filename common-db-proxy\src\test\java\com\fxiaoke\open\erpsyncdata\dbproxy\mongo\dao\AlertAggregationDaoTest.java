package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AlertAggregationDao 单元测试
 * 
 * <AUTHOR>
 * @date 2024.01.01
 */
@ExtendWith(MockitoExtension.class)
class AlertAggregationDaoTest {

    @InjectMocks
    private AlertAggregationDao alertAggregationDao;

    @Mock
    private DatastoreExt store;

    @Mock
    private MongoDatabase mongoDatabase;

    @Mock
    private MongoCollection<AlertAggregationEntity> mongoCollection;

    @Mock
    private FindIterable<AlertAggregationEntity> findIterable;

    @Mock
    private MongoCursor<AlertAggregationEntity> mongoCursor;

    @Mock
    private UpdateResult updateResult;

    @Mock
    private DeleteResult deleteResult;

    private final String tenantId = "test-tenant-123";
    private final String dataCenterId = "test-dc-456";
    private final String ployDetailId = "test-ploy-789";
    private final AlertAggregationType alertAggregationType = AlertAggregationType.POLLING_ERP_ALERT;
    private final AlarmLevel alarmLevel = AlarmLevel.URGENT;

    @BeforeEach
    void setUp() {
        // Mock MongoDB collection chain
        when(store.getMongo()).thenReturn(mongoDatabase);
        when(mongoDatabase.withCodecRegistry(any())).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(eq("alert_aggregation"), eq(AlertAggregationEntity.class)))
                .thenReturn(mongoCollection);

        // Mock ConfigFactory for database name
        try (MockedStatic<ConfigFactory> configFactoryMock = mockStatic(ConfigFactory.class)) {
            ConfigFactory mockConfigFactory = mock(ConfigFactory.class);
            configFactoryMock.when(ConfigFactory::getInstance).thenReturn(mockConfigFactory);
            when(mockConfigFactory.getConfig("erp-sync-data-logmongo")).thenReturn(mock(com.github.autoconf.Config.class));
            when(mockConfigFactory.getConfig("erp-sync-data-logmongo").get("syncLogDbName", "fs-erp-sync-data"))
                    .thenReturn("test-db");
            
            // Initialize the DAO
            alertAggregationDao.init();
        }
    }

    private AlertAggregationEntity createTestEntity() {
        AlertAggregationEntity entity = new AlertAggregationEntity();
        entity.setId(new ObjectId());
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dataCenterId);
        entity.setPloyDetailId(ployDetailId);
        entity.setAlertAggregationType(alertAggregationType);
        entity.setAlarmLevel(alarmLevel);
        entity.setLastAlertTime(System.currentTimeMillis());
        entity.setCount(5);
        entity.setAlertRecover(false);
        entity.setErrCode("ERR001");
        entity.setErrMsg("Test error message");
        entity.setTraceMsg("test-trace-123");
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        return entity;
    }

    @Test
    void testGetDataListByTenantId() {
        // Arrange
        List<AlertAggregationEntity> expectedList = Arrays.asList(createTestEntity());
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(expectedList);

        // Act
        List<AlertAggregationEntity> result = alertAggregationDao.getDataList(tenantId, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(tenantId, result.get(0).getTenantId());
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetDataListByTenantIdAndDcId() {
        // Arrange
        List<AlertAggregationEntity> expectedList = Arrays.asList(createTestEntity());
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(expectedList);

        // Act
        List<AlertAggregationEntity> result = alertAggregationDao.getDataList(tenantId, dataCenterId, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(tenantId, result.get(0).getTenantId());
        assertEquals(dataCenterId, result.get(0).getDataCenterId());
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetDataListWithAllParameters() {
        // Arrange
        List<AlertAggregationEntity> expectedList = Arrays.asList(createTestEntity());
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(expectedList);

        // Act
        List<AlertAggregationEntity> result = alertAggregationDao.getDataList(
                tenantId, dataCenterId, ployDetailId, alertAggregationType, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ployDetailId, result.get(0).getPloyDetailId());
        assertEquals(alertAggregationType, result.get(0).getAlertAggregationType());
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetPollingErpAlertRecoverDataList() {
        // Arrange
        AlertAggregationEntity entity1 = createTestEntity();
        entity1.setAlertRecover(true);
        entity1.setAlertAggregationType(AlertAggregationType.POLLING_ERP_ALERT);
        
        AlertAggregationEntity entity2 = createTestEntity();
        entity2.setTenantId("tenant2");
        entity2.setDataCenterId("dc2");
        entity2.setAlertRecover(true);
        entity2.setAlertAggregationType(AlertAggregationType.POLLING_ERP_ALERT);

        List<AlertAggregationEntity> expectedList = Arrays.asList(entity1, entity2);
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(expectedList);

        // Act
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getPollingErpAlertRecoverDataList(10);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(tenantId + "-" + dataCenterId));
        assertTrue(result.containsKey("tenant2-dc2"));
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetAlertAggregationDataList() {
        // Arrange
        AlertAggregationEntity entity = createTestEntity();
        entity.setAlertRecover(false);
        entity.setLastAlertTime(System.currentTimeMillis() - 10000); // 10 seconds ago
        entity.setCount(3);

        List<AlertAggregationEntity> expectedList = Arrays.asList(entity);
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(expectedList);

        // Act
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getAlertAggregationDataList(
                alertAggregationType, 5000L, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(tenantId + "-" + dataCenterId));
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetAlertDataList() {
        // Arrange
        AlertAggregationEntity entity = createTestEntity();
        entity.setAlertRecover(false);
        entity.setCreateTime(new Date(System.currentTimeMillis() - 1000));

        List<AlertAggregationEntity> expectedList = Arrays.asList(entity);
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(expectedList);

        // Act
        long startTime = System.currentTimeMillis() - 5000;
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getAlertDataList(startTime, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(tenantId));
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetAlertDataMap() {
        // Arrange
        AlertAggregationEntity urgentEntity = createTestEntity();
        urgentEntity.setAlarmLevel(AlarmLevel.URGENT);
        
        AlertAggregationEntity importantEntity = createTestEntity();
        importantEntity.setAlarmLevel(AlarmLevel.IMPORTANT);
        
        AlertAggregationEntity generalEntity = createTestEntity();
        generalEntity.setAlarmLevel(AlarmLevel.GENERAL);

        List<AlertAggregationEntity> entityList = Arrays.asList(urgentEntity, importantEntity, generalEntity);

        // Act
        Map<AlarmLevel, List<AlertAggregationEntity>> result = alertAggregationDao.getAlertDataMap(entityList);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.containsKey(AlarmLevel.URGENT));
        assertTrue(result.containsKey(AlarmLevel.IMPORTANT));
        assertTrue(result.containsKey(AlarmLevel.GENERAL));
        assertEquals(1, result.get(AlarmLevel.URGENT).size());
        assertEquals(1, result.get(AlarmLevel.IMPORTANT).size());
        assertEquals(1, result.get(AlarmLevel.GENERAL).size());
    }

    @Test
    void testGetAlertDataMapWithEmptyList() {
        // Act
        Map<AlarmLevel, List<AlertAggregationEntity>> result = alertAggregationDao.getAlertDataMap(Collections.emptyList());

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAlertDataMapWithNullList() {
        // Act
        Map<AlarmLevel, List<AlertAggregationEntity>> result = alertAggregationDao.getAlertDataMap(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetTenantDcPloyDetailDataMap() {
        // Arrange
        AlertAggregationEntity entity1 = createTestEntity();
        entity1.setTenantId("tenant1");
        entity1.setDataCenterId("dc1");
        entity1.setPloyDetailId("ploy1");

        AlertAggregationEntity entity2 = createTestEntity();
        entity2.setTenantId("tenant1");
        entity2.setDataCenterId("dc1");
        entity2.setPloyDetailId("ploy2");

        AlertAggregationEntity entity3 = createTestEntity();
        entity3.setTenantId("tenant2");
        entity3.setDataCenterId("dc2");
        entity3.setPloyDetailId("ploy1");

        List<AlertAggregationEntity> entityList = Arrays.asList(entity1, entity2, entity3);

        // Act
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getTenantDcPloyDetailDataMap(entityList);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.containsKey("tenant1-dc1-ploy1"));
        assertTrue(result.containsKey("tenant1-dc1-ploy2"));
        assertTrue(result.containsKey("tenant2-dc2-ploy1"));
        assertEquals(1, result.get("tenant1-dc1-ploy1").size());
        assertEquals(1, result.get("tenant1-dc1-ploy2").size());
        assertEquals(1, result.get("tenant2-dc2-ploy1").size());
    }

    @Test
    void testGetTenantDcPloyDetailDataMapWithEmptyList() {
        // Act
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getTenantDcPloyDetailDataMap(Collections.emptyList());

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetTenantDcPloyDetailDataMapWithNullList() {
        // Act
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getTenantDcPloyDetailDataMap(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetTenantDcPloyDetailDataMapFromDatabase() {
        // Arrange
        AlertAggregationEntity entity1 = createTestEntity();
        entity1.setTenantId("tenant1");
        entity1.setDataCenterId("dc1");
        entity1.setPloyDetailId("ploy1");

        AlertAggregationEntity entity2 = createTestEntity();
        entity2.setTenantId("tenant2");
        entity2.setDataCenterId("dc2");
        entity2.setPloyDetailId("ploy2");

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.tryNext()).thenReturn(entity1, entity2);

        // Act
        Map<String, AlertAggregationEntity> result = alertAggregationDao.getTenantDcPloyDetailDataMap();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("tenant1-dc1-ploy1"));
        assertTrue(result.containsKey("tenant2-dc2-ploy2"));
        assertEquals(entity1, result.get("tenant1-dc1-ploy1"));
        assertEquals(entity2, result.get("tenant2-dc2-ploy2"));
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetData() {
        // Arrange
        AlertAggregationEntity expectedEntity = createTestEntity();
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(expectedEntity);

        // Act
        AlertAggregationEntity result = alertAggregationDao.getData(tenantId, dataCenterId, ployDetailId, alertAggregationType);

        // Assert
        assertNotNull(result);
        assertEquals(expectedEntity, result);
        verify(mongoCollection).find(any(Bson.class));
        verify(findIterable).first();
    }

    @Test
    void testGetDataWithEmptyPloyDetailId() {
        // Arrange
        AlertAggregationEntity expectedEntity = createTestEntity();
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(expectedEntity);

        // Act
        AlertAggregationEntity result = alertAggregationDao.getData(tenantId, dataCenterId, "", alertAggregationType);

        // Assert
        assertNotNull(result);
        assertEquals(expectedEntity, result);
        verify(mongoCollection).find(any(Bson.class));
        verify(findIterable).first();
    }

    @Test
    void testGetDataWithNullPloyDetailId() {
        // Arrange
        AlertAggregationEntity expectedEntity = createTestEntity();
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(expectedEntity);

        // Act
        AlertAggregationEntity result = alertAggregationDao.getData(tenantId, dataCenterId, null, alertAggregationType);

        // Assert
        assertNotNull(result);
        assertEquals(expectedEntity, result);
        verify(mongoCollection).find(any(Bson.class));
        verify(findIterable).first();
    }

    @Test
    void testInsert() {
        // Arrange
        Long lastAlertTime = System.currentTimeMillis();
        Integer count = 5;
        Boolean alertRecover = false;
        String errCode = "ERR001";
        String errMsg = "Test error";
        String traceMsg = "trace-123";

        // Act
        alertAggregationDao.insert(tenantId, dataCenterId, ployDetailId, alertAggregationType,
                alarmLevel, lastAlertTime, count, alertRecover, errCode, errMsg, traceMsg);

        // Assert
        verify(mongoCollection).insertOne(any(AlertAggregationEntity.class));
    }

    @Test
    void testInsertWithNullTraceMsg() {
        // Arrange
        Long lastAlertTime = System.currentTimeMillis();
        Integer count = 5;
        Boolean alertRecover = false;
        String errCode = "ERR001";
        String errMsg = "Test error";

        // Act
        alertAggregationDao.insert(tenantId, dataCenterId, ployDetailId, alertAggregationType,
                alarmLevel, lastAlertTime, count, alertRecover, errCode, errMsg, null);

        // Assert
        verify(mongoCollection).insertOne(any(AlertAggregationEntity.class));
    }

    @Test
    void testReplace() {
        // Arrange
        AlertAggregationEntity entity = createTestEntity();
        when(mongoCollection.replaceOne(any(Bson.class), any(AlertAggregationEntity.class))).thenReturn(updateResult);

        // Act
        alertAggregationDao.replace(entity);

        // Assert
        verify(mongoCollection).replaceOne(any(Bson.class), eq(entity));
    }

    @Test
    void testReplaceWithEmptyTraceMsg() {
        // Arrange
        AlertAggregationEntity entity = createTestEntity();
        entity.setTraceMsg("");
        when(mongoCollection.replaceOne(any(Bson.class), any(AlertAggregationEntity.class))).thenReturn(updateResult);

        // Act
        alertAggregationDao.replace(entity);

        // Assert
        verify(mongoCollection).replaceOne(any(Bson.class), eq(entity));
        assertNotNull(entity.getTraceMsg());
    }

    @Test
    void testBatchUpdateLastAlertTime() {
        // Arrange
        List<String> ployDetailIdList = Arrays.asList("ploy1", "ploy2", "ploy3");
        Long lastAlertTime = System.currentTimeMillis();
        when(mongoCollection.updateMany(any(Bson.class), any(Bson.class), any())).thenReturn(updateResult);
        when(updateResult.getModifiedCount()).thenReturn(3L);

        // Act
        long result = alertAggregationDao.batchUpdateLastAlertTime(tenantId, dataCenterId,
                ployDetailIdList, alertAggregationType, lastAlertTime);

        // Assert
        assertEquals(3L, result);
        verify(mongoCollection).updateMany(any(Bson.class), any(Bson.class), any());
        verify(updateResult).getModifiedCount();
    }

    @Test
    void testInsertOrUpdateWhenEntityExists() {
        // Arrange
        AlertAggregationEntity existingEntity = createTestEntity();
        Long lastAlertTime = System.currentTimeMillis();
        Integer count = 10;
        Boolean alertRecover = true;
        String errCode = "ERR002";
        String errMsg = "Updated error";
        String traceMsg = "updated-trace-456";

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(existingEntity);
        when(mongoCollection.replaceOne(any(Bson.class), any(AlertAggregationEntity.class))).thenReturn(updateResult);

        // Act
        alertAggregationDao.insertOrUpdate(tenantId, dataCenterId, ployDetailId, alertAggregationType,
                alarmLevel, lastAlertTime, count, alertRecover, errCode, errMsg, traceMsg);

        // Assert
        verify(mongoCollection).find(any(Bson.class));
        verify(findIterable).first();
        verify(mongoCollection).replaceOne(any(Bson.class), any(AlertAggregationEntity.class));
        verify(mongoCollection, never()).insertOne(any(AlertAggregationEntity.class));

        assertEquals(lastAlertTime, existingEntity.getLastAlertTime());
        assertEquals(count, existingEntity.getCount());
        assertEquals(alertRecover, existingEntity.getAlertRecover());
        assertEquals(errCode, existingEntity.getErrCode());
        assertEquals(errMsg, existingEntity.getErrMsg());
        assertEquals(traceMsg, existingEntity.getTraceMsg());
    }

    @Test
    void testInsertOrUpdateWhenEntityNotExists() {
        // Arrange
        Long lastAlertTime = System.currentTimeMillis();
        Integer count = 5;
        Boolean alertRecover = false;
        String errCode = "ERR001";
        String errMsg = "New error";
        String traceMsg = "new-trace-123";

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(null);

        // Act
        alertAggregationDao.insertOrUpdate(tenantId, dataCenterId, ployDetailId, alertAggregationType,
                alarmLevel, lastAlertTime, count, alertRecover, errCode, errMsg, traceMsg);

        // Assert
        verify(mongoCollection).find(any(Bson.class));
        verify(findIterable).first();
        verify(mongoCollection).insertOne(any(AlertAggregationEntity.class));
        verify(mongoCollection, never()).replaceOne(any(Bson.class), any(AlertAggregationEntity.class));
    }

    @Test
    void testInsertOrUpdateWithNullTraceMsg() {
        // Arrange
        AlertAggregationEntity existingEntity = createTestEntity();
        Long lastAlertTime = System.currentTimeMillis();
        Integer count = 10;
        Boolean alertRecover = true;
        String errCode = "ERR002";
        String errMsg = "Updated error";

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.first()).thenReturn(existingEntity);
        when(mongoCollection.replaceOne(any(Bson.class), any(AlertAggregationEntity.class))).thenReturn(updateResult);

        // Act
        alertAggregationDao.insertOrUpdate(tenantId, dataCenterId, ployDetailId, alertAggregationType,
                alarmLevel, lastAlertTime, count, alertRecover, errCode, errMsg, null);

        // Assert
        verify(mongoCollection).find(any(Bson.class));
        verify(mongoCollection).replaceOne(any(Bson.class), any(AlertAggregationEntity.class));
        assertNotNull(existingEntity.getTraceMsg());
    }

    @Test
    void testDelete() {
        // Arrange
        when(mongoCollection.deleteOne(any(Bson.class))).thenReturn(deleteResult);
        when(deleteResult.getDeletedCount()).thenReturn(1L);

        // Act
        long result = alertAggregationDao.delete(tenantId, dataCenterId, ployDetailId, alertAggregationType);

        // Assert
        assertEquals(1L, result);
        verify(mongoCollection).deleteOne(any(Bson.class));
        verify(deleteResult).getDeletedCount();
    }

    @Test
    void testDeleteManyByTenantId() {
        // Arrange
        when(mongoCollection.deleteMany(any(Bson.class))).thenReturn(deleteResult);
        when(deleteResult.getDeletedCount()).thenReturn(5L);

        // Act
        long result = alertAggregationDao.deleteMany(tenantId);

        // Assert
        assertEquals(5L, result);
        verify(mongoCollection).deleteMany(any(Bson.class));
        verify(deleteResult).getDeletedCount();
    }

    @Test
    void testDeleteManyByTenantIdAndDcIdAndType() {
        // Arrange
        when(mongoCollection.deleteMany(any(Bson.class))).thenReturn(deleteResult);
        when(deleteResult.getDeletedCount()).thenReturn(3L);

        // Act
        long result = alertAggregationDao.deleteMany(tenantId, dataCenterId, alertAggregationType);

        // Assert
        assertEquals(3L, result);
        verify(mongoCollection).deleteMany(any(Bson.class));
        verify(deleteResult).getDeletedCount();
    }

    @Test
    void testDeleteManyByTypeAndRecover() {
        // Arrange
        when(mongoCollection.deleteMany(any(Bson.class))).thenReturn(deleteResult);
        when(deleteResult.getDeletedCount()).thenReturn(2L);

        // Act
        long result = alertAggregationDao.deleteMany(alertAggregationType, true);

        // Assert
        assertEquals(2L, result);
        verify(mongoCollection).deleteMany(any(Bson.class));
        verify(deleteResult).getDeletedCount();
    }

    @Test
    void testDeleteAll() {
        // Arrange
        when(mongoCollection.deleteMany(any(Bson.class))).thenReturn(deleteResult);
        when(deleteResult.getDeletedCount()).thenReturn(10L);

        // Act
        long result = alertAggregationDao.deleteAll();

        // Assert
        assertEquals(10L, result);
        verify(mongoCollection).deleteMany(any(Bson.class));
        verify(deleteResult).getDeletedCount();
    }

    @Test
    void testGetDataListWithPagination() {
        // Arrange - 模拟分页查询，第一页返回满页数据，第二页返回部分数据
        List<AlertAggregationEntity> firstPageData = Arrays.asList(createTestEntity(), createTestEntity());
        List<AlertAggregationEntity> secondPageData = Arrays.asList(createTestEntity());

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(0)).thenReturn(findIterable);
        when(findIterable.skip(2)).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class)))
                .thenReturn(firstPageData)  // 第一页
                .thenReturn(secondPageData); // 第二页

        // Act
        List<AlertAggregationEntity> result = alertAggregationDao.getDataList(tenantId, 2);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size()); // 总共3条数据
        verify(mongoCollection, times(2)).find(any(Bson.class));
    }

    @Test
    void testGetDataListWithEmptyResult() {
        // Arrange
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.skip(anyInt())).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(Collections.emptyList());

        // Act
        List<AlertAggregationEntity> result = alertAggregationDao.getDataList(tenantId, 10);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoCollection).find(any(Bson.class));
    }

    @Test
    void testGetTenantDcPloyDetailDataMapWithDuplicateKeys() {
        // Arrange - 测试相同key的情况
        AlertAggregationEntity entity1 = createTestEntity();
        entity1.setTenantId("tenant1");
        entity1.setDataCenterId("dc1");
        entity1.setPloyDetailId("ploy1");
        entity1.setCount(1);

        AlertAggregationEntity entity2 = createTestEntity();
        entity2.setTenantId("tenant1");
        entity2.setDataCenterId("dc1");
        entity2.setPloyDetailId("ploy1");
        entity2.setCount(2);

        List<AlertAggregationEntity> entityList = Arrays.asList(entity1, entity2);

        // Act
        Map<String, List<AlertAggregationEntity>> result = alertAggregationDao.getTenantDcPloyDetailDataMap(entityList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("tenant1-dc1-ploy1"));
        assertEquals(2, result.get("tenant1-dc1-ploy1").size()); // 两个实体都应该在列表中
    }

    @Test
    void testGetAlertDataMapWithMultipleSameLevel() {
        // Arrange - 测试同一告警级别有多个实体的情况
        AlertAggregationEntity urgentEntity1 = createTestEntity();
        urgentEntity1.setAlarmLevel(AlarmLevel.URGENT);
        urgentEntity1.setCount(1);

        AlertAggregationEntity urgentEntity2 = createTestEntity();
        urgentEntity2.setAlarmLevel(AlarmLevel.URGENT);
        urgentEntity2.setCount(2);

        List<AlertAggregationEntity> entityList = Arrays.asList(urgentEntity1, urgentEntity2);

        // Act
        Map<AlarmLevel, List<AlertAggregationEntity>> result = alertAggregationDao.getAlertDataMap(entityList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(AlarmLevel.URGENT));
        assertEquals(2, result.get(AlarmLevel.URGENT).size());
    }

    @Test
    void testGetTenantDcPloyDetailDataMapFromDatabaseWithNullEntity() {
        // Arrange - 测试iterator返回null的情况
        AlertAggregationEntity entity = createTestEntity();

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.tryNext()).thenReturn(null, entity); // 第一次返回null，第二次返回实体

        // Act
        Map<String, AlertAggregationEntity> result = alertAggregationDao.getTenantDcPloyDetailDataMap();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size()); // 只有一个有效实体
        String expectedKey = entity.getTenantId() + "-" + entity.getDataCenterId() + "-" + entity.getPloyDetailId();
        assertTrue(result.containsKey(expectedKey));
        assertEquals(entity, result.get(expectedKey));
    }

    @Test
    void testGetTenantDcPloyDetailDataMapFromDatabaseWithDuplicateKeys() {
        // Arrange - 测试putIfAbsent的行为
        AlertAggregationEntity entity1 = createTestEntity();
        entity1.setTenantId("tenant1");
        entity1.setDataCenterId("dc1");
        entity1.setPloyDetailId("ploy1");
        entity1.setCount(1);

        AlertAggregationEntity entity2 = createTestEntity();
        entity2.setTenantId("tenant1");
        entity2.setDataCenterId("dc1");
        entity2.setPloyDetailId("ploy1");
        entity2.setCount(2);

        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.tryNext()).thenReturn(entity1, entity2);

        // Act
        Map<String, AlertAggregationEntity> result = alertAggregationDao.getTenantDcPloyDetailDataMap();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        String expectedKey = "tenant1-dc1-ploy1";
        assertTrue(result.containsKey(expectedKey));
        assertEquals(entity1, result.get(expectedKey)); // 应该是第一个实体，因为使用putIfAbsent
        assertEquals(1, result.get(expectedKey).getCount());
    }

    @Test
    void testBatchUpdateLastAlertTimeWithEmptyList() {
        // Arrange
        List<String> emptyList = Collections.emptyList();
        Long lastAlertTime = System.currentTimeMillis();
        when(mongoCollection.updateMany(any(Bson.class), any(Bson.class), any())).thenReturn(updateResult);
        when(updateResult.getModifiedCount()).thenReturn(0L);

        // Act
        long result = alertAggregationDao.batchUpdateLastAlertTime(tenantId, dataCenterId,
                emptyList, alertAggregationType, lastAlertTime);

        // Assert
        assertEquals(0L, result);
        verify(mongoCollection).updateMany(any(Bson.class), any(Bson.class), any());
    }

    @Test
    void testInsertWithEmptyTraceMsg() {
        // Arrange
        Long lastAlertTime = System.currentTimeMillis();
        Integer count = 5;
        Boolean alertRecover = false;
        String errCode = "ERR001";
        String errMsg = "Test error";
        String traceMsg = "";

        // Act
        alertAggregationDao.insert(tenantId, dataCenterId, ployDetailId, alertAggregationType,
                alarmLevel, lastAlertTime, count, alertRecover, errCode, errMsg, traceMsg);

        // Assert
        verify(mongoCollection).insertOne(any(AlertAggregationEntity.class));
    }
}
