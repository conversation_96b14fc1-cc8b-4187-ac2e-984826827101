package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.service;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TenantCleanupRecordDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TenantCleanupRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 企业账号资源清理服务
 * 提供企业清理记录的业务逻辑处理
 */
@Slf4j
@Service
public class TenantCleanupService {

    @Autowired
    private TenantCleanupRecordDao tenantCleanupRecordDao;

    /**
     * 添加企业清理记录
     */
    public void addTenantCleanupRecord(String tenantId, String tenantName, String tenantOwner,
                                       String tenantLevel, String cleanStatus,
                                       Date expireTime, Date licenseExpireTime, String remark) {
        if (StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("tenant is null");
        }

        TenantCleanupRecord record = TenantCleanupRecord.builder()
                .tenantId(tenantId)
                .tenantName(tenantName)
                .tenantOwner(tenantOwner)
                .tenantLevel(tenantLevel)
                .cleanStatus(cleanStatus)
                .expireTime(expireTime)
                .licenseExpireTime(licenseExpireTime)
                .isDeleted(false)
                .remark(remark)
                .build();

        tenantCleanupRecordDao.batchUpsert(Arrays.asList(record));
        log.info("添加企业清理记录成功: tenantId={}, tenantName={}, tenantLevel={}, cleanStatus={}",
                tenantId, tenantName, tenantLevel, cleanStatus);
    }

    /**
     * 添加企业清理记录（简化版本，保持向后兼容）
     */
    public void addTenantCleanupRecord(String tenantId, String tenantName, String tenantOwner, Date expireTime, String remark) {
        addTenantCleanupRecord(tenantId, tenantName, tenantOwner, null, null, expireTime, null, remark);
    }

    /**
     * 批量添加企业清理记录
     */
    public void batchAddTenantCleanupRecords(List<TenantCleanupRecord> records) {
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        // 设置默认值
        records.forEach(record -> {
            if (record.getIsDeleted() == null) {
                record.setIsDeleted(false);
            }
        });

        tenantCleanupRecordDao.batchUpsert(records);
        log.info("批量添加企业清理记录成功，数量: {}", records.size());
    }

    /**
     * 根据企业ID查询清理记录
     */
    public TenantCleanupRecord getTenantCleanupRecord(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        return tenantCleanupRecordDao.queryByTenantId(tenantId);
    }

    /**
     * 获取未删除的企业清理记录
     */
    public List<TenantCleanupRecord> getUndeletedRecords(int limit) {
        return tenantCleanupRecordDao.queryUndeleted(limit);
    }

    /**
     * 获取已过期且未删除的企业清理记录
     */
    public List<TenantCleanupRecord> getExpiredUndeletedRecords(int limit) {
        return tenantCleanupRecordDao.queryExpiredUndeleted(limit);
    }

    /**
     * 根据企业级别获取清理记录
     */
    public List<TenantCleanupRecord> getRecordsByTenantLevel(String tenantLevel, int limit) {
        return tenantCleanupRecordDao.queryByTenantLevel(tenantLevel, limit);
    }

    /**
     * 根据清理状态获取清理记录
     */
    public List<TenantCleanupRecord> getRecordsByCleanStatus(String cleanStatus, int limit) {
        return tenantCleanupRecordDao.queryByCleanStatus(cleanStatus, limit);
    }

    /**
     * 获取许可证已过期的企业清理记录
     */
    public List<TenantCleanupRecord> getLicenseExpiredRecords(int limit) {
        return tenantCleanupRecordDao.queryLicenseExpired(limit);
    }

    /**
     * 根据多个条件获取清理记录
     */
    public List<TenantCleanupRecord> getRecordsByMultipleConditions(String tenantLevel, String cleanStatus,
                                                                    Boolean isDeleted, int limit) {
        return tenantCleanupRecordDao.queryByMultipleConditions(tenantLevel, cleanStatus, isDeleted, limit);
    }

    /**
     * 标记企业为已删除
     */
    public void markTenantAsDeleted(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("tenant is null");
        }

        tenantCleanupRecordDao.markAsDeleted(tenantId);
        log.info("delete data: tenantId={}", tenantId);
    }

    /**
     * 更新企业清理记录（完整版本）
     */
    public void updateTenantCleanupRecord(String tenantId, String tenantName, String tenantOwner,
                                          String tenantLevel, String cleanStatus,
                                          Date expireTime, Date licenseExpireTime, String remark) {
        if (StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("tenant is null");
        }

        TenantCleanupRecord existingRecord = tenantCleanupRecordDao.queryByTenantId(tenantId);
        if (existingRecord == null) {
            // 如果记录不存在，创建新记录
            addTenantCleanupRecord(tenantId, tenantName, tenantOwner, tenantLevel, cleanStatus, expireTime, licenseExpireTime, remark);
            return;
        }

        // 更新现有记录
        TenantCleanupRecord updateRecord = TenantCleanupRecord.builder()
                .tenantId(tenantId)
                .tenantName(StringUtils.isNotBlank(tenantName) ? tenantName : existingRecord.getTenantName())
                .tenantOwner(StringUtils.isNotBlank(tenantOwner) ? tenantOwner : existingRecord.getTenantOwner())
                .tenantLevel(StringUtils.isNotBlank(tenantLevel) ? tenantLevel : existingRecord.getTenantLevel())
                .cleanStatus(StringUtils.isNotBlank(cleanStatus) ? cleanStatus : existingRecord.getCleanStatus())
                .expireTime(expireTime != null ? expireTime : existingRecord.getExpireTime())
                .licenseExpireTime(licenseExpireTime != null ? licenseExpireTime : existingRecord.getLicenseExpireTime())
                .isDeleted(existingRecord.getIsDeleted())
                .remark(StringUtils.isNotBlank(remark) ? remark : existingRecord.getRemark())
                .build();

        tenantCleanupRecordDao.batchUpsert(Arrays.asList(updateRecord));
        log.info("更新企业清理记录成功: tenantId={}, tenantName={}, tenantLevel={}, cleanStatus={}",
                tenantId, tenantName, tenantLevel, cleanStatus);
    }

    /**
     * 更新企业清理记录（简化版本，保持向后兼容）
     */
    public void updateTenantCleanupRecord(String tenantId, String tenantName, String tenantOwner, Date expireTime, String remark) {
        updateTenantCleanupRecord(tenantId, tenantName, tenantOwner, null, null, expireTime, null, remark);
    }

    /**
     * 检查企业是否已过期
     */
    public boolean isTenantExpired(String tenantId) {
        TenantCleanupRecord record = getTenantCleanupRecord(tenantId);
        if (record == null || record.getExpireTime() == null) {
            return false;
        }
        return record.getExpireTime().before(new Date());
    }

    /**
     * 检查企业是否已被标记为删除
     */
    public boolean isTenantDeleted(String tenantId) {
        TenantCleanupRecord record = getTenantCleanupRecord(tenantId);
        if (record == null) {
            return false;
        }
        return Boolean.TRUE.equals(record.getIsDeleted());
    }
}
