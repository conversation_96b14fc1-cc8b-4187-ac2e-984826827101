package com.fxiaoke.open.erpsyncdata.web.interceptor;

import cn.hutool.jwt.JWT;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-10-27
 */
@Slf4j
public class SuperAdminTokenUtil {

    public static final String erpDssToken = "ESAToken";

    public static boolean checkToken(String token) {
        return parseToken(token) != null;
    }

    public static UserVo parseToken(String token) {
        try {
            byte[] key = getKey();
            JWT jwt = JWT.of(token);
            boolean verify = jwt.setKey(key).verify();
            if (!verify) {
                return null;
            }
            Object ti = jwt.getPayload("ti");
            Object ea = jwt.getPayload("ea");
            Object userId = jwt.getPayload("userId");
            log.info("auth token,{},{},{}", ea, userId, ti);
            long tokenTime = Long.parseLong(ti.toString());
            long diff = System.currentTimeMillis() - tokenTime;
            boolean authPass = diff > 0 && diff < TimeUnit.MINUTES.toMillis(10L);
            if (!authPass){
                return null;
            }
            UserVo userVo = new UserVo();
            userVo.setEnterpriseAccount(String.valueOf(ea));
            userVo.setEmployeeId(Integer.parseInt(userId.toString()));
            return userVo;
        } catch (Exception e) {
            log.info("invalid token,{}", token);
            return null;
        }

    }

    public static String generateToken(UserVo superAdmin) {
        byte[] key = getKey();
        String token = JWT.create().setKey(key)
                .setPayload("ea", superAdmin.getEnterpriseAccount())
                .setPayload("userId", superAdmin.getEmployeeId())
                //增加10s的延迟
                .setPayload("ti", System.currentTimeMillis()-10000)
                .sign();
        return token;
    }

    private static byte[] getKey() {
        return ConfigCenter.SECRET_KEY.getBytes(StandardCharsets.UTF_8);
    }
}
