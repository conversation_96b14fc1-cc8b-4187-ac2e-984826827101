package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UserModel;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class EmployeeProviderServiceUtils {
    public static List<UserModel> batchGetEmployeeDto(String tenantId,
                                                      List<Integer> userIdList,
                                                      EmployeeProviderService employeeProviderService) {
        List<UserModel> userModelList = new ArrayList<>();

        BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
        arg.setEnterpriseId(Integer.valueOf(tenantId));
        arg.setRunStatus(RunStatus.ALL);
        arg.setEmployeeIds(userIdList);
        BatchGetEmployeeDtoResult result = employeeProviderService.batchGetEmployeeDto(arg);
        if (result != null && CollectionUtils.isNotEmpty(result.getEmployeeDtos())) {
            for (EmployeeDto employeeDto : result.getEmployeeDtos()) {
                UserModel userModel = new UserModel(employeeDto.getEmployeeId(),
                        employeeDto.getName());
                userModelList.add(userModel);
            }
        }

        return userModelList;
    }

    public static List<UserModel> batchGetEmployeeDto2(String tenantId,
                                                      List<String> userIdList,
                                                      EmployeeProviderService employeeProviderService) {
        List<Integer> list = userIdList.stream().map((item)->Integer.valueOf(item)).collect(Collectors.toList());
        return batchGetEmployeeDto(tenantId,list,employeeProviderService);
    }
}
