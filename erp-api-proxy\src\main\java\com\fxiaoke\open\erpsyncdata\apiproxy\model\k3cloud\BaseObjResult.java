package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseObjResult {
    @SerializedName("Id")
    @JsonProperty("Id")
    private String id;
    @SerializedName("Number")
    @JsonProperty("Number")
    private String number;
    @SerializedName("Name")
    @JsonProperty("Name")
    private String name;
}
