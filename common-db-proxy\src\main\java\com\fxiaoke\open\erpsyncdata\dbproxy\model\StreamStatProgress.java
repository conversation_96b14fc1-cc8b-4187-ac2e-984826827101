package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Data;

/**
 * 集成流统计进度
 */
@Data
public class StreamStatProgress {
    private String traceId;
    private int total;
    private Long beginTime;
    private Long lastUpdateTime;
    /**
     * 当任务异常时有值
     */
    private String exceptionMsg;
    /**
     * 已完成的数量
     */
    private int completedNum = 0;

    public boolean isAllCompleted() {
        return completedNum >= total;
    }

    public String toString(){
        return JacksonUtil.toJson(this);
    }

    public static StreamStatProgress parse(String json){
        return JacksonUtil.fromJson(json, StreamStatProgress.class);
    }
}
