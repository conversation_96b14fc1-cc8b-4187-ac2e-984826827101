package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.sharding

import com.github.autoconf.api.IConfig
import spock.lang.Specification

import java.nio.charset.StandardCharsets

/**
 * <AUTHOR> 
 * @date 2024/10/24 11:58:46
 */
class ShardPolicyTest extends Specification {
    ShardPolicy shardPolicy = new ShardPolicy()

    def setup() {
        shardPolicy.loadConfig(Mock(IConfig) {
            getInt("max") >> 2
            has("mongo.userPwd") >> true
            getContent() >> {
                return '''
[mongo-common]
mongo.userPwd=test_user:test_pwd
encrypt=true
max=2

[instance]
instance0=db1/data1
instance1=db2/data2

special1=db-special/relation1

[shard]
ds0=instance0
ds1=instance1

s0=special1

[special]
s0=123
'''.getBytes(StandardCharsets.UTF_8)
            }
        })
    }

    def "test get Uri"() {
        given:

        when:
        String result = shardPolicy.getUri(tenantId)

        then:
        result == url

        where:
        tenantId    || url
        '88521'     || '**************************************'
        '88520'     || '**************************************'
        '123'       || '*************************************************'

        'instance0' || '**************************************'
        'instance1' || '**************************************'
        'special1'  || '*************************************************'
    }

    def "test get All Instances Key"() {
        given:
        when:
        Set<String> result = shardPolicy.getAllInstancesKey()

        then:
        result == ['instance0', 'instance1', 'special1'] as Set<String>
    }
}
