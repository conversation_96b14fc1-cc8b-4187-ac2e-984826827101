<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:configName="erp-sync-data-all"/>

    <import resource="classpath:spring/common-db-proxy.xml"/>



<!--    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>-->
<!--    <dubbo:application name="erp-sync-data-test"/>-->
<!--    <dubbo:registry address="${dubbo.registry.address}"/>-->

    <!--redisson-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="erp-sync-data-all"/>
    </bean>

    <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>