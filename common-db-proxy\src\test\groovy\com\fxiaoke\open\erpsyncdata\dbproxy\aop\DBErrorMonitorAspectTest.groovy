package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.MongoStore
import org.aspectj.lang.ProceedingJoinPoint
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/6/3
 */
// 参考 TemplateJetCacheInvalidateAspectTest.groovy
class DBErrorMonitorAspectTest extends Specification {

    ErpObjectRelationshipDao pgDao
    ErpTempDataDao mongoDao
    def dataList = []

    def setup() {
        pgDao = Mock(ErpObjectRelationshipDao) {
            findBySplit(*_) >> {
                dataList.add(it[0])
                throw new Exception("pgDao find")
            }
        }
        mongoDao = Mock(ErpTempDataDao) {
            getErpObjData(*_) >> { args ->
                dataList.remove(args[0])
//                println(args)
                throw new Exception("mongoDao delete")
            }
            deleteAllErpTempData(*_) >> { arg ->
                dataList.remove(arg)
//                println(args)
                throw new Exception("mongoDao delete")
            }
        }
    }

    def "测试aop-InvokeErrorMonitorAspect-PG"() {
        given:
        def pgProxy = getAspect(pgDao)

        when:
        pgProxy.findBySplit("84801", "inventory_1fma457oo0")
        println(dataList)

        then:
        thrown(Exception)
        InvokeErrorMonitorAspect.MethodObjApiNameMap.get(ErpObjectRelationshipDao.class.getMethod("findBySplit", String.class, String.class)).apply((String[])["84801", "inventory_1fma457oo0"]) == "inventory_1fma457oo0"
        InvokeErrorMonitorAspect.MethodDataIdMap.get(ErpObjectRelationshipDao.class.getMethod("findBySplit", String.class, String.class)).apply((String[])["84801", "inventory_1fma457oo0"]) == null

    }

    def "测试aop-InvokeErrorMonitorAspect-Mongo"() {
        given:
        def mongoProxyService = getAspect(mongoDao)

        when:
        mongoProxyService.deleteAllErpTempData("84801")
        println(dataList)

        then:
        thrown(Exception)
        dataList.size() == 0

    }

    private <T> T getAspect(T o) {
        def aop = new DBErrorMonitorAspect()
        println("get " + o)

        // 使用AspectJProxyFactory来创建被增强的代理对象
        AspectJProxyFactory factory = new AspectJProxyFactory(o);
        factory.setProxyTargetClass(true) // 使用CGLib代理
        factory.addAspect(aop); // 添加切面到代理工厂中
        return factory.getProxy(); // 获取代理对象
    }
}
