package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class ViewResult implements RAMEstimable {
    @SerializedName("Result")
    @JsonProperty("Result")
    private Result result;

    @Override
    public long ramBytesUsed(int depth) {
        return RamUsageEstimateUtil.sizeOfObject(result,depth);
    }

    @Data
    public static class Result implements RAMEstimable {
        @SerializedName("ResponseStatus")
        @JsonProperty("ResponseStatus")
        private ResponseStatus responseStatus;

        @SerializedName("Result")
        @JsonProperty("Result")
        private K3Model result;

        @Override
        public long ramBytesUsed(int depth) {
            return RamUsageEstimateUtil.sizeOfObject(result,depth);
        }
    }
}
