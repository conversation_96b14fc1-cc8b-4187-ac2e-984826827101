<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- temp mongo-->
    <bean id="erpSyncDataMongoStore" name="erpSyncDataMongoStore"
          class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="erp-sync-data-all"/>
        <property name="sectionNames" value="mongo"/>
    </bean>


    <!-- log mongo-->
    <bean id="erpSyncDataLogMongoStore" name="erpSyncDataLogMongoStore"
          class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="erp-sync-data-logmongo"/>
        <property name="sectionNames" value="logmongo"/>
    </bean>
    <!--  分发框架的mongo-->
    <import resource="classpath*:spring/dispatcher-mongo.xml"/>


    <!--  分库dao层  -->
    <bean id="shardingSyncLogMongo" class="com.github.mongo.support.MongoDataStoreFactoryBean"
          p:configName="erp-sync-log-mongo"
          p:tenantPolicy-ref="syncLogPolicy"/>
    <bean id="syncLogPolicy" class="com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.sharding.ShardPolicy"
          p:configName="erp-sync-log-mongo"/>


    <!--  分层临时库  -->
    <bean id="shardingSyncTempMongo" class="com.github.mongo.support.MongoDataStoreFactoryBean"
          p:configName="erp-sync-temp-mongo">
        <property name="tenantPolicy">
            <bean class="com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.sharding.ShardPolicy"
                  p:configName="erp-sync-temp-mongo"/>
        </property>
    </bean>
</beans>