package com.fxiaoke.open.erpsyncdata.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.open.erpsyncdata.admin.arg.EnableStreamByTenant;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.IntegrationTaskEntity;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/25 09:53:49
 */
@Component
@Slf4j
public class IntegrationTaskService {

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;

    @Autowired
    private I18NStringManager i18NStringManager;

    public Pair<Boolean, String> doTask(IntegrationTaskEntity integrationTaskEntity) {
        try {
            final Result<Set<CheckAndUpdatePloyValidStatusDetailData>> setResult = adminSyncPloyDetailService.checkAndUpdatePloyStatus(integrationTaskEntity.getTenantId(), -10000, integrationTaskEntity.getDataCenterId(), integrationTaskEntity.getPolicyId(), SyncPloyDetailStatusEnum.ENABLE.getStatus(), true,null);
            if (!setResult.isSuccess()) {
                return Pair.of(false, setResult.getErrMsg());
            }

            final Set<CheckAndUpdatePloyValidStatusDetailData> data = setResult.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                final String message = data.stream()
                        .map(CheckAndUpdatePloyValidStatusDetailData::getErrorMessage)
                        .collect(Collectors.joining(";"));
                return Pair.of(false, message);
            }

            return Pair.of(true, null);
        } catch (ErpSyncDataException e) {
            return Pair.of(false, e.getErrMsg());
        } catch (Exception e) {
            log.warn("doTask failed,integrationTaskEntity:{}", JSON.toJSONString(integrationTaskEntity), e);
            return Pair.of(false, e.getMessage());
        }
    }

    public void sendResultMessage(String taskId, long success, List<IntegrationTaskEntity> allFailTask) {
//        final Map<String, Set<String>> errorMessageMap = allFailTask.stream()
//                .collect(Collectors.groupingBy(IntegrationTaskEntity::getTenantId,
//                        Collectors.mapping(IntegrationTaskEntity::getMessage, Collectors.toSet())));
//        final IntegrationTaskEntity task = integrationTaskDao.findOneByTaskId(taskId);
//        sendResultMessage(taskId, (int) success, allFailTask.size(), errorMessageMap, task.getCreateTime(), task.getNotifyTenantId(), task.getNotifyEmployeeIds());
    }

    private void sendResultMessage(String taskId, int success, int fail, Map<String, Set<String>> errorMessageMap, Long createTime, String notifyTenantId, List<Integer> notifyEmployeeIds) {
        String message = errorMessageMap.entrySet().stream()
                .map(entry -> createErrorMessage(entry.getKey(), entry.getValue()))
                .collect(Collectors.joining("\n\n"));
        message = i18NStringManager.getByEi(I18NStringEnum.s1147, notifyTenantId) + "\n" +
                i18NStringManager.getByEi(I18NStringEnum.s1148, notifyTenantId) + taskId + "\n" +
                i18NStringManager.getByEi(I18NStringEnum.s1137, notifyTenantId) + DateFormatUtils.format(createTime, "MM-dd hh:mm:ss") + "  " +
                i18NStringManager.getByEi(I18NStringEnum.s1138, notifyTenantId) + DateFormatUtils.format(System.currentTimeMillis(), "MM-dd hh:mm:ss") + "\n" +
                i18NStringManager.getByEi2(I18NStringEnum.s1149.getI18nKey(),
                        notifyTenantId,
                        String.format(I18NStringEnum.s1149.getI18nValue(), success, fail),
                        Lists.newArrayList(success + "", fail + "")) + "\n" + message;

        final String title = i18NStringManager.getByEi(I18NStringEnum.s1146,notifyTenantId);

        final String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(notifyTenantId));
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(notifyTenantId);
//        通知的企业没有
        sendTextNoticeArg.setDataCenterId("");
        sendTextNoticeArg.setEnterpriseAccount(ea);
        sendTextNoticeArg.setReceivers(notifyEmployeeIds);
        sendTextNoticeArg.setMsgTitle(title);
        sendTextNoticeArg.setMsg(message);
        Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,notifyTenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
        if (!voidResult.isSuccess()) {
            log.error("sendErpSyncDataAppNotice failed,arg:{} result:{}", sendTextNoticeArg, JSON.toJSONString(voidResult));
        }
    }

    private String createErrorMessage(String tenantId, Collection<String> message) {
        final String errMsg = message.stream().distinct().map(s -> "    " + s).collect(Collectors.joining("\n"));
        return tenantId + ":\n" + errMsg;
    }

    private void createIntegrationTask(IntegrationTaskEntity entity) {
        // 写mongo,发送mq
//        final String id = integrationTaskDao.create(entity);
//        dataCenterMqProducer.sendMqWithoutSelector(IntegrationTaskMqConsumer.INTEGRATION_TASK_TOPIC, entity.getType(), entity.getTenantId() + "_" + entity.getTaskId(), new IntegrationTaskEvent(id, entity.getTaskId()));
    }

//    private void sendIntegrationTaskNotify(String notifyTenantId, List<Integer> notifyEmployeeIds, String message) {
//        final String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(notifyTenantId));
//        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
//        sendTextNoticeArg.setTenantId(notifyTenantId);
//        sendTextNoticeArg.setDataCenterId("");
//        sendTextNoticeArg.setEnterpriseAccount(ea);
//        sendTextNoticeArg.setReceivers(notifyEmployeeIds);
//        sendTextNoticeArg.setMsgTitle("启用集成流通知");
//        sendTextNoticeArg.setMsg(message);
//        Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg, NotificationType.OTHER);
//        if (!voidResult.isSuccess()) {
//            log.error("sendErpSyncDataAppNotice failed,arg:{} result:{}", sendTextNoticeArg, JSON.toJSONString(voidResult));
//        }
//    }

//    private void saveIntegrationTaskAndNotify(EnableStreamByTenant.Arg arg, List<String> tenantIds, String taskId) {
//        //        key:ei value:errMsg
//        Map<String, String> errorMap = Maps.newHashMap();
////                生成task任务
//        long count = tenantIds.parallelStream()
//                .map(tenantId -> {
//                    final Result<List<SyncPloyDetailResult>> listResult = adminSyncPloyDetailService.listByTenantId(tenantId);
//                    if (!listResult.isSuccess()) {
//                        errorMap.put(tenantId, listResult.getErrMsg());
//                        return null;
//                    }
//                    return listResult.getData();
//                }).filter(CollectionUtils::isNotEmpty)
//                .flatMap(List::stream)
//                .filter(syncPloyDetailResult -> Objects.equals(syncPloyDetailResult.getStatus(), 2))
//                .peek(syncPloyDetailResult -> createIntegrationTask(convert2IntegrationTaskEntity(syncPloyDetailResult, taskId, arg.getNotifyTenantId(), arg.getNotifyEmployeeIds())))
//                .count();
//
//        String errorMessage = errorMap.entrySet().stream().limit(10).map(entry -> "  " + entry.getKey() + ":" + entry.getValue()).collect(Collectors.joining("\n"));
//        if (StringUtils.isNotBlank(errorMessage)) {
//            errorMessage = "\n错误信息:\n" + errorMessage;
//        }
//        if (count <= 0) {
//            final String resultMsg = "没有可启用的集成流" + errorMessage;
//            sendIntegrationTaskNotify(arg.getNotifyTenantId(), arg.getNotifyEmployeeIds(), "启用集成流失败," + resultMsg);
//            return;
//        }
//
//        final String message = "共有" + tenantIds.size() + "个企业需要同步,taskId:" + taskId + errorMessage;
//        sendIntegrationTaskNotify(arg.getNotifyTenantId(), arg.getNotifyEmployeeIds(), message);
//    }

    @NotNull
    private static IntegrationTaskEntity convert2IntegrationTaskEntity(SyncPloyDetailResult syncPloyDetailResult, String taskId, String notifyTenantId, List<Integer> notifyEmployeeIds) {
        final String ployTenantId = syncPloyDetailResult.getPloyTenantId();
        final String ployId = syncPloyDetailResult.getId();
        final String erpDcId = Objects.equals(syncPloyDetailResult.getSourceTenantType(), TenantType.ERP) ?
                syncPloyDetailResult.getSourceDataCenterId() : syncPloyDetailResult.getDestDataCenterId();

        final IntegrationTaskEntity integrationTaskEntity = new IntegrationTaskEntity();
        integrationTaskEntity.setTaskId(taskId);
        integrationTaskEntity.setTenantId(ployTenantId);
        integrationTaskEntity.setDataCenterId(erpDcId);
        integrationTaskEntity.setPolicyId(ployId);
        integrationTaskEntity.setType(IntegrationTaskEntity.Type.enableIntegration.name());
        integrationTaskEntity.setStatus(0);
        integrationTaskEntity.setCreateTime(System.currentTimeMillis());
        integrationTaskEntity.setUpdateTime(System.currentTimeMillis());
        integrationTaskEntity.setNotifyTenantId(notifyTenantId);
        integrationTaskEntity.setNotifyEmployeeIds(notifyEmployeeIds);
        return integrationTaskEntity;
    }

    public void enableStreamByTenant(EnableStreamByTenant.Arg arg, List<String> tenantIds, String taskId) {
        final long startTime = System.currentTimeMillis();
        final Map<String, Set<String>> errorMessageMap = new HashMap<>();
        AtomicInteger success = new AtomicInteger();
        AtomicInteger fail = new AtomicInteger();
//        内部方法耗时较长,且有较多io,不要使用parallelStream,防止将线程占了导致其他任务无法执行
        final ThreadPoolExecutor threadPoolExecutor = DynamicExecutors.newThreadPool(0, 1000, 10_000, new ThreadFactoryBuilder().setNameFormat("enableStreamByTenant-%d").build());

        try {
            final List<Future<List<Future<?>>>> futures = tenantIds.stream()
                    .map(ei -> threadPoolExecutor.submit((Callable<List<Future<?>>>) () -> {
                        final Result<List<SyncPloyDetailResult>> listResult = adminSyncPloyDetailService.listByTenantId(ei);
                        if (!listResult.isSuccess()) {
                            errorMessageMap.put(ei, Sets.newHashSet(listResult.getErrMsg()));
                            return new ArrayList<>();
                        }

                        return listResult.getData().stream()
                                .filter(syncPloyDetailResult -> Objects.equals(syncPloyDetailResult.getStatus(), 2))
                                .map(syncPloyDetailResult -> threadPoolExecutor.submit(() -> {
                                    final IntegrationTaskEntity integrationTaskEntity = convert2IntegrationTaskEntity(syncPloyDetailResult, taskId, arg.getNotifyTenantId(), arg.getNotifyEmployeeIds());
                                    final Pair<Boolean, String> booleanStringPair = doTask(integrationTaskEntity);
                                    if (BooleanUtils.isTrue(booleanStringPair.getLeft())) {
                                        success.incrementAndGet();
                                    } else {
                                        errorMessageMap.computeIfAbsent(syncPloyDetailResult.getPloyTenantId(), s -> Sets.newHashSet()).add(booleanStringPair.getRight());
                                        fail.incrementAndGet();
                                    }
                                })).collect(Collectors.toList());
                    })).collect(Collectors.toList());

            futures.stream()
                    .map(futures1 -> {
                        try {
                            return futures1.get();
                        } catch (Exception e) {
                            log.warn("future.get failed", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .forEach(future -> {
                        try {
                            future.get();
                        } catch (Exception e) {
                            log.warn("future.get failed", e);
                        }
                    });
        } finally {
            threadPoolExecutor.shutdown();
        }

        sendResultMessage(taskId, success.get(), fail.get(), errorMessageMap, startTime, arg.getNotifyTenantId(), arg.getNotifyEmployeeIds());
    }
}
