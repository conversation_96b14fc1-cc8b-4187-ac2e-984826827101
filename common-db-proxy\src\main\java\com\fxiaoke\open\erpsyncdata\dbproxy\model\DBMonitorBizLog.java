package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:49:34
 */
@Data
@Builder
public class DBMonitorBizLog {
//    ei, 对象，时间，数据id,  报错信息
    private String tenantId;
    private String className;
    private String method;
    private String objectApiName;
    private String dataId;
    /**
     * 错误类型 {@link Class#getSimpleName()}
     */
    private String exception;
    private String errorMessage;
    private String allArgs;
}
