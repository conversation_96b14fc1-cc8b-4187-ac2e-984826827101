package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SyncLogNode;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Component
public class SyncLogManagerFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;

    /**
     * 日志级别实例的映射关系
     */
    private static final Map<SyncLogTypeEnum, BaseLinkWalkingService> MANAGER_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(SyncLogNode.class);
        for (Object bean : beanMap.values()) {
            //使用工具获取注解，避免aop导致getAnnotation为空的问题
            SyncLogNode channel = AnnotationUtils.findAnnotation(bean.getClass(), SyncLogNode.class);
            MANAGER_MAP.put(channel.value(), (BaseLinkWalkingService) bean);
        }
    }

    /**
     * 获取实例
     *
     * @param channel
     * @return AbstractWorkBench
     */
    public static BaseLinkWalkingService getInstance(SyncLogTypeEnum channel) {
        return MANAGER_MAP.get(channel);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
