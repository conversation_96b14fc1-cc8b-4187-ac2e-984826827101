package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.TimePointRecord;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TimePoint;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.LinkedList;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/2
 */
@Service
@Slf4j
public class TimePointRecorderImpl implements TimePointRecorderStatic.TimePointRecorder {
    private final TimePointManager timePointManager;
    private final ThreadLocal<TimePointRecord> timePointLocal;

    private static final TimedCache<String, TimePointRecord> syncDataId2TimePointRecord = CacheUtil.newTimedCache(1000 * 60 * 10);//设置过期时间,syncData要唯一

    static {
        //定时清理，不然超时后不会自动清理。10分钟
        syncDataId2TimePointRecord.schedulePrune(TimeUnit.MINUTES.toMillis(10));
    }
    @PostConstruct
    public void init() {
        TimePointRecorderStatic.setTimePointRecorder(this);
    }

    @Autowired
    public TimePointRecorderImpl(TimePointManager timePointManager) {
        this.timePointManager = timePointManager;
        this.timePointLocal = new ThreadLocal<>();
    }

    @Override
    public void begin(String tenantId, String objApiName, String dataId, Long firstParseTime) {
        try {
            timePointLocal.set(TimePointRecord.builder()
                    .tenantId(tenantId)
                    .objApiName(objApiName)
                    .sourceDataId(dataId)
                    .traceId(TraceUtil.get())
                    .listenTime(System.currentTimeMillis())
                    .firstParseTime(firstParseTime)
                    .lastSendMqTime(firstParseTime)
                    .syncDataTimePoints(new LinkedList<>())
                    .build());
        } catch (Exception e) {
            log.error("record time point error,", e);
        }
    }

    @Override
    public void record(String pointName) {
        try {
            TimePointRecord timePointRecord = timePointLocal.get();
            if (timePointRecord == null) {
                return;
            }
            if ("streamBegin".equals(pointName)) {
                timePointRecord.addSyncDataTimePoint();
            }
            timePointRecord.addTimePoint(pointName);
            if ("allFinish".equals(pointName)) {
                timePointRecord.setAllFinishTime(System.currentTimeMillis());
                timePointManager.syncCalculateCostAndSendBizLog(timePointRecord);
                timePointLocal.remove();
            }
        } catch (Exception e) {
            log.error("record time point error,", e);
        }

    }

    @Override
    public void recordSync(String pointName, String syncDataId) {
        try {
            TimePointRecord timePointRecord = getTimePointRecord(syncDataId);
            if (timePointRecord == null || timePointRecord.getSyncDataTimePoints().isEmpty()) {
                return;
            }
            if (timePointRecord.getLastSyncDataTimePoint().getSyncDataId() == null && StringUtils.isNotBlank(syncDataId)) {
                timePointRecord.setSyncDataId(syncDataId);
            }
            timePointRecord.addTimePoint(pointName);
            if ("allFinish".equals(pointName)) {
                timePointRecord.setAllFinishTime(System.currentTimeMillis());
                timePointManager.syncCalculateCostAndSendBizLog(timePointRecord);
                syncDataId2TimePointRecord.remove(syncDataId);
                timePointLocal.remove();
            }
        } catch (Exception e) {
            log.error("record time point error,", e);
        }
    }

    @Override
    public void asyncRecord(String tenantId, String objApiName, String dataId, String pointName) {
        //异步的操作存到mongo里面
        timePointManager.syncSend(TimePoint.create(tenantId, pointName, objApiName, dataId));
    }

    @Override
    public void setOtherMsg(String syncDataId, Integer count, Boolean reWriteFailed, String reWriteFailedMsg, Boolean afterSyncFailed, String afterSyncFailedMsg,
                            Boolean syncStepException, String syncStepExceptionMsg, Boolean throwable, String throwableMsg) {
        //设置其他信息
        TimePointRecord timePointRecord = getTimePointRecord(syncDataId);
        if (timePointRecord == null) {
            return;
        }
        TimePointRecord.SyncDataTimePoint syncDataTimePoint = null;
        LinkedList<TimePointRecord.SyncDataTimePoint> syncDataTimePoints = timePointRecord.getSyncDataTimePoints();
        if (syncDataTimePoints != null && syncDataId != null) {
            TimePointRecord.SyncDataTimePoint point = timePointRecord.getLastSyncDataTimePoint();
            if (syncDataId.equals(point.getSyncDataId())) {
                syncDataTimePoint = point;
            }
        }
        if (syncDataTimePoint == null) {
            return;
        }
        if (!Objects.isNull(count)) {
            syncDataTimePoint.setCount(count);
        }
        if (!Objects.isNull(reWriteFailed)) {
            syncDataTimePoint.setReWriteFailed(reWriteFailed);
        }
        if (!Objects.isNull(reWriteFailedMsg)) {
            syncDataTimePoint.setReWriteFailedMsg(reWriteFailedMsg);
        }
        if (!Objects.isNull(afterSyncFailed)) {
            syncDataTimePoint.setAfterSyncFailed(afterSyncFailed);
        }
        if (!Objects.isNull(afterSyncFailedMsg)) {
            syncDataTimePoint.setAfterSyncFailedMsg(afterSyncFailedMsg);
        }
        if (!Objects.isNull(syncStepException)) {
            syncDataTimePoint.setSyncStepException(syncStepException);
        }
        if (!Objects.isNull(syncStepExceptionMsg)) {
            syncDataTimePoint.setSyncStepExceptionMsg(syncStepExceptionMsg);
        }
        if (!Objects.isNull(throwable)) {
            syncDataTimePoint.setThrowable(throwable);
        }
        if (!Objects.isNull(throwableMsg)) {
            syncDataTimePoint.setThrowableMsg(throwableMsg);
        }
    }

    private TimePointRecord getTimePointRecord(String syncDataId) {
        TimePointRecord timePointRecord = timePointLocal.get();
        if(timePointRecord==null&&StringUtils.isNotBlank(syncDataId)){
            timePointRecord = syncDataId2TimePointRecord.get(syncDataId,false);
        }
        return timePointRecord;
    }

    @Override
    public void changeTimePointRecorder2OtherThread(String syncDataId) {
        TimePointRecord timePointRecord = timePointLocal.get();
        if (timePointRecord == null||StringUtils.isBlank(syncDataId)) {
            return;
        }
        syncDataId2TimePointRecord.put(syncDataId,timePointRecord);
        timePointLocal.remove();
    }
}
