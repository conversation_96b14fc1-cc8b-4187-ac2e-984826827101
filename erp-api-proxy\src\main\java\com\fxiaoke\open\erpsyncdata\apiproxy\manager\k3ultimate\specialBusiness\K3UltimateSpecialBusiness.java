package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * 云星空旗舰版特殊业务逻辑
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface K3UltimateSpecialBusiness {
    /**
     * 获取列表数据的后动作。
     */
    void afterListErpObjDataByTime(String tenantId,
                                   String dataCenterId,
                                   String objApiName,
                                   TimeFilterArg timeFilterArg,
                                   List<StandardData> standardDataList,
                                   K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);

    /**
     * 获取ERP数据的后动作。
     */
    void afterGetErpObjData(String tenantId,
                            String dataCenterId,
                            String objApiName,
                            ErpIdArg erpIdArg,
                            StandardData standardData,
                            K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);

    /**
     * 根据id获取数据的后动作。
     */
    void afterReSyncDataById(String tenantId,
                             String dataCenterId,
                             String objApiName,
                             ErpIdArg erpIdArg,
                             List<StandardData> standardDataList,
                             K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);

    /**
     * 创建数据的前动作
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param standardData
     * @param requestData 准备写入到ERP的数据
     * @param k3UltimateApiService
     */
    void beforeCreateErpObjData(String tenantId,
                                String dataCenterId,
                                String objApiName,
                                StandardData standardData,
                                JSONObject requestData,
                                K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);

    /**
     * 创建数据的后动作。
     */
    void afterCreateErpObjData(String tenantId,
                               String dataCenterId,
                               String objApiName,
                               StandardData standardData,
                               JSONObject requestData,
                               Result<ErpIdResult> result,
                               K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);

    /**
     * 更新数据的前动作。
     */
    void beforeUpdateErpObjData(String tenantId,
                                String dataCenterId,
                                String objApiName,
                                StandardData standardData,
                                JSONObject requestData,
                                K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);

    /**
     * 更新数据的后动作。
     */
    void afterUpdateErpObjData(String tenantId,
                               String dataCenterId,
                               String objApiName,
                               StandardData standardData,
                               JSONObject requestData,
                               Result<ErpIdResult> result,
                               K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo);
}
