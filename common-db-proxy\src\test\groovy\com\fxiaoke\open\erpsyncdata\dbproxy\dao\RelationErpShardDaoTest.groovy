package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.api.IdGenerator
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.CheckResultEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/2/28 17:39:11
 */
// TODO: Ignore Spring
@Ignore
@ContextConfiguration(["classpath*:spring-test.xml"])
class RelationErpShardDaoTest extends Specification {

    static {
        System.setProperty("process.profile", "fstest");
    }

    @Autowired
    private RelationErpShardDao relationErpShardDao

    def "测试typeHandler"() {
        when:
        def one = relationErpShardDao.queryFirstNormalByDownstreamId("testD")
        if (one == null) {
            // TODO: 构造方法错误
            relationErpShardDao.insert(new RelationErpShardEntity(IdGenerator.get(), "testT", "testTI", "test", "testD",
                    new CheckResultEntity(["qwe", "123"], [], null, ["kjh"]), 1,
                    System.currentTimeMillis(), System.currentTimeMillis()))
            one = relationErpShardDao.queryFirstNormalByDownstreamId("testD")
        }

        println one

        then:
        one.getCheckResult().getClass() == CheckResultEntity.class
    }
}
