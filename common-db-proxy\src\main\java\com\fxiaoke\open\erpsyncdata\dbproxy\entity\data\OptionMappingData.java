package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import lombok.Data;

import java.io.Serializable;

@Data
public class OptionMappingData implements Serializable {
    private Object sourceOption;
    private Object destOption;
//    public static List<com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData> convert(List<OptionMappingData> sourceList){
//        if (CollectionUtils.isEmpty(sourceList)){
//            return null;
//        }
//        return sourceList.stream().map(OptionMappingData::convert).collect(Collectors.toList());
//    }
//    public static com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData convert(OptionMappingData source){
//        if(source==null){
//            return null;
//        }
//        com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData result = new com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData();
//        result.setSourceOption(source.getSourceOption());
//        result.setDestOption(source.getDestOption());
//        return result;
//    }
}