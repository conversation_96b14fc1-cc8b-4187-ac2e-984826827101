package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 注意：如果对象的属性变了需要修改序列化的的name
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
@ManagedTenantReplace
public interface ErpObjectDao extends BaseTenantDao<ErpObjectEntity,ErpObjectDao> {

    /**
     * get单个对象的缓存不清楚，等超时。
     * @param record the record
     * @return
     */
    @InsertProvider(type = CrudProvider.class, method = "insert")
    @Result(javaType = int.class)
    int insert(ErpObjectEntity record);


    /**
     * 批量插入记录
     * 谨慎使用， postgres对传的参数数量有限制。
     * get单个对象的缓存不清楚，等超时。
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = BatchProvider.class, method = "batchInsert")
    @Result(javaType = int.class)
    int batchInsert(@Param(BatchProvider.KEY) List<ErpObjectEntity> record);

    /**
     * 通过APiName查找对象
     * @param tenantId
     * @param erpObjectApiNameCollection
     * @return
     */
    List<ErpObjectEntity> queryByApiNames(@Param("tenantId")String tenantId,
                                          @Param("erpObjectApiNameCollection")Collection<String> erpObjectApiNameCollection);


    /**
     * 通过APiName查找对象
     * @param tenantId
     * @param erpObjectApiNameCollection
     * @return
     */
    List<ErpObjectEntity> queryByApiNames2(@Param("tenantId")String tenantId,@Param("dcId")String dcId,
                                          @Param("erpObjectApiNameCollection")Collection<String> erpObjectApiNameCollection);

    ErpObjectEntity getByObjApiName(@Param("tenantId")String tenantId,
                                    @Param("dcId")String dcId,
                                    @Param("erpObjectApiName")String erpObjectApiName);



    int deleteByTenantIdAndErpObjectApiName(@Param("tenantId")String tenantId,@Param("dataCenterId")String dataCenterId,@Param("erpObjectApiName")String erpObjectApiName);

    int deleteByTenantIdAndDcId(@Param("tenantId")String tenantId,@Param("dataCenterId")String dataCenterId);

    int deleteByTenantId(String tenantId);

    List<ErpObjectEntity> listByTenantId(@Param("tenantId")String tenantId);

    List<ErpObjectEntity> listByDcId(@Param("tenantId")String tenantId, @Param("dataCenterId")String dataCenterId);

    List<ErpObjectEntity> listErpNotSplitObjByDcIds(@Param("tenantId")String tenantId, @Param("dataCenterIds")List<String> dataCenterIds);



    List<ErpObjectEntity> queryByRealObjApiName(@Param("tenantId")String tenantId,@Param("dcId")String dcId,
                                          @Param("realObjApiName")String realObjApiName);

    /**
     * 查找所有拆分对象DTO
     * @param tenantId
     * @param dcId 传null查所有
     * @return
     */
    List<ErpObjExtendDto> queryAllObjExtendDTOByDc(@Param("tenantId") String tenantId,
                                                   @Param("dcId") String dcId);

}