package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

@Entity(value = "user_operator_log", noClassnameStored = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserOperatorLog {

    @Id
    private String id;

    //企业id
    private String tenantId;

    //数据中心id
    private String dataCenterId;


    private Integer userId;

    //操作时间
    private Long operatorTime;

    /**
     * <p>操作模块:集成流，连接器，erp对象...</p>
     * {@link }
     */
    private String module;

    /**
     * <p>操作模块的id：策略id，操作连接器id,操作对象id</p>
     */
    private String moduleId;

    /**
     * <p>操作动作:新增，编辑，删除</p>
     * {@link }
     */
    private String action;

    /**
     * message：可以为空
     */
    private String message;

    //快照数据
    private String snapshotData;


    public static UserOperatorLog create(String tenantId,
                                        String dataCenterId,
                                        String module,
                                        String moduleId,
                                        Integer userId,
                                        String action,
                                        String message,
                                        String snapshotData) {

        return UserOperatorLog.builder()
                              .tenantId(tenantId)
                              .dataCenterId(dataCenterId)
                              .module(module)
                              .moduleId(moduleId)
                              .userId(userId)
                              .operatorTime(System.currentTimeMillis())
                              .action(action)
                              .message(message).snapshotData(snapshotData).build();

    }

}
