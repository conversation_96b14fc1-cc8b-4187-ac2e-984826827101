{"type": "page", "title": "统计数据库表数量", "toolbar": [], "body": [{"type": "crud", "initFetch": false, "headerToolbar": ["statistics"], "api": {"method": "post", "url": "../ddl/db/allTableCount", "data": {"tenantIds": "${tenantIds|split:,}", "type": "${type}", "limit": "${limit}", "forceCheckInvalid": "${forceCheckInvalid}", "countAll": "${countAll}", "checkSyncLog": "${checkSyncLog}"}}, "filter": {"type": "group", "columnCount": 3, "mode": "horizontal", "body": [{"label": "tenantIds", "type": "input-text", "placeholder": "企业id以 , 分隔", "name": "tenantIds"}, {"label": "检查类型", "type": "select", "name": "type", "value": 1, "options": [{"label": "中间表PG库", "value": 1}, {"label": "临时库", "value": 2}, {"label": "分发框架", "value": 3}]}, {"label": "限额", "type": "input-text", "name": "limit", "value": 700, "placeholder": "默认超限额的库检查作废表"}, {"option": "强制检查作废表", "type": "checkbox", "name": "forceCheckInvalid", "value": false}, {"option": "计算所有表数量", "type": "checkbox", "name": "countAll", "value": false}, {"option": "过滤syncLog日志", "type": "checkbox", "name": "checkSyncLog", "value": false}]}, "defaultParams": {"perPage": 100}, "columns": [{"name": "id", "label": "id"}, {"name": "count", "label": "表数量"}, {"type": "Textarea", "readOnly": true, "maxRows": 5, "name": "invalidEis", "label": "作废企业"}, {"type": "Textarea", "readOnly": true, "maxRows": 5, "name": "invalidTables", "label": "作废表名"}, {"type": "Textarea", "readOnly": true, "maxRows": 5, "name": "allTables", "label": "所有表名"}, {"type": "Textarea", "readOnly": true, "maxRows": 5, "name": "deleteTableSql", "label": "删除表语句"}], "loadDataOnce": true, "affixHeader": true, "columnToggled": "auto", "placeholder": "暂无数据", "combineNum": 0}]}