package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class K3UltimateRequestBySave extends K3UltimateBaseRequest implements Serializable {
    private List<Object> data = new ArrayList<>();

    public static K3UltimateRequestBySave buildSaveRequest(String tenantId,
                                                           String dataCenterId,
                                                           String objApiName,
                                                           ErpObjInterfaceUrlEnum interfaceUrl,
                                                           List<Object> dataList) {

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId, dataCenterId, objApiName,
                interfaceUrl.name());

        K3UltimateRequestBySave requestBySave = new K3UltimateRequestBySave();
        requestBySave.setInterfaceMonitorData(interfaceMonitorData);
        requestBySave.setData(dataList);

        return requestBySave;
    }
}