package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import org.springframework.stereotype.Component;

@Component
public class CustomerApiTemplate extends K3UltimateBaseApiTemplate {
    @Override
    public String getObjApiName() {
        return K3UltimateObjApiName.bd_customer;
    }

    @Override
    public String getBatchQueryApi() {
        return "/kapi/v2/basedata/bd_customer/query";
    }

    @Override
    public String getBatchAddApi() {
        return "/kapi/v2/basedata/bd_customer/add";
    }

    @Override
    public String getBatchUpdateApi() {
        return "/kapi/v2/basedata/bd_customer/batchUpdate";
    }

    @Override
    public String getBatchSubmitApi() {
        return "/kapi/v2/basedata/bd_customer/batchSubmit";
    }

    @Override
    public String getBatchUnSubmitApi() {
        return "/kapi/v2/basedata/bd_customer/batchUnSubmit";
    }

    @Override
    public String getBatchAuditApi() {
        return "/kapi/v2/basedata/bd_customer/batchAudit";
    }

    @Override
    public String getBatchUnAuditApi() {
        return "/kapi/v2/basedata/bd_customer/batchUnAudit";
    }

    @Override
    public String getBatchEnableApi() {
        return "/kapi/v2/basedata/bd_customer/batchEnable";
    }

    @Override
    public String getBatchDisableApi() {
        return "/kapi/v2/basedata/bd_customer/batchDisable";
    }

    @Override
    public String getBatchDeleteApi() {
        return "/kapi/v2/basedata/bd_customer/batchDelete";
    }
}
