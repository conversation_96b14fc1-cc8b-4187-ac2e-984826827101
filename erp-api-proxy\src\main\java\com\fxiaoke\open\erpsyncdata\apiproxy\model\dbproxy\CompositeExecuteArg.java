package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
public class CompositeExecuteArg implements Serializable {
    private static final long serialVersionUID = 6844558531121124695L;

    /**
     * 是否事务
     */
    private boolean tx = true;

    private List<ExecuteArg<?>> args = new ArrayList<>();


    public String toJsonStr() {
        return JacksonUtil.toJson(this);
    }
}
