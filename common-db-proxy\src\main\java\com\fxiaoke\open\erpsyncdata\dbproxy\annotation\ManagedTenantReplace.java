package com.fxiaoke.open.erpsyncdata.dbproxy.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/2/21 19:16:10
 *
 * 用于指定哪些Dao需要将下游代管企业替换为模板企业
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface ManagedTenantReplace {
}
