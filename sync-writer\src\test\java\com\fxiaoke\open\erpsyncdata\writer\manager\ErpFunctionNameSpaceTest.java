package com.fxiaoke.open.erpsyncdata.writer.manager;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;

/**
 * <AUTHOR>
 * @date 2022/12/22 14:51:05
 */
public interface ErpFunctionNameSpaceTest {
    StandardData getErpObjDataById(ErpIdArg arg);

    StandardListData queryMasterBatch(TimeFilterArg timeFilterArg);

    ErpIdResult create(StandardData standardData);

    StandardDetailId createDetail(StandardDetailData standardDetailData);

    ErpIdResult update(StandardData standardData);

    StandardDetailId updateDetail(StandardDetailData standardDetailData);

    String invalid(StandardInvalidData standardInvalidData);

    String invalidDetail(StandardInvalidData standardInvalidData);

    String recover(StandardRecoverData standardRecoverData);
}
