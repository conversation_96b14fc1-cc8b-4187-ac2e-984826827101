package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.MultiLanguageTextCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AplTemplateDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AplTemplateDoc.Fields;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.MultiLanguageText;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * APL模板，
 * 不做历史版本功能，仅保留最新版本
 *
 * <AUTHOR> (^_−)☆
 * @date 2022-11-17
 */
@Repository
@Slf4j
public class AplTemplateDao {
    @Qualifier("erpSyncDataMongoStore")
    @Autowired
    private DatastoreExt store;

    private static final String dbName = "erp_sync_data2";


    @PostConstruct
    void init() {
        createIndex();
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<AplTemplateDoc> coll = getColl();
        //aplClassName+版本不允许重复
        coll.createIndex(Indexes.ascending(Fields.aplClassName), new IndexOptions().unique(true).background(true));
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(
                        CodecRegistries.fromCodecs(new MultiLanguageTextCodec()),
                        MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder()
                                .register(AplTemplateDoc.class)
                                .automatic(true).build()));
    }

    private MongoCollection<AplTemplateDoc> getColl() {
        MongoCollection<AplTemplateDoc> coll = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("apl_template", AplTemplateDoc.class);
        return coll;
    }


    /**
     * 覆盖式，不再校验版本
     *
     * @param newDoc
     * @param newVersion
     */
    public void upsertDoc(AplTemplateDoc newDoc, String newVersion) {
        //旧版本，不传将无法更新
        String oldVersion = newDoc.getVersion();
        String aplClassName = newDoc.getAplClassName();
        MongoCollection<AplTemplateDoc> coll = getColl();
        Date updateTime = new Date();
        String today = DateUtil.format(updateTime, "yyyyMMdd");
        newDoc.setUpdateTime(updateTime);
        if (StrUtil.isEmpty(newVersion)) {
            if (oldVersion != null) {
                //更新版本号,小版本+1
                String[] split = oldVersion.split("\\.");
                String ver1 = split[0];
                String ver2 = split[1];
                //小版本+1
                ver2 = Integer.toString(Integer.parseInt(ver2) + 1);
                newVersion = String.format("%s.%s.%s", ver1, ver2, today);
            } else {
                newVersion = "1.0." + today;
            }
        } else {
            //校验传输的版本
            String[] split = newVersion.split("\\.");
            if (split.length < 2 || !NumberUtil.isInteger(split[0]) || !NumberUtil.isInteger(split[1])) {
                log.error("version is invalid,valid version like '1.2.20231024'");
                throw new ErpSyncDataException(ResultCodeEnum.PARAM_ERROR, null);
            }
            String ver1 = split[0];
            String ver2 = split[1];
            newVersion = String.format("%s.%s.%s", ver1, ver2, today);
        }
        newDoc.setVersion(newVersion);
        //更新
        List<Bson> updates = CollUtil.newArrayList(Updates.currentDate(Fields.updateTime));
        Map<String, Object> docMap = BeanUtil.beanToMap(newDoc, false, true);
        docMap.forEach((k, v) -> {
            if (Fields.updateTime.equals(k) || Fields.createTime.equals(k) || Fields.aplClassName.equals(k) || "id".equals(k)) {
                return;
            }
            if (v instanceof MultiLanguageText) {
                //多语言文本 字段单独更新
                ((MultiLanguageText) v).getAll().forEach((lang, text) -> {
                            updates.add(Updates.set(k + "." + lang, text));
                        }
                );
            } else {
                updates.add(Updates.set(k, v));
            }
        });
        updates.add(Updates.setOnInsert(Fields.createTime, new Date()));
        updates.add(Updates.setOnInsert(Fields.aplClassName, aplClassName));
        UpdateResult updateResult = coll.updateOne(
                Filters.eq(Fields.aplClassName, aplClassName),
                Updates.combine(updates),
                new UpdateOptions().upsert(true)
        );
    }

    public void updateOrder(List<String> aplClassNames) {
        List<UpdateOneModel<AplTemplateDoc>> updates = new ArrayList<>();
        for (int i = 0; i < aplClassNames.size(); i++) {
            String aplClassName = aplClassNames.get(i);
            updates.add(new UpdateOneModel<>(Filters.eq(Fields.aplClassName, aplClassName), Updates.set(Fields.order, i * 10)));
        }
        getColl().bulkWrite(updates);
    }

    /**
     * 查最新资源
     *
     * @param includeDetails 是否包含明细
     * @return
     */
    public AplTemplateDoc get(String aplClassName) {
        FindIterable<AplTemplateDoc> findIter = getColl().find();
        AplTemplateDoc first = findIter
                .filter(Filters.eq(Fields.aplClassName, aplClassName))
                .limit(1).first();
        return first;
    }

    /**
     * 获取模板
     *
     * @param enable 为空时查所有，不为空时增加筛选
     * @return
     */
    public List<AplTemplateDoc> listAllBaseInfo(Boolean enable) {
        FindIterable<AplTemplateDoc> iterable = getColl().find()
                .projection(Projections.exclude(Fields.description, Fields.readmeStr, Fields.aplCode))
                .sort(Sorts.ascending(Fields.order))
                .limit(2000);
        if (enable != null) {
            iterable.filter(Filters.eq(Fields.enable, enable));
        }
        ArrayList<AplTemplateDoc> resultList = CollUtil.newArrayList(iterable);
        return resultList;
    }


    public void delete(String aplClassName) {
        getColl().deleteOne(Filters.eq(Fields.aplClassName, aplClassName));
    }

}
