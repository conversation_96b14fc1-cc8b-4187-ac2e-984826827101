package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.model.K3UltimateConnectParam;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetAccessTokenRequestArg implements Serializable {
    private String apptoken;//必须小写
    private String user;
    private String tenantId;
    private String accountId;
    private K3UltimateConnectParam.UserTypeEnum usertype;//必须小写
    private String language;
}
