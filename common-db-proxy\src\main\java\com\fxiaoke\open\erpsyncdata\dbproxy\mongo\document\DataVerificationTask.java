package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.DataVerificationTaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 15:41 2022/11/3
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class DataVerificationTask implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * 集成流id
     */
    private String streamId;
    /**
     *任务id
     */
    private String taskId;
    /**
     *文件nPath
     */
    private String fileNPath;
    /**
     *文件名称
     */
    private String fileName;
    /**
     * 接口返回数量
     */
    private Integer pollingListSize;
    /**
     *导入id数量
     */
    private Integer allIdListSize;
    /**
     *未读取数量
     */
    private Integer notTempIdListSize;
    /**
     *读取数量
     */
    private Integer tempIdListSize;
    /**
     *未触发同步数量
     */
    private Integer notMappingIdListSize;
    /**
     *触发同步数量
     */
    private Integer mappingIdListSize;
    /**
     *未创建成功数量
     */
    private Integer notCreatedIdListSize;
    /**
     *创建成功数量
     */
    private Integer createdIdListSize;
    /**
     *状态
     */
    private DataVerificationTaskStatusEnum status;
    /**
     *需要停止核对
     */
    private Boolean needStop;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *更新时间
     */
    private Date updateTime;

}
