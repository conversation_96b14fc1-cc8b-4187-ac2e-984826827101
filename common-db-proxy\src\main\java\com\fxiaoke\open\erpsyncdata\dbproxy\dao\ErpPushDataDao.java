package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushDataEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @dateTime 2020/12/7 12:02
 */
@Repository
public interface ErpPushDataDao extends ErpBaseDao<ErpPushDataEntity>, ITenant<ErpPushDataDao> {

    List<String> listErpObjDataByTime(@Param("tenantId") String tenantId,
                                      @Param("objectApiName") String objectApiName,
                                      @Param("startTime") Long startTime,
                                      @Param("endTime") Long endTime,
                                      @Param("operationTypes") Collection<Integer> operationType,
                                      @Param("offset") Integer offset,
                                      @Param("limit") Integer limit

    );

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    ErpPushDataEntity findByTenantObjectId(@Param("tenantId") String tenantId,
                                           @Param("objectApiName") String objectApiName,
                                           @Param("sourceDataId") String sourceDataId,
                                           @Param("dataCenterId") String dataCenterId);
    Integer deleteErpDataByCreateAndUpdateTime(@Param("tenantId") String tenantId,
                                               @Param("objApiName") String objApiName,
                                               @Param("createTime") Long createTime,
                                               @Param("updateTime") Long updateTime);

    List<String> listByTenantIdLimit1000(@Param("tenantId") String tenantId);

    int deleteByIdIn(@Param("idList") Collection<String> idList);
}