package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * erp基础数据映射
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DataId("fsDataId")
@Table(name = "erp_field_data_mapping")
public class ErpFieldDataMappingEntity {

    @Id
    private String id;

    /**
    * 企业id
    */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
    * 渠道
    */
    private ErpChannelEnum channel;

    /**
    * 基础数据类型
    */
    private ErpFieldTypeEnum dataType;

    /**
    * 纷享数据id
    */
    private String fsDataId;

    /**
    * 纷享数据name
    */
    private String fsDataName;

    /**
    * erp数据id
    */
    private String erpDataId;

    /**
    * erp数据name
    */
    private String erpDataName;

    /**
     * 数据json
     */
    private String fieldDataExtendValue;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 修改时间
    */
    private Long updateTime;
}