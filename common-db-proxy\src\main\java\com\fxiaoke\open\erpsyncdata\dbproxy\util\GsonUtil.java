package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
public class GsonUtil {
    private static final Gson GSON = new GsonBuilder().disableHtmlEscaping().create();


    public GsonUtil() {
    }


    public static Gson getGson() {
        return GSON;
    }


    public static String toJson(Object object) {
        if (object == null) {
            return "";
        }
        if (object instanceof String) {
            return (String) object;
        }
        return GSON.toJson(object);
    }
    /**
     * @param json list的序列化字符串
     * @param <T>  T类型
     * @return List<T>
     */
    public static  <T> List<T> toList(String json, Class<T> clazz) {
        return GSON.fromJson(json, TypeToken.getParameterized(List.class, clazz).getType());
    }


    public static <T> T fromJson(String json, Type type) {
        return GSON.fromJson(json, type);
    }

    public static <T> List<T> fromArrayJson(String json, Class<T> type) {
        return fromJson(json, TypeToken.getParameterized(List.class, type).getType());
    }

    public static <T> T fromJson(JsonElement json, Type type) {
        return GSON.fromJson(json, type);
    }

    public static Boolean equals(Object obj1, Object obj2) {
        return toJson(obj1).equals(toJson(obj2));
    }

    public static void main(String[] args) {
        String str = null;
        System.out.println(toJson(str));
        System.out.println(GSON.toJson(str));
        System.out.println(str);
        String str2 = fromJson(str, String.class);
        System.out.println(str2);

    }
}
