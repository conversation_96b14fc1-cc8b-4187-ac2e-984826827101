<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao">

    <select id="superQuerySql" resultType="java.util.Map">
        ${sqlStr}
    </select>

    <select id="superQuerySql2" resultType="cn.hutool.core.lang.Dict">
        ${sqlStr}
    </select>
    <update id="superUpdateSql">
        ${sqlStr}
    </update>


    <select id="listAllTableLeftMatching" resultType="java.lang.String">
        select tablename from pg_tables where tablename like CONCAT( #{prefix}, '%');
    </select>

    <select id="listTables" resultType="java.lang.String">
        select tablename from pg_tables where tablename in
        <foreach item="item" collection="tables" separator="," open="(" close=")" index="">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryReltuplesFromPgClass" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.PgClassInfo">
        SELECT
            nsp.nspname AS schemaName,
            cls.relname AS tableName,
            cls.reltuples::bigint AS reltuples
        FROM
            pg_class cls
                JOIN
            pg_namespace nsp ON cls.relnamespace = nsp.oid
        WHERE
            cls.relkind = 'r' -- 只查询普通表
          AND nsp.nspname NOT LIKE 'pg_%' -- 排除系统表
          AND nsp.nspname != 'information_schema' -- 排除信息模式
        ORDER BY
            cls.reltuples DESC;
    </select>
</mapper>