package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
public interface ErpFieldDataMappingDao extends ErpBaseDao<ErpFieldDataMappingEntity>, ITenant<ErpFieldDataMappingDao> {

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    List<ErpFieldDataMappingEntity> listNoSearch(@Param("tenantId")String tenantId,
                                          @Param("dataCenterId")String dataCenterId,
                                          @Param("dataType")ErpFieldTypeEnum dataType,
                                          @Param("fsDataId")String fsDataId,
                                          @Param("erpDataId")String erpDataId);

    /**
     * 上面那个方法被加了缓存，不敢随意去掉，copy一个方法。
     */
    List<ErpFieldDataMappingEntity> listNoSearch2(@Param("tenantId")String tenantId,
                                                 @Param("dataCenterId")String dataCenterId,
                                                 @Param("dataType")ErpFieldTypeEnum dataType,
                                                 @Param("fsDataId")String fsDataId,
                                                 @Param("erpDataId")String erpDataId);

    List<ErpFieldDataMappingEntity> listByFsIds(@Param("tenantId")String tenantId,
                                                 @Param("dataCenterId")String dataCenterId,
                                                 @Param("dataType")ErpFieldTypeEnum dataType,
                                                 @Param("fsDataIds")Collection<String> fsDataIds);

    List<ErpFieldDataMappingEntity> listByIdList(@Param("tenantId")String tenantId,
                                                @Param("dataCenterId")String dataCenterId,
                                                @Param("idList")List<String> idList);

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    List<ErpFieldDataMappingEntity> listByErpIds(@Param("tenantId")String tenantId,
                                                @Param("dataCenterId")String dataCenterId,
                                                @Param("dataType")ErpFieldTypeEnum dataType,
                                                @Param("erpDataIds")List<String> erpDataIds);

	int batchDeleteByIds(@Param("tenantId")String tenantId, @Param("idCollection")Collection<String> idCollection);


    /**
     * 删除某类型记录
     *
     * @param tenantId
     * @param channel
     * @param dataType
     * @return
     */
    int deleteByType(@Param("tenantId") String tenantId, @Param("channel") ErpChannelEnum channel, @Param("dataType") String dataType);

    /**
     * 删除数据
     *
     * @param tenantId
     * @param dataCenterId
     * @param dataType
     * @return
     */
    int deleteByDataType(@Param("tenantId") String tenantId,
                     @Param("dataCenterId") String dataCenterId,
                     @Param("dataType") ErpFieldTypeEnum dataType);

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    ErpFieldDataMappingEntity findByDataId(@Param("tenantId") String tenantId,
                                           @Param("dataCenterId") String dataCenterId,
                                           @Param("dataType") ErpFieldTypeEnum dataType,
                                           @Param("fsDataId") String fsDataId,
                                           @Param("erpDataId") String erpDataId);

    int countByTenantIdAndDataType(@Param("tenantId") String tenantId, @Param("dataType") ErpFieldTypeEnum dataType, @Param("queryStr") String queryStr);

    List<ErpFieldDataMappingEntity> listByTenantIdAndDataType(@Param("tenantId") String tenantId,
                                                              @Param("dataType") ErpFieldTypeEnum dataType,
                                                              @Param("limit") Integer limit,
                                                              @Param("offset") Integer offset,
                                                              @Param("queryStr") String queryStr);

    int countByTenantIdAndDataTypeAndDcId(@Param("tenantId") String tenantId,@Param("dataCenterId") String dataCenterId, @Param("dataType") ErpFieldTypeEnum dataType, @Param("isBind") Boolean isBind,@Param("queryStr") String queryStr);

    List<ErpFieldDataMappingEntity> listByTenantIdAndDataTypeAndDcId(@Param("tenantId") String tenantId,
                                                                     @Param("dataCenterId") String dataCenterId,
                                                                     @Param("dataType") ErpFieldTypeEnum dataType,
                                                                     @Param("limit") Integer limit,
                                                                     @Param("offset") Integer offset,
                                                                     @Param("isBind") Boolean isBind,
                                                                     @Param("queryStr") String queryStr);

    int countByTenantIdAndDataTypeAndQueryStr(@Param("tenantId") String tenantId,
                                              @Param("dataCenterId") String dataCenterId,
                                              @Param("dataType") ErpFieldTypeEnum dataType,
                                              @Param("queryStr") String queryStr);

    List<ErpFieldDataMappingEntity> listByTenantIdAndDataTypeAndQueryStr(@Param("tenantId") String tenantId,
                                                                         @Param("dataCenterId") String dataCenterId,
                                                                         @Param("dataType") ErpFieldTypeEnum dataType,
                                                                         @Param("queryStr") String queryStr,
                                                                         @Param("limit") Integer limit,
                                                                         @Param("offset") Integer offset);

    List<String> queryOAUserCodeList(@Param("receiverIds") List<String> receiverIds, @Param("tenantId") String tenantId);

    String queryOAUserCode(@Param("receiverId") String receiverId, @Param("tenantId") String tenantId);

    String queryFxUserCode(@Param("receiverId") String receiverId, @Param("tenantId") String tenantId);

    int deleteByTenantId(String tenantId);

    List<String> getTenantIdListByChannel(@Param("channel") ErpChannelEnum channel);

    List<ErpFieldDataMappingEntity> getDataListByChannel(@Param("tenantId") String tenantId,
                                                         @Param("channel") ErpChannelEnum channel,
                                                         @Param("dataType") ErpFieldTypeEnum dataType);
}