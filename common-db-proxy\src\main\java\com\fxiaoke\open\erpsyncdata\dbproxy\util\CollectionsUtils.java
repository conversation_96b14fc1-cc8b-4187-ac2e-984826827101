package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2024/3/5 16:47:31
 */
public class CollectionsUtils {
    public static <T> boolean anyEmpty(Collection<T>... collections) {
        for (Collection<T> collection : collections) {
            if (CollectionUtils.isEmpty(collection)) {
                return true;
            }
        }
        return false;
    }

    public static <T> boolean notEmpty(Collection<T>... collections) {
        return !anyEmpty(collections);
    }

    public static boolean anyNotEmpty(Collection<?>... collections) {
        return !allEmpty(collections);
    }

    public static boolean allEmpty(Collection<?>... collections) {
        for (Collection<?> collection : collections) {
            if (CollectionUtils.isNotEmpty(collection)) {
                return false;
            }
        }
        return true;
    }
}
