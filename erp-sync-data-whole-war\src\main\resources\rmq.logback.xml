<?xml version="1.0" encoding="UTF-8"?>
<!--定制RocketMq 5.x client日志，注意文件名是RocketMQ规定的不能随便改-->
<configuration>
    <conversionRule conversionWord="pid" converterClass="io.github.aliyunmq.logback.extensions.ProcessIdConverter"/>
    <appender name="DefaultAppenderInner" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <file>${catalina.home:-.}/logs/rocketmq_client.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${catalina.home:-.}/logs/rocketmq_client-%i.log.zip</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>${rocketmq.log.file.maxIndex:-7}</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyy-MM-dd HH:mm:ss.SSS,GMT+8} %-5p [%pid] [%t] [%logger{12}#%M:%L] - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="DefaultAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="DefaultAppenderInner"/>
    </appender>
    <root level="${rocketmq.log.level:-warn}">
        <appender-ref ref="DefaultAppender" additivity="false"/>
    </root>
</configuration>
