{"type": "page", "title": "集成流", "remark": null, "name": "streamList", "toolbar": [], "body": [{"type": "crud", "name": "streams", "api": {"method": "get", "url": "../streamInfoQuery", "sendOn": "tenantId != null"}, "loadDataOnce": true, "defaultParams": {"perPage": 100}, "primaryField": "tenantId", "headerToolbar": ["export-csv", "reload", "bulkActions"], "bulkActions": [], "filter": {"mode": "horizontal", "body": [{"size": "sm", "label": "tenantId", "type": "input-text", "name": "tenantId", "autoFill": {}}, {"label": "关键字", "type": "input-text", "name": "keywords", "placeholder": "可以使用||搜索多个值-new"}], "actions": [{"label": "一键熔断", "type": "button", "level": "danger", "actionType": "ajax", "confirmText": "确定要熔断企业${tenantId}的全部集成流吗？", "confirmTitle": "一键熔断", "api": {"url": "../integrationstream/alert", "method": "post", "data": {"tenantId": "${tenantId}", "needBreak": true}}, "messages": {"success": "一键熔断请求成功，执行结果请关注企信通知", "failed": "一键熔断请求失败，请检查重试"}, "reload": "streams"}, {"type": "submit", "label": "搜索", "primary": true}], "persistData": true}, "columns": [{"name": "streamId", "label": "集成流id"}, {"name": "streamName", "label": "集成流名称"}, {"name": "sourceObjectApiName", "label": "源对象"}, {"name": "destObjectApiName", "label": "目标对象"}, {"name": "enable", "type": "status", "label": "是否启用", "sortable": true}, {"name": "isRollingErp", "type": "status", "label": "轮询ERP数据中", "sortable": true}, {"name": "lastBreakTime", "label": "上次熔断时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"name": "lastCountTime", "label": "上次统计时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"name": "lastCount", "label": "上次精确异常增量"}, {"name": "currentCount", "label": "当前预估异常增量"}, {"name": "pollingFailedInfo", "type": "json", "label": "当前异常轮询信息"}, {"type": "operation", "label": "操作", "fixed": "right", "buttons": [{"label": "告警", "type": "button", "level": "danger", "actionType": "dialog", "dialog": {"title": "告警内容", "body": {"type": "form", "api": "post:../integrationstream/alert", "body": [{"label": "tenantId", "type": "input-text", "name": "tenantId", "disabled": true}, {"label": "erpDcId", "type": "input-text", "name": "erpDcId", "disabled": true}, {"type": "input-text", "label": "集成流Id", "name": "streamId", "disabled": true}, {"type": "input-text", "label": "集成流名称", "name": "streamName", "disabled": true}, {"label": "源对象", "name": "sourceObjectApiName", "type": "input-text", "disabled": true}, {"name": "destObjectApiName", "label": "目标对象", "type": "input-text", "disabled": true}, {"type": "textarea", "name": "content", "label": "告警内容", "required": true}, {"type": "checkbox", "name": "needBreak", "option": "是否触发熔断"}]}}}]}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "combineNum": 0, "bodyClassName": "panel-default", "keepItemSelectionOnPageChange": false}]}