package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.U8DataManagerAbstracta;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.List;

/**
 * U8 接口封装
 *
 * @date 2020/8/25
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_U8)
public class U8DataManager extends BaseErpDataManager {

    private static ApplicationContext applicationContext;

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        U8DataManager.applicationContext = applicationContext;
    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        return getBean(timeFilterArg.getObjAPIName()).listErpObjDataByTime(timeFilterArg, connectInfo);
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return getBean(standardData.getObjAPIName()).createErpObjData(standardData, connectInfo);
    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return getBean(standardData.getObjAPIName()).updateErpObjData(standardData, connectInfo);
    }

    /**审核erp对象*/
    public Result<String> verifyErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return getBean(standardData.getObjAPIName()).verifyErpObjData(standardData,connectInfo);
    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        return getBean(erpIdArg.getObjAPIName()).getErpObjData(erpIdArg, connectInfo);
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if(erpObjDataResult!=null){
            Result<List<StandardData>> dataList=new Result<>();
            if(erpObjDataResult.getData()!=null){
                dataList.setData(Lists.newArrayList(erpObjDataResult.getData()));
                return dataList;
            }else {
                dataList.setData(Lists.newArrayList());
                return dataList;
            }
        }
        return null;
    }

    private U8DataManagerAbstracta getBean(String objApiName) {
        if (applicationContext.containsBean(objApiName)) {
            return (U8DataManagerAbstracta) applicationContext.getBean(objApiName);
        } else {
            return (U8DataManagerAbstracta) applicationContext.getBean("u8default");
        }
    }

    public U8DataManagerAbstracta getDefaultBean() {
        return (U8DataManagerAbstracta) applicationContext.getBean("u8default");
    }
}
