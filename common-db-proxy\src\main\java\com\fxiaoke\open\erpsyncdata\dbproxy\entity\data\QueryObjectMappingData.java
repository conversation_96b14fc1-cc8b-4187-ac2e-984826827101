package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:41 2023/5/19
 * @Desc:
 */
@Data
public class QueryObjectMappingData implements Serializable {
    private String sourceObjectApiName;//取值的源对象
    private String destObjectApiName;//需要查询的目标对象
    private QueryObjectOrFilterFieldMappingsData queryFieldMappings;//取值的源对象字段：需要查询的目标对象字段
}
