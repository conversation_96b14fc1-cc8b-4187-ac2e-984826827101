package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.DynamicThresholdFilter;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.Marker;


/** hardy。
 * */
public class ErrLogFilter extends DynamicThresholdFilter {
    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {
        FilterReply reply = super.decide(marker, logger, level, format, params, t);
        if(reply.equals(FilterReply.ACCEPT) && !level.equals(Level.ERROR)) {
            if(logger.getName().startsWith("com.fxiaoke.open.erpsyncdata")) {
                return FilterReply.ACCEPT;
            }else {
                return FilterReply.NEUTRAL;
            }
        }
        return reply;
    }
}