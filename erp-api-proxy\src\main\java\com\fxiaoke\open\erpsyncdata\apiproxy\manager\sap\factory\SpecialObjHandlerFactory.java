package com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.factory;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandler;
import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @Date: 9:58 2020/12/22
 * @Desc:
 */
@Component
public class SpecialObjHandlerFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    private Map<String, SpecialObjHandler> objHandlerMap = Maps.newHashMap();

    @PostConstruct
    private void init() {
        Map<String, ? extends SpecialObjHandler> map = applicationContext.getBeansOfType(SpecialObjHandler.class);
        objHandlerMap = map.values().stream().collect(Collectors.toMap(SpecialObjHandler::getObjApiName, val -> val));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public SpecialObjHandler getObjHandler(String objApiName) {
        return objHandlerMap.get(objApiName);
    }

}
