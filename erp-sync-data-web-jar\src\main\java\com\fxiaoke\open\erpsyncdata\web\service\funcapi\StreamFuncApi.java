package com.fxiaoke.open.erpsyncdata.web.service.funcapi;

import com.fxiaoke.open.erpsyncdata.admin.model.FuncApiContext;
import com.fxiaoke.open.erpsyncdata.admin.model.TriggerSyncByCrmData;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 集成流相关
 *
 * <AUTHOR> (^_−)☆
 */
@Service
public class StreamFuncApi  implements FuncApiService {
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;

    @FuncApiMethod
    public Result<TriggerSyncByCrmData.AllResult> triggerSyncByCrmData(FuncApiContext ctx, TriggerSyncByCrmData.Arg arg) {
        String tenantId = ctx.getTenantId();
        Integer userId = ctx.getUserId();
        List<List<String>> streamIds = arg.getStreamIds();
        if (streamIds == null || streamIds.isEmpty()) {
            return Result.newError("streamIds is empty");
        }
        List<String> allStreamIds = streamIds.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<StreamSimpleInfo> streamSimpleInfos = adminSyncPloyDetailDao.listSimpleById(tenantId, allStreamIds);
        //排序，先crm->erp,再erp->CRM
        streamSimpleInfos.sort(Comparator.comparingInt(v -> v.getSourceTenantType()));
        List<String> crmDataIdList = Splitter.on(",").splitToList(arg.getCrmDataIds());
        List<TriggerSyncByCrmData.SingleResult> singleResults = Lists.newArrayList();
        for (StreamSimpleInfo streamSimpleInfo : streamSimpleInfos) {
            if (streamSimpleInfo.getSourceTenantType() == 1) {
                for (String crmDataId : crmDataIdList) {
                    //crm往ERP方向
                    ErpIdArg erpIdArg = new ErpIdArg();
                    erpIdArg.setTenantId(tenantId);
                    erpIdArg.setObjAPIName(streamSimpleInfo.getCrmObjApiName());
                    erpIdArg.setDataId(crmDataId);
                    String destObjApiName = streamSimpleInfo.getErpObjApiName();
                    Result<Void> voidResult = adminSyncDataMappingService.syncSingletonData(erpIdArg, destObjApiName, DataReceiveTypeEnum.FUNCTION_TRIGGER);
                    singleResults.add(TriggerSyncByCrmData.SingleResult.builder()
                            .streamId(streamSimpleInfo.getStreamId())
                            .crmObjApiName(streamSimpleInfo.getCrmObjApiName())
                            .crmDataId(crmDataId)
                            .success(voidResult.isSuccess())
                            .errorMsg(voidResult.isSuccess() ? null : voidResult.getErrMsg())
                            .build());
                }
            } else {
                //erp往crm方向，需要先查询映射
                List<SyncDataMappingsEntity> mappingsEntities = syncDataMappingsDao.queryByDestDataIdList(tenantId, streamSimpleInfo.getErpObjApiName(), crmDataIdList, streamSimpleInfo.getCrmObjApiName());
                Set<String> notTriggerCrmDataIds = new HashSet<>(crmDataIdList);
                for (SyncDataMappingsEntity mappingEntity : mappingsEntities) {
                    //已经创建成功的才可以触发
                    if (mappingEntity.getIsCreated()) {
                        ErpIdArg erpIdArg = new ErpIdArg();
                        erpIdArg.setTenantId(tenantId);
                        erpIdArg.setObjAPIName(streamSimpleInfo.getErpObjApiName());
                        erpIdArg.setDataId(mappingEntity.getSourceDataId());
                        String destObjApiName = streamSimpleInfo.getCrmObjApiName();
                        Result<Void> voidResult = adminSyncDataMappingService.syncSingletonData(erpIdArg, destObjApiName, DataReceiveTypeEnum.FUNCTION_TRIGGER);
                        singleResults.add(TriggerSyncByCrmData.SingleResult.builder()
                                .streamId(streamSimpleInfo.getStreamId())
                                .crmObjApiName(streamSimpleInfo.getCrmObjApiName())
                                .crmDataId(mappingEntity.getDestDataId())
                                .success(voidResult.isSuccess())
                                .errorMsg(voidResult.isSuccess() ? null : voidResult.getErrMsg())
                                .build());
                        notTriggerCrmDataIds.remove(mappingEntity.getDestDataId());
                    }
                    //未获取映射给失败结果
                    for (String notTriggerCrmDataId : notTriggerCrmDataIds) {
                        singleResults.add(TriggerSyncByCrmData.SingleResult.builder()
                                .streamId(streamSimpleInfo.getStreamId())
                                .crmObjApiName(streamSimpleInfo.getCrmObjApiName())
                                .crmDataId(notTriggerCrmDataId)
                                .success(false)
                                .errorMsg("not found mapping")
                                .build());
                    }
                }
            }

        }
        return Result.newSuccess(new TriggerSyncByCrmData.AllResult(singleResults));
    }
}
