package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.RedisLockUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryInterfaceMonitorLogArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("queryInterfaceMonitorLog")
public class QueryInterfaceMonitorLogServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;
    @Autowired
    private RedisDataSource redisDataSource;

    private final static String queryInterfaceMonitorLogOnlyKey = "queryInterfaceMonitorLogOnlyKey_%s";
    private final static String queryInterfaceMonitorLogTimesKey = "queryInterfaceMonitorLogTimesKey_%s";
    private static final long SEVEN_DAYS_IN_MILLIS = 7 * 24 * 60 * 60 * 1000L;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        QueryInterfaceMonitorLogArg queryCrmInterfaceMonitorLogArg = JacksonUtil.fromJson(commonArg.getParams(), QueryInterfaceMonitorLogArg.class);

        if (queryCrmInterfaceMonitorLogArg.getStartTime() == null
                || queryCrmInterfaceMonitorLogArg.getEndTime() == null
                || queryCrmInterfaceMonitorLogArg.getStartTime() >= queryCrmInterfaceMonitorLogArg.getEndTime()
                || StringUtils.isAnyEmpty(queryCrmInterfaceMonitorLogArg.getDataCenterId(), queryCrmInterfaceMonitorLogArg.getPloyDetailId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        if (queryCrmInterfaceMonitorLogArg.getPageSize() > 10) {
            return Result.newError(ResultCodeEnum.QUERY_MAX_PAGE_SIZE_ERROR);
        }

        //查询间隔不超过7天
        if (Math.abs(queryCrmInterfaceMonitorLogArg.getEndTime() - queryCrmInterfaceMonitorLogArg.getStartTime()) > SEVEN_DAYS_IN_MILLIS) {
            return Result.newError(ResultCodeEnum.QUERY_MAX_TIME_INTERVAL_ERROR);
        }

        String requestId = TraceUtil.get();
        String onlyKey = String.format(queryInterfaceMonitorLogOnlyKey, tenantId);
        String timesKey = String.format(queryInterfaceMonitorLogTimesKey, tenantId);
        if (RedisLockUtils.tryGetDistributedLock(redisDataSource.get(), onlyKey, requestId, 60)) {
            try {
                if (!redisDataSource.isAllow(timesKey, 500L, 60L)) {
                    log.info("QueryInterfaceMonitorLogServiceImpl.executeLogic.limit,tenantId={}", tenantId);
                    //限速
                    return Result.newError(ResultCodeEnum.QUERY_CRM_INTERFACE_LOG_LIMIT);
                }
                Result<Page<ErpInterfaceMonitorResult>> pageResult = erpInterfaceMonitorService.queryInterfaceMonitorLogList(tenantId, queryCrmInterfaceMonitorLogArg, TraceUtil.getLocale());
                if (!pageResult.isSuccess()) {
                    return Result.copy(pageResult);
                }
                return Result.newSuccess(JacksonUtil.toJson(pageResult.getData()));
            } catch (Exception e) {
                log.warn("QueryInterfaceMonitorLogServiceImpl.executeLogic.error={}", e.getMessage());
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            } finally {
                RedisLockUtils.releaseDistributedLock(redisDataSource.get(), onlyKey, requestId);
            }
        } else {
            log.info("QueryInterfaceMonitorLogServiceImpl.executeLogic.exist,tenantId={}", tenantId);
            //只允许一个企业当前时间内调用一次
            return Result.newError(ResultCodeEnum.EXIST_QUERY_CRM_INTERFACE_LOG_REQUEST);
        }
    }
}
