{"type": "page", "title": "查询云星辰字段列表", "body": [{"type": "alert", "body": [{"type": "link", "href": "https://open.jdy.com/#/files/api/detail?index=3&categrayId=3cc8ee9a663e11eda5c84b5d383a2b93", "body": "点我去云星辰API文档", "blank": true}], "level": "info"}, {"type": "crud", "name": "lists", "api": "./listFields", "loadDataOnce": false, "autoFillHeight": true, "defaultParams": {"perPage": 50}, "primaryField": "id", "headerToolbar": ["reload", "export-excel"], "filter": {"mode": "horizontal", "body": [{"type": "group", "body": [{"id": "dcIdSelect", "name": "dcId", "label": "连接器", "type": "select", "size": "lg", "labelField": "dataCenterName", "valueField": "id", "selectFirst": true, "clearable": true, "source": {"method": "get", "url": "./listDcInfos", "autoRefresh": false}, "searchable": true, "multiple": false, "placeholder": "仅支持云星辰连接器", "showInvalidMatch": false}, {"type": "input-number", "label": "分页大小", "name": "perPage", "keyboard": true, "step": 5, "value": 50, "min": 10, "max": 100, "size": "lg"}]}, {"type": "input-text", "label": "请求地址", "name": "url", "placeholder": "请输入完整的请求地址", "description": "示例：https://api.kingdee.com/jdy/v2/arap/ar_credit", "required": true}, {"type": "input-text", "label": "查询字段", "name": "fieldNames", "placeholder": "默认包含id，请填写除id以外的字段，多个字段用英文逗号分隔", "description": "示例：bill_no,bill_status"}], "actions": [{"type": "submit", "label": "查询", "primary": true}], "feat": "Insert"}, "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "combineNum": 0}], "remark": null, "name": "queryList"}