package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service("splitHttpHeader")
public class SplitHttpHeaderServiceImpl implements CustomFunctionCommonService {

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {

        Map<String,Object> arg = JsonUtil.fromJson(commonArg.getParams(), Map.class);
        String requestUrl=arg.get("requestUrl").toString();

        // 1 创建okhttp客户端对象
        OkHttpClient client = new OkHttpClient.Builder()
          .connectTimeout(1, TimeUnit.MINUTES)
          .readTimeout(3,TimeUnit.MINUTES)
          .writeTimeout(2,TimeUnit.MINUTES)
          .build();
        // 2 request 默认是get请求
        Request.Builder builder = new Request.Builder().url(requestUrl);
        if (arg.get("requestHeader")!=null){
            Map<String,String>requestHeader= (Map<String, String>) arg.get("requestHeader");
            requestHeader.forEach((key,value)->builder.addHeader(key,value));
        }
        //设置请求方式，请求报文
        String method=arg.get("method")==null?"get":arg.get("method").toString();

        if ("post".equals(method)){
            String postData= (String) arg.get("postData");
            MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(JSON_TYPE, postData);
            builder.post(requestBody);
        }

        Request request =builder.build();

        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("trace tenantId:{}  arg:{} ",commonArg.getTenantId(),arg);
                return Result.newSystemError(I18NStringEnum.s30, response.message());
            }
            Headers requestHeader=response.headers();
            Map<String,Object>responseHeader=new HashMap<>();
            requestHeader.names().forEach(n->responseHeader.put(n,requestHeader.values(n)));
            return Result.newSuccess(JacksonUtil.toJson(responseHeader));
        } catch (IOException e) {
            log.error("调用接口异常:",e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

    }

}
