package com.fxiaoke.open.erpsyncdata.common.jetcache

import cn.hutool.core.date.DateUtil
import cn.hutool.core.lang.Pair
import com.alicp.jetcache.anno.SerialPolicy
import com.alicp.jetcache.support.*
import com.fasterxml.jackson.core.type.TypeReference
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemInfo
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo

/**
 * 测试encoder和decoder的性能
 *
 * 结论：fastJson2的性能也不差，但是会丢失泛型，容易出问题。
 * <AUTHOR> (^_−)☆
 */
class EncoderAndDecoderTest {

    static void main(String[] args) {
        def encodersAndDecoders = [
                Pair.of(Kryo5ValueEncoder.INSTANCE, Kryo5ValueDecoder.INSTANCE),
                Pair.of(Fastjson2ValueEncoder.INSTANCE, Fastjson2ValueDecoder.INSTANCE),
        ]
        for (final def pair in encodersAndDecoders) {
            AbstractValueDecoder d = (AbstractValueDecoder) pair.getValue();
            DecoderMap dm = new DecoderMap();
            dm.initDefaultDecoder();
            dm.register(SerialPolicy.IDENTITY_NUMBER_FASTJSON2, Fastjson2ValueDecoder.INSTANCE);
            d.setDecoderMap(dm);
        }
        def objs = [JacksonUtil.fromJson('[ { "name": "金蝶云苍穹" }, { "name": "金蝶KIS" }, { "name": "金蝶EAS" }, { "name": "金蝶K3 WISE" }, { "name": "金蝶管易云" }, { "name": "金蝶精斗云" }, { "name": "SAP-S/4 HANA" }, { "name": "SAP-ECC" }, { "name": "SAP Business One" }, { "name": "用友U8Cloud" }, { "name": "用友YonBIP" }, { "name": "用友EAI" }, { "name": "用友YonSuite" }, { "name": "DB-Oracle" }, { "name": "DB-MySql" }, { "name": "DB-PostgreSQL" }, { "name": "DB-SqlServer" }, { "name": "管家婆" }, { "name": "易订货" }, { "name": "NC" }, { "name": "畅捷通" }, { "name": "聚水潭" }, { "name": "销售易" }, { "name": "尘峰SCRM" }, { "name": "Salesforce" }, { "name": "泛微" }, { "name": "问卷星" }, { "name": "金数据" }, { "name": "自研" } ]', new TypeReference<List<SystemInfo>>() {
        }),
                    JacksonUtil.fromJson('{ "name": "default", "baseUrl": "http://**************:43454/hub", "outerConnectors": [ { "apiName": "Salesforce", "defaultName": "Salesforce", "moduleCode": "salesforce_data_sync_app", "systemName": "Salesforce", "iconUrl": "https://c1.sfdcstatic.com/content/dam/sfdc-docs/www/logos/logo-salesforce.svg" } ] }', new TypeReference<HubInfo>() {
                    })
        ]
        def callsNums = [1]
        callsNums.each { callsNum ->
            objs.eachWithIndex { def obj, int i ->
                encodersAndDecoders.each { pair ->
                    try {
                        def costPair = testEncodeAndDecode(pair, callsNum, obj)
                        println("test obj$i,${pair.getKey().getClass().simpleName},$callsNum,${DateUtil.formatBetween(costPair.getKey())},${DateUtil.formatBetween(costPair.getValue())}")
                    } catch (Exception e) {
                        println("test obj excpetion$i,${pair.getKey().getClass().simpleName},$callsNum,${e.getMessage()}")
                    }
                }
            }
        }
    }

    static Pair<Long, Long> testEncodeAndDecode(Pair pair, long testTimes, Object obj) {

        def encoder = pair.getKey() as AbstractValueEncoder
        def decoder = pair.getValue() as AbstractValueDecoder

        //预热不算
        def encodeVal = encoder.apply(obj)
        def decodeVal = decoder.apply(encodeVal)

        def t1 = System.currentTimeMillis()
        for (i in 0..<testTimes) {
            encoder.apply(obj)
        }
        def cost1 = System.currentTimeMillis() - t1
        decoder.apply(encodeVal)

        def t2 = System.currentTimeMillis()
        for (i in 0..<testTimes) {
            decoder.apply(encodeVal)
        }
        def cost2 = System.currentTimeMillis() - t2
        return Pair.of(cost1, cost2)
    }
}
