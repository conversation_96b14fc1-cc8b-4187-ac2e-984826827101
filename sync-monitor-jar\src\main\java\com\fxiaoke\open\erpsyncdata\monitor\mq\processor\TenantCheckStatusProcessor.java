package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.vo.TenantCheckStatusMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 17:44 2023/3/16
 * @Desc: Erp服务检活上报
 */
@Service
@Slf4j
public class TenantCheckStatusProcessor extends AbstractMonitorMqProcessor<TenantCheckStatusMsg> {
    @Autowired
    private MonitorReportManager monitorReportManager;

    public TenantCheckStatusProcessor() {
        super(MonitorType.TENANT_CHECK_STATUS, new TypeReference<TenantCheckStatusMsg>() {
        });
    }
    @Override
    void process(TenantCheckStatusMsg tenantCheckStatusMsg) {
        monitorReportManager.sendTenantCheckStatus2BizLog(tenantCheckStatusMsg);
    }
}
