package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;


import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReSyncDataNodeMsg;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.MongoStore;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/15
 * @Desc:
 */
@Slf4j
@Component
public class ReSyncDataNodeMsgDao {
    @Autowired
    private MongoStore mongoStore;

    private static final String f_id = "_id";
    private static final String f_tenantId = "tenantId";
    private static final String f_uniqueKey = "uniqueKey";
    private static final String f_sourceObjApiName = "sourceObjApiName";
    private static final String f_destObjApiName = "destObjApiName";
    private static final String f_sourceDataId = "sourceDataId";
    private static final String f_streamId = "streamId";

    private static final String f_reSyncTimeInterval = "reSyncTimeInterval";
    private static final String f_nextReSyncTime = "nextReSyncTime";
    private static final String f_tries = "tries";
    private static final String f_tryLimit = "tryLimit";
    private static final String f_syncDataEntity = "syncDataEntity";
    private static final String f_traceId = "traceId";
    private static final String f_locale = "locale";
    private static final String f_createTime = "createTime";
    private static final String f_updateTime = "updateTime";

    public void batchUpsertDataNodeMsgDoc(String tenantId, List<ReSyncDataNodeMsg> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        List<WriteModel<ReSyncDataNodeMsg>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (int i = 0; i < dataList.size(); i++) {
            ReSyncDataNodeMsg nodeMsg = dataList.get(i);
            UpdateOneModel<ReSyncDataNodeMsg> updateOneModel = new UpdateOneModel<>(this.updateBy(nodeMsg), this.upsert(nodeMsg), updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        MongoCollection<ReSyncDataNodeMsg> collection = this.getMongoCollection(tenantId);
        BulkWriteResult bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
    }
    public void batchUpdateDataNodeMsgDoc(String tenantId, List<ReSyncDataNodeMsg> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        List<WriteModel<ReSyncDataNodeMsg>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(false);
        for (int i = 0; i < dataList.size(); i++) {
            ReSyncDataNodeMsg nodeMsg = dataList.get(i);
            UpdateOneModel<ReSyncDataNodeMsg> updateOneModel = new UpdateOneModel<>(this.updateBy(nodeMsg), this.update(nodeMsg), updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        MongoCollection<ReSyncDataNodeMsg> collection = this.getMongoCollection(tenantId);
        BulkWriteResult bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
    }

    private Bson update(ReSyncDataNodeMsg message) {
        Document updateDoc = new Document();
        //更新
        updateDoc.append(f_nextReSyncTime, Objects.requireNonNull(message.getNextReSyncTime(), "nextReSyncTime cannot be null"))
                .append(f_tries, Objects.requireNonNull(message.getTries(), "tries cannot be null"))
                .append(f_updateTime, new Date());

        Document doc = new Document();
        doc.put("$set", updateDoc);
        return doc;
    }

    private Bson updateBy(ReSyncDataNodeMsg message) {
        Document result = new Document();
        result.put(f_uniqueKey, message.getUniqueKey());
        return result;
    }

    private Bson upsert(ReSyncDataNodeMsg message) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();
        Document addToSet = new Document();

        updateDoc.append(f_updateTime,new Date());

        if(message.getId()==null){
            setOnInsertDoc.append(f_id,new ObjectId());
        }else {
            setOnInsertDoc.append(f_id,message.getId());
        }
        //插入
        setOnInsertDoc
                .append(f_uniqueKey, Objects.requireNonNull(message.getUniqueKey(), "uniqueKey cannot be null"))
                .append(f_tenantId, Objects.requireNonNull(message.getTenantId(), "tenantId cannot be null"))
                .append(f_sourceObjApiName, Objects.requireNonNull(message.getSourceObjApiName(), "sourceObjApiName cannot be null"))
                .append(f_destObjApiName, Objects.requireNonNull(message.getDestObjApiName(), "destObjApiName cannot be null"))
                .append(f_sourceDataId, Objects.requireNonNull(message.getSourceDataId(), "sourceDataId cannot be null"))
                .append(f_streamId, Objects.requireNonNull(message.getStreamId(), "streamId cannot be null"))
                .append(f_reSyncTimeInterval, Objects.requireNonNull(message.getReSyncTimeInterval(), "reSyncTimeInterval cannot be null"))
                .append(f_nextReSyncTime, Objects.requireNonNull(message.getNextReSyncTime(), "nextReSyncTime cannot be null"))
                .append(f_tries, Objects.requireNonNull(message.getTries(), "tries cannot be null"))
                .append(f_tryLimit, Objects.requireNonNull(message.getTryLimit(), "tryLimit cannot be null"))
                .append(f_syncDataEntity, Objects.requireNonNull(message.getSyncDataEntity(), "syncDataEntity cannot be null"))
                .append(f_traceId, message.getTraceId())
                .append(f_locale, message.getLocale())
                .append(f_createTime, new Date());


        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        if (!addToSet.isEmpty()) {
            doc.put("$addToSet", addToSet);
        }
        return doc;
    }

    public Long deleteReSyncDataByStreamId(String tenantId, Set<String> streamIds) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.in(f_streamId, streamIds));
        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteMany(Filters.and(filters));
        return deleteResult.getDeletedCount();
    }

    public Long deleteReSyncDataByUniqueKey(String tenantId, List<String> uniqueKeyList) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_uniqueKey, uniqueKeyList));
        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteMany(Filters.and(filters));
        return deleteResult.getDeletedCount();
    }

    public MongoCollection<ReSyncDataNodeMsg> getMongoCollection(String tenantId) {
        return mongoStore.getOrCreateReSyncDataNodeMsgCollectionAndIndex(tenantId);
    }

    public List<ReSyncDataNodeMsg> listByTenantIdAndNextReSyncTime(String tenantId, Long nextReSyncTime, Integer offset, Integer limit) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.lt(f_nextReSyncTime, nextReSyncTime));
        FindIterable<ReSyncDataNodeMsg> list = this.getMongoCollection(tenantId).find(Filters.and(filters)).skip(offset).limit(limit);
        return Lists.newArrayList(list);
    }

    public Long deleteByObjectId(String tenantId, List<ObjectId> needDeleteIdList) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_id, needDeleteIdList));
        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteMany(Filters.and(filters));
        return deleteResult.getDeletedCount();
    }
    public List<ReSyncDataNodeMsg> getByUniqueKeys(String tenantId, List<String> uniqueKeys) {
        if(CollectionUtils.isEmpty(uniqueKeys)){
            return Lists.newArrayList();
        }
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_uniqueKey, uniqueKeys));
        FindIterable<ReSyncDataNodeMsg> list = this.getMongoCollection(tenantId).find(Filters.and(filters)).skip(0).limit(uniqueKeys.size());
        return Lists.newArrayList(list);
    }
}
