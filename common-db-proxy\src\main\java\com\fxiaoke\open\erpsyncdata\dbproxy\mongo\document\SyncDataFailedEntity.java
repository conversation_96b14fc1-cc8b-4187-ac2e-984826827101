package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023.07.25
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
public class SyncDataFailedEntity {
    @BsonId
    private ObjectId id;
    private String tenantId;
    private String dataCenterId;
    private String ployDetailId;

    /**
     * 源数据ID
     */
    private String sourceDataId;

    /**
     * trace id
     */
    private String traceId;

    private Date createTime;
    private Date updateTime;
}
