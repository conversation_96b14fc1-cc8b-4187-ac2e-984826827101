package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import java.io.ByteArrayOutputStream;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

/**
 * 计算Java对象大小工具类
 * <AUTHOR>
 * @date 2021/6/9
 */
public class JavaObjectUtils {
    /**
     * 通过序列化的方式来计算java对象在内存中的大小，计算结果以字节表示
     * @param object
     * @return
     * @deprecated 这个方法计算序列化的值，对象实际内存占用预估并不准确。改用jdk8的api。
     */
    @Deprecated
    public static int computeObjectSizeInByte(Object object) {
        if (object == null) return 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream(1 * 1024 * 1024);//初始化一个1MB的缓存区
        try {
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(object);
            oos.flush();
            bos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return bos.size();
    }

    public static double computeObjectSizeInMB(Object object) {
        double sizeInMB = computeObjectSizeInByte(object) / 1024.0 / 1024.0;
        BigDecimal bigDecimal = new BigDecimal(sizeInMB,new MathContext(5, RoundingMode.HALF_UP));
        return bigDecimal.doubleValue();
    }

    public static double computeObjectSizeInKB(Object object) {
        double sizeInMB = computeObjectSizeInByte(object) / 1024.0;
        BigDecimal bigDecimal = new BigDecimal(sizeInMB,new MathContext(5, RoundingMode.HALF_UP));
        return bigDecimal.doubleValue();
    }
}
