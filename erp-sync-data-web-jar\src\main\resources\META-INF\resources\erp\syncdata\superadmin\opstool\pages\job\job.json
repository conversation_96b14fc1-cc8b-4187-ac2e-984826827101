{"type": "page", "body": [{"type": "form", "mode": "horizontal", "api": "../scanErpTemp", "title": "以创建时间扫描临时库未触发数据，并重置最后轮询时间", "body": [{"label": "tenantId", "type": "input-text", "name": "tenantId"}, {"label": "开始时间", "type": "input-number", "placeholder": "时间戳", "name": "beginTime"}, {"label": "结束时间", "type": "input-number", "placeholder": "时间戳", "name": "endTime"}]}, {"type": "form", "mode": "horizontal", "api": "../copyErpSyncConfig", "title": "复制企业配置", "body": [{"label": "源tenantId", "type": "input-text", "name": "from"}, {"label": "目标tenantId", "type": "input-text", "name": "to"}]}, {"type": "form", "mode": "horizontal", "api": "../sendDoDispatchMq", "title": "发送数据到分发框架", "body": [{"label": "tenantId", "type": "input-text", "name": "tenantId"}, {"label": "eventDataList", "type": "textarea", "desc": "示例：[ { \"sourceData\": { \"_id\": \"6284a30e5bfa1b00017a1a8f\", \"object_describe_api_name\": \"AccountObj\", \"tenant_id\": \"83952\" }, \"sourceEventType\": 3, \"sourceTenantType\": 1 }, { \"sourceData\": { \"_id\": \"6284a3905bfa1b00017a211c\", \"object_describe_api_name\": \"AccountObj\", \"tenant_id\": \"83952\" }, \"sourceEventType\": 3, \"sourceTenantType\": 1 } ]", "name": "eventDataList"}]}]}