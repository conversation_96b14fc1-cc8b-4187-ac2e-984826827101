<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpUserOperationLogDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        operation_type,
        operation_user,
        user_phone,
        obj_api_name,
        arg,
        result,
        operation_time
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpUserOperationLogEntity">
        <!--@mbg.generated-->
        <!--@Table erp_connect_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
        <result column="operation_user" jdbcType="VARCHAR" property="operationUser"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"/>
        <result column="obj_api_name" jdbcType="LONGVARCHAR" property="objApiName"/>
        <result column="arg" jdbcType="LONGVARCHAR" property="arg"/>
        <result column="result" jdbcType="LONGVARCHAR" property="result"/>
        <result column="operation_time" jdbcType="BIGINT" property="operationTime"/>

    </resultMap>

</mapper>