package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjInterfaceCheckedEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * ERP对象接口选择dao
 *
 * <AUTHOR>
 * @date 2023.08.02
 */
@Repository
@ManagedTenantReplace
public interface ErpObjInterfaceCheckedDao extends BaseTenantDao<ErpObjInterfaceCheckedEntity, ErpObjInterfaceCheckedDao> {
    ErpObjInterfaceCheckedEntity findData(@Param("tenantId") String tenantId,
                                          @Param("dataCenterId") String dataCenterId,
                                          @Param("objApiName") String objApiName,
                                          @Param("interfaceUrl") ErpObjInterfaceUrlEnum interfaceUrl);
}