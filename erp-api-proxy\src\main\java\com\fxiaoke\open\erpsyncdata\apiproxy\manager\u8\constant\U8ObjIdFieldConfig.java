package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置从对象的id字段
 */
public class U8ObjIdFieldConfig {

  private final static String DEFAULT_MASTER_ID="code";

  private final static String DEFAULT_ENTRY_ID="iid";

//  private static Map<String,String> config=new HashMap<String, String>(){{
//
//    //客户地址
//    put("customer.addresses","caddcode");
//    //客户银行
//    put("customer.banks","caccountnum");
//    //客户开票
//    put("customer.invoicecustomers","cinvoicecompany");
//    //客户员工
//    put("customer.users","user_id");
//    //客户管理维度
//    put("customer.auths","privilege_type,privilege_id");
//
//    //客户订单明细
//    put("saleorder.entry","inventorycode,rowno");
//
//    //销售出库单
//    put("saleout.entry","inventorycode,rowno");
//
//    //收款单
//    put("accept","vouchcode,vouchtype");
//
//    //其他入库单
//    put("otherin.entry","inventorycode,rowno");
//
//    //其他出库单
//    put("otherout.entry","inventorycode,rowno");
//
//  }};


  public static String getApiName(String apiName) {
    if (ConfigCenter.U8_MASTERID_STRUCTURE_MAP.containsKey(apiName)){
      return ConfigCenter.U8_MASTERID_STRUCTURE_MAP.get(apiName);
    }
    if (apiName.contains(".")){
      return DEFAULT_ENTRY_ID;
    }
    return DEFAULT_MASTER_ID;
  }

}
