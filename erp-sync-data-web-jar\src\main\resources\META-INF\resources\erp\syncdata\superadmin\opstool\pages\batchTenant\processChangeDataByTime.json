{"type": "page", "title": "按时间补刷数据", "body": [{"type": "form", "mode": "horizontal", "api": {"method": "get", "url": "../processChangeDataByTime?tenantIds=${tenantIds}&startTime=${startTime}&endTime=${endTime}&ignoreErp=${ignoreErp}&ignoreCrm=${ignoreCrm}&resetAllErp=${resetAllErp}"}, "body": [{"label": "tenantIds", "type": "input-text", "placeholder": "企业id以 , 分隔", "name": "tenantIds"}, {"label": "开始时间", "type": "input-datetime", "placeholder": "开始补刷数据的时间", "name": "startTime"}, {"label": "开始时间", "type": "input-datetime", "placeholder": "开始补刷数据的时间", "name": "endTime"}, {"option": "不同步ERP->CRM", "type": "checkbox", "name": "ignoreErp"}, {"option": "不同步CRM->ERP", "type": "checkbox", "name": "ignoreCrm"}, {"option": "更新所有ERP对象", "type": "checkbox", "name": "resetAllErp"}, {"type": "static-json", "name": "data", "visibleOn": "typeof data !== 'undefined'", "label": "失败的企业"}]}]}