package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.api.model.message.content.TextInfo;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ReSyncDataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReSyncDataNodeMsg;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SimpleSyncDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg;
import com.fxiaoke.paasauthrestapi.result.RoleUserResult;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.bson.Document;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/1/5 18:56
 * @desc 数据同步的状态，聚合通知
 * @Version 1.0
 */
@Component
@Slf4j
public class DataSyncNotifyManager implements InitializingBean {

    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private PaasAuthService paasAuthService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ErpFieldMappingManager erpFieldMappingManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private FieldDbManager fieldDbManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ReSyncDataNodeMsgDao reSyncDataNodeMsgDao;

    private LocalDispatcherUtil<SimpleSyncDataArg> localDispatcherUtil;

    @Override
    public void afterPropertiesSet() {
        localDispatcherUtil = new LocalDispatcherUtil<>((key, list) -> {
            //数据结构由一个tenant_ploy的企业集成集合与以企业集成集合的value做为key,记录一段时间内同步过的数据id+score(time)
            SyncStatusMessageArg syncStatusMessageArg = generateTenantIdInfo(key);
            sendSyncMessageToUser(list, syncStatusMessageArg);
        });
        localDispatcherUtil.setBatchProcessTimeLimitInSecond(ConfigCenter.NOTIFY_DELAY_TIME_SECONDS);//delay 5分钟
    }

    /**
     * 数据进本地聚合框架
     *
     * @param tenantId
     * @param syncDataEntityMap
     */
    public void pushDataToNotify(String tenantId, Collection<SyncDataEntity> syncDataEntityMap) {

        try {
            for (SyncDataEntity dataEntity : syncDataEntityMap) {
                TraceUtil.initTrace(dataEntity.getSyncLogId());

                Triple triple = new ImmutableTriple(tenantId, dataEntity.getSourceObjectApiName(), dataEntity.getDestObjectApiName());
                String syncPloyDetailSnapshotId = dataEntity.getSyncPloyDetailSnapshotId();
                SyncPloyDetailSnapshotEntity entryBySnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, syncPloyDetailSnapshotId);
                if (!entryBySnapshotEntity.isContinueNotify()) {//没有通知节点
                    return;
                }
                if (!dataEntity.getSourceObjectApiName().equals(entryBySnapshotEntity.getSourceObjectApiName())) {
                    //从对象的不通知
                    continue;
                }
                int failedReturnInt = SyncDataStatusEnum.isSyncDataStatusReturnInt(dataEntity.getStatus());
                if (failedReturnInt == SyncStatusEnum.FAILED.getStatus() && needPassNotice(dataEntity, entryBySnapshotEntity)) {//如果失败的，会重试也不通知
                    continue;
                }
                IntegrationStreamNodesData.NotifyComplementNode notifyComplementNode = entryBySnapshotEntity.getSyncPloyDetailData().getIntegrationStreamNodes().getNotifyComplementNode();
                //判断状态
                Boolean needNotifySyncDataStatus=notifyComplementNode.getNotifyStatus().contains(failedReturnInt);
                if (!needNotifySyncDataStatus && notNeedNotifyOther(dataEntity, notifyComplementNode)) {
                    //dataEntity的状态参考 SyncDataStatusEnum
                    return;
                }

                Integer sourceTenantType = entryBySnapshotEntity.getSyncPloyDetailData().getSourceTenantType();
                //判断数据是不是在历史任务存在。是的话，不通知
                if (sourceTenantType.equals(TenantTypeEnum.ERP.getType())) {
                    ErpIdArg erpIdArg = new ErpIdArg();
                    String dataId = dataEntity.getSourceDataId();
                    String sourceObjApiName = dataEntity.getSourceObjectApiName();
                    String dataCenterId = entryBySnapshotEntity.getSyncPloyDetailData().getSourceDataCenterId();
                    erpIdArg.setDataId(dataId);
                    erpIdArg.setObjAPIName(sourceObjApiName);
                    erpIdArg.setTenantId(tenantId);
                    ErpObjectRelationshipEntity relation = fieldDbManager.getRelation(tenantId, sourceObjApiName);
                    String realObjApiName = relation.getErpRealObjectApiname();
                    fieldDbManager.convertIdArg2Erp(erpIdArg);
                    //这里用真实apiName
                    erpIdArg.setObjAPIName(realObjApiName);
                    boolean usedIdQuery = fieldDbManager.hasUsedIdQuery(erpIdArg, dataCenterId);
                    Document erpObjDataById = null;
                    if (usedIdQuery) {
                        erpObjDataById = erpTempDataDao.getErpObjDataById(tenantId, dataCenterId, realObjApiName, dataId);
                    } else {
                        erpObjDataById = erpTempDataDao.getErpObjDataByNum(tenantId, dataCenterId, realObjApiName, dataId);
                    }
                    if (ObjectUtil.isNotEmpty(erpObjDataById) && CollectionUtils.isNotEmpty(erpObjDataById.getList("task_num", String.class))) {
                        List<String> taskNum = erpObjDataById.getList("task_num", String.class);
                        boolean ploy_auto_query = taskNum.stream().allMatch(str -> str.equals("ploy_auto_query"));
                        if (!ploy_auto_query) {
                            return;
                        }
                    }
                }

                //表达式计算
                if (StringUtils.isNotEmpty(notifyComplementNode.getNotifyConditionAviator())) {
                    String expression = notifyComplementNode.getNotifyConditionAviator();
                    Map<String, Object> envMap = Maps.newHashMap();
                    envMap.put("remark", dataEntity.getRemark());
                    boolean executeResult = AviatorUtils.normalConditionValid(expression, envMap, true);
                    if (!executeResult) {
                        log.info("notify data not match condition:{}", dataEntity);
                        return;
                    }
                }
                //按照单条数据的人员列表，进行拆分
                Set<Integer> employees = Sets.newHashSet();
                if (CollectionUtils.isNotEmpty(notifyComplementNode.getNotifyEmployees())) {
                    employees.addAll(notifyComplementNode.getNotifyEmployees());
                }
                if (CollectionUtils.isNotEmpty(notifyComplementNode.getNotifyRoles())) {
                    com.fxiaoke.paasauthrestapi.common.data.HeaderObj headerObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(Integer.valueOf(tenantId));
                    for (String roleCode : notifyComplementNode.getNotifyRoles()) {
                        //角色
                        try {
                            com.fxiaoke.paasauthrestapi.common.result.Result<RoleUserResult> roleUserRes = paasAuthService.roleUser(headerObj, new RoleUserArg("CRM", Integer.valueOf(tenantId), -10000, roleCode));
                            if (roleUserRes.getErrCode() != 0) {
                                log.warn("find role user failed,{},{}", roleCode, roleUserRes);
                            } else {
                                roleUserRes.getResult().getUsers().stream().mapToInt(Integer::parseInt).forEach(employees::add);
                            }
                        } catch (Exception e) {
                            log.error("find role user error", e);
                        }
                    }
                }
                //数据变量
                if (CollectionUtils.isNotEmpty(notifyComplementNode.getDataRelatedOwner())) {
                    String dataCenterId = entryBySnapshotEntity.getSyncPloyDetailData().getSourceDataCenterId();
                    //判断是否是crm方向，目前crm默认 last_modified_by created_by owner

                    if (sourceTenantType.equals(TenantTypeEnum.CRM.getType())) {
                        dataCenterId = entryBySnapshotEntity.getSyncPloyDetailData().getDestDataCenterId();
                        GetByIdArg getByIdArg = new GetByIdArg();
                        getByIdArg.setDescribeApiName(dataEntity.getSourceObjectApiName());
                        getByIdArg.setDataId(dataEntity.getSourceDataId());
                        getByIdArg.setSelectFields(IntegrationStreamNodesData.GET_CRM_DATA_VARIABLES);
                        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId, i18NStringManager);
                        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> crmDataOwner = objectDataServiceV3.getById(headerObj, getByIdArg);
                        if (crmDataOwner.isSuccess() && crmDataOwner.getData() != null && crmDataOwner.getData().getObjectData() != null) {
                            for (String empField : notifyComplementNode.getDataRelatedOwner()) {
                                //需要再查询一遍数据
                                Object crmReceiverData = crmDataOwner.getData().getObjectData().get(empField);
                                if (crmReceiverData != null && crmReceiverData instanceof List && ((List) crmReceiverData).size() > 0) {
                                    employees.add(Integer.valueOf(((List) crmReceiverData).get(0).toString()));
                                }
                            }
                        }
                    } else {
                        //erp方向
                        List<Object> erpReceiverAccount = Lists.newArrayList();
                        for (String empField : notifyComplementNode.getDataRelatedOwner()) {
                            Object erpObjectData = dataEntity.getSourceData().get(empField);
                            if (ObjectUtil.isNotEmpty(erpObjectData)) {
                                erpReceiverAccount.add(erpObjectData);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(erpReceiverAccount)) {
                            //批量转换成crm的账号
                            List<Integer> crmEmpAccount = Lists.newArrayList();
                            try {
                                for (Object empValue : erpReceiverAccount) {
                                    if (empValue instanceof List) {
                                        List<Integer> collect = (List<Integer>) ((List) empValue).stream().map(v -> Integer.valueOf(v.toString())).collect(Collectors.toList());
                                        crmEmpAccount.addAll(collect);
                                    } else {
                                        crmEmpAccount.add((Integer) empValue);
                                    }
                                }
                                employees.addAll(crmEmpAccount);
                            } catch (Exception e) {
                                //可能出现预处理转换crm账号失败的情况。
                                log.info(" notify transefer exception :{},{}", e.getMessage(), TraceUtil.get());
                                List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities = erpFieldMappingManager.listByErpIds(tenantId, dataCenterId, ErpFieldTypeEnum.employee, erpReceiverAccount);
                                Set<Integer> convertData = erpFieldDataMappingEntities.stream().map(ErpFieldDataMappingEntity::getFsDataId).map(item -> Integer.valueOf(item)).collect(Collectors.toSet());
                                employees.addAll(convertData);
                            }
                        }
                    }
                }
                log.info("createRedisData:{}.", employees.size());
                if (CollectionUtils.isEmpty(employees) || (employees.size() == 1 && employees.contains(-10000))) {
                    return;
                }
                employees.remove(-10000);
                List<SimpleSyncDataArg> redisData = createRedisData(needNotifySyncDataStatus,dataEntity, employees,notifyComplementNode);
                log.info("data sync notigy list:{}", JSONObject.toJSONString(redisData));
                localDispatcherUtil.produceDataBatch(JSONObject.toJSONString(triple), redisData);


            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        } finally {
            TraceUtil.removeTrace();
        }

    }

    private boolean notNeedNotifyOther(SyncDataEntity dataEntity, IntegrationStreamNodesData.NotifyComplementNode notifyComplementNode) {
        if (notifyComplementNode.getNeedNotifyReverseWrite2CrmFailed() != null && notifyComplementNode.getNeedNotifyReverseWrite2CrmFailed()
                && dataEntity.getData() != null && dataEntity.getData().getReverseWrite2CrmFailed() != null && dataEntity.getData().getReverseWrite2CrmFailed()) {
            return false;
        }
        if (notifyComplementNode.getNeedNotifyAfterFuncFailed() != null && notifyComplementNode.getNeedNotifyAfterFuncFailed() && dataEntity.getData() != null
                && dataEntity.getData().getAfterFuncFailed() != null && dataEntity.getData().getAfterFuncFailed()) {
            return false;
        }
        return true;
    }

    private boolean needPassNotice(SyncDataEntity dataEntity, SyncPloyDetailSnapshotEntity snapshot) {//是否需要过滤通知，等待重试完成才通知
        SyncPloyDetailData syncPloyDetailData = snapshot.getSyncPloyDetailData();
        if (syncPloyDetailData.getIntegrationStreamNodes() == null || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode() == null
                || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTimeInterval() == null
                || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTopLimit() == null) {//没有重试组件
            return false;
        }
        //有重试组件
        String uniqueKey = ReSyncDataNodeMsg.getMsgUniqueKey(dataEntity.getTenantId(), snapshot.getSyncPloyDetailId(), dataEntity.getSourceDataId());
        List<ReSyncDataNodeMsg> msgs = reSyncDataNodeMsgDao.getByUniqueKeys(dataEntity.getTenantId(), Lists.newArrayList(uniqueKey));
        if (CollectionUtils.isNotEmpty(msgs)) {//重试表存在数据
            ReSyncDataNodeMsg reSyncDataNodeMsg = msgs.get(0);
            if (reSyncDataNodeMsg.getTries() < reSyncDataNodeMsg.getTryLimit()) {
                return true;
            } else {
                return false;
            }
        } else {//重试表不存在数据
            if (dataEntity.getDataReceiveType() != null && dataEntity.getDataReceiveType() == DataReceiveTypeEnum.RE_SYNC_NODE.getType()) {//重试发起,需要通知，是最后一次重试了
                return false;
            } else {//非重试发起,不需要通知，后续会进入重试mongo
                return true;
            }
        }
    }


    public SyncStatusMessageArg generateTenantIdInfo(String tenantApiName) {
        //SYNC_DATA_NOTIFY_企业id_sourceApiName_destApiName
        log.info("generateTenantIdInfo key:{}", tenantApiName);
        MutableTriple<String, String, String> triple = JSONObject.parseObject(tenantApiName, MutableTriple.class);
        String tenantId = triple.getLeft();
        String sourceApiName = triple.getMiddle();
        String destApiName = triple.getRight();
        SyncPloyDetailEntity entityByTenantIdAndObjApiName = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, sourceApiName, destApiName);
        //源数据ERP
        boolean isSourceErp = entityByTenantIdAndObjApiName.getSourceTenantType().equals(TenantTypeEnum.CRM.getType()) ? false : true;
        String dataCenterId = isSourceErp ? entityByTenantIdAndObjApiName.getSourceDataCenterId() : entityByTenantIdAndObjApiName.getDestDataCenterId();

        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        SyncStatusMessageArg syncStatusMessageArg = new SyncStatusMessageArg();
        syncStatusMessageArg.setTenantId(tenantId);
        syncStatusMessageArg.setConnInfoName(connectInfoEntity.getDataCenterName());

        syncStatusMessageArg.setSrcObjectName(getObjectName(tenantId, dataCenterId, entityByTenantIdAndObjApiName.getSourceObjectApiName(), isSourceErp));
        syncStatusMessageArg.setEndTime(System.currentTimeMillis());
        //减掉聚合时间
        syncStatusMessageArg.setStartTime(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5));
        String direction = entityByTenantIdAndObjApiName.getSourceTenantType().equals(TenantTypeEnum.CRM.getType()) ? "CRM->" + connectInfoEntity.getDataCenterName() : connectInfoEntity.getDataCenterName() + "->CRM";
        syncStatusMessageArg.setSyncDirection(direction);
        if (entityByTenantIdAndObjApiName.getIntegrationStreamNodes() != null && entityByTenantIdAndObjApiName.getIntegrationStreamNodes().getNotifyComplementNode() != null) {
            syncStatusMessageArg.setNotifyType(entityByTenantIdAndObjApiName.getIntegrationStreamNodes().getNotifyComplementNode().getNotifyType());
        }
        return syncStatusMessageArg;

    }

    private String getObjectName(String tenantId, String dataCenterId, String apiName, boolean erp) {
        String erpObjName = "";
        if (erp) {
            erpObjName = erpObjManager.getErpObjName(tenantId, dataCenterId, apiName);
        } else {
            HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
            com.fxiaoke.crmrestapi.common.result.Result<ListObjectDescribeResult> result = objectDescribeService.list(headerObj, false, Lists.newArrayList(apiName));
            if (!result.isSuccess()) {
                return "";
            }
            List<ObjectDescribe> describe = result.getData().getDescribe();
            erpObjName = describe.get(0).getDisplayName();
        }
        return erpObjName;
    }


    private List<SimpleSyncDataArg> createRedisData(Boolean needNotifySyncDataStatus,SyncDataEntity syncDataEntity, Set<Integer> empSet,IntegrationStreamNodesData.NotifyComplementNode notifyComplementNode) {
        log.info("createRedis data:{}.{}", syncDataEntity, JSONObject.toJSONString(empSet));
        StringBuilder msg= new StringBuilder();
        if(needNotifySyncDataStatus){
            msg.append(syncDataEntity.getRemark());
        }
        Boolean reverseWrite2CrmFailed=null,afterFuncFailed=null;
        if (notifyComplementNode.getNeedNotifyReverseWrite2CrmFailed() != null && notifyComplementNode.getNeedNotifyReverseWrite2CrmFailed()
                && syncDataEntity.getData() != null && syncDataEntity.getData().getReverseWrite2CrmFailed() != null
                && syncDataEntity.getData().getReverseWrite2CrmFailed()) {
            if(msg.length()>0){
                msg.append(".");
            }
            msg.append(syncDataEntity.getData().getReverseWrite2CrmFailedRemark());
            reverseWrite2CrmFailed=true;
        }
        if (notifyComplementNode.getNeedNotifyAfterFuncFailed() != null && notifyComplementNode.getNeedNotifyAfterFuncFailed() && syncDataEntity.getData() != null
                && syncDataEntity.getData().getAfterFuncFailed() != null && syncDataEntity.getData().getAfterFuncFailed()) {
            if(msg.length()>0){
                msg.append(".");
            }
            msg.append(syncDataEntity.getData().getAfterFuncFailedRemark());
            afterFuncFailed=true;
        }
        List<SimpleSyncDataArg> pushDataLists = Lists.newArrayList();
        // 根据集成流条件拆分接收者，存储在redis里面的是按接收人拆分后的信息。
        for (Integer receiver : empSet) {
            SimpleSyncDataArg dataArg = BeanUtil.copy(syncDataEntity, SimpleSyncDataArg.class);
            dataArg.setReverseWrite2CrmFailed(reverseWrite2CrmFailed);
            dataArg.setAfterFuncFailed(afterFuncFailed);
            dataArg.setRemark(msg.toString());
            dataArg.setSourceDataName(syncDataEntity.getSourceData().getName());
            dataArg.setReceivers(receiver);
            pushDataLists.add(dataArg);
        }
        return pushDataLists;
    }

    public void sendSyncMessageToUser(List<SimpleSyncDataArg> syncDataString, SyncStatusMessageArg syncStatusMessageArg) {
        //聚合人员数据
        log.info("syncDataString:{}", JSONObject.toJSONString(syncDataString));
        Map<Integer, List<SimpleSyncDataArg>> receiverUserMap = syncDataString.stream().collect(Collectors.groupingBy(SimpleSyncDataArg::getReceivers));
        //组装发布消息
        for (Integer received : receiverUserMap.keySet()) {
            List<SimpleSyncDataArg> simpleSyncDataArgs = receiverUserMap.get(received);
            Map<Integer, List<SyncStatusMessageArg.SyncDataStatusMessage>> statusAllList = simpleSyncDataArgs.stream().map(item -> {
                SyncStatusMessageArg.SyncDataStatusMessage syncDataStatusMessage = new SyncStatusMessageArg.SyncDataStatusMessage();
                String dataName = StringUtils.isNotEmpty(item.getSourceDataName()) ? item.getSourceDataName() : item.getSourceDataId();
                syncDataStatusMessage.setDataId(item.getSourceDataId());
                syncDataStatusMessage.setDataName(dataName);
                syncDataStatusMessage.setReverseWrite2CrmFailed(item.getReverseWrite2CrmFailed());
                syncDataStatusMessage.setAfterFuncFailed(item.getAfterFuncFailed());
                syncDataStatusMessage.setStatus(SyncDataStatusEnum.isSyncDataStatusReturnInt(item.getStatus()));
                syncDataStatusMessage.setStatusMessage(SyncStatusEnum.getNameByStatus(SyncDataStatusEnum.isSyncDataStatusReturnInt(item.getStatus())));
                syncDataStatusMessage.setRemark(item.getRemark());
                return syncDataStatusMessage;
            }).collect(Collectors.groupingBy(item -> item.getStatus()));

            syncStatusMessageArg.setSuccessList(statusAllList.get(SyncStatusEnum.SUCCESS.getStatus()));
            syncStatusMessageArg.setErrorList(statusAllList.get(SyncStatusEnum.FAILED.getStatus()));
            Result<List<TextInfo>> messageResult = notificationService.generateDataSyncResult(syncStatusMessageArg);
            notificationService.sendErpSyncDataAppMultiNotice(messageResult.getData(),
                    syncStatusMessageArg.getTenantId(),
                    Lists.newArrayList(received),
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager, null, syncStatusMessageArg.getTenantId()),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL,syncStatusMessageArg.getNotifyType());
        }

    }

}
