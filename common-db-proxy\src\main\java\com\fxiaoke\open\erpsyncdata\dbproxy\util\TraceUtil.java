package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

/**
 * trace 工具类
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/9
 */
@Slf4j
@Component
public class TraceUtil {

    private static Function<String, String> funcEiTEa = v -> v;

    private static Function<String,String> funcEiTLocale = v -> "zh-CN";

    @Autowired
    public void setFuncEiTEa(EIEAConverter eieaConverter) {
        if (eieaConverter != null) {
            funcEiTEa = v -> {
                try {
                    v = eieaConverter.enterpriseIdToAccount(Integer.parseInt(v));
                } catch (Exception e) {
                    log.warn("trace convert 2 ea exception:{}", v, e);
                }
                return v;
            };
        }
    }


    @Autowired
    public void setFuncEiTEa(I18NStringManager i18NStringManager) {
        if (i18NStringManager != null) {
            funcEiTLocale = v -> i18NStringManager.getDefaultLang(v);
        }
    }

    public static String get() {
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        return traceId;
    }

    /**
     * 如果原来有traceid，会移除
     *
     * @param parentId
     */
    public static void initTrace(String parentId) {
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (!Objects.equals(parentId, traceId)) {
            //不一样才替换，并且打一条日志，防止不知道换了。
            if (StrUtil.isNotEmpty(traceId)) {
                //原traceId不为空打一条日志
                log.info("trans trace id,old:{},new:{}", traceId, parentId);
            }
            context.setTraceId(parentId);
        }
    }

    /**
     * 增加年月日的traceId
     *
     * @param tenantId
     */
    public static String initTraceWithFormat(String tenantId) {
        return initTraceWithUser(tenantId, -10000, "J-E.%s.%s-erp" + TimeUtil.hms());
    }

    public static void initColorLogEi(String currentEi, TenantConfigurationManager tenantConfigurationManager) {
        Set<String> colorEiLogSet = tenantConfigurationManager.getColorLogTenantIds();
        /**根据ei染色, 会打印该ei所有level的日志**/
        if (colorEiLogSet.contains(currentEi)) {
            //区别下其他MDC重写了colorized
            ThreadContext.put("colorized_tenant", "1");
        } else {
            ThreadContext.remove("colorized_tenant");
        }
    }

    public static void setEi(String tenantId) {
        TraceContext traceContext = TraceContext.get();
        if (!ObjectUtil.equals(traceContext.getEi(), tenantId)) {
            traceContext.setEi(tenantId);
        }
        if (StrUtil.isEmpty(traceContext.getLocale())) {
            traceContext.setLocale(funcEiTLocale.apply(tenantId));
        }
    }

    public static String initTraceWithUser(String tenantId,Integer userId, String format) {
        setEi(tenantId);
        String ea = funcEiTEa.apply(tenantId);
        // 我知道以上代码有点迷惑，但其实只是想最大程度减少不必要的调用。
        String traceId = String.format(format, ea, userId);
        initTrace(traceId);
        return traceId;
    }

    public static String addChildTrace(String childId) {
        //对子id增加urlEncode，防止放到header异常
        childId = encodeTrace(childId);
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "anon";
        }
        if (traceId.contains(childId)) {
            //已经包含了将不再设置,防止出现循环调用的情况
            return traceId;
        }
        traceId = traceId + "-" + childId;
        context.setTraceId(traceId);
        return traceId;
    }

    public static void addConsumer(String consumer) {
        TraceContext context = TraceContext.get();
        context.setConsumer(consumer);
    }

    public static void addServer(String server) {
        TraceContext context = TraceContext.get();
        context.setConsumer(server);
    }

    public static String removeChildTrace(String childId) {
        //对子id增加urlEncode，防止放到header异常
        childId = encodeTrace(childId);
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "anon";
        }
        String remove = "-" + childId;
        if (!traceId.endsWith(remove)) {
            log.warn(" trace id invalid，context:{},traceId:{}", context, traceId);
        }
        traceId = StringUtils.removeEnd(traceId, remove);
        context.setTraceId(traceId);
        return traceId;
    }

    public static void removeTrace() {
        TraceContext.remove();
    }

    /**
     * encode编码防止放到header异常
     */
    private static String encodeTrace(String childId) {
        try {
            childId = URLEncoder.encode(childId, "utf-8").replaceAll("%", "");
            if (childId.length() > 64) {
                childId = childId.substring(0, 64);
            }
        } catch (UnsupportedEncodingException e) {
            log.error("encode error", e);
        }
        return childId;
    }


    public static void setLocale(String locale) {
        TraceContext.get().setLocale(locale);
    }

    public static String getLocale() {
        return TraceContext.get().getLocale();
    }

    public static void cleanLocale() {
        TraceContext.get().setLocale(null);
    }
}
