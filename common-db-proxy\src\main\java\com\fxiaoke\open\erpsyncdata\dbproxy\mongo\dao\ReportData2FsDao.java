package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.NeedReportFsEiMsgDoc;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @Date: 19:01 2024/10/23
 * @Desc:
 */
@Slf4j
@Component
@DependsOn("erpSyncDataLogMongoStore")
public class ReportData2FsDao {
    private static final String f_id = "_id";
    private static final String f_uniqueKey = "uniqueKey";
    private static final String f_tenantId = "tenantId";
    private static final String f_dcId = "dcId";
    private static final String f_channel = "channel";
    private static final String f_connectCreateTime = "connectCreateTime";
    private static final String f_type = "type";
    private static final String f_needCheck = "needCheck";
    private static final String f_isFinish = "isFinish";

    private static final String f_createTime = "createTime";
    private static final String f_updateTime = "updateTime";
    private String DATABASE;
    private DatastoreExt store;

    @Getter
    private final Set<String> reportData2FsCollectionCache = Sets.newConcurrentHashSet();
    private final static String collectionName = "need_report_fs_msg";

    ReportData2FsDao() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    public void updateReportData(List<ObjectId> objectIds, Boolean needCheck, Boolean isFinish) {
        if (CollectionUtils.isEmpty(objectIds) || (needCheck == null && isFinish == null)) {
            return;
        }
        MongoCollection<NeedReportFsEiMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        Bson filters = and(Filters.in(f_id, objectIds));

        List<Bson> updates = new ArrayList<>();
        if (needCheck != null) {
            updates.add(set(f_needCheck, needCheck));
        }
        if (isFinish != null) {
            updates.add(set(f_isFinish, isFinish));
        }
        Bson update = combine(updates);
        mongoCollection.updateMany(filters, update, new UpdateOptions().upsert(false));
    }


    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(NeedReportFsEiMsgDoc.class)
                        .automatic(true).build()));
    }

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    public void setStore(DatastoreExt store) {
        this.store = store;
        store.getMongo().getDatabase(DATABASE)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.equals(collectionName)) {
                        reportData2FsCollectionCache.add(v);
                    }
                });
    }

    public void deleteByIds(List<ObjectId> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        MongoCollection<NeedReportFsEiMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_id, idList));

        mongoCollection.deleteMany(Filters.and(filters));
    }

    public List<NeedReportFsEiMsgDoc> listNeedCheckData(List<String> eis) {
        List<NeedReportFsEiMsgDoc> result = new ArrayList<>();
        MongoCollection<NeedReportFsEiMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_needCheck, true));
        filters.add(Filters.eq(f_isFinish, false));
        filters.add(Filters.in(f_tenantId, eis));
        mongoCollection.find()
                .filter(Filters.and(filters))
                .into(result);
        return result;
    }

    public Set<String> getNeedCheckEi() {
        List<NeedReportFsEiMsgDoc> result = new ArrayList<>();
        MongoCollection<NeedReportFsEiMsgDoc> mongoCollection = this.getOrCreateDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq(f_needCheck, true));
        filters.add(Filters.eq(f_isFinish, false));
        mongoCollection.find()
                .projection(Projections.include(f_tenantId))
                .limit(5000)
                .filter(Filters.and(filters))
                .into(result);
        return result.stream().map(NeedReportFsEiMsgDoc::getTenantId).collect(Collectors.toSet());
    }


    public void batchUpsertDataNodeMsgDoc(List<NeedReportFsEiMsgDoc> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        int size = dataList.size();
        StopWatch sw = new StopWatch("batchUpsert-" + collectionName + '[' + size + ']');
        List<WriteModel<NeedReportFsEiMsgDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (int i = 0; i < dataList.size(); i++) {
            NeedReportFsEiMsgDoc needReportFsEiMsgDoc = dataList.get(i);
            UpdateOneModel<NeedReportFsEiMsgDoc> updateOneModel = new UpdateOneModel<>(this.updateBy(needReportFsEiMsgDoc), this.upsert(needReportFsEiMsgDoc), updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        sw.start("bulkWrite-" + collectionName + '[' + size + ']');
        MongoCollection<NeedReportFsEiMsgDoc> collection = this.getOrCreateDataNodeMsgCollection();
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
            sw.stop();
            long cost = sw.getTotalTimeMillis();
            if (cost > 5000) {
                log.warn("bulkWrite collection: {}, cost: {}ms, items: {}", collectionName, cost, size);
                log.warn(sw.prettyPrint());
            }
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
    }

    private Bson updateBy(NeedReportFsEiMsgDoc message) {
        Document result = new Document();
        result.put(f_uniqueKey, message.getUniqueKey());
        return result;
    }

    private Bson upsert(NeedReportFsEiMsgDoc message) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        //更新
        updateDoc.append(f_updateTime, new Date());
        updateDoc.append(f_needCheck, message.getNeedCheck());
        //插入
        setOnInsertDoc
                .append(f_tenantId, Objects.requireNonNull(message.getTenantId(), "tenantId cannot be null"))
                .append(f_type, Objects.requireNonNull(message.getType(), "type cannot be null"))
                .append(f_isFinish, Objects.requireNonNull(message.getIsFinish(), "isFinish cannot be null"))
                .append(f_dcId, Objects.requireNonNull(message.getDcId(), "dcId cannot be null"))
                .append(f_uniqueKey, Objects.requireNonNull(message.getUniqueKey(), "uniqueKey cannot be null"))
                .append(f_channel, Objects.requireNonNull(message.getChannel(), "channel cannot be null"))
                .append(f_connectCreateTime, Objects.requireNonNull(message.getConnectCreateTime(), "connectCreateTime cannot be null"))
                .append(f_createTime, new Date());

        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        return doc;
    }


    public MongoCollection<NeedReportFsEiMsgDoc> getOrCreateDataNodeMsgCollection() {
        MongoCollection<NeedReportFsEiMsgDoc> collectionList = store.getMongo().getDatabase(DATABASE).withCodecRegistry(ReportData2FsDao.SingleCodecHolder.codecRegistry).getCollection(collectionName, NeedReportFsEiMsgDoc.class);
        if (!reportData2FsCollectionCache.add(collectionName)) {
            return collectionList;
        }
        List<String> exists = Lists.newArrayList();
        collectionList.listIndexes().iterator().forEachRemaining(doc -> {
            String name = doc.getString("name");
            if (!"_id_".equals(name)) {
                exists.add(name);
            }
        });
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_uniqueKey";
        if (!exists.remove(key)) {
            Bson index_uniqueKey = Indexes.compoundIndex(Indexes.ascending(f_uniqueKey));
            toBeCreate.add(new IndexModel(index_uniqueKey, new IndexOptions().name(key).background(true)));
        }
        key = "index_check_finish";
        if (!exists.remove(key)) {
            Bson index_check_finish = Indexes.compoundIndex(Indexes.ascending(f_needCheck), Indexes.ascending(f_isFinish));
            toBeCreate.add(new IndexModel(index_check_finish, new IndexOptions().name(key).background(true)));
        }
        key = "index_ei_check_finish";
        if (!exists.remove(key)) {
            Bson index_ei_check_finish = Indexes.compoundIndex(Indexes.ascending(f_tenantId),Indexes.ascending(f_needCheck), Indexes.ascending(f_isFinish));
            toBeCreate.add(new IndexModel(index_ei_check_finish, new IndexOptions().name(key).background(true)));
        }

        if (!toBeCreate.isEmpty()) {
            List<String> created = collectionList.createIndexes(toBeCreate);
            log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        }
        return collectionList;
    }

}
