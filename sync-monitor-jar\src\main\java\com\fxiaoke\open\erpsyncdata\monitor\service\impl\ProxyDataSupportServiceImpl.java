package com.fxiaoke.open.erpsyncdata.monitor.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.helper.DataSupport;
import com.fxiaoke.open.erpsyncdata.monitor.manager.DataSourceInfoManager;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByTimeArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByTimeResult;
import com.fxiaoke.open.erpsyncdata.monitor.model.ProxyDataSourceInfo;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataSupportService;
import com.fxiaoke.open.erpsyncdata.monitor.util.ProxyTokenUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CommonConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/30
 */
@Service
@DataSupport(DataSourceType.PROXY)
public class ProxyDataSupportServiceImpl implements DataSupportService {
    @Autowired
    private DataSourceInfoManager dataSourceInfoManager;
    @Autowired
    private OkHttpSupport okHttpSupport;
    @Autowired
    private I18NStringManager i18NStringManager;

    @SuppressWarnings("unchecked")
    @Override
    public Result<ListDataByTimeResult> listDataByTime(ListDataByTimeArg arg) {
        ProxyDataSourceInfo proxyDataSourceInfo = dataSourceInfoManager.getProxyConnectInfo(arg.getTenantId(), arg.getObjApiName());
        String url = proxyDataSourceInfo.getUrl();
        String secretKey = proxyDataSourceInfo.getSecretKey();
        String token = ProxyTokenUtil.generateToken(secretKey);
        String body = JSON.toJSONString(arg);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body);
        Request request = new Request.Builder()
                .url(url)
                .header(CommonConstants.JWT_TOKEN_NAME, token)
                .post(requestBody)
                .build();
        Result<ListDataByTimeResult> result = (Result<ListDataByTimeResult>) okHttpSupport.syncExecute(request, new SyncCallback() {
            @Override
            public Result<ListDataByTimeResult> response(Response response) {
                Result<ListDataByTimeResult> result = Result.newSuccess();
                try {
                    ListDataByTimeResult resultData = parseObj(arg.getTenantId(),response, proxyDataSourceInfo, result);
                    result.setData(resultData);
                } catch (Exception e) {
                    result.setErrCode(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrCode());
                    result.setErrMsg(String.format(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrMsg(), e.getMessage()));
                }
                return result;
            }
        });
        if (result.isSuccess() && result.getData() == null) {
            result.setErrCode(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrCode());
            result.setErrMsg(String.format(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrMsg(), i18NStringManager.getByEi(I18NStringEnum.s166,arg.getTenantId())));
        }
        return result;
    }

    private static ListDataByTimeResult parseObj(String tenantId,Response response, ProxyDataSourceInfo proxyDataSourceInfo, Result<ListDataByTimeResult> result) throws IOException {
        ResponseBody body = response.body();
        if (body == null) {
            throw new ErpSyncDataException(I18NStringEnum.s166,tenantId);
        }
        JSONObject resultObj;
        try{
            resultObj = JSON.parseObject(body.string());
        }catch (Exception e){
            throw new ErpSyncDataException(I18NStringEnum.s167,tenantId);
        }
        String resultCode = resultObj.getString(proxyDataSourceInfo.getStatusField());
        String resultMsg = resultObj.getString(proxyDataSourceInfo.getMsgField());
        if (Objects.equals(resultCode, proxyDataSourceInfo.getSuccessStatus())) {
            //成功
            ListDataByTimeResult resultData = resultObj.getObject(proxyDataSourceInfo.getDataField(), new TypeReference<ListDataByTimeResult>() {
            });
            //校验
            boolean invalid = resultData == null ||
                    resultData.getCompareDataList() == null;
            if (invalid) {
                throw new ErpSyncDataException(I18NStringEnum.s168,tenantId);
            }
            return resultData;
        } else {
            throw new ErpSyncDataException(resultMsg,null,null);
        }
    }
}
