package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/20
 */
@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum K3ElementTypeEnum {

    e1(1, "文本", ErpFieldTypeEnum.text, ""),
    e2(2, "小数", ErpFieldTypeEnum.number, ""),
    e3(3, "整数", ErpFieldTypeEnum.number, ""),
    e4(4, "日期", ErpFieldTypeEnum.date, ""),
    e5(5, "长日期", ErpFieldTypeEnum.date_time, "期"),
    e6(6, "多行文本", ErpFieldTypeEnum.long_text, "文本"),
    /**
     * 查找关联
     */
    e7(7, "关联", ErpFieldTypeEnum.object_reference, "组织 查找关联组织"),
    e8(8, "复选", ErpFieldTypeEnum.true_or_false, "框 布尔值"),
    /**
     * 下拉列表，不用增加后缀
     */
    e9(9, "下拉列表", ErpFieldTypeEnum.select_one, " 单选 选项解析extends"),
    e12(12, "单据编号", ErpFieldTypeEnum.text, " 单行文本"),
    e13(13, "基础资料", ErpFieldTypeEnum.object_reference, " 查找关联 组织、物料、客户、仓库、库存、部门、员工,或文本，增加id、number、name字段"),
    /**
     * 查找关联空对象
     */
    e16(16, "多类别基础资料", ErpFieldTypeEnum.text, "查找关联空对象"),
    e17(17, "创建人", ErpFieldTypeEnum.user, "用户"),
    /**
     * 用户,查看code增加.Id，保存code增加.FUserId
     */
    e18(18, "用户", ErpFieldTypeEnum.user, " 用户，查.Id，写.FUserId"),
    e19(19, "批次", ErpFieldTypeEnum.object_reference, " 查找关联批次"),
    e20(20, "单价", ErpFieldTypeEnum.currency, "金额"),
    e21(21, "金额", ErpFieldTypeEnum.currency, ""),
    e22(22, "数量", ErpFieldTypeEnum.number, "数字"),
    e26(26, "创建日期", ErpFieldTypeEnum.date_time, " 日期"),
    e27(27, "修改人", ErpFieldTypeEnum.user, " 用户"),
    e28(28, "最后修改日期", ErpFieldTypeEnum.date_time, " 日期"),
    /**
     * 单选辅助资料 查看code加.FNumber,名称加.FDataValue
     */
    e30(30, "单选辅助资料", ErpFieldTypeEnum.select_one, " 查找关联辅助资料 可修改为单选,增加number、name"),
    e33(33, "数据分组", ErpFieldTypeEnum.select_one, "数据分组"),
    /**
     * 多语言文本，暂不支持解析
     */
    e36(36, "多语言文本", ErpFieldTypeEnum.long_text, "多语言文本"),
    /**
     * 不用增加后缀
     */
    e40(40, "单据状态", ErpFieldTypeEnum.select_one, " 单选 选项从extends取"),
    /**
     * 加number单选，加name
     */
    e44(44, "单据类型", ErpFieldTypeEnum.select_one, " 单选 选项使用空，后续选项需要查表，"),
    e46(46, "计量单位", ErpFieldTypeEnum.select_one, " 单位 number单选,增加name"),
    e47(47, "基本单位数量", ErpFieldTypeEnum.number, " 数字"),
    /**
     * e.g.: "SrcType": "SAL_SaleOrder",
     */
    e50(50, "源单类型", ErpFieldTypeEnum.text, ""),
    /**
     * e.g.: "SrcBillNo": "XSDD000154",
     */
    e51(51, "源单编号", ErpFieldTypeEnum.text, ""),
    e52(52, "基本计量单位", ErpFieldTypeEnum.select_one, " 单选 number单选,增加name"),
    e54(54, "小数", ErpFieldTypeEnum.number, ""),
    e55(55, "折扣额", ErpFieldTypeEnum.number, " 数字"),
    e56(56, "整数", ErpFieldTypeEnum.text, "查找关联单据Id"),
    e76(76, "红蓝字", ErpFieldTypeEnum.select_one, "红蓝字"),

    unKnow(-1, "未知类型", ErpFieldTypeEnum.text, "未知类型"),
    ;
    /**
     * 该列表使用number查找关联（写code），增加name字段
     */
    public static final Set<K3ElementTypeEnum> NUMBER_REFERENCE_TYPES = new HashSet<>();
    /**
     * 单位字段，增加number单选（写code）,增加name
     */
    public static final Set<K3ElementTypeEnum> UNIT_TYPES = new HashSet<>();
    /**
     * 用户,查看code增加.Id，保存code增加.FUserId
     */
    public static final Set<K3ElementTypeEnum> USER_TYPES = new HashSet<>();

    /**
     * 下拉列表,不用增加后缀，从extend解析选项
     */
    public static final Set<K3ElementTypeEnum> PULL_DOWN_LIST_TYPES = new HashSet<>();
    /**
     * 业务员单据列表
     */
    public static final Set<String> OPERATOR_FORMS = new HashSet<>();

    static {
        Collections.addAll(NUMBER_REFERENCE_TYPES, e7, e13, e19, e16);
        Collections.addAll(USER_TYPES, e17, e18, e27);
        Collections.addAll(UNIT_TYPES, e46, e52, e44);
        Collections.addAll(PULL_DOWN_LIST_TYPES, e9, e40, e76);
        Collections.addAll(OPERATOR_FORMS, "BD_Saler", "BD_BUYER", "BD_WAREHOUSEWORKERS", "BD_Inspector", "BD_PLANNER");
    }


    /**
     * 类型编码
     */
    private int type;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 平台类型
     */
    private ErpFieldTypeEnum erpFieldType;

    /**
     * 备注22
     */
    private String remark = "";

    public static K3ElementTypeEnum getByType(int type) {
        String eType = "e" + type;
        try{
            return K3ElementTypeEnum.valueOf(eType);
        }catch (Exception e){
            return K3ElementTypeEnum.unKnow;
        }
    }
}
