package com.fxiaoke.open.erpsyncdata.apiproxy.manager.standard;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorConfigHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorMetaDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringResponse;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CheckStreamEnableArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SimpleEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * apl类连接器
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.APL_CLASS, channel = ErpChannelEnum.STANDARD_CHANNEL)
public class StdAplClassHandler implements ConnectorDataHandler, ConnectorMetaDataHandler, ConnectorConfigHandler {
    @Autowired
    private AplManager aplManager;


    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams());
        String tenantId = erpIdArg.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objAPIName = erpIdArg.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.queryMasterById;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objAPIName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, erpIdArg),
                null,
                null,
                new TypeReference<StandardData>() {
                }
        );

    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(timeFilterArg.getTenantId(), connectInfo.getConnectParams());
        final ErpObjInterfaceUrlEnum interEnum;
        String tenantId = timeFilterArg.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objAPIName = timeFilterArg.getObjAPIName();
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            interEnum = ErpObjInterfaceUrlEnum.queryInvalid;
        } else {
            interEnum = ErpObjInterfaceUrlEnum.queryMasterBatch;
        }
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objAPIName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, timeFilterArg),
                null,
                timeFilterArg,
                new TypeReference<StandardListData>() {
                }
        );
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        final String objApiName = standardData.getMasterFieldVal().getApiName();
        //创建时取出Id字段
        standardData.removeId();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.create;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, standardData),
                null,
                null,
                new TypeReference<ErpIdResult>() {
                }
        );
    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData 不使用
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardDetailData.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.create;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, standardDetailData),
                null,
                null,
                new TypeReference<StandardDetailId>() {
                }
        );
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, true);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<StringResponse> webhook(StringRequest arg, ErpConnectInfoEntity connectInfo) {

        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.webhook;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, arg),
                null,
                null,
                new TypeReference<StringResponse>() {
                }
        );
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, false);
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<List<ErpObjTreeNode>> listObjects(ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.listObjects;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId),
                null,
                null,
                new TypeReference<List<ErpObjTreeNode>>() {
                }
        );
    }

    @Override
    public Result<ErpObjTreeNode> getObjectTree(ErpConnectInfoEntity connectInfo, ObjectDescArg arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getObjectTree;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, arg),
                null,
                null,
                new TypeReference<ErpObjTreeNode>() {
                }
        );
    }

    @Override
    public Result<ErpObjTreeNode> getObjectDesc(ErpConnectInfoEntity connectInfo, ObjectDescArg.ParseObjField arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getObjectDesc;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, arg),
                null,
                null,
                new TypeReference<ErpObjTreeNode>() {
                }
        );
    }

    @Override
    public Result<List<ConnectorAuthType>> getAuthTypeList(ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getAuthTypeList;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId),
                null,
                null,
                new TypeReference<List<ConnectorAuthType>>() {
                }
        );
    }

    public Result<ConnectorIntro> getConnectorIntro(ErpConnectInfoEntity connectInfo, GetConnectorIntroArg arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getConnectorIntro;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "all",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, arg),
                null,
                null,
                new TypeReference<ConnectorIntro>() {
                }
        );
    }


    public Result<SystemParams> processUserInputSystemParams(ErpConnectInfoEntity connectInfo, SystemParams systemParams) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.processUserInputSystemParams;
        String objApiName = "all";
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, systemParams),
                null,
                null,
                new TypeReference<SystemParams>() {
                }
        );
    }


    /**
     * 获取OAuth2 获取授权码URL
     *
     * @param connectInfo
     * @param systemParams
     * @return
     */
    public Result<String> getOAuth2AuthUrl(ErpConnectInfoEntity connectInfo, SystemParams systemParams) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.getOAuth2AuthUrl;
        String objApiName = "all";
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, systemParams),
                null,
                null,
                new TypeReference<String>() {
                }
        );
    }


    /**
     * 检查授权状态
     */
    public Result<Void> checkAuthStatus(ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.checkAuthStatus;
        String objApiName = "all";
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId),
                null,
                null,
                new TypeReference<Void>() {
                }
        );
    }

    @Override
    public Result<Void> checkEnableStream(ErpConnectInfoEntity connectInfo, CheckStreamEnableArg arg) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.checkEnableStream;
        String objApiName = arg.getErpRealObjApiName();
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, arg),
                null,
                null,
                new TypeReference<Void>() {
                }
        );
    }

    private Result<String> comInvalid(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo, boolean isMaster) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        String objApiName1 = standardInvalidData.getObjAPIName();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(tenantId, connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = isMaster ? ErpObjInterfaceUrlEnum.invalid : ErpObjInterfaceUrlEnum.invalidDetail;
        Optional<Map.Entry<String, String>> detail = standardInvalidData.getDetailFieldVals().entrySet().stream().findAny();
        if (detail.isPresent()) {
            objApiName1 += objApiName1 + "==" + detail.get().getKey();
        }
        final String objApiName = objApiName1;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId,standardInvalidData),
                null,
                null,
                new TypeReference<String>() {
                }
        );
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        final String tenantId = connectInfo.getTenantId();
        final String dataCenterId = connectInfo.getId();
        final String objApiName = standardData.getMasterFieldVal().getApiName();
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.update;
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, standardData),
                null,
                null,
                new TypeReference<ErpIdResult>() {
                }
        );

    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        String objApiName = standardDetailData.getObjAPIName();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.updateDetail;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                objApiName,
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, standardDetailData),
                null,
                null,
                new TypeReference<StandardDetailId>() {
                }
        );
    }

    @Override
    public Result<Void> processChangeEmployeeMapping(SimpleEmployeeMapping simpleEmployeeMapping, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String dataCenterId = connectInfo.getId();
        ErpObjInterfaceUrlEnum interEnum = ErpObjInterfaceUrlEnum.processChangeEmployeeMapping;
        StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(connectInfo.getTenantId(), connectInfo.getConnectParams());
        //使用apl类
        return aplManager.executeAplMethod(tenantId,
                dataCenterId,
                "PersonnelObj",
                interEnum,
                standardConnectParam.getApiName(),
                ListUtil.of(dataCenterId, simpleEmployeeMapping),
                null,
                null,
                new TypeReference<Void>() {
                }
        );
    }
}
