package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/12/22 11:39
 * @desc
 */
@Data
public class DataMonitorScreenDTO {
    /**
     * 数据唯一编码
     */
    String name;
    /**
     * 企业账号
     */
    String tenantId;
    /**
     * 外部系统
     */
    String dataCenterId;

    /**
     * 集成流
     */
    String ployDetailId;

    /**
     * 外部对象
     */
    String outSideObjApiName;

    /**
     * 外部对象数据id
     */
    String outSideObjId;

    /**
     * crm对象
     */
    String crmObjApiName;

    /**
     * crm对象数据id
     */
    String crmObjId;

    /**
     * 操作类型
     */
    String operationType;
    /**
     * 具体操作类型
     * @see ErpObjInterfaceUrlEnum
     */
    String operationTypeDetail;
    /**
     * 源系统
     */
    String sourceSystemType;

    /**
     * 执行状态
     */
    String operateStatus;

    /**
     * 区分是否是历史数据
     */
    Boolean historyDataType=false;

    /**
     * 数据量
     */
    int outDataCount=0;

    /**
     * 执行时间
     */
    long executeTime;

    /**
     * 耗时
     */
    long executeCost;
    /**
     * 创建时间
     */
    long createTime;

    /**
     * 更新时间
     */
    long updateTime;
    /**
     * 是否跳过上报
     */
    String skipSend;

}
