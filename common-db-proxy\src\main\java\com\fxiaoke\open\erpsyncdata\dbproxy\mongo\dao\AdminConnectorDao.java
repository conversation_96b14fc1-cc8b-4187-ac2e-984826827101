package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminConnectorInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseLogMongoStore;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Repository
@Slf4j
public class AdminConnectorDao extends BaseLogMongoStore<AdminConnectorInfo> {
    public static final String TENANT_ID = "1";

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                                                      .register(AdminConnectorInfo.class)
                                                      .automatic(true).build()));
    }

    protected AdminConnectorDao() {
        super(AdminConnectorDao.SingleCodecHolder.codecRegistry);
    }

    @Override
    public String getCollName(String tenantId) {
        return "connector_temp";
    }

    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> indexModels = new ArrayList<>();
        indexModels.add(new IndexModel(Indexes.ascending(AdminConnectorInfo.Fields.enterpriseAccount, AdminConnectorInfo.Fields.connectorKey, AdminConnectorInfo.Fields.connectorId), new IndexOptions().background(true)));
        indexModels.add(new IndexModel(Indexes.ascending(AdminConnectorInfo.Fields.mongoLastSyncTime),
                                       new IndexOptions().expireAfter(7L, TimeUnit.DAYS).background(true)));
        return indexModels;
    }

    public Date getMongoLastSyncTime() {
        MongoCollection<AdminConnectorInfo> collection = getOrCreateCollection(TENANT_ID);
        AdminConnectorInfo first = collection.find()
                .projection(Projections.include(AdminConnectorInfo.Fields.mongoLastSyncTime))
                .sort(Sorts.descending(AdminConnectorInfo.Fields.mongoLastSyncTime))
                .limit(1).first();
        if (first != null) {
            return first.getMongoLastSyncTime();
        }
        return null;
    }

    public List<String> distinctSystemNameList() {
        MongoCollection<AdminConnectorInfo> collection = getOrCreateCollection(TENANT_ID);
        List<String> systemNames = collection.distinct(AdminConnectorInfo.Fields.systemName, String.class)
                .into(new ArrayList<>());
        return systemNames;
    }

    public void batchUpsert(List<AdminConnectorInfo> infos) {
        if (CollectionUtils.isEmpty(infos)) {
            return;
        }
        MongoCollection<AdminConnectorInfo> collection = getOrCreateCollection(TENANT_ID);
        List<UpdateOneModel<AdminConnectorInfo>> bulkOps = new ArrayList<>();
        for (AdminConnectorInfo info : infos) {
            info.setMongoLastSyncTime(null);
            // 收集更新字段形成updates
            List<Bson> updates = CollUtil.newArrayList(Updates.currentDate(AdminConnectorInfo.Fields.mongoLastSyncTime));
            Map<String, Object> infoMap = BeanUtil.beanToMap(info, false, true);
            infoMap.forEach((k, v) -> {
                updates.add(Updates.set(k, v));
            });
            // 指定操作为upsert
            bulkOps.add(new UpdateOneModel<>(Filters.eq(AdminConnectorInfo.Fields.connectorId, info.getConnectorId()),
                                             Updates.combine(updates), new UpdateOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = collection.bulkWrite(bulkOps);
        return;
    }

    /**
     * 分页查询AdminConnectorInfo
     * 参考com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DelayDataNodeMsgDao#listDataNodeMsg
     * @param param
     * @return
     */
    public List<AdminConnectorInfo> pageInfo(Map<String, String> param) {
        int perPage = Convert.toInt(param.get("perPage"), 50);
        int page = Convert.toInt(param.get("page"), 1);
        int skipped = (page-1) * perPage;
        List<Bson> filters = obtainFilters(param);
        Bson sort = Sorts.ascending(AdminConnectorInfo.Fields.connectorId);

        MongoCollection<AdminConnectorInfo> collection = getOrCreateCollection(TENANT_ID);
        List<AdminConnectorInfo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(filters)) {
            collection.find().sort(sort).skip(skipped).limit(perPage).into(result);
        } else {
            collection.find(Filters.and(filters)).sort(sort).skip(skipped).limit(perPage).into(result);
        }
        return result;
    }

    public List<AdminConnectorInfo> getAllInfo() {
        Bson sort = Sorts.ascending(AdminConnectorInfo.Fields.connectorId);

        MongoCollection<AdminConnectorInfo> collection = getOrCreateCollection(TENANT_ID);
        List<AdminConnectorInfo> result = new ArrayList<>();
        collection.find().sort(sort).into(result);
        return result;
    }

    public int filteredCount(Map<String, String> param) {
        List<Bson> filters = obtainFilters(param);
        MongoCollection<AdminConnectorInfo> collection = getOrCreateCollection(TENANT_ID);
        int count;
        if (CollectionUtils.isEmpty(filters)) {
            count = (int) collection.countDocuments();
        } else {
            count = (int) collection.countDocuments(Filters.and(filters));
        }
        return count;
    }

    private List<Bson> obtainFilters(Map<String, String> param) {
        String eaFilter = param.get("ea");
        Set<String> eaFilters = null;
        if (StrUtil.isNotEmpty(eaFilter)) {
            eaFilters = new HashSet<>(StrUtil.split(eaFilter, ","));
        }
        String connectorKeyFilter = param.get("connectorKey");
        Set<String> connectorKeyFilters = null;
        if (StrUtil.isNotEmpty(connectorKeyFilter)) {
            connectorKeyFilters = new HashSet<>(StrUtil.split(connectorKeyFilter, ","));
        }
        String typeFilter = param.get("enterpriseType");
        Set<String> typeFilters = null;
        if (StrUtil.isNotEmpty(typeFilter)) {
            typeFilters = new HashSet<>(StrUtil.split(typeFilter, ","));
        }
        List<Bson> filters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(eaFilters)) {
            filters.add(Filters.in(AdminConnectorInfo.Fields.enterpriseAccount, eaFilters));
        }
        if (CollectionUtils.isNotEmpty(connectorKeyFilters)) {
            filters.add(Filters.in(AdminConnectorInfo.Fields.connectorKey, connectorKeyFilters));
        }
        if (CollectionUtils.isNotEmpty(typeFilters)) {
            filters.add(Filters.in(AdminConnectorInfo.Fields.enterpriseType, typeFilters));
        }
        return filters;
    }

    @Deprecated
    public void truncate() {
        getOrCreateCollection(TENANT_ID).deleteMany(Filters.ne(AdminConnectorInfo.Fields.connectorKey, ""));
    }
}
