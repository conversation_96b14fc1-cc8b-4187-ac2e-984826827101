package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpU8EaiConfigEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ManagedTenantReplace
public interface ErpU8EaiConfigDao extends ErpBaseDao<ErpU8EaiConfigEntity> , ITenant<ErpU8EaiConfigDao> {

    ErpU8EaiConfigEntity getEaiConfigByTenantAndObjApiName(@Param("tenantId") String tenantId, @Param("objApiName") String objApiName, @Param("datacenterId") String datacenterId);
    
    List<ErpU8EaiConfigEntity> queryEaiConfigByTenantAndParentObjApiName(@Param("tenantId") String tenantId, @Param("parentObjApiName") String parentObjApiName, @Param("datacenterId") String datacenterId);

    int deleteByTenantId(@Param("tenantId")String tenantId);

}
