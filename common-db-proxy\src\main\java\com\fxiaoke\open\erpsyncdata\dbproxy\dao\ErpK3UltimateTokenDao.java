package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateTokenEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * erp token dao
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
@Repository
public interface ErpK3UltimateTokenDao extends ErpBaseDao<ErpK3UltimateTokenEntity>, ITenant<ErpK3UltimateTokenDao> {
    ErpK3UltimateTokenEntity findByToken(@Param("tenantId") String tenantId,
                                         @Param("token") String token);

    ErpK3UltimateTokenEntity findData(@Param("tenantId") String tenantId,
                                      @Param("dataCenterId") String dataCenterId,
                                      @Param("erpObjApiName") String erpObjApiName,
                                      @Param("version") String version);
}