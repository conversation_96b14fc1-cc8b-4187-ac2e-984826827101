package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.stereotype.Service;


@Service("inventory")
public class U8ProductObjManager extends U8DefaultMananger {

  @Override
  protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                           String tenantId,
                                           String dataCenterId,
                                           String snapshotId) {
    if (standardListDataResult.getData()!=null&&standardListDataResult.getData().getDataList()!=null){
      for (StandardData standardData : standardListDataResult.getData().getDataList()) {
       standardData.getMasterFieldVal().put("namealias",standardData.getMasterFieldVal().get("code")+"-"+standardData.getMasterFieldVal().get("name"));
      }
    }
  }

  @Override
  protected void getErpObjDataAfter(StandardData standardData) {
    standardData.getMasterFieldVal().put("namealias",standardData.getMasterFieldVal().get("code")+"-"+standardData.getMasterFieldVal().get("name"));
  }

}
