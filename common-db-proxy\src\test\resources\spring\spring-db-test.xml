<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <!--统一异常捕捉 erp-sync-data-->
    <bean id="erpApiExceptionInterceptor"
          class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.ApiExceptionInterceptor"/>
    <aop:config>
        <aop:aspect id="apiExceptionTransfer" ref="erpApiExceptionInterceptor" order="2">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                " method="around"/>
        </aop:aspect>
    </aop:config>
    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:syncdata-applicationContext.properties"
          p:configName="erp-sync-data-all"/>

    <bean id="accessLog" class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.LogInterceptor">
    </bean>
    <aop:config>
        <aop:aspect id="accessLogMonitor" ref="accessLog" order="0">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.dao..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.mq..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.manager..*.*(..)))
                           " method="around"/>
        </aop:aspect>
    </aop:config>

    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/common-spring.xml"/>
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>


    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}"/>
</beans>