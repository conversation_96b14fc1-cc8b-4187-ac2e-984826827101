package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 查询元数据
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/12/17
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
public class QueryBusinessInfoArg {
    @SerializedName("FormId")
    @JsonProperty("FormId")
    private String formId;
}
