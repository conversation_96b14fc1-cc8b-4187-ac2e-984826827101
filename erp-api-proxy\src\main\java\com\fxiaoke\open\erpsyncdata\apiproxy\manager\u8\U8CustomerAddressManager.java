package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("customeraddress")
public class U8CustomerAddressManager extends U8DefaultMananger {

    @Override
    protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                             String tenantId,
                                             String dataCenterId,
                                             String snapshotId) {
        if (standardListDataResult.getData() != null && standardListDataResult.getData().getDataList() != null) {
            for (StandardData standardData : standardListDataResult.getData().getDataList()) {
                getRealData(standardData);
            }
        }
    }


    /**
     * 获取接口
     */
    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectParam) {
        U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectParam.getConnectParams(), U8ConnectParam.class);
        String queryPath = "/api/customeraddress/batch_get";
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectParam.getId(),
                erpIdArg.getObjAPIName(),
                ErpObjInterfaceUrlEnum.queryMasterById.name());

        Map<String, String> params = new HashMap<>();
        String dataId=erpIdArg.getDataId();
        if (dataId.contains("#")){
            params.put("ccuscode",dataId.split("#")[0]);
            params.put("caddcode_begin",dataId.split("#")[1]);
            params.put("caddcode_end",dataId.split("#")[1]);
        }else {
            params.put("id",dataId);
        }
        //发送请求
        Result<List<StandardData>> rs = get(U8ConnectParam, queryPath, params, interfaceMonitorData);
        StandardData standardData = rs.getData().get(0);
        return new Result(getRealData(standardData));
    }

    private StandardData getRealData(StandardData standardData) {
        if (standardData == null) {
            return null;
        }
        ObjectData masterObj = standardData.getMasterFieldVal();
        String ccuscode=masterObj.getString("ccuscode");
        String caddcode=masterObj.getString("caddcode");
        masterObj.put("customeraddressId",ccuscode+"#"+caddcode);

        return standardData;
    }


}
