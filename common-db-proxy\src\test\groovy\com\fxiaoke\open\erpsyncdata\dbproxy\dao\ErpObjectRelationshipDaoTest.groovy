package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import spock.lang.Specification

class ErpObjectRelationshipDaoTest extends Specification {

    ErpObjectRelationshipDao dao
    List dataList = []

    def setup() {
        dao = new ErpObjectRelationshipDao() {
            @Override
            int insert(ErpObjectRelationshipEntity record) {
                def tenantId = record.getTenantId()
                if (dataList.add(tenantId))
                    return 1
                return 0
            }

            @Override
            int insertIgnore(ErpObjectRelationshipEntity erpObjectRelationshipEntity) {
                return insert(erpObjectRelationshipEntity)
            }

            @Override
            int batchInsert(List<ErpObjectRelationshipEntity> record) {
                return 0
            }
            @Override
            ErpObjectRelationshipEntity findBySplit(String tenantId, String erpSplitObjectApiname) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> ListBySplit(String tenantId, String dataCenterId, List<String> erpObjApiNames) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> findByRealObjectApiName(String tenantId, String dataCenterId, String erpRealObjectApiname) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> findAllByRealObjectApiName(String tenantId, String erpRealObjectApiname) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> findAllByTenantId(String tenantId) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> listByTenantIdAndDataCenterId(String tenantId, String dataCenterId) {
                return null
            }
            @Override
            List<String> findAllTenantId() {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> findMasterByRealObjectApiName(String tenantId, String dataCenterId, String erpRealObjectApiname) {
                return null
            }
            @Override
            int deleteByTenantIdAndDcId(String tenantId, String dataCenterId) {
                return 0
            }
            @Override
            int deleteByTenantId(String tenantId) {
                return 0
            }
            @Override
            List<ErpObjectRelationshipEntity> findNotSplit(String tenantId, String dataCenterId) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> getSplitObjApiName(String tenantId, String actualApiName) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> listByTenantId(String tenantId) {
                return null
            }
            @Override
            List<ErpObjectRelationshipEntity> queryList(ErpObjectRelationshipEntity record) {
                return null
            }
            @Override
            int updateById(ErpObjectRelationshipEntity record) {
                return 0
            }
            @Override
            int deleteById(String Id) {
                return 0
            }
            @Override
            int deleteByEiAndId(String ei, String Id) {
                return 0
            }
            @Override
            ErpObjectRelationshipEntity findById(String id) {
                return null
            }
        }
    }

    def "test invalidCacheErpObj"() {
        when:
        dao.invalidCacheErpObj("0123", "001")
        then:
        noExceptionThrown()
    }
}
