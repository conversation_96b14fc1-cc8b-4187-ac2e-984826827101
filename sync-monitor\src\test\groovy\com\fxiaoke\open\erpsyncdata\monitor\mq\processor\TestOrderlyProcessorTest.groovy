package com.fxiaoke.open.erpsyncdata.monitor.mq.processor

import cn.hutool.core.thread.ThreadUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorUtil
import com.fxiaoke.open.erpsyncdata.monitor.helper.MonitorMqProcessorHelper
import com.fxiaoke.open.erpsyncdata.monitor.mq.MonitorMqConsumer
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType
import lombok.extern.slf4j.Slf4j
import org.junit.Ignore
import spock.lang.Specification

import java.util.concurrent.atomic.AtomicLong

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/1/11
 */
@Ignore
@Slf4j
class TestOrderlyProcessorTest extends Specification {
    MonitorMqConsumer consumer

    void setup() {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-erp-sync-data-common")
        consumer = new MonitorMqConsumer()
        consumer.init()
        consumer.start()
    }

    void cleanup() {
        consumer.close()
    }

    def "testOrderlyConsumer"() {
        List<String> msgs = []
        AbstractMonitorMqOrderlyProcessor processor = new AbstractMonitorMqOrderlyProcessor<String>(MonitorType.TEST){
            @Override
            void process(String obj) {
                msgs.add(obj)
                println("process,"+obj)
            }

            @Override
            void batchProcess(List<String> obj) {

            }
        }
        new MonitorMqProcessorHelper().setMonitorMqOrderlyProcessorMap([processor])

        AtomicLong atomicLong = new AtomicLong();
        for (i in 0..<1) {
            println("batch ${i} begin")
            for (j in 0..<100) {
                def id = atomicLong.getAndIncrement()
                MonitorUtil.send("${id}".toString(), String.valueOf(i), MonitorType.TEST)
            }
            println("batch ${i} end ")
        }
        ThreadUtil.sleep(5000)
        expect:
        msgs.size()==100
        for (i in 0..<100) {
            msgs[i].equals(String.valueOf(i))
        }
    }
}
