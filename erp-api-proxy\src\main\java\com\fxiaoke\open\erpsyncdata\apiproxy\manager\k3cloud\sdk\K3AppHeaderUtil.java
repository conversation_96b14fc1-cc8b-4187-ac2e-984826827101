package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk;

import cn.hutool.core.codec.Base64;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/13
 */
@UtilityClass
public class K3AppHeaderUtil {
    private byte[] pwd = "0054f397c6234378b09ca7d3e5debce7".getBytes(StandardCharsets.UTF_8);

    private String decodeSec(String sec) {
        byte[] buffer = Base64.decode(sec);
        for (int i = 0; i < buffer.length; ++i) {
            buffer[i] ^= pwd[i];
        }
        return Base64.encode(buffer);
    }

    @SneakyThrows
    public Map<String, String> buildAppToken(String url, K3CloudConnectParam param) {
        Map<String, String> header = new LinkedHashMap<>();
        String cookieHD;
        cookieHD = "";
        String apigwSec = "";
        String[] arr = param.getAppId().split("_");
        if (arr.length >= 2) {
            cookieHD = arr[0];
            apigwSec = decodeSec(arr[1]);
        } else if (arr.length == 1) {
            cookieHD = arr[0];
        }

        header.put("X-Api-ClientID", cookieHD);
        header.put("X-Api-Auth-Version", "2.0");
        Date date = new Date();
        Timestamp ts = new Timestamp(date.getTime());
        String tsVal = Long.toString(ts.getTime());
        header.put("x-api-timestamp", tsVal);
        String nonceVal = Long.toString(ts.getTime());
        header.put("x-api-nonce", nonceVal);
        header.put("x-api-signheaders", "X-Api-TimeStamp,X-Api-Nonce");
        String urlPath = URLEncoder.encode(getUrlPath(url), "UTF-8");
        String context = String.format("POST\n%s\n\nx-api-nonce:%s\nx-api-timestamp:%s\n", urlPath, nonceVal, tsVal);
        if (!Objects.equals(apigwSec, "")) {
            header.put("X-Api-Signature", hashMAC(context, apigwSec));
        } else {
            header.put("X-Api-Signature", "");
        }
        header.put("X-Kd-Appkey", param.getAppId());
        String data = String.format("%s,%s,%s,%s", param.getDbId(), param.getUserName(), param.getLcid(), null);
        header.put("X-Kd-Appdata", Base64.encode(data.getBytes(StandardCharsets.UTF_8)));
        header.put("X-Kd-Signature", hashMAC(param.getAppId() + data, param.getPassword()));
        header.put("User-Agent", "Kingdee/Java WebApi SDK 8.0.4 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        return header;
    }

    String getUrlPath(String url) {
        if (url.startsWith("http")) {
            int index = url.indexOf("/", 10);
            return index > -1 ? url.substring(index) : url;
        } else {
            return url;
        }
    }

    public static String hashMAC(String data, String secret) {
        try {
            Mac kdmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            kdmac.init(secret_key);
            byte[] rawHmac = kdmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encode(bytesToHex(rawHmac).getBytes(StandardCharsets.UTF_8));
        } catch (Exception var5) {
            var5.printStackTrace();
            return null;
        }
    }


    private static String bytesToHex(byte[] hashInBytes) {
        StringBuilder sb = new StringBuilder();
        for (byte hashInByte : hashInBytes) {
            String hex = Integer.toHexString(hashInByte & 255);
            if (hex.length() < 2) {
                hex = "0" + hex;
            }
            sb.append(hex);
        }
        return sb.toString();
    }
}
