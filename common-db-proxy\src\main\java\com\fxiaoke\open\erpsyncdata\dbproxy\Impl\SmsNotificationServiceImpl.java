package com.fxiaoke.open.erpsyncdata.dbproxy.Impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SmsNotificationService;
import com.fxiaoke.otherrestapi.marketingsms.arg.CheckSmsStatusArg;
import com.fxiaoke.otherrestapi.marketingsms.arg.SendSmsArg;
import com.fxiaoke.otherrestapi.marketingsms.data.CheckSmsStatusData;
import com.fxiaoke.otherrestapi.marketingsms.data.PhoneInfoData;
import com.fxiaoke.otherrestapi.marketingsms.data.SendSmsInfoData;
import com.fxiaoke.otherrestapi.marketingsms.result.BaseSmsResult;
import com.fxiaoke.otherrestapi.marketingsms.service.SmsService;
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg;
import com.fxiaoke.paasauthrestapi.result.RoleUserResult;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SmsNotificationServiceImpl implements SmsNotificationService {
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private PaasAuthService paasAuthService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public Result<SendSmsInfoData> sendSmsNotice(String tenantId, SendSmsArg arg) {
        BaseSmsResult<SendSmsInfoData> result = smsService.sendSms(arg);
        return new Result(result.getErrCode() + "", result.getErrMsg(), result.getData());
    }

    @Override
    public Result<Boolean> checkSmsStatus(String tenantId, CheckSmsStatusArg arg) {
        BaseSmsResult<CheckSmsStatusData> result = smsService.checkSmsStatus(arg);
        if (result.isSuccess()) {
            return Result.newSuccess(result.getData().getStatus() == 0);
        } else {
            return Result.newError(result.getErrCode() + "", result.getErrMsg());
        }
    }

    @Override
    public Result<SendSmsInfoData> sendSmsNotice(String tenantId, List<Integer> userIdList, List<String> roleIdList, String templateContent) {
        if (userIdList == null) {
            userIdList = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            com.fxiaoke.paasauthrestapi.common.data.HeaderObj headerObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(Integer.valueOf(tenantId));
            for (String roleCode : roleIdList) {
                //角色
                try {
                    com.fxiaoke.paasauthrestapi.common.result.Result<RoleUserResult> roleUserRes = paasAuthService.roleUser(headerObj, new RoleUserArg("CRM", Integer.valueOf(tenantId), -10000, roleCode));
                    if (roleUserRes.getErrCode() != 0) {
                        log.warn("find role user failed,{},{}", roleCode, roleUserRes);
                    } else {
                        roleUserRes.getResult().getUsers().stream().mapToInt(Integer::parseInt).forEach(userIdList::add);
                    }
                } catch (Exception e) {
                    log.error("find role user error", e);
                }
            }
        }
        SendSmsArg arg = new SendSmsArg();
        arg.setChannelType(13);
        arg.setType(1);
        arg.setUserId(Integer.valueOf(userIdList.get(0)));
        String enterpriseAccount= eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        arg.setEa(enterpriseAccount);
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(Integer.valueOf(tenantId));
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);
        batchGetEmployeeDtoArg.setEmployeeIds(userIdList);
        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        if (batchGetEmployeeDtoResult != null && batchGetEmployeeDtoResult.getEmployeeDtos() != null) {
            List<String> phoneList = batchGetEmployeeDtoResult.getEmployeeDtos().stream().filter(e -> StringUtils.isNotBlank(e.getMobile())).map(e -> e.getMobile()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(phoneList)) {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            PhoneInfoData phoneData = new PhoneInfoData();
            phoneData.setPhoneList(phoneList);
            arg.setPhoneDataList(Lists.newArrayList(phoneData));
        }
        arg.setTemplateContent(templateContent);
        return this.sendSmsNotice(tenantId, arg);
    }

}
