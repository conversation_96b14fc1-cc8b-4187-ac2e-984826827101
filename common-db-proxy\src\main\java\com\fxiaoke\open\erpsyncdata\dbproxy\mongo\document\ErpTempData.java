package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.dispatcher.common.MessageField;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ErpTempDataStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 15:27 2021/7/15
 * @Desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objApiName")
@DataId("dataId")
@SuperBuilder
public class ErpTempData {

    /**
     * id
     */
    @MessageField(name = "_id")
    private ObjectId id;

    /**
     * 企业id
     */
    @MessageField(name = "tenant_id")
    private String tenantId;

    /**
     * 数据中心id
     */
    @MessageField(name = "dc_id")
    private String dcId;

    /**
     * 对象apiName
     */
    @MessageField(name = "obj_api_name")
    private String objApiName;

    /**
     * 操作类型
     */
    @MessageField(name = "operation_type")
    private Integer operationType;
    /**
     * 数据id
     */
    @MessageField(name = "data_id")
    private String dataId;
    /**
     * 数据编码
     */
    @MessageField(name = "data_number")
    private String dataNumber;

    /**
     * 数据体
     */
    @MessageField(name = "data_body")
    private String dataBody;
    /**
     * 数据md5
     */
    @MessageField(name = "data_md5")
    private String dataMd5;
    /**
     * 数据状态,
     * @see ErpTempDataStatusEnum
     */
    @MessageField(name = "status")
    private Integer status;
    /**
     * 数据同步状态
     */
    @MessageField(name = "sync_status")
    private Map<String,Integer> syncStatusMap;

    /**
     * 备注
     */
    @MessageField(name = "remark")
    private String remark;
    /**
     * 数据体最后一次traceId
     */
    @MessageField(name = "trace_id")
    private String traceId;
    /**
     * 所属任务id（历史数据同步任务）
     */
    @MessageField(name = "task_num")
    private Set<String> taskNum;
    /**
     * 每次进临时库数据的标识id
     */
    @MessageField(name = "sync_log_id")
    private String syncLogId;
    @MessageField(name = "data_receive_type")
    /**
     * @see com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum
     */
    private Integer dataReceiveType;

    /**
     * 多语
     */
    @MessageField(name = "locale")
    private String locale;
    /**
     * 数据体最后更新时间，
     */
    @MessageField(name = "last_sync_time")
    private Long lastSyncTime;
    /**
     * 数据体最后更新时间，mongo存了Timestamp，
     */
    @MessageField(name = "new_last_sync_time")
    private Date newLastSyncTime;
    /**
     * 数据体最后轮询/推送时间
     * 历史数据同步任务不会修改这个值,所以这个值可能为null
     */
    @MessageField(name = "last_polling_time")
    private Long lastPollingTime;

    /**
     * 创建时间
     */
    @MessageField(name = "create_time")
    private Long createTime;
    /**
     * 更新时间
     */
    @MessageField(name = "update_time")
    private Long updateTime;

    /**
     * 过期时间,保存的new Date()是UTC：世界标准时间，与东八区差了8个小时
     */
    @MessageField(name = "expire_time")
    private Date expireTime;

    /**
     * 主+从，所有对象的数量
     */
    @MessageField(name = "all_obj_count")
    private Integer allObjCount;

    /**
     * 分发优先级，默认情况下不存储值，进分发框架为50
     */
    @MessageField(name = "priority")
    private Integer priority;
}
