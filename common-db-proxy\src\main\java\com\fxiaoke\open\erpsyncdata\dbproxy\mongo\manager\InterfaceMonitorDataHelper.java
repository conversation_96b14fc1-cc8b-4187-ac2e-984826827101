package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.SyncTrace;
import lombok.experimental.UtilityClass;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 16:04 2021/7/15
 * @Desc:
 */
@UtilityClass
public class InterfaceMonitorDataHelper {

    public static InterfaceMonitorData buildInterfaceMonitorData(String tenantId,String dcId, String objApiName, String type, String arg, String result,
                                                                 Integer status, Long callTime, Long returnTime, String remark, String traceId, Long costTime) {
        InterfaceMonitorData interfaceMonitorData = new InterfaceMonitorData();
        interfaceMonitorData.setTenantId(tenantId);
        interfaceMonitorData.setDcId(dcId);
        interfaceMonitorData.setObjApiName(objApiName);
        interfaceMonitorData.setType(type);
        interfaceMonitorData.setArg(arg);
        interfaceMonitorData.setResult(result);
        interfaceMonitorData.setStatus(status);
        interfaceMonitorData.setCallTime(callTime);
        interfaceMonitorData.setReturnTime(returnTime);
        interfaceMonitorData.setRemark(remark);
        interfaceMonitorData.setTraceId(traceId);
        interfaceMonitorData.setCostTime(costTime);
        interfaceMonitorData.setCreateTime(System.currentTimeMillis());
        interfaceMonitorData.setExpireTime(new Date());
        if (SyncTrace.get()!=null){
            interfaceMonitorData.setSyncDataId(SyncTrace.get().getSyncDataId());
            SyncTrace.get().setInterfaceMonitorData(interfaceMonitorData);
        }
        return interfaceMonitorData;
    }


    public static InterfaceMonitorData buildInterfaceMonitor(String tenantId,
                                                             String dcId,
                                                             String objApiName,
                                                             String type) {
        return buildInterfaceMonitorData(tenantId, dcId, objApiName, type, null, null, null,
                null, null, null, null, null);
    }
}
