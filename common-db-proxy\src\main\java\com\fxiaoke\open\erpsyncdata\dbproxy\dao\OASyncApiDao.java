package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OASyncApiEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/9
 * @Desc:
 */
@Repository
public interface OASyncApiDao extends ErpBaseDao<OASyncApiEntity> , ITenant<OASyncApiDao> {

    /**
     * 获取企业策略信息
     *
     * @param tenantId
     * @return
     */
    OASyncApiEntity getByTenantId(@Param("tenantId") String tenantId, @Param("eventType") String eventType, @Param("objApiName") String objApiName);

    /**
     * 查询企业所有策略信息
     * @param tenantId
     * @return
     */
    List<OASyncApiEntity> getOASyncApiList(@Param("tenantId") String tenantId,@Param("eventType") String eventType);

    List<OASyncApiEntity> getOAUsedSyncApiList(@Param("tenantId") String tenantId);

    /**
     * 根据event更新状态
     */
    Integer updateOaApiStatus(@Param("tenantId") String tenantId,@Param("eventType") String eventType,@Param("status") String status);

    /**
     * 支持删除api oa配置表
     * @param tenantId
     * @param ids 列表
     * @param
     * @return
     */
    Integer batchDeleteIds(@Param("tenantId") String tenantId,@Param("ids") List<String> ids);
}