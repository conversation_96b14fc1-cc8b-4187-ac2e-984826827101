package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;


import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Repository
public interface CHSyncDataDao {

    CHSyncDataEntity getById(@Param(value = "tenantId") String tenantId, @Param(value = "id") String id,
                             @Param(value = "startLogTime") Date startLogTime,
                             @Param(value = "endTLogTime") Date endLogTime);


    CHSyncDataEntity getByIdSelective(@Param(value = "tenantId") String tenantId,
                                      @Param(value = "id") String id,
                                      @Param(value = "startLogTime") Date startLogTime,
                                      @Param(value = "endTLogTime") Date endLogTime,
                                      @Param(value = "returnField") String returnField);

    List<CHSyncDataEntity> listByIds(@Param(value = "tenantId") String tenantId, @Param(value = "ids") Collection<String> ids,
                                     @Param(value = "startLogTime") Date startLogTime,
                                     @Param(value = "endTLogTime") Date endLogTime);

    List<CHSyncDataEntity> listSimpleByIds(@Param(value = "tenantId") String tenantId, @Param(value = "ids") Collection<String> ids,
                                           @Param(value = "startLogTime") Date startLogTime,
                                           @Param(value = "endTLogTime") Date endLogTime);

    List<CHSyncDataEntity> listBySourceData(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiNames") List<String> sourceObjectApiNames,
                                            @Param(value = "sourceDataId") List<String> sourceDataId, @Param(value = "destObjectApiNames") List<String> destObjectApiNames,
                                            @Param(value = "startLogTime") Date startLogTime,
                                            @Param(value = "endTLogTime") Date endLogTime);

    /**
     * 只查了几个字段
     *
     * @param tenantId
     * @param sourceObjectApiNames
     * @param destObjectApiNames
     * @param startLogTime
     * @param endLogTime
     * @param offset
     * @param limit
     * @return
     */
    List<CHSyncDataEntity> listByPage(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiNames") List<String> sourceObjectApiNames,
                                      @Param(value = "destObjectApiNames") List<String> destObjectApiNames,
                                      @Param(value = "logId") String logId,
                                      @Param(value = "sourceDataId") String sourceDataId,
                                      @Param(value = "sourceDataName") String sourceDataName,
                                      @Param(value = "startLogTime") Date startLogTime,
                                      @Param(value = "endTLogTime") Date endLogTime,
                                      @Param(value = "offset") Integer offset,
                                      @Param(value = "limit") Integer limit);

    Integer countByApiNames(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiNames") List<String> sourceObjectApiNames,
                            @Param(value = "destObjectApiNames") List<String> destObjectApiNames,
                            @Param(value = "logId") String logId,
                            @Param(value = "sourceDataId") String sourceDataId,
                            @Param(value = "sourceDataName") String sourceDataName,
                            @Param(value = "startLogTime") Date startLogTime,
                            @Param(value = "endTLogTime") Date endLogTime);

    List<CHSyncDataEntity> listByStatusListAndEndUpdateTime(@Param(value = "tenantId") String tenantId, @Param(value = "statusList") List<Integer> statusList,
                                                            @Param(value = "startUpdateTime") Date startUpdateTime, @Param(value = "endUpdateTime") Date endUpdateTime,
                                                            @Param(value = "offset") Integer offset, @Param(value = "limit") Integer limit);

    List<CHSyncDataEntity> listByFsDataId(@Param(value = "tenantId") String tenantId, @Param(value = "objApiName") String objApiName,
                                          @Param(value = "fsDataId") List<String> fsDataId, @Param(value = "syncDirection") String syncDirection,
                                          @Param(value = "lastId") String lastId, @Param(value = "limit") Integer limit,
                                          @Param(value = "startLogTime") Date startLogTime,
                                          @Param(value = "endTLogTime") Date endLogTime);

    List<CHSyncDataEntity> limitGroupByObj(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiName") String sourceObjectApiName,
                                           @Param(value = "destObjectApiName") String destObjectApiName, @Param(value = "srcIds") List<String> srcIds,
                                           @Param(value = "lengthLimit") Integer lengthLimit,
                                           @Param(value = "startLogTime") Date startLogTime,
                                           @Param(value = "endTLogTime") Date endLogTime);

    long countByTenantId(@Param(value = "tenantId") String tenantId);

    List<CHSyncDataEntity> listSyncDatas(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjApiName") String sourceObjApiName,
                                         @Param(value = "destObjApiName") String destObjApiName, @Param(value = "lastId") String lastId, @Param(value = "limit") Integer limit,
                                         @Param(value = "startLogTime") Date startLogTime,
                                         @Param(value = "endTLogTime") Date endLogTime);

    List<CHSyncDataEntity> listBySourceDataIds(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiName") String sourceObjectApiName,
                                               @Param(value = "destObjectApiName") String destObjectApiName, @Param(value = "srcIds") List<String> srcIds,
                                               @Param(value = "startLogTime") Date startLogTime,
                                               @Param(value = "endTLogTime") Date endLogTime);

    List<Map<Integer, Integer>> getAllTypeCount(@Param(value = "tenantId") String tenantId, @Param(value = "startTime") Date startTime,
                                                @Param(value = "endTime") Date endTime, @Param(value = "ids") List<String> ids,
                                                @Param(value = "sourceApiName") String sourceApiName, @Param(value = "destApiName") String destApiName);

    Long findMinDate(@Param(value = "tenantId") String tenantId);

    List<CHSyncDataEntity> listBetween(@Param(value = "tenantId") String tenantId, @Param(value = "beginTime") Date beginTime,
                                       @Param(value = "endTime") Date endTime, @Param(value = "lastId") String lastId, @Param(value = "limit") Integer limit);

}