package com.fxiaoke.open.erpsyncdata.web.service.customFunction;


import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListSyncButtonPloyDetailsArg;
import com.fxiaoke.open.erpsyncdata.admin.service.DoTaskUrlService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>自定义函数触发立即同步</p>
 * @dateTime 2022-02-28 14:41
 * <AUTHOR>
 * @version 1.0
 */
@Service("manualSyncErpDataByErpObjApiName")
@Slf4j
public class ManualSyncErpDataByErpObjApiNameServiceImpl implements CustomFunctionCommonService {

    @Autowired
    private DoTaskUrlService doTaskUrlService;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;


    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String params = commonArg.getParams();
        ListSyncButtonPloyDetailsArg listSyncButtonPloyDetailsArg =
          JacksonUtil.fromJson(params, ListSyncButtonPloyDetailsArg.class);
        String objectApiName=listSyncButtonPloyDetailsArg.getObjectApiName();
        if (StringUtils.isEmpty(objectApiName)){
            return Result.newError("-100", I18NStringEnum.s22);
        }

        ErpObjectRelationshipEntity bySplit =
          erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(commonArg.getTenantId())).findBySplit(commonArg.getTenantId(), objectApiName);
        if (bySplit==null){
            return Result.newError("-100", I18NStringEnum.s1145);
        }

        Result<String> result =
          doTaskUrlService.manualExecutePloys(commonArg.getTenantId(), listSyncButtonPloyDetailsArg.getObjectApiName());
        return result;
    }

}
