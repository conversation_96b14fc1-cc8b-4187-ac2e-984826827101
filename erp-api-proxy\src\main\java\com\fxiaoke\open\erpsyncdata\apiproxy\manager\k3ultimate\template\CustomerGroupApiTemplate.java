package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import org.springframework.stereotype.Component;

@Component
public class CustomerGroupApiTemplate extends K3UltimateBaseApiTemplate {
    @Override
    public String getObjApiName() {
        return K3UltimateObjApiName.bd_customergroup;
    }

    @Override
    public String getBatchQueryApi() {
        return "/kapi/v2/basedata/bd_customergroup/query";
    }

    @Override
    public String getBatchAddApi() {
        return "/kapi/v2/basedata/bd_customergroup/add";
    }

    @Override
    public String getBatchUpdateApi() {
        return "/kapi/v2/basedata/bd_customergroup/batchUpdate";
    }

    @Override
    public String getBatchSubmitApi() {
        return null;
    }

    @Override
    public String getBatchUnSubmitApi() {
        return null;
    }

    @Override
    public String getBatchAuditApi() {
        return null;
    }

    @Override
    public String getBatchUnAuditApi() {
        return null;
    }

    @Override
    public String getBatchEnableApi() {
        return "/kapi/v2/basedata/bd_customergroup/batchEnable";
    }

    @Override
    public String getBatchDisableApi() {
        return "/kapi/v2/basedata/bd_customergroup/batchDisable";
    }

    @Override
    public String getBatchDeleteApi() {
        return "/kapi/v2/basedata/bd_customergroup/batchDelete";
    }
}
