package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "oa_flow_mq_config")
public class OAFlowMqConfigEntity {

    @Id
    @Column(name = "id")
    private String id;
    /**
     * 事件类型
     */
    @Column(name = "event_type")
    private String eventType;
    /**
     * 对象编码：ApprovalTaskObj、BpmTask
     */
    @Column(name = "obj_api_name")
    private String objApiName;
    /**
     * APL函数编码
     */
    @Column(name = "apl_api_name")
    private String aplApiName;
    /**
     * 企业ID
     */
    @Column(name = "tenant_id")
    private String tenantId;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;
    /**
     * 创建人
     */
    @Column(name = "creator")
    private String creator;
    /**
     * 修改人
     */
    @Column(name = "modifier")
    private String modifier;

}
