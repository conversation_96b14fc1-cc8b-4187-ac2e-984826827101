package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * k3的字段扩展类
 * <AUTHOR> (^_−)☆
 * @date 2021/1/14
 */
public class K3FieldExtend {
    @AllArgsConstructor
    @Getter
    public enum OperatorTypeEnum{
        /**
         * 员工
         */
        EMP("EMP",""),
        /**
         * 销售员
         */
        XSY("XSY",""),
        ;

        /**
         * 类型
         */
        private final String type;

        /**
         * 独立表单id
         */
        private final String formId;
    }
    /**
     * 人员
     */
    @Data
    public static class EmpInfo {
        /**
         * 业务员类型，包含员工
         */
        private OperatorTypeEnum operatorType;
    }
}
