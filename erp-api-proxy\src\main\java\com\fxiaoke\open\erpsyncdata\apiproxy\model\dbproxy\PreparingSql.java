package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.clone.CloneSupport;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 预备的Sql
 * 在准备后之后调用toNamedSql转换为NamedSql
 *
 * <AUTHOR> (^_−)☆
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PreparingSql extends CloneSupport<PreparingSql> {
    /**
     * 原始sql，变量使用 #+字段 标识变量
     */
    private String sql;
    /**
     * 变量,不一定是最终的，复合请求中，允许使用变量获取其他请求的结果。
     * <br/>
     * 使用 @{referenceId.path} 标识变量，找不到则使用原值。
     * <br/>
     * 为什么不是用 #+字段？ 那误判概率太高了。
     */
    private Map<String, Object> parameters;

    /**
     * 子查询，在获取结果后，遍历每条结果进行查询,并将结果放到key内。
     * 和parameters+查询结果（相同key后者覆盖前者）作为parameters。
     */
    private LinkedHashMap<String, String> relationalQueryMap;

    public <T extends PreparingSql> T putAllParametersFromBean(Object bean) {
        putAllParameters(BeanUtil.beanToMap(bean));
        //noinspection unchecked
        return (T) this;
    }

    public <T extends PreparingSql> T addParameters(String key, Object value) {
        if (parameters == null) {
            parameters = new HashMap<>();
        }
        parameters.put(key, value);
        //noinspection unchecked
        return (T) this;
    }


    public <T extends PreparingSql> T putAllParameters(Map<String, Object> otherParameters) {
        if (parameters == null) {
            parameters = new HashMap<>();
        }
        parameters.putAll(otherParameters);
        //noinspection unchecked
        return (T) this;
    }

    public PreparingSql(String sql) {
        this.sql = sql;
    }
}
