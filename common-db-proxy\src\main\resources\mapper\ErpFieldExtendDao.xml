<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity">
        <!--@mbg.generated-->
        <!--@Table erp_field_extend-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="field_api_name" jdbcType="VARCHAR" property="fieldApiName"/>
        <result column="field_define_type" jdbcType="VARCHAR" property="fieldDefineType"/>
        <result column="view_code" jdbcType="VARCHAR" property="viewCode"/>
        <result column="view_extend" jdbcType="VARCHAR" property="viewExtend"/>
        <result column="save_code" jdbcType="VARCHAR" property="saveCode"/>
        <result column="save_extend" jdbcType="VARCHAR" property="saveExtend"/>
        <result column="used_query" jdbcType="VARCHAR" property="usedQuery"/>
        <result column="query_code" jdbcType="VARCHAR" property="queryCode"/>
        <result column="erp_field_type" jdbcType="VARCHAR" property="erpFieldType"/>
        <result column="priority" jdbcType="BIGINT" property="priority"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, obj_api_name, field_api_name, field_define_type, view_code, view_extend,
        save_code, save_extend, query_code, priority, create_time, update_time,erp_field_type,used_query,data_center_id
    </sql>

    <delete id="deleteByTenantId">
        delete from erp_field_extend
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and (data_center_id is null or data_center_id='')
    </delete>

    <!--auto generated by MybatisCodeHelper on 2020-11-10-->
    <select id="queryByObjApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <choose>
            <when test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                and (data_center_id is null or data_center_id='')
            </otherwise>
        </choose>
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
        order by priority, id
    </select>

    <select id="queryIdFieldByObjApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
          and obj_api_name = #{objApiName,jdbcType=VARCHAR}
          and field_define_type = 'id'
    </select>

    <insert id="superInsertSql">
        ${sqlStr}
    </insert>

    <select id="superQuerySql" resultType="java.util.Map">
        ${sqlStr}
    </select>

    <update id="superUpdateSql">
        ${sqlStr}
    </update>

    <!--auto generated by MybatisCodeHelper on 2020-12-15-->
    <delete id="deleteByTenantIdAndObjApiName">
        delete
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
    </delete>

    <!--auto generated by MybatisCodeHelper on 2020-12-24-->
    <select id="findOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <choose>
            <when test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                and (data_center_id is null or data_center_id='')
            </otherwise>
        </choose>
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
        and field_api_name = #{fieldApiName,jdbcType=VARCHAR}
    </select>

    <!--auto generated by MybatisCodeHelper on 2020-12-24-->
    <update id="updateByApiName">
        update erp_field_extend
        <set>
            <if test="updated.tenantId != null">
                tenant_id = #{updated.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="updated.dataCenterId != null">
                data_center_id = #{updated.dataCenterId,jdbcType=VARCHAR},
            </if>
            <if test="updated.objApiName != null">
                obj_api_name = #{updated.objApiName,jdbcType=VARCHAR},
            </if>
            <if test="updated.fieldApiName != null">
                field_api_name = #{updated.fieldApiName,jdbcType=VARCHAR},
            </if>
            <if test="updated.fieldDefineType != null">
                field_define_type = #{updated.fieldDefineType,jdbcType=VARCHAR},
            </if>
            <if test="updated.viewCode != null">
                view_code = #{updated.viewCode,jdbcType=VARCHAR},
            </if>
            <if test="updated.viewExtend != null">
                view_extend = #{updated.viewExtend,jdbcType=VARCHAR},
            </if>
            <if test="updated.saveCode != null">
                save_code = #{updated.saveCode,jdbcType=VARCHAR},
            </if>
            <if test="updated.saveExtend != null">
                save_extend = #{updated.saveExtend,jdbcType=VARCHAR},
            </if>
            <if test="updated.queryCode != null">
                query_code = #{updated.queryCode,jdbcType=VARCHAR},
            </if>
            <if test="updated.priority != null">
                priority = #{updated.priority,jdbcType=BIGINT},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=BIGINT},
            </if>
            <if test="updated.erpFieldType != null">
                erp_field_type = #{updated.erpFieldType,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="updated.tenantId != '01'">
                and tenant_id = #{updated.tenantId,jdbcType=VARCHAR}
            </if>
            and data_center_id = #{updated.dataCenterId,jdbcType=VARCHAR}
            and obj_api_name = #{updated.objApiName,jdbcType=VARCHAR}
            and field_api_name = #{updated.fieldApiName,jdbcType=VARCHAR}
        </where>
    </update>

<!--auto generated by MybatisCodeHelper on 2021-03-02-->
    <delete id="deleteFieldExtendOne">
        delete from erp_field_extend
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name=#{objApiName,jdbcType=VARCHAR}
        and field_api_name=#{fieldApiName,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByTenantIdAndDataCenterId">
        delete from erp_field_extend
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
    </delete>

    <select id="getNumFieldByObjApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
        and erp_field_type = 'e12'
        order by update_time desc
        limit 1
    </select>
    <select id="getAllNeedQueryFieldExtend"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
        and used_query=true
        order by priority, id
    </select>
    <select id="queryByObjQueryCode"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
        and query_code in
        (
        <foreach collection="codeList" item="code" separator=",">
            #{code}
        </foreach>
        )
        order by priority, id
    </select>

    <select id="findByDefineType"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and obj_api_name = #{objApiName,jdbcType=VARCHAR}
        <choose>
            <when test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                and (data_center_id is null or data_center_id='')
            </otherwise>
        </choose>
        and field_define_type = #{fieldDefineType,jdbcType=VARCHAR};
    </select>
    <select id="pageByDcId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_field_extend
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        <if test="id != null">
            and id &lt; #{id,jdbcType=VARCHAR}
        </if>
        order by id desc
        limit #{pageSize,jdbcType=INTEGER}
    </select>

    <update id="updateQueryCodeById">
        update erp_field_extend
        <set>
            <if test="queryCode != null">
                query_code = #{queryCode,jdbcType=VARCHAR},
            </if>
            <if test="erpFieldType != null">
                erp_field_type = #{erpFieldType,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            id = #{id,jdbcType=VARCHAR}
        </where>
    </update>

    <update id="updatePriorityField">
        update erp_field_extend
        set priority = #{priority}
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name=#{objApiName,jdbcType=VARCHAR}
        and field_api_name=#{fieldApiName,jdbcType=VARCHAR} 
    </update>
    <update id="batchUpdateFieldExtendStatus">
        update erp_field_extend
        set used_query =#{status}
        where tenant_id=#{tenantId}
        and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        and obj_api_name=#{objApiName}
        and field_define_type not in ('detail','id')
        and field_api_name in
        (
        <foreach collection="fieldApiNames" item="id" separator=",">
            #{id}
        </foreach>
        );
    </update>

<!--auto generated by MybatisCodeHelper on 2022-11-29-->
    <update id="updateSaveExtendById">
        update erp_field_extend
        set save_extend=#{updatedSaveExtend,jdbcType=VARCHAR}
        where id=#{id,jdbcType=VARCHAR}
    </update>

    <update id="updateErpFieldTypeById">
        update erp_field_extend
        set erp_field_type=#{erpFieldType,jdbcType=VARCHAR}
        where id=#{id,jdbcType=VARCHAR}
    </update>
</mapper>