package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialWayDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.header.HeaderReplaceService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ObjToMapUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HeaderFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/16 10:26 hearder相关的manger
 * @Version 1.0
 */
@Slf4j
@Component
public class HeaderManager {
    @Autowired
    private SpecialWayDataService specialWayDataService;
    @Autowired
    private List<HeaderReplaceService> headerReplaceServices;


    public Result<Map<String,String>> getHeaderMapByFunction(HeaderFunctionArg headerFunctionArg){
        //前端header函数
        Result<String> headerResult = specialWayDataService.executeCustomFunction(headerFunctionArg.getTenantId(), headerFunctionArg.getDataCenterId(), headerFunctionArg.getFunctionName(), ObjToMapUtil.objectToMap(headerFunctionArg.getParams()),
                headerFunctionArg.getInterfaceUrl(), null, null, null);
        if(headerResult.isSuccess()){
           Map<String,String> headerMap= null;
            try {
                headerMap=JSONObject.parseObject(headerResult.getData(), Map.class);
            } catch (Exception e) {
                log.info("getHeaderMapByFunction error:{}",e.getMessage());
                return Result.newError(ResultCodeEnum.VERIFY_STATUS_FAIL.getErrCode(),
                        I18NStringEnum.s1157);
            }
            return Result.newSuccess(headerMap);
        }
        return Result.newError(ResultCodeEnum.VERIFY_STATUS_FAIL.getErrCode(), I18NStringEnum.s153);
    }

    public Result<Map<String, String>> getHeaderMap(Map<String, String> headerMap, String tenantId) {
        if (MapUtils.isEmpty(headerMap)) {
            return Result.newSuccess(new HashMap<>());
        }

        return Result.newSuccess(headerMap.entrySet().stream()
                .map(entry -> {
                    final String value = entry.getValue();
                    final String newValue = headerReplaceServices.stream()
                            .filter(headerReplaceService -> headerReplaceService.isAccept(value))
                            .findFirst()
                            .map(headerReplaceService -> headerReplaceService.replace(tenantId, value))
                            .orElse(value);

                    return Pair.of(entry.getKey(), newValue);
                }).collect(Collectors.toMap(Pair::getKey, Pair::getValue)));
    }
}
