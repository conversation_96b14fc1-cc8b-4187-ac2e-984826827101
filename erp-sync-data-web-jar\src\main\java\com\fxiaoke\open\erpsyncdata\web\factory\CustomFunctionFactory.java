package com.fxiaoke.open.erpsyncdata.web.factory;

import com.fxiaoke.open.erpsyncdata.web.service.customFunction.BaseCustomFunctionCommonServiceImpl;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.CustomFunctionCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 10:15 2021/3/18
 * @Desc:
 */
@Component
public class CustomFunctionFactory {
    @Autowired
    private Map<String, CustomFunctionCommonService> customFunctionCommonServiceFactory;

    public CustomFunctionCommonService getHandlerByType(String type) {
        CustomFunctionCommonService customFunctionCommonService = customFunctionCommonServiceFactory.get(type);
        return customFunctionCommonService == null ? new BaseCustomFunctionCommonServiceImpl() : customFunctionCommonService;
    }
}
