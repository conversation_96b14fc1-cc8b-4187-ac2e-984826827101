package com.fxiaoke.open.erpsyncdata.web.service.customFunction;


import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.UpLoadFileArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.UpLoadFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("downloadAndUploadFile")
public class DownloadAndUploadFileServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private UpLoadFileService upLoadFileService;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        UpLoadFileArg arg = JsonUtil.fromJson(commonArg.getParams(), UpLoadFileArg.class);
        return upLoadFileService.doFileService(tenantId, arg);
    }
}
