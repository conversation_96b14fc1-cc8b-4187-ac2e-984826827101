# 单元测试规范

## 1. 覆盖率要求以及生产代码修改约束
- 不允许直接修改生产代码（src/main/java目录下的代码）
- 所有功能改动必须通过修改测试代码（src/test目录下的代码）来实现
- 如果发现生产代码有问题，应该先写测试用例复现问题，然后再修改生产代码
- **核心包的单元测试必须在 erp-sync-data-all 模块中补充完整的 JUnit 测试用例**

### 1.1 核心包覆盖率目标
- 行覆盖率（Line Coverage）：≥ 80%
- 分支覆盖率（Branch Coverage）：≥ 70%
- 方法覆盖率（Method Coverage）：≥ 85%
- 类覆盖率（Class Coverage）：≥ 90%

### 1.2 重点覆盖包
```
com.fxiaoke.open.erpsyncdata.writer.manager.**
com.fxiaoke.open.erpsyncdata.main.service
com.fxiaoke.open.erpsyncdata.main.dispatcher.**
com.fxiaoke.open.erpsyncdata.admin.manager.**
com.fxiaoke.open.erpsyncdata.admin.service.impl.**
```

## 2. erp-sync-data-all 模块测试要求

### 2.1 测试范围
1. 所有核心包的功能都必须在 erp-sync-data-all 模块中有对应的单元测试
2. 测试用例应覆盖完整的业务流程
3. 需要包含集成测试场景

### 2.2 测试组织结构
1. 测试类应按照被测试类的包结构组织
2. 集成测试和单元测试应分开放置
3. 测试资源文件应统一管理

### 2.3 测试环境配置
1. 提供完整的测试环境配置文件
2. 使用专门的测试配置类
3. 配置必要的测试依赖

## 3. 测试规范

### 3.1 基本规范
1. 测试类命名：被测试类名 + Test
2. 测试方法命名：test + 方法名 + 测试场景
3. 每个测试方法只测试一个功能点
4. 遵循 AAA (Arrange-Act-Assert) 模式

### 3.2 测试框架选择
1. 主要测试框架：JUnit/Spock
2. Mock框架：Mockito
3. 断言框架：AssertJ/Spock内置断言

### 3.3 测试用例设计
1. 正常流程测试（Happy Path）
2. 边界条件测试
3. 异常流程测试
4. 参数校验测试

### 3.4 代码组织
```java
@Test
public void testMethodName_Scenario() {
    // Arrange - 准备测试数据和环境
    
    // Act - 执行被测试方法
    
    // Assert - 验证结果
}
```

## 4. 最佳实践

### 4.1 测试隔离
1. 每个测试用例必须独立
2. 使用@Before/@After进行测试环境准备和清理
3. 避免测试用例之间的依赖

### 4.2 测试数据管理
1. 使用Builder模式构建测试数据
2. 避免使用硬编码的测试数据
3. 使用测试工具类管理公共测试数据

### 4.3 Mock使用规范
1. 只Mock外部依赖
2. Mock返回值要符合实际业务场景
3. 验证Mock对象的调用次数和参数

### 4.4 数据库测试
1. 使用H2等内存数据库
2. 每个测试用例后清理测试数据
3. 使用事务回滚保证数据一致性

## 5. 代码审查要求

### 5.1 测试代码审查清单
1. 测试覆盖率是否达标
2. 测试用例是否完整
3. 是否包含边界条件测试
4. 是否包含异常流程测试
5. Mock使用是否合理
6. 测试代码是否简洁清晰
7. **是否在 erp-sync-data-all 模块中补充了对应的测试**

### 5.2 常见问题
1. 测试用例过于复杂
2. 测试数据准备不充分
3. 断言不够具体
4. 测试用例之间存在依赖
5. 没有测试异常流程
6. **缺少在 erp-sync-data-all 模块中的测试用例**

## 6. 持续集成

### 6.1 CI配置
1. 在每次提交时运行单元测试
2. 生成测试覆盖率报告
3. 如果测试失败或覆盖率不达标则阻止合并
4. **确保 erp-sync-data-all 模块的测试全部通过**

### 6.2 覆盖率报告要求
1. 在CI pipeline中生成HTML格式的覆盖率报告
2. 保存历史覆盖率数据，用于趋势分析
3. 设置覆盖率阈值，低于阈值则构建失败
4. **单独统计并展示 erp-sync-data-all 模块的测试覆盖率** 