package com.fxiaoke.open.erpsyncdata.apiproxy.model.crm;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/9
 */
public class ProductCategory {

    public static final String CATEGORY_CODE = "category_code";
    public static final String CODE = "code";
    public static final String PID = "pid";

    /**
     * crm返回的加过还加一层result
     * @param <T>
     */
    @Data
    public static class inResult<T> implements Serializable {
        private static final long serialVersionUID = 7782420902737364093L;
        private T result;
    }

    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    public static class ListResult extends BaseResult<inResult<List<ObjectData>>> implements Serializable {
        private static final long serialVersionUID = 3050971808317578483L;
    }

    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    public static class SaveResult extends BaseResult<inResult<ObjectData>> implements Serializable {
        private static final long serialVersionUID = 3050971808317578483L;
    }

    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    public static class UpdateResult extends BaseResult<inResult<Boolean>> implements Serializable {
        private static final long serialVersionUID = 3050971808317578484L;
    }

    @Data
    public static class Vo implements Serializable {
        private static final long serialVersionUID = -5143254259396054685L;

        private String id;

        /**
         * 实际使用的code
         */
        private String code;

        /**
         * 分类编码
         */
        private String categoryCode;

        /**
         * 父id
         */
        private String pid;

        /**
         * 分类名称
         */
        private String name;

        /**
         * 父分类编码
         */
        private String parentCateGoryCode;

        /**
         * 顺序
         */
        private Integer orderField;

        /**
         * 更新标识，0不用更新，1新增，2更新
         */
        private int updateTag = 0;

        private List<Vo> children = new ArrayList<>();

        public static Vo fromObjData(ObjectData objectData){
            ProductCategory.Vo category = new ProductCategory.Vo();
            category.setId(objectData.getId());
            category.setCode(objectData.getString(ProductCategory.CODE));
            category.setCategoryCode(objectData.getString(ProductCategory.CATEGORY_CODE));
            category.setPid(objectData.getString(ProductCategory.PID));
            category.setOrderField(objectData.getInt("order_field"));
            category.setName(objectData.getName());
            return category;
        }

        public  ObjectData toObjData(){
            ObjectData objectData = new ObjectData();
            objectData.putId(this.id);
            objectData.put(ProductCategory.CODE,this.code);
            objectData.put(ProductCategory.CATEGORY_CODE,this.categoryCode);
            objectData.put(ProductCategory.PID,this.pid);
            objectData.put("order_field",this.orderField);
            objectData.putApiName("ProductCategoryObj");
            objectData.put("name",this.name);
            return objectData;
        }
    }
}
