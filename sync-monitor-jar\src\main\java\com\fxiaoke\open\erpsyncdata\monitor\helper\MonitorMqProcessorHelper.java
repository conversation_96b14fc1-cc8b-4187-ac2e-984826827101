package com.fxiaoke.open.erpsyncdata.monitor.helper;

import com.fxiaoke.open.erpsyncdata.monitor.mq.processor.AbstractMonitorMqOrderlyProcessor;
import com.fxiaoke.open.erpsyncdata.monitor.mq.processor.AbstractMonitorMqProcessor;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@Service
public class MonitorMqProcessorHelper {
    private static final Map<MonitorType, AbstractMonitorMqProcessor<?>> monitorMqProcessorMap = new HashMap<>();

    private static final Map<MonitorType, AbstractMonitorMqOrderlyProcessor<?>> monitorMqOrderlyProcessorMap = new HashMap<>();
    @Autowired
    public void setMonitorMqProcessorMap(List<AbstractMonitorMqProcessor<?>> services) {
        for (AbstractMonitorMqProcessor<?> service : services) {
            monitorMqProcessorMap.put(service.getMonitorType(), service);
        }
    }

    @Autowired
    public void setMonitorMqOrderlyProcessorMap(List<AbstractMonitorMqOrderlyProcessor<?>> services) {
        for (AbstractMonitorMqOrderlyProcessor<?> service : services) {
            monitorMqOrderlyProcessorMap.put(service.getMonitorType(), service);
        }
    }

    public static AbstractMonitorMqProcessor<?> get(MonitorType type) {
        return monitorMqProcessorMap.get(type);
    }

    public static AbstractMonitorMqOrderlyProcessor<?> getOrderly(MonitorType type) {
        return monitorMqOrderlyProcessorMap.get(type);
    }
}
