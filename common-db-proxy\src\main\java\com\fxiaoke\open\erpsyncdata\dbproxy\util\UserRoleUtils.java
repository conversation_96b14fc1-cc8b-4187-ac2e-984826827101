package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.RoleModel;
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg;
import com.fxiaoke.paasauthrestapi.common.data.HeaderObj;
import com.fxiaoke.paasauthrestapi.common.result.Result;
import com.fxiaoke.paasauthrestapi.result.RoleResult;
import com.fxiaoke.paasauthrestapi.result.RoleUserResult;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class UserRoleUtils {
    public static List<RoleModel> getRoleModelList(String tenantId, List<String> roleIdList, PaasAuthService paasAuthService) {
        List<RoleModel> roleModelList = new ArrayList<>();
        int ei = Integer.valueOf(tenantId);
        for (String roleCode : roleIdList) {
            //角色
            try {
                HeaderObj headerObj = HeaderObj.newInstance(ei);
                Result<RoleResult> result = paasAuthService.roleTypes(headerObj, new RoleUserArg("CRM",
                        ei,
                        -10000,
                        roleCode));
                RoleModel roleModel = new RoleModel();
                if (result.getErrCode() != 0) {
                    roleModel.setRoleId(roleCode);
                    roleModel.setRoleName(roleCode);
                } else {
                    RoleResult.RoleData roleData = result.getResult().getRoles().get(0);
                    roleModel.setRoleId(roleCode);
                    roleModel.setRoleName(roleData.getRoleName());
                }

                roleModelList.add(roleModel);

            } catch (Exception e) {
                log.info("UserRoleUtils.getRoleModelList, error", e);
            }
        }

        return roleModelList;
    }

    public static List<String> getRoleUserList(String tenantId, List<String> roleIdList, PaasAuthService paasAuthService) {
        List<String> userList = new ArrayList<>();
        int ei = Integer.valueOf(tenantId);
        for (String roleCode : roleIdList) {
            //角色
            try {
                HeaderObj headerObj = HeaderObj.newInstance(ei);
                Result<RoleUserResult> result = paasAuthService.roleUser(headerObj, new RoleUserArg("CRM",
                        ei,
                        -10000,
                        roleCode));
                log.info("UserRoleUtils.getRoleUserList,roleUser,reuslt={}", result);
                if (result.getErrCode() != 0) {
                    log.info("UserRoleUtils.getRoleUserList,roleUser,errorMsg={}", result.getErrMessage());
                    continue;
                }
                List<String> userIdList = result.getResult().getUsers().stream().collect(Collectors.toList());
                userList.addAll(userIdList);
            } catch (Exception e) {
                log.info("UserRoleUtils.getRoleUserList, error", e);
            }
        }

        return userList;
    }
}
