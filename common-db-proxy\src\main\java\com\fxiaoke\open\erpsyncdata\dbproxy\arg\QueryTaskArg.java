package com.fxiaoke.open.erpsyncdata.dbproxy.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/4 17:58
 * @desc
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class QueryTaskArg  extends BaseDbQueryArg{
    private String taskId;
    @Builder.Default
    private Integer page=1;
    @Builder.Default
    private Integer pageSize=100;
    private List<String> taskIds;
}
