package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheInvalidateContainer;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/29 09:51:58
 * 模板企业缓存失效需要同时失效下游企业缓存
 */
@Component
@Slf4j
@Order
@Aspect
public class TemplateJetCacheInvalidateAspect extends AbstractReplaceEnterpriseAspect {


    public static final Set<Class<? extends Annotation>> checkAnnotation = Sets.newHashSet(CacheInvalidateContainer.class, CacheInvalidate.class);

    @Around("execution(* com.fxiaoke.open.erpsyncdata..*.*(..))")
    public Object templateJetCacheInvalidate(ProceedingJoinPoint jp) throws Throwable {
        final Object proceed = jp.proceed();

        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final Method method = methodSignature.getMethod();
        if (checkAnnotation.stream().anyMatch(annotation -> Objects.nonNull(method.getAnnotation(annotation)))) {
            cleanDownstreamCache(jp);
        }

        return proceed;
    }

    private void cleanDownstreamCache(ProceedingJoinPoint jp) throws Throwable {
        // 入参的tenantId为一个企业
        final String tenantId = getTenantIdByParameter(jp);
        if (Objects.isNull(tenantId)) {
            return;
        }

        final List<String> downstreamIds = relationErpShardDao.getAllDownstreamIdsByTemplateId(tenantId);
        if (CollectionUtils.isEmpty(downstreamIds)) {
            return;
        }

        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final Integer tenantIdIndex = getTenantIdIndex(methodSignature);
        final Object[] args = jp.getArgs();

        final Object[] newArgs = new Object[args.length];
        System.arraycopy(args, 0, newArgs, 0, args.length);
        downstreamIds.forEach(downstreamId -> {
            newArgs[tenantIdIndex] = downstreamId;
            try {
                jp.proceed(newArgs);
            } catch (Throwable e) {
                log.error("通知下游企业缓存失效失败,tenantId:{},downstreamId:{}", tenantId, downstreamId, e);
            }
        });
        log.info("cleanDownstreamCache end tenantId:{} downstreamIds:{}", tenantId, downstreamIds);
    }
}
