package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjFieldsDto;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
@ManagedTenantReplace
public interface ErpObjectFieldDao extends ErpBaseDao<ErpObjectFieldEntity>, ITenant<ErpObjectFieldDao> {
    ErpObjectFieldEntity findByTenantIdAndId(@Param("tenantId") String tenantId, @Param("id") String id);

    int deleteByObjApiName(@Param("ei") String ei, @Param("objApiName") String objApiName, @Param("dataCenterId") String dataCenterId);

    List<ErpObjectFieldEntity> pageByDcId(@Param("tenantId") String tenantId, @Param("dataCenterId")String dcId, @Param("id") String maxId, @Param("pageSize") int pageSize);

    int deleteByTenantIdAndDcId(@Param("tenantId")String tenantId,@Param("dataCenterId")String dataCenterId);

    ErpObjectFieldEntity findIdField(@Param("tenantId") String tenantId,
                                     @Param("erpObjectApiName") String erpObjectApiName);

    ErpObjectFieldEntity findIdField2(@Param("tenantId") String tenantId,
                                      @Param("dataCenterId")String dataCenterId,
                                      @Param("erpObjectApiName") String erpObjectApiName);

    /**
     * 查企业所有字段，理论上就一些工具类用这个方法
     * @param tenantId
     * @return
     */
    List<ErpObjectFieldEntity> findByTenantId(@Param("tenantId")String tenantId);


    List<ErpObjectFieldEntity> findData(@Param("tenantId") String tenantId,
                                        @Param("dataCenterId") String dataCenterId,
                                        @Param("erpObjectApiName") String erpObjectApiName);

    List<ErpObjectFieldEntity> findSelectOneFieldList(@Param("tenantId") String tenantId,
                                        @Param("dataCenterId") String dataCenterId,
                                        @Param("objectApiNameList") List<String> objectApiNameList);


    List<ErpObjectFieldEntity> findByObjsAndType(@Param("tenantId")String tenantId, @Param("dataCenterId")String dataCenterId, @Param("erpObjectApiNameCollection")Collection<String> erpObjectApiNameCollection, @Param("fieldDefineType")ErpFieldTypeEnum fieldDefineType);


    /**
     * 主从字段
     * @param extendObjectApiName 主对象apiName
     */
    List<ErpObjectFieldEntity> queryMasterDetailField(@Param("tenantId") String tenantId,
                                               @Param("erpObjectApiName") String erpObjectApiName,
                                               @Param("extendObjectApiName") String extendObjectApiName);


    /**
     * 根据真实apiName查找所有拆分对象DTO
     * 包含id字段
     * @param tenantId
     * @param realApiName
     * @return
     */
    List<ErpObjExtendDto> queryObjExtendDTO(@Param("tenantId") String tenantId,
                                            @Param("dataCenterId") String dataCenterId,
                                            @Param("realApiName") String realApiName);

    ErpObjExtendDto findObjExtendDtoBySplit(@Param("tenantId") String tenantId,
                                            @Param("splitApiName") String splitApiName);
    /**
     * 通过类型查找字段
     * 禁止直接调用该方法
     *
     * @param tenantId
     * @param erpObjectApiName
     * @param fieldDefineTypeCollection
     * @return
     */
    List<ErpObjectFieldEntity> findByObjApiNameAndType(@Param("tenantId")String tenantId,
            @Param("erpObjectApiName")String erpObjectApiName,
            @Param("fieldDefineTypeCollection")Collection<ErpFieldTypeEnum> fieldDefineTypeCollection);
    /**
     * 分页查找所有字段
     *
     * @param tenantId
     * @param erpObjectApiName
     * @return
     */
    List<ErpObjectFieldEntity> queryFieldsByTenantIdAndObjectApiName(@Param("tenantId") String tenantId, @Param("erpObjectApiName") String erpObjectApiName, @Param("queryStr") String queryStr, @Param("limit") Integer limit, @Param("offset") Integer offset);
    /**
     * 分页查找所有字段
     *
     * @param tenantId
     * @param erpObjectApiName
     * @return
     */
    int countByTenantIdAndObjectApiName(@Param("tenantId") String tenantId, @Param("erpObjectApiName") String erpObjectApiName, @Param("queryStr") String queryStr);

    int deleteByTenantId(String tenantId);
    /**
     * id,erp_object_apiname,field_label,field_apiname,field_define_type,field_extend_value
     * @param tenantId
     * @return
     */
    List<ErpObjectFieldEntity>  getObjectFieldByTenantId(@Param("tenantId") String tenantId, @Param("erpObjectApiName") String erpObjectApiName);

    /**
     * 批量查询对象和部分字段
     * @param tenantId
     * @param dcId
     * @param objFieldsDtos
     * @return
     */
    List<ErpObjectFieldEntity> bulkQuery(@Param("tenantId") String tenantId,@Param("dcId")String dcId,
              @Param("objFieldsDtos")Collection<ObjFieldsDto> objFieldsDtos);
}