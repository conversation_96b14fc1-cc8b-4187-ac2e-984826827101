package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store

import cn.hutool.core.lang.Singleton
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.sharding.ShardPolicy
import com.github.mongo.support.DatastoreExt
import com.github.mongo.support.MongoDataStoreFactoryBean
import com.github.mongo.support.TenantPolicy
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import com.google.gson.Gson
import com.mongodb.client.model.Filters
import com.mongodb.client.model.Sorts
import org.bson.Document
import org.bson.conversions.Bson
import spock.lang.Ignore

import javax.validation.constraints.AssertTrue

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023-06-19
 *
 *
 * 如果运行报错：
 * Caused by: net.sf.cglib.core.CodeGenerationException: java.lang.reflect.InaccessibleObjectException
 -->Unable to make protected final java.lang.Class
 java.lang.ClassLoader.defineClass(java.lang.String,byte[],int,int,java.security.ProtectionDomain) throws java.lang.ClassFormatError accessible:
 module java.base does not "opens java.lang" to unnamed module
 *
 * 解决方法：
 * 在 Run->Edit Configurations...->VM options 中添加如下参数：
 * --add-opens java.base/java.lang=ALL-UNNAMED --add-exports java.base/jdk.internal.module=ALL-UNNAMED
 */
// 依赖数据库数据,先Ignore
@Ignore
class ErpTempMongoTest extends BaseSpockTest {
    private ErpTempMongoStore erpTempMongoStore;
    private DatastoreExt store;

    void setup() {
        MongoDataStoreFactoryBean bean = new MongoDataStoreFactoryBean()
        bean.setConfigName("erp-sync-data-all")
         bean.setTenantPolicy(new TenantPolicy() {
         //无路由，直接返回server mongo
         @Override
          String getUri(String tenantId) { //82777在这个地址上
          return "***************************************************************************************************************************";
          }
          })
        bean.afterPropertiesSet()
        store = bean.getObject()
        erpTempMongoStore = new ErpTempMongoStore(store: store,configCenterConfig : Mock(ConfigCenterConfig) {
            readTempSecondaryPreference() >> {
                return Sets.newHashSet();
            }
        });
    }
     /**使用listErpTempDataDocumentField 替换 comparelistErpTempDataDocument 的测试用例
     * */
    def "comparelistErpTempDataDocument"() {
        List<Document> documents1 = listErpTempDataDocument(0, 2);
        List<Document> documents2 = listErpTempDataDocumentField(0, 2)
        List<Document> documents3 = listErpTempDataDocument(2, 2);
        List<Document> documents4 = listErpTempDataDocumentField(2, 2)

        expect:
        documents1.size()>0
        documents1.size() == documents2.size()
        documents3.size() >0
        documents3.size() == documents4.size()
        for(int i = 0; i < documents1.size(); i++) {
            boolean sameField1 = documents1.get(i).get("tenant_id").equals(documents2.get(i).get("tenant_id"))
            boolean sameField2 = documents1.get(i).get("dc_id").equals(documents2.get(i).get("dc_id"))
            boolean sameField3 = documents1.get(i).get("obj_api_name").equals( documents2.get(i).get("obj_api_name"))
            boolean sameField4 = documents1.get(i).get("data_id").equals( documents2.get(i).get("data_id"))
            boolean sameField5 = documents1.get(i).get("data_md5").equals( documents2.get(i).get("data_md5"));
            assert sameField1 && sameField2 && sameField3 && sameField4 && sameField5;
        }

        for(int i = 0; i < documents3.size(); i++) {
            boolean sameField1 = documents3.get(i).get("tenant_id").equals(documents4.get(i).get("tenant_id"))
            boolean sameField2 = documents3.get(i).get("dc_id").equals(documents4.get(i).get("dc_id"))
            boolean sameField3 = documents3.get(i).get("obj_api_name").equals( documents4.get(i).get("obj_api_name"))
            boolean sameField4 = documents3.get(i).get("data_id").equals( documents4.get(i).get("data_id"))
            boolean sameField5 = documents3.get(i).get("data_md5").equals( documents4.get(i).get("data_md5"));
            assert sameField1 && sameField2 && sameField3 && sameField4 && sameField5;
        }
    }

    def "listErpTempDataDocument" (offset, limit) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", "82777"));
        filters.add(Filters.eq("dc_id", "696453487420604416"));
        filters.add(Filters.eq("obj_api_name", "AR_RECEIVEBILL"));
        //filters.add(Filters.in("data_id", Lists.newArrayList("104347","104348")));
        Bson sort = Sorts.ascending("data_id");
        List<Document> documents = erpTempMongoStore.listErpTempDataDocument("82777", filters, sort, offset, limit);
        //System.out.println("print listErpTempDataDocument: ");
        //System.out.println(new Gson().toJson(documents));
        return documents;
    }

    def "listErpTempDataDocumentField" (offset, limit) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenant_id", "82777"));
        filters.add(Filters.eq("dc_id", "696453487420604416"));
        filters.add(Filters.eq("obj_api_name", "AR_RECEIVEBILL"));
        //filters.add(Filters.in("data_id", Lists.newArrayList("104347")));
        Bson sort = Sorts.ascending("data_id");
        List<String> projectFields = Lists.newArrayList("tenant_id","dc_id", "obj_api_name", "data_id", "data_md5");
        List<Document> documents = erpTempMongoStore.listErpTempDataDocumentField("82777", filters, sort, offset, limit, projectFields);
        System.out.println("print listErpTempDataDocumentField: ");
        System.out.println(new Gson().toJson(documents));
        return documents;
    }

}
