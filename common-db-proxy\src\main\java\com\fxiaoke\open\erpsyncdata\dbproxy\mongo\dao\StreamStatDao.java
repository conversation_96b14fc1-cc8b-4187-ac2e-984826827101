package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseLogMongoStore;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 弃用，短时间保留
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */
@Slf4j
//@Repository
@Deprecated
public class StreamStatDao extends BaseLogMongoStore<StreamStat> {


    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(StreamStat.class, StreamStat.ObjMappingStat.class)
                        .automatic(true).build()));
    }

    protected StreamStatDao() {
        super(SingleCodecHolder.codecRegistry);
    }

    @Override
    public String getCollName(String tenantId) {
        return "stream_stat";
    }

    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> indexModels = new ArrayList<>();
        //过期时间
        indexModels.add(new IndexModel(Indexes.ascending(StreamStat.Fields.createTime),
                new IndexOptions().expireAfter(10L, TimeUnit.DAYS).background(true)));
        return indexModels;
    }

    public void batchInsert(String tenantId, List<StreamStat> streamStats) {
        MongoCollection<StreamStat> collection = getOrCreateCollection(tenantId);
        collection.insertMany(streamStats);
    }


    public StreamStat getLatestTraceIdAndTime(String tenantId, String dcId, Date date7DaysAge) {
        Bson filters = Filters.and(
                Filters.eq(StreamStat.Fields.tenantId, tenantId),
                Filters.eq(StreamStat.Fields.dcId, dcId),
                Filters.gt(StreamStat.Fields.createTime, date7DaysAge)
        );
        Bson sorts = Sorts.descending(StreamStat.Fields.createTime);
        StreamStat first = getOrCreateCollection(tenantId).find(filters).limit(1).sort(sorts).first();
        return first;
    }
}
