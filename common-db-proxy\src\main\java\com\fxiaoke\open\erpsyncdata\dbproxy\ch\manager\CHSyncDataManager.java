package com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager;


import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ArrayUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.ErpSyncLogDTO;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ObjectIdTimeUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CHSyncDataManager {
    @Autowired
    private CHSyncDataDao chSyncDataDao;
    @Autowired
    private TenantInfoManager tenantInfoManager;


    private List<SyncDataEntity> changeCHSyncData2SyncDataEntityList(List<CHSyncDataEntity> chSyncDataEntities) {
        List<SyncDataEntity> logs = Lists.newArrayList();
        if (CollectionUtils.isEmpty(chSyncDataEntities)) {
            return logs;
        }
        Set<String> ids= Sets.newHashSet();
        for (CHSyncDataEntity data : chSyncDataEntities) {
            if(ids.contains(data.getId())){//去掉id重复的
                continue;
            }
            SyncDataEntity log = changeCHSyncData2SyncDataEntity(data);
            ids.add(data.getId());
            logs.add(log);
        }
        return logs;
    }

    private SyncDataEntity changeCHSyncData2SyncDataEntity(CHSyncDataEntity data) {
        if (data == null) {
            return null;
        }
        SyncDataEntity log = new SyncDataEntity();
        log.setId(data.getId());
        log.setTenantId(data.getTenantId());
        log.setSourceTenantId(data.getTenantId());
        log.setDestTenantId(data.getTenantId());
        log.setSourceTenantType(data.getSourceTenantType());
        log.setDestTenantType(data.getDestTenantType());
        log.setSourceEventType(data.getSourceEventType());
        log.setSourceObjectApiName(data.getSourceObjectApiName());
        log.setSourceDataId(data.getSourceDataId());
        log.setSourceDataName(data.getErpTempDataDataNumber());
        if (StringUtils.isNotBlank(data.getSourceData())) {
            log.setSourceData(JacksonUtil.fromJson(data.getSourceData(), ObjectData.class));
        }
        if (StringUtils.isNotBlank(data.getSourceDetailSyncDataIds())&&data.getSourceDetailSyncDataIds().startsWith("{")) {//第二个条件兼容一些垃圾数据
            log.setSourceDetailSyncDataIds(JacksonUtil.fromJson(data.getSourceDetailSyncDataIds(), MapListStringData.class));
        }
        log.setDestEventType(data.getDestEventType());
        log.setDestObjectApiName(data.getDestObjectApiName());
        log.setDestDataId(data.getDestDataId());
        if (StringUtils.isNotBlank(data.getDestData())) {
            log.setDestData(JacksonUtil.fromJson(data.getDestData(), ObjectData.class));
        }
        log.setStatus(data.getSyncDataStatus());
        log.setSyncPloyDetailSnapshotId(data.getSyncPloyDetailSnapshotId());
        log.setOperatorId(data.getOperatorId());
        log.setRemark(data.getRemark());
        log.setErrorCode(data.getErrorCode());
        log.setIsDeleted(data.getIsDeleted());
        if (StringUtils.isNotBlank(data.getNeedReturnDestObjectData())) {
            log.setNeedReturnDestObjectData(JacksonUtil.fromJson(data.getNeedReturnDestObjectData(), ObjectData.class));
        }
        log.setSyncLogId(data.getLogId());
        log.setDataReceiveType(data.getDataReceiveType());
        if(StringUtils.isNotBlank(data.getData())){
            log.setData(JacksonUtil.fromJson(data.getData(), SyncDataEntity.NodeMsg.class));
        }
        if (data.getCreateTime() != null) {
            log.setCreateTime(data.getCreateTime().getTime());
        }
        if (data.getUpdateTime() != null) {
            log.setUpdateTime(data.getUpdateTime().getTime());
        }
        return log;
    }


    public int insertIgnore(SyncDataEntity syncDataEntity) {
        if (syncDataEntity == null) {
            return 0;
        }
        CHSyncDataEntity chSyncDataEntity = this.buildCHSyncDataFromSyncData(syncDataEntity);
        return sendBizLog(Lists.newArrayList(chSyncDataEntity));
    }


    public SyncDataEntity getById(String tenantId, String id, String... returnFields) {
        org.apache.commons.lang3.tuple.Pair<Long, Long> objectIdTime = ObjectIdTimeUtil.getObjectIdTime(id);
        CHSyncDataEntity syncDataEntity;
        if (ArrayUtil.isEmpty(returnFields)) {
            syncDataEntity = chSyncDataDao.getById(tenantId, id,new Date(objectIdTime.getLeft()),new Date(objectIdTime.getRight()));
        }else {
            String returnField = ArrayUtil.join(returnFields, ", ");
            syncDataEntity = chSyncDataDao.getByIdSelective(tenantId, id,new Date(objectIdTime.getLeft()),new Date(objectIdTime.getRight()),returnField);
        }
        if (syncDataEntity == null) {
            return null;
        }
        return this.changeCHSyncData2SyncDataEntity(syncDataEntity);
    }


    public List<SyncDataEntity> listByIds(String tenantId, Collection<String> ids,Long startLogTime, Long endLogTime) {
        List<CHSyncDataEntity> res = chSyncDataDao.listByIds(tenantId, ids,new Date(startLogTime),new Date(endLogTime));
        return this.changeCHSyncData2SyncDataEntityList(res);
    }


    public List<SyncDataEntity> listSimpleByIds(String tenantId, Collection<String> ids,Long startLogTime, Long endLogTime) {
        List<CHSyncDataEntity> res = chSyncDataDao.listSimpleByIds(tenantId, ids,new Date(startLogTime),new Date(endLogTime));
        return this.changeCHSyncData2SyncDataEntityList(res);
    }


    public List<SyncDataEntity> listBySourceData(String tenantId, String sourceTenantId, List<String> sourceObjectApiName, List<String> sourceDataIds,
                                                 String destTenantId, List<String> destObjectApiName,Long startLogTime, Long endLogTime) {
        List<CHSyncDataEntity> res = chSyncDataDao.listBySourceData(tenantId, sourceObjectApiName, sourceDataIds, destObjectApiName,
                new Date(startLogTime),new Date(endLogTime));
        return this.changeCHSyncData2SyncDataEntityList(res);
    }
    public Page<CHSyncDataEntity> listByPage(String tenantId, List<String> sourceObjectApiName, List<String> destObjectApiName, String logId,String sourceDataId,
                                             String sourceDataName,Long startLogTime, Long endLogTime, Integer offset, Integer limit) {
        List<CHSyncDataEntity> res = chSyncDataDao.listByPage(tenantId, sourceObjectApiName, destObjectApiName,logId,sourceDataId,sourceDataName,
                new Date(startLogTime),new Date(endLogTime),offset,limit);//
        Page<CHSyncDataEntity> syncDataListResultPage = new Page<>();
        if(res==null||res.size()<limit){
            syncDataListResultPage.setHasNext(false);
        }else {
            syncDataListResultPage.setHasNext(true);
        }
        syncDataListResultPage.setData(res);
        Integer total =chSyncDataDao.countByApiNames(tenantId, sourceObjectApiName, destObjectApiName,logId,sourceDataId,sourceDataName,new Date(startLogTime),new Date(endLogTime));
        syncDataListResultPage.setTotalNum(total);
        return syncDataListResultPage;
    }

    public int updateStatus(String tenantId, String id, Integer newStatus, String remark) {//先不做处理
//        CHSyncDataEntity chSyncDataEntity = this.getCHSyncDataById(tenantId, id);
//        if (Objects.nonNull(chSyncDataEntity)) {
//            chSyncDataEntity.setSyncDataStatus(newStatus);
//            chSyncDataEntity.setRemark(remark);
//            return this.sendBizLog(Lists.newArrayList(chSyncDataEntity));
//        }
        return 0;
    }



    public int updateRemark(String tenantId, String id, String remark) {//先不做处理
//        CHSyncDataEntity chSyncDataEntity = this.getCHSyncDataById(tenantId, id);
//        if (Objects.nonNull(chSyncDataEntity)) {
//            chSyncDataEntity.setRemark(remark);
//            return this.sendBizLog(Lists.newArrayList(chSyncDataEntity));
//        }
        return 0;
    }


    public List<SyncDataEntity> listByStatusListAndEndUpdateTime(String tenantId, List<Integer> statusList, Long startUpdateTime, Long endUpdateTime, Integer offset, Integer limit) {
        List<CHSyncDataEntity> res = chSyncDataDao.listByStatusListAndEndUpdateTime(tenantId, statusList, new Date(startUpdateTime), new Date(endUpdateTime), offset, limit);
        return this.changeCHSyncData2SyncDataEntityList(res);
    }


    public int deleteSingleSyncData(String tenantId, Long updateTime, String id) {//先不做处理
//        CHSyncDataEntity chSyncDataEntity = this.getCHSyncDataById(tenantId, id);
//        if (Objects.nonNull(chSyncDataEntity)) {
//            chSyncDataEntity.setIsDeleted(true);
//            if (updateTime != null) {
//                chSyncDataEntity.setUpdateTime(new Date(updateTime));
//            }
//            return this.sendBizLog(Lists.newArrayList(chSyncDataEntity));
//        }
        return 0;
    }


    public Integer deleteWithLogicBySourceDatas(String tenantId, String sourceObjectApiName, String destObjectApiName, List<String> srcIds) {//先不做处理
//        List<CHSyncDataEntity> chSyncDataEntity = chSyncDataDao.listBySourceDataIds(tenantId, sourceObjectApiName, destObjectApiName, srcIds);
//        if (CollectionUtils.isNotEmpty(chSyncDataEntity)) {
//            for (CHSyncDataEntity item : chSyncDataEntity) {
//                item.setIsDeleted(true);
//                item.setUpdateTime(new Date());
//            }
//            this.sendBizLog(chSyncDataEntity);
//            return chSyncDataEntity.size();
//        }
        return 0;
    }


    public int deleteSyncDatas(String tenantId, String sourceObjApiName, String destObjApiName) {//先不做处理
        String lastId = null;
        Integer size = 0;
//        while (true) {
//            List<CHSyncDataEntity> result = chSyncDataDao.listSyncDatas(tenantId, sourceObjApiName, destObjApiName, lastId, 1000);
//            if (org.springframework.util.CollectionUtils.isEmpty(result)) {
//                break;
//            }
//            lastId = result.get(result.size() - 1).getId();
//            for (CHSyncDataEntity entity : result) {
//                entity.setIsDeleted(true);
//            }
//            sendBizLog(result);
//            size += result.size();
//            if (result.size() < 1000) {
//                break;
//            }
//        }
        return size;
    }

    public int sendBizLog(List<CHSyncDataEntity> chSyncDataEntity) {
        if (CollectionUtils.isNotEmpty(chSyncDataEntity)) {
            for (CHSyncDataEntity data : chSyncDataEntity) {
                ErpSyncLogDTO dto = ErpSyncLogDTO.builder()
                        .appName(data.getAppName())// 服务名称
                        .traceId(data.getTraceId()) // 分布式跟踪id
                        .serverIp(data.getServerIp()) // 发出日志的ip
                        .tenantId(data.getTenantId()) // 租户ei信息
                        .createTime(data.getCreateTime().getTime()) // 日志上报时间
                        .updateTime(data.getUpdateTime().getTime()) // 日志上报时间
                        .expireTime(data.getExpireTime().getTime())//过期时间
                        .logType(data.getLogType())//日志类型：sync_data\sync_log\interface_monitor
                        .id(data.getId())//mongoId
                        .sourceTenantType(data.getSourceTenantType())//企业类型 目前为CRM 1
                        .destTenantType(data.getDestTenantType())//企业类型 目前为CRM 1
                        .sourceEventType(data.getSourceEventType())//源数据的数据事件类型 1、新增 2、修改 3、作废
                        .sourceObjectApiName(data.getSourceObjectApiName())//源企业主对象apiName
                        .sourceDataId(data.getSourceDataId())//源数据主键id
                        .erpTempDataDataNumber(data.getErpTempDataDataNumber())//源数据数据主属性
                        .sourceData(data.getSourceData())//源主对象数据
                        .sourceDetailSyncDataIds(data.getSourceDetailSyncDataIds())//源从对象id封装
                        .destEventType(data.getDestEventType())//目标企业事件类型 1、新增 2、修改 3、作废
                        .destObjectApiName(data.getDestObjectApiName())//目标企业主对象apiName
                        .destDataId(data.getDestDataId())//目标企数据id
                        .destData(data.getDestData())//目标主对象数据
                        .syncDataStatus(data.getSyncDataStatus())//数据状态
                        .syncPloyDetailSnapshotId(data.getSyncPloyDetailSnapshotId())//快照id
                        .operatorId(data.getOperatorId())//操作人主键id
                        .remark(data.getRemark())//备注
                        .errorCode(data.getErrorCode())//错误编码
                        .isDeleted(data.getIsDeleted())//逻辑删除
                        .needReturnDestObjectData(data.getNeedReturnDestObjectData())//需要返回的目标数据
                        .data(data.getData())//节点信息
                        .logId(data.getLogId())//日志id
                        .dataReceiveType(data.getDataReceiveType())//数据接收方式
                        .build();
                BizLogClient.send("biz_log_erp_sync_logs", Pojo2Protobuf.toMessage(dto, com.fxiaoke.log.ErpSyncLog.class).toByteArray());
            }
        }
        return 0;
    }

    private CHSyncDataEntity getCHSyncDataById(String tenantId, String id,Long startLogTime, Long endLogTime) {
        return chSyncDataDao.getById(tenantId, id,new Date(startLogTime),new Date(endLogTime));
    }


    public void save(SyncDataEntity syncDataEntity) {
        CHSyncDataEntity chSyncDataEntity = this.buildCHSyncDataFromSyncData(syncDataEntity);
        this.sendBizLog(Lists.newArrayList(chSyncDataEntity));
    }


    public List<String> batchReplace(String tenantId, Collection<SyncDataEntity> record) {
        if (CollectionUtils.isEmpty(record)) {
            return Lists.newArrayList();
        }
        List<CHSyncDataEntity> chSyncDataEntities = this.buildCHSyncDataFromSyncDataList(Lists.newArrayList(record));
        sendBizLog(chSyncDataEntities);
        List<String> ids = chSyncDataEntities.stream().map(CHSyncDataEntity::getId).collect(Collectors.toList());
        return ids;
    }


    public void limitGroupByObj(String tenantId, Pair<String, String> objPair, List<String> srcIds) {//先不做处理
//        List<CHSyncDataEntity> idList = chSyncDataDao.limitGroupByObj(tenantId, objPair.getKey(), objPair.getValue(), srcIds, 2);
//        if (CollectionUtils.isNotEmpty(idList)) {
//            for (CHSyncDataEntity data : idList) {
//                data.setIsDeleted(true);
//            }
//            sendBizLog(idList);
//        }
    }


    public long countByTenantId(String tenantId) {
        long startTime = System.currentTimeMillis();

        long count = chSyncDataDao.countByTenantId(tenantId);

        long costTime = System.currentTimeMillis() - startTime;
        log.info("SyncDataMongoDao.countByTenantId,costTime={}", costTime);
        return count;
    }


    public List<Triple<Integer, Integer, Integer>> getAllTypeCount(String tenantId, Long startTime, Long endTime, List<String> ids, String sourceApiName, String destApiName) {
        List<Triple<Integer, Integer, Integer>> typeCounts = new ArrayList<>();
        List<Map<Integer, Integer>> result = chSyncDataDao.getAllTypeCount(tenantId, startTime==null?null:new Date(startTime), endTime==null?null:new Date(endTime), ids, sourceApiName, destApiName);
        if (CollectionUtils.isNotEmpty(result)) {
            for (Map<Integer, Integer> map : result) {
                typeCounts.add(Triple.of(map.get("destEventType"), map.get("syncDataStatus"), map.get("countNum")));
            }
        }
        return typeCounts;
    }

    public List<CHSyncDataEntity> buildCHSyncDataFromSyncDataList(List<SyncDataEntity> syncDataEntities) {
        List<CHSyncDataEntity> logs = Lists.newArrayList();
        if (CollectionUtils.isEmpty(syncDataEntities)) {
            return logs;
        }
        for (SyncDataEntity data : syncDataEntities) {
            CHSyncDataEntity log = buildCHSyncDataFromSyncData(data);
            logs.add(log);
        }
        return logs;
    }

    private CHSyncDataEntity buildCHSyncDataFromSyncData(SyncDataEntity data) {
        if (data == null) {
            return null;
        }
        CHSyncDataEntity log = new CHSyncDataEntity();
        log.setServerIp(ProcessInfo2.serverIp);
        log.setAppName(ProcessInfo2.appName);
        if (StringUtils.isNotBlank(data.getId())) {
            log.setId(data.getId());
        } else {
            log.setId(new ObjectId().toString());
        }
        log.setTenantId(data.getTenantId());
        log.setLogType("sync_data");
        log.setSourceTenantType(data.getSourceTenantType());
        log.setDestTenantType(data.getDestTenantType());
        log.setSourceEventType(data.getSourceEventType());
        log.setSourceObjectApiName(data.getSourceObjectApiName());
        log.setSourceDataId(data.getSourceDataId());
        log.setErpTempDataDataNumber(data.getSourceDataName());
        if (data.getSourceData() != null) {
            log.setSourceData(JacksonUtil.toJson(data.getSourceData()));
        }
        if (data.getSourceDetailSyncDataIds() != null) {
            log.setSourceDetailSyncDataIds(JacksonUtil.toJson(data.getSourceDetailSyncDataIds()));
        }
        log.setDestEventType(data.getDestEventType());
        log.setDestObjectApiName(data.getDestObjectApiName());
        log.setDestDataId(data.getDestDataId());
        if (data.getDestData() != null) {
            log.setDestData(JacksonUtil.toJson(data.getDestData()));
        }
        log.setSyncDataStatus(data.getStatus());
        log.setSyncPloyDetailSnapshotId(data.getSyncPloyDetailSnapshotId());
        log.setOperatorId(data.getOperatorId());
        log.setRemark(data.getRemark());
        log.setErrorCode(data.getErrorCode());
        log.setIsDeleted(data.getIsDeleted());
        if (data.getNeedReturnDestObjectData() != null) {
            log.setNeedReturnDestObjectData(JacksonUtil.toJson(data.getNeedReturnDestObjectData()));
        }
        if(data.getData()!=null){
            log.setData(JacksonUtil.toJson(data.getData()));
        }
        log.setLogId(data.getSyncLogId());
        log.setDataReceiveType(data.getDataReceiveType());
        if (data.getCreateTime() != null) {
            log.setCreateTime(new Date(data.getCreateTime()));
        } else {
            log.setCreateTime(new Date());
        }
        if (data.getUpdateTime() != null) {
            log.setUpdateTime(new Date(data.getUpdateTime()));
        } else {
            log.setUpdateTime(new Date());
        }
        log.setExpireTime(getExpireTimeByEi(data.getTenantId(), log.getCreateTime().getTime()));
        return log;
    }

    private Date getExpireTimeByEi(String tenantId, Long createTime) {
        Long expireTime = null;
        if (createTime != null) {
            expireTime = createTime + tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        } else {
            expireTime = System.currentTimeMillis() + tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        }
        return new Date(expireTime);
    }

    public Long findMinDate(String tenantId) {
        return chSyncDataDao.findMinDate(tenantId);
    }

    public Long deleteBetween(String tenantId, Date beginTime, Date endTime) {
        String lastId = null;
        Long size = 0L;
        while (true) {
            List<CHSyncDataEntity> result = chSyncDataDao.listBetween(tenantId, beginTime, endTime, lastId, 1000);
            if (CollectionUtils.isEmpty(result)) {
                break;
            }
            lastId = result.get(result.size() - 1).getId();
            for (CHSyncDataEntity entity : result) {
                entity.setIsDeleted(true);
            }
            sendBizLog(result);
            size += result.size();
            if (result.size() < 1000) {
                break;
            }
        }
        return size;
    }
}
