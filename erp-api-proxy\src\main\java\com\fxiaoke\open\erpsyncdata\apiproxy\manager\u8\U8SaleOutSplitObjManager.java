package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("saleoutsplit")
public class U8SaleOutSplitObjManager extends U8DefaultMananger {

  @Override
  public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
    timeFilterArg.setObjAPIName("saleout");
    Result<StandardListData> standardListDataResult = super.listErpObjDataByTime(timeFilterArg, connectInfo);
    timeFilterArg.setObjAPIName("saleoutsplit");
    return standardListDataResult;
  }

  /**
   * <p>销售出库单->crm 拆单逻辑。同一个销售出库单可能有多个销售订单，需要拆成一对一的关系</p>
   *
   * @dateTime 2020/9/18 11:08
   * <AUTHOR>
   * @version 1.0
   */
  @Override
  protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                           String tenantId,
                                           String dataCenterId,
                                           String snapshotId) {
    StandardListData standardListData = standardListDataResult.getData();
    if (standardListData == null) {
      return;
    }

    List<StandardData> dataList = new ArrayList<>();
    for (StandardData standardData : standardListData.getDataList()) {

      List<ObjectData> detailObjectDataList = standardData.getDetailFieldVals().get("entry");
      ObjectData masterFieldVal = standardData.getMasterFieldVal();

      Map<String, List<ObjectData>> map = new HashMap<>();
      for (ObjectData objectData : detailObjectDataList) {
        String ordercode = (String) objectData.get("ordercode");
        if (!map.containsKey(ordercode)) {
          map.put(ordercode, new ArrayList<>());
        }
        map.get(ordercode).add(objectData);
      }
      for (Map.Entry<String, List<ObjectData>> entry : map.entrySet()) {
        ObjectData masterObj = BeanUtil2.deepCopy(masterFieldVal, ObjectData.class);
        masterObj.put("ordercode", entry.getKey());
        String saleoutId=masterObj.get("code") + "#" + entry.getKey();
        masterObj.put("saleoutId", saleoutId);

        try {
          SimpleDateFormat dateFormat=new SimpleDateFormat("yyyy-MM-dd");
          String dataStr = (String) masterObj.get("date");
          Date date = dateFormat.parse(dataStr);
          masterObj.put("date",date.getTime());
        }catch (Exception e){
            log.error("日期格式失败：{}",e);
        }
        StandardData data = new StandardData();
        data.setMasterFieldVal(masterObj);
        Map<String, List<ObjectData>> detailObj = new HashMap<>();
        for (ObjectData objectData : entry.getValue()) {
          objectData.put("saleoutId", masterObj.get("saleoutId"));
          objectData.put("code", masterObj.get("code"));
          String rowno=objectData.getString("rowno");
          String iorderseq=objectData.getString("iorderseq");
          if (StringUtils.isEmpty(iorderseq)){
            iorderseq=rowno;
          }
          objectData.put("entryId", objectData.get("code") + "#" + objectData.get("inventorycode")+"#"+rowno);//虚拟主键
          objectData.put("orderInvertoryCode",
            objectData.get("ordercode") + "#" + objectData.get("inventorycode")+"#"+iorderseq);//订单产品编码
          Object batch=objectData.get("serial");
          if (batch!=null&& StringUtils.isNotEmpty((String)batch)){
            objectData.put("currentStockId",
              objectData.get("inventorycode") + "#" + masterObj.get("warehousecode")+"#"+batch);//库存id
          }else {
            objectData.put("currentStockId",
              objectData.get("inventorycode") + "#" + masterObj.get("warehousecode"));//库存id
          }

        }
        detailObj.put("entry", entry.getValue());
        data.setDetailFieldVals(detailObj);
        dataList.add(data);
      }
    }
    standardListDataResult.getData().setDataList(dataList);
  }

  /**
   * 获取接口
   */
  @Override
  public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
    erpIdArg.setObjAPIName("saleout");
    U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectInfo.getConnectParams(), U8ConnectParam.class);
    String queryPath = getLoadUrl(erpIdArg.getObjAPIName());
    //构建接口日志
      InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
              connectInfo.getId(),
              erpIdArg.getObjAPIName(),
              ErpObjInterfaceUrlEnum.queryMasterById.name());

    Map<String, String> params = new HashMap<>();
    String id = erpIdArg.getDataId().split("#")[0];
    params.put("id", id);
    //发送请求
    Result<StandardData> result = get(U8ConnectParam, queryPath, params, interfaceMonitorData);
    result.setData(getRealData(erpIdArg.getDataId(), result.getData()));
    erpIdArg.setObjAPIName("saleoutsplit");
    return result;
  }

  private StandardData getRealData(String dataId, StandardData standardData) {
    if (standardData==null){
      return null;
    }

    String orderId = dataId.split("#")[1];

    List<ObjectData> detailObjectDataList = standardData.getDetailFieldVals().get("entry");
    ObjectData masterFieldVal = standardData.getMasterFieldVal();

    Map<String, List<ObjectData>> map = new HashMap<>();
    for (ObjectData objectData : detailObjectDataList) {
      String ordercode = (String) objectData.get("ordercode");
      if (!map.containsKey(ordercode)) {
        map.put(ordercode, new ArrayList<>());
      }
      map.get(ordercode).add(objectData);
    }

    StandardData data = new StandardData();
    for (Map.Entry<String, List<ObjectData>> entry : map.entrySet()) {
      if (entry.getKey().equals(orderId)) {
        ObjectData masterObj = BeanUtil2.deepCopy(masterFieldVal, ObjectData.class);
        masterObj.put("ordercode", entry.getKey());
        String saleoutId=masterObj.get("code") + "#" + entry.getKey();
        masterObj.put("saleoutId", saleoutId);

        data.setMasterFieldVal(masterObj);

        Map<String, List<ObjectData>> detailObj = new HashMap<>();
        for (ObjectData objectData : entry.getValue()) {
          objectData.put("saleoutId",saleoutId);
          objectData.put("code", masterObj.get("code"));
          String rowno=objectData.getString("rowno");
          String iorderseq=objectData.getString("iorderseq");
          if (StringUtils.isEmpty(iorderseq)){
            iorderseq=rowno;
          }
          objectData.put("entryId", objectData.get("code") + "#" + objectData.get("inventorycode")+"#"+rowno);//虚拟主键
          objectData.put("orderInvertoryCode",
            objectData.get("ordercode") + "#" + objectData.get("inventorycode")+"#"+iorderseq);//订单产品编码
          Object batch=objectData.get("serial");
          if (batch!=null&& StringUtils.isNotEmpty((String)batch)){
            objectData.put("currentStockId",
              objectData.get("inventorycode") + "#" + masterObj.get("warehousecode")+"#"+batch);//库存id
          }else {
            objectData.put("currentStockId",
              objectData.get("inventorycode") + "#" + masterObj.get("warehousecode"));//库存id
          }
        }

        detailObj.put("entry", entry.getValue());
        data.setDetailFieldVals(detailObj);

      }
    }

    return data;
  }



}
