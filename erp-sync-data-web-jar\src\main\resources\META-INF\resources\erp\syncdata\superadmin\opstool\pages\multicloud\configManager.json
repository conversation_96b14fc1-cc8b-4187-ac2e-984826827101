{"type": "page", "title": "多云配置管理", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "post", "url": "${baseUrl}/multicloud/multiRequestFromFsList", "dataType": "json", "data": {"method": "post", "path": "/erp/syncdata/superadmin/config/getConfig", "requestBody": {"tenantId": "${tenantId}", "dcId": "${dcId}", "channel": "${channel}", "type": "${type}"}, "domains": "${domains}"}, "sendOn": "type"}, "columns": [{"label": "domain", "type": "text", "name": "domain", "id": "u:89489af7ee7e"}, {"label": "configuration", "type": "text", "name": "configuration", "id": "u:9cf1e7ea00ba"}, {"label": "success", "type": "status", "name": "success", "id": "u:aa5120a2f285", "value": 1}, {"type": "text", "label": "msg", "id": "u:4c1ee6e1f6f5", "name": "msg"}, {"type": "button-group", "buttons": [{"type": "button", "label": "修改", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "drawer", "drawer": {"id": "u:2260cc3cd2a2", "type": "drawer", "title": "修改配置", "body": [{"type": "form", "reload": "configs", "body": [{"name": "tenantId", "label": "tenantId", "type": "input-text", "id": "u:bb080958b880", "disabled": true, "value": "0", "static": true}, {"name": "dataCenterId", "label": "dataCenterId", "type": "input-text", "id": "u:c79d0e6519ee", "static": true, "value": "0"}, {"name": "channel", "label": "channel", "type": "input-text", "id": "u:79791f12fdeb", "value": "ALL", "static": true}, {"name": "type", "label": "type", "type": "input-text", "id": "u:b9675f92805a", "static": true, "required": true}, {"type": "checkboxes", "label": "domains", "name": "domains", "multiple": true, "checkAll": true, "joinValues": false, "defaultCheckAll": true, "source": {"url": "${baseUrl}/multicloud/listMultiDomains", "method": "get"}, "labelField": "fullName", "valueField": "domain", "extractValue": true, "required": true, "id": "u:af791d2fb612"}, {"type": "editor", "label": "configuration", "name": "configuration", "id": "u:7f594d5d082d", "minRows": 3, "maxRows": 20, "language": "json"}], "id": "u:32922b8f5ce0", "actions": [{"type": "submit", "label": "提交", "primary": true}], "feat": "Edit", "dsType": "api", "api": {"url": "${baseUrl}/multicloud/multiRequestFromFsList", "method": "post", "dataType": "json", "data": {"method": "post", "path": "/erp/syncdata/superadmin/config/upsertConfig", "requestBody": {"tenantId": "${tenantId}", "channel": "${channel}", "type": "${type}", "dataCenterId": "${dcId}", "configuration": "${configuration}"}, "domains": "${domains}"}, "sendOn": "type"}, "autoFocus": false, "onEvent": {}}], "className": "app-popover :AMISCSSWrapper", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:5b4427e0fb8c"}, {"type": "button", "actionType": "confirm", "label": "确认", "primary": true, "id": "u:ad57583c2c0e"}], "resizable": false, "withDefaultData": false, "dataMapSwitch": false, "size": "lg", "onEvent": {}}}]}}, "id": "u:00a8e23d136c", "level": "primary"}], "label": "操作", "id": "u:eba24548b30f", "placeholder": "-"}], "bulkActions": [], "itemActions": [], "id": "u:4e150a694564", "filterSettingSource": ["id", "tenantId", "dataCenterId", "channel", "type", "configuration", "createTime", "updateTime", "success", "msg", "domain"], "perPageAvailable": [10], "messages": {}, "combineNum": 1, "filter": {"title": "", "body": [{"type": "input-text", "name": "tenantId", "label": "tenantId", "id": "u:b9aa6f01e8d7", "value": "0"}, {"type": "input-text", "label": "dcId", "name": "dcId", "id": "u:d83fb3e0b35b", "value": "0"}, {"type": "input-text", "label": "channel", "name": "channel", "id": "u:7437811cb976", "value": "ALL"}, {"type": "select", "label": "类型", "name": "type", "id": "u:a845638472eb", "multiple": false, "source": {"url": "${baseUrl}/config/getConfigTypes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}}, "value": "", "creatable": true, "searchable": true, "clearable": true, "size": "md", "required": true}, {"type": "divider", "id": "u:f49934c08fc3"}, {"type": "checkboxes", "label": "domains", "name": "domains", "multiple": true, "id": "u:8c9f06ed6211", "checkAll": true, "joinValues": false, "defaultCheckAll": true, "source": {"url": "${baseUrl}/multicloud/listMultiDomains", "method": "get"}, "labelField": "fullName", "valueField": "domain", "extractValue": true, "required": true}], "id": "u:461e0005ffa6", "feat": "Insert", "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:d034a5d533b2"}], "autoFocus": true}, "showHeader": false, "headerToolbar": [{"type": "columns-toggler", "tpl": "内容", "wrapperComponent": ""}, {"type": "export-excel", "tpl": "内容", "wrapperComponent": ""}]}], "id": "u:f444e533be91", "data": {"baseUrl": ".."}}