package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.utils.K3UltimateUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 云星空旗舰版转换器
 *
 * <AUTHOR>
 * @date 2023-09-15
 */
@Component
@Slf4j
public class K3UltimateConverter {
    @Autowired
    private ErpFieldManager erpFieldManager;

    /**
     * 明细拼接字段转换
     *
     * @param standardData
     * @return
     */
    public void convertDetailData(StandardData standardData) {
        for (String detailObjApiName : standardData.getDetailFieldVals().keySet()) {
            for (ObjectData detailData : standardData.getDetailFieldVals().get(detailObjApiName)) {
                List<String> needRemoveKeyList = new ArrayList<>();

                Map<String, JSONObject> newDataMap = new LinkedHashMap<>();
                for (String fieldApiName : detailData.keySet()) {
                    if (StringUtils.containsIgnoreCase(fieldApiName, ".")) {
                        List<String> fieldApiNameList = Splitter.on(".").splitToList(fieldApiName);
                        if (fieldApiNameList.size() > 2) continue; //只支持一级特殊字段转换
                        Object detailFieldValue = detailData.get(fieldApiName);
                        needRemoveKeyList.add(fieldApiName);

                        String firstLevelKey = fieldApiNameList.get(0);
                        String secondLevelKey = fieldApiNameList.get(1);
                        JSONObject fieldValue = newDataMap.get(firstLevelKey);
                        if (fieldValue == null) {
                            fieldValue = new JSONObject();
                            newDataMap.put(firstLevelKey, fieldValue);
                        }
                        fieldValue.put(secondLevelKey, detailFieldValue);
                    }
                }

                detailData.putAll(newDataMap);

                if (CollectionUtils.isNotEmpty(needRemoveKeyList)) {
                    for (String key : needRemoveKeyList) {
                        detailData.remove(key);
                    }
                }
            }
        }
    }

    /**
     * 组装新增或更新请求body
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param standardData
     * @param k3UltimateApiService
     * @param update
     * @return
     */
    public Result<JSONObject> assemblySaveRequestData(String tenantId,
                                                      String dataCenterId,
                                                      String objApiName,
                                                      StandardData standardData,
                                                      K3UltimateApiService k3UltimateApiService,
                                                      boolean update, ErpConnectInfoEntity connectInfo) {
        if(update) {
            ErpObjectFieldEntity idField = erpFieldManager.findMasterIdField(tenantId, dataCenterId, objApiName);
            if (!StringUtils.equalsIgnoreCase(idField.getFieldApiName(), "id")) {
                String number = standardData.getMasterFieldVal().getString(idField.getFieldApiName());
                Result<K3UltimateResponseByQuery> result = K3UltimateUtils.queryByNumber(tenantId,
                        dataCenterId,
                        objApiName,
                        number,
                        null,
                        k3UltimateApiService,connectInfo);
                if (!result.isSuccess()) {
                    return Result.copy(result);
                }
                if (CollectionUtils.isEmpty(result.getData().getData().getRows())) {
                    return Result.newError(ResultCodeEnum.UNABLE_TO_FIND_DATA);
                }
                String id = result.getData().getData().getRows().get(0).getString("id");
                standardData.getMasterFieldVal().put("id", id);
            }
        }

        JSONObject data = new JSONObject();
        data.putAll(standardData.getMasterFieldVal());
        for (String detailObjApiName : standardData.getDetailFieldVals().keySet()) {
            data.put(detailObjApiName, standardData.getDetailFieldVals().get(detailObjApiName));
        }
        log.info("K3UltimateConverter.assemblySaveRequestData,objApiName={},data={}", objApiName, data);
        return Result.newSuccess(data);
    }
}
