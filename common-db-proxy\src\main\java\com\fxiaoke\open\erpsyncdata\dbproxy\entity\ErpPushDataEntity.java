package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>erp接口数据</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @dateTime 2020/12/7 11:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objectApiName")
@DataId("sourceDataId")
@Table(name = "erp_push_data")
public class ErpPushDataEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 对象apiName
     */
    private String objectApiName;

    /**
     * 数据Id
     */
    private String sourceDataId;

    /**
     * 源数据
     */
    private String sourceData;

    /**
     * 源数据标准格式
     */
    private String standardFormat;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}