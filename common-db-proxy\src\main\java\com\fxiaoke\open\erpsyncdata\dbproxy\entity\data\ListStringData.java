package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import java.util.ArrayList;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/2.
 */

public class ListStringData  extends ArrayList<String> {
    public static ListStringData newListStringData(String... objs){
        ListStringData data = new ListStringData();
        for (String obj : objs) {
            data.add(obj);
        }
        return data;
    }
    public static ListStringData newListStringData(List<String> objs){
        ListStringData data = new ListStringData();
        for (String obj : objs) {
            data.add(obj);
        }
        return data;
    }
}
