package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/4/1
 */
public class ChannelCodec implements Codec<ErpChannelEnum> {
    @Override
    public ErpChannelEnum decode(BsonReader reader, DecoderContext decoderContext) {
        String channelName = reader.readString();
        try {
            ErpChannelEnum channelEnum = ErpChannelEnum.valueOf(channelName);
            return channelEnum;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void encode(BsonWriter writer, ErpChannelEnum value, EncoderContext encoderContext) {
        if (value != null) {
            writer.writeString(value.name());
        }
    }

    @Override
    public Class<ErpChannelEnum> getEncoderClass() {
        return ErpChannelEnum.class;
    }
}
