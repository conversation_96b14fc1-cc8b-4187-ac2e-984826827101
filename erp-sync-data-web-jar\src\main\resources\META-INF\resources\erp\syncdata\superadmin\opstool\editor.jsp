<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no"/>
    <meta name="viewport" content="initial-scale=1,user-scalable=no,width=device-width,viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>amis-editor Demo</title>
    <link rel="stylesheet" href="https://bce.bdstatic.com/iconfont/iconfont.css"/>
    <link rel="stylesheet" href="https://unpkg.com/animate.css@4.1.1/animate.min.css"/>
    <link data-react-helmet="true" rel="shortcut icon"
          href="https://avatars.githubusercontent.com/u/78204817?s=200&v=4"/>
    <link href="https://aisuda.github.io/amis-editor-demo/demo-6.11.0/css/vendor.a1c9d2c0.css" rel="stylesheet">
    <link href="https://aisuda.github.io/amis-editor-demo/demo-6.11.0/css/index.ac981141.css" rel="stylesheet">

    <style>
        .waterMarker {
            position: absolute;
            font-size: 20px;
            opacity: 0.08;
            color: #aaa;
            z-index: 9999;
            transform: rotate(-45deg);
            white-space: nowrap;
            /*确保水印不会干扰页面上的其他元素的交互*/
            pointer-events: none;
            user-select: none;
            -webkit-user-select: none; /* Safari 3.1+ */
            -moz-user-select: none; /* Firefox 2+ */
            -ms-user-select: none; /* Internet Explorer 10+ */
        }
    </style>
</head>
<body>
<div id="root"></div>
<script defer src="https://aisuda.github.io/amis-editor-demo/demo-6.11.0/scripts/chunk/vendor.8e75b671.js"></script>
<script defer src="https://aisuda.github.io/amis-editor-demo/demo-6.11.0/scripts/chunk/index.94e2b10d.js"></script>

<script>
    <%--增加水印，白色背景肉眼基本不可见--%>
    document.addEventListener("DOMContentLoaded", function () {
        var parent = document.getElementById('root');
        for (let i = 0; i < 8; i++) {
            for (let j = 0; j < 8; j++) {
                let newDiv = document.createElement('div');
                newDiv.innerText = "${userName}"
                newDiv.classList.add("waterMarker")
                let style = newDiv.style;
                style.top = (4 + 12 * i) + "%"
                style.left = (4 + 12 * j) + "%"
                parent.appendChild(newDiv)
            }
        }
    })

</script>
</body>
</html>