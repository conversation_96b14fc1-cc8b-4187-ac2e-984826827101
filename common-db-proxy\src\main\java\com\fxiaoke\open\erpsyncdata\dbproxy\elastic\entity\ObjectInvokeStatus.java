package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:10:01
 */
public enum ObjectInvokeStatus {
    success(1),
    error(2),

    // 集成流失败专用
    blow(10, "熔断"),
    ;

    @Getter
    private Integer status;
    private String detail;

    ObjectInvokeStatus(final Integer status) {
        this.status = status;
    }

    ObjectInvokeStatus(final Integer status, final String detail) {
        this.status = status;
        this.detail = detail;
    }
}
