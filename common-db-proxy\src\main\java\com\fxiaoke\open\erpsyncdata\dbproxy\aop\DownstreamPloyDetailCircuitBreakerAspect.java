package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/3/15 12:02:07
 * <p>
 * 下游熔断集成流处理
 * 1.不能停用集成流
 * 2.需要发送通知,但不能频繁发送
 */
@Component
@Aspect
public class DownstreamPloyDetailCircuitBreakerAspect extends AbstractReplaceEnterpriseAspect {
    @Autowired
    private RedisCacheManager redisCacheManager;

    private Cache<Pair<String, String>, Long> caffeineCache;
    public static final String downstreamPloyDetailCircuitBreakerFormat = "erpSyncData:D_breakPloy:%s:%s";

    @PostConstruct
    public void init() {
        // 初始化缓存
        caffeineCache = Caffeine.newBuilder()
                // 蒙牛3000下游,集成流有问题的话3000都会熔断,所以size需>=3000
                .maximumSize(5000)
                .expireAfterWrite(ConfigCenter.RelationPloyBreakerExpireSeconds, TimeUnit.SECONDS)
                .build();
    }

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager.disablePloyDetailByStreamId(String, String, String, String, String, boolean))")
    public Object ployDetailCircuitBreaker(ProceedingJoinPoint jp) throws Throwable {
        final String tenantId = getTenantIdByParameter(jp);
        if (Objects.isNull(tenantId) || !managedEnterprise(tenantId)) {
            return jp.proceed();
        }

        final String templateId = getTemplateId(tenantId);
        if (Objects.isNull(templateId)) {
            return jp.proceed();
        }

        // 熔断时需要发送告警,但是又不能一直发送,也不能只发送一次,所以加个过期缓存
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final Integer index = getParameterIndex(methodSignature, String.class, Pattern.compile("ployDetailId"));
        String ployDetailId = (String) jp.getArgs()[index];
        if (StringUtils.isEmpty(ployDetailId)) {
            return false;
        }

        return getOrSaveCache(tenantId, ployDetailId);
    }

    /**
     * @return true:熔断成功,后续处理会发送告警通知 ; false:熔断失败
     */
    private Object getOrSaveCache(String tenantId, String ployDetailId) {
        // 保存本地和redis缓存,查询的时候先查本地,减少redis的调用量
        final Pair<String, String> key = Pair.of(tenantId, ployDetailId);
        final Long ifPresent = caffeineCache.getIfPresent(key);
        if (Objects.nonNull(ifPresent) && System.currentTimeMillis() < ifPresent) {
            return false;
        }

        final Pair<Boolean, Long> result = getOrSaveRedis(tenantId, ployDetailId);
        caffeineCache.put(key, result.getValue());

        return result.getKey();
    }

    public static final String MONITOR_NAME = "erpSyncData:downstreamPloyDetailCircuitBreaker";

    private Pair<Boolean, Long> getOrSaveRedis(String tenantId, String ployDetailId) {
        String breakKey = String.format(downstreamPloyDetailCircuitBreakerFormat, tenantId, ployDetailId);
        final String cache = redisCacheManager.getCache(breakKey, MONITOR_NAME);
        if (StringUtils.isNotBlank(cache)) {
            return Pair.of(Boolean.FALSE, Long.valueOf(cache));
        }

        // nx 防止并发推送多次告警
        SetParams setParams = SetParams.setParams().ex((long) ConfigCenter.RelationPloyBreakerExpireSeconds).nx();
        Long expire = System.currentTimeMillis() + ConfigCenter.RelationPloyBreakerExpireSeconds;
        final String value = String.valueOf(expire);
        final boolean b = redisCacheManager.setCache(breakKey, value, setParams, MONITOR_NAME);
        return Pair.of(b, expire);
    }
}
