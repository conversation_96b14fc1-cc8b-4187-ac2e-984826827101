package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.BaseConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import com.google.common.reflect.Reflection;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Component
public class ConnectorHandlerFactory implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<String, ConnectorDataHandler> DATA_HANDLER_MAP = Maps.newConcurrentMap();
    private static final Map<String, ConnectorMetaDataHandler> META_DATA_HANDLER_MAP = Maps.newConcurrentMap();
    private static final Map<String, ConnectorConfigHandler> CONFIG_HANDLER_MAP = Maps.newConcurrentMap();


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(Connector.class);
        for (Object bean : beanMap.values()) {
            Connector connector = AnnotationUtils.findAnnotation(bean.getClass(), Connector.class);
            ConnectorHandlerType connectorHandlerType = connector.handlerType();
            ErpChannelEnum channel = connector.channel();
            String connectorHandlerKey;
            if (connectorHandlerType == ConnectorHandlerType.INNER) {
                connectorHandlerKey = channel.name();
            } else {
                connectorHandlerKey = connectorHandlerType.name();
            }
            DATA_HANDLER_MAP.put(connectorHandlerKey, (ConnectorDataHandler) bean);
            if (bean instanceof ConnectorMetaDataHandler) {
                META_DATA_HANDLER_MAP.put(connectorHandlerKey, (ConnectorMetaDataHandler) bean);
            }
            if (bean instanceof ConnectorConfigHandler) {
                CONFIG_HANDLER_MAP.put(connectorHandlerKey, (ConnectorConfigHandler) bean);
            }
        }
    }

    public static ConnectorDataHandler getDataHandler(String handlerType) {
        return DATA_HANDLER_MAP.get(handlerType);
    }

    public static ConnectorConfigHandler getConfigHandler(ConnectorHandlerType handlerType) {
        return CONFIG_HANDLER_MAP.get(handlerType.name());
    }

    public static ConnectorDataHandler getDataHandler(ErpChannelEnum channel, String baseConnectParamStr) {
        return getDataHandler2(channel, channel.getConnectParam(baseConnectParamStr));
    }

    public static ConnectorDataHandler getDataHandler2(@NotNull ErpChannelEnum channel, BaseConnectParam baseConnectParam) {
        return DATA_HANDLER_MAP.get(channel.getConnectorHandlerKey(baseConnectParam));
    }

    @Nullable
    public static ConnectorMetaDataHandler getMetaDataHandler(ErpChannelEnum channel, String baseConnectParamStr) {
        return getMetaDataHandler2(channel, channel.getConnectParam(baseConnectParamStr));
    }

    @Nullable
    public static ConnectorMetaDataHandler getMetaDataHandler2(@NotNull ErpChannelEnum channel, BaseConnectParam baseConnectParam) {
        return META_DATA_HANDLER_MAP.get(channel.getConnectorHandlerKey(baseConnectParam));
    }

    @Nullable
    public static ConnectorConfigHandler getConfigHandler(ErpChannelEnum channel, String baseConnectParamStr) {
        return getConfigHandler(channel, (BaseConnectParam) channel.getConnectParam(baseConnectParamStr));
    }

    @Nullable
    public static ConnectorConfigHandler getConfigHandler(@NotNull ErpChannelEnum channel, BaseConnectParam baseConnectParam) {
        return CONFIG_HANDLER_MAP.get(channel.getConnectorHandlerKey(baseConnectParam));
    }


    /**
     * 如果没有则返回固定的不支持的handler
     */
    public static ConnectorMetaDataHandler getMetaDataHandlerNotNull(ErpChannelEnum channel, String baseConnectParamStr) {
        ConnectorMetaDataHandler metaDataHandler = getMetaDataHandler(channel, baseConnectParamStr);
        if (metaDataHandler == null) {
            return UnsupportedConnectorHandler.metaDataHandler;
        }
        return metaDataHandler;
    }

    public static ConnectorConfigHandler getConfigHandlerNotNull(ErpChannelEnum channel, String baseConnectParam) {
        ConnectorConfigHandler handler = getConfigHandler(channel, baseConnectParam);
        if (handler == null) {
            return UnsupportedConnectorHandler.configHandler;
        }
        return handler;
    }

    public static ConnectorConfigHandler getConfigHandlerNotNull(ErpChannelEnum channel, BaseConnectParam baseConnectParam) {
        ConnectorConfigHandler handler = getConfigHandler(channel, baseConnectParam);
        if (handler == null) {
            return UnsupportedConnectorHandler.configHandler;
        }
        return handler;
    }

    private static class UnsupportedConnectorHandler implements InvocationHandler {
        private static ConnectorConfigHandler configHandler = Reflection.newProxy(ConnectorConfigHandler.class, new UnsupportedConnectorHandler());
        private static ConnectorMetaDataHandler metaDataHandler = Reflection.newProxy(ConnectorMetaDataHandler.class, new UnsupportedConnectorHandler());


        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            final String methodName = method.getName();
            switch (methodName) {
                case "hashCode":
                    return this.hashCode();
                case "toString":
                    return this.toString();
                case "equals":
                    return this.equals(args[0]);
            }
            //未实现方法
            return Result.newError(ResultCodeEnum.UNSUPPORTED_CHANNEL);
        }
    }
}
