package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
@Data
public abstract class K3UltimateApiTemplate implements Serializable {
    /**
     * 获取旗舰版对象apiName
     * @return
     */
    public abstract String getObjApiName();

    /**
     * 批量查询api
     */
    private String batchQueryApi;

    /**
     * 查询详情api
     */
    public String getDetailApi() {
        return getBatchQueryApi();
    }

    /**
     * 批量新建api
     */
    private String batchAddApi;
    /**
     * 批量更新api
     */
    private String batchUpdateApi;

    public String getBizChangeApi(ErpObjInterfaceUrlEnum interfaceUrl) {
        return null;
    }

    /**
     * 批量提交api
     */
    private String batchSubmitApi;
    /**
     * 批量反提交api
     */
    private String batchUnSubmitApi;

    /**
     * 批量审核api
     */
    private String batchAuditApi;
    /**
     * 批量反审核api
     */
    private String batchUnAuditApi;

    /**
     * 批量启用api
     */
    private String batchEnableApi;
    /**
     * 批量停用api
     */
    private String batchDisableApi;

    /**
     * 批量删除api
     */
    private String batchDeleteApi;

    /**
     * 批量生效api，仅用于销售订单变更单
     */
    private String batchValidApi;
    /**
     * 元数据接口
     */
    private String mateDataApi;
}
