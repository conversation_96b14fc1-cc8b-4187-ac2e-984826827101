package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ClassUtil;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MongoUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoSocketReadTimeoutException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexModel;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 日志Mongo的base dao，增加了索引管理等
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/29
 */
@Slf4j
public abstract class BaseMongoStore<T> {
    private final Class<T> clazz;

    private final CodecRegistry codecRegistry;
    /**
     * 记录已经尝试创建索引的db
     */
    protected final Set<String> dbCollNameCache = Sets.newConcurrentHashSet();

    /**
     * 允许并发创建索引的信号量
     * redisson的分布式信号量也有缺陷，先只使用本地的信号量来控制
     */
    private final Semaphore localSemaphore = new Semaphore(2);

    /**
     * 使用线程池来处理索引创建,防止超时等问题
     * 只有一个线程处理,防止并发创建索引
     */
    private static ThreadPoolExecutor createMongoIndexExecutor = DynamicExecutors.newThreadPool(0, 1, 10 * 1000, new ThreadFactoryBuilder().setNameFormat("create-mongo-index-%d").build());


    protected BaseMongoStore(CodecRegistry codecRegistry) {
        this.codecRegistry = codecRegistry;
        //noinspection unchecked
        this.clazz = (Class<T>) ClassUtil.getTypeArgument(getClass());
    }


    protected BaseMongoStore() {
        //noinspection unchecked
        this.clazz = (Class<T>) ClassUtil.getTypeArgument(getClass());
        if (this.clazz!= Document.class){
            //只允许Document作为泛型参数时，使用默认codecRegistry
            throw new ErpSyncDataException("Mongo store must set a codecRegistry for not Document",null,null);
        }
        this.codecRegistry = MongoClientSettings.getDefaultCodecRegistry();
    }

    /**
     * 获取db
     *
     * @param tenantId
     * @return
     */
    abstract public MongoDatabase getDatabase(final String tenantId);

    /**
     * 获取集合名称
     *
     * @param tenantId
     * @return
     */
    abstract public String getCollName(final String tenantId);

    /**
     * 获取集合
     *
     * @param tenantId
     * @return
     */
    public synchronized MongoCollection<T> getOrCreateCollection(final String tenantId) {
        MongoDatabase database = getDatabase(tenantId).withCodecRegistry(codecRegistry);
        String collectionName = getCollName(tenantId);
        String dbName = database.getName();
        MongoCollection<T> coll = database.getCollection(collectionName, clazz);
        //不等待，下次get coll会再次检查
        if (localSemaphore.tryAcquire()){
            try {
                if (dbCollNameCache.add(dbName + "." + collectionName)) {
                    checkIndex(tenantId, database, coll, collectionName);
                    log.info("try create index for {}.{}", database.getName(), collectionName);
                }
            }finally {
                localSemaphore.release();
            }
        }
        return coll;
    }


    private void checkIndex(String tenantId, MongoDatabase db, MongoCollection<T> coll, String collName) {
        createMongoIndexExecutor.execute(() -> {
            //创建索引,如果已存在mongo不会执行操作
            List<IndexModel> indexModels = buildIndexes(tenantId);
            List<Document> existIndexes = new ArrayList<>();
            coll.listIndexes().into(existIndexes);
            Document modCollCommand = MongoUtil.getNeedModifyCollExpireIndex(collName, existIndexes, indexModels);
            if (modCollCommand != null) {
                try {
                    Document modResult = db.runCommand(modCollCommand);
                    log.info("mod coll,{},{}", modCollCommand, modResult);
                } catch (Exception e) {
                    log.error("mod index exception", e);
                }
            }
            MongoUtil.removeExistIndexes(existIndexes, indexModels);
            if (!indexModels.isEmpty()) {
                try {
                    coll.createIndexes(indexModels);
                } catch (MongoSocketReadTimeoutException e) {
//                    超时,则丢弃后面的索引创建,防止cpu大涨
                    log.error("create index timeout,tenantId:{} collName:{}", tenantId, collName, e);
                    createMongoIndexExecutor.getQueue().clear();

//                    等待index创建完成
                    boolean needWait = true;
                    for (int i = 0; i < 100 && needWait; i++) {
                        List<Document> indexes = new ArrayList<>();
                        coll.listIndexes().into(indexes);
                        MongoUtil.removeExistIndexes(existIndexes, indexModels);
                        needWait = !indexModels.isEmpty();
                        try {
                            Thread.sleep(10_000L);
                        } catch (Throwable ex) {}
                    }
                } catch (Exception e) {
                    log.error("create index exception", e);
                }
            }
        });
    }


    /**
     * 预置索引,不允许不创建
     * @return 索引列表
     */
    public abstract List<IndexModel> buildIndexes(String tenantId);


    public CollStat getCollStat(String tenantId) {
        String collName = getCollName(tenantId);
        MongoDatabase db = getDatabase(tenantId);
        Document collStats = db.runCommand(new Document("collStats", collName));
        return BeanUtil.toBean(collStats, CollStat.class, CopyOptions.create().setIgnoreNullValue(true));
    }
}
