package com.fxiaoke.open.erpsyncdata.dbproxy.arg;

import lombok.Data;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/30 11:09
 * @desc
 */
@Data
public class RetryConfig implements Serializable {

    private String id;
    private String tenantId;
    private String retryDataEnum;
    private Long startTime;
    private List<Integer> status;
    private Long endTime;
    private Integer limit;
    private Integer offset;
    private List<String> dataIds;
    private String ids;
}
