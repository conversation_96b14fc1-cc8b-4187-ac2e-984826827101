package com.fxiaoke.open.erpsyncdata.monitor.util;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.lang.Pair;
import cn.hutool.crypto.KeyUtil;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTValidator;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.experimental.UtilityClass;

import javax.crypto.SecretKey;
import java.security.KeyPair;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@UtilityClass
public class ProxyTokenUtil {
    //缓存半小时
    TimedCache<String, String> cache = CacheUtil.newTimedCache(TimeUnit.MINUTES.toMillis(30L));

    static {
        //每一分钟清理一次key
        cache.schedulePrune(TimeUnit.MINUTES.toMillis(1L));
    }

    /**
     * 集成平台保存私钥生成token
     * rsa算法
     *
     * @param privateKey base64的私钥
     * @return
     */
    public String generateToken(String privateKey) {
        if (privateKey == null) {
            throw new ErpSyncDataException("secreteKey can not be null",null,null);
        }
        return cache.get(privateKey,false, () -> generateTokenNoCache(privateKey));
    }

    public String generateTokenNoCache(String privateKey) {
        //一小时过期
        DateTime oneHourAfter = DateTime.now().offset(DateField.HOUR_OF_DAY, 1);
        JWTSigner jwtSigner = JWTSignerUtil.rs256(KeyUtil.generateRSAPrivateKey(Base64.decode(privateKey)));
        String token = JWT.create()
                .setSigner(jwtSigner)
                //加上超时时间，每s生成的token就会不一样。
                .setExpiresAt(oneHourAfter)
                .sign();
        return token;
    }

    public boolean checkToken(String publicKey, String token) {
        try {
            JWTSigner jwtSigner = JWTSignerUtil.rs256(KeyUtil.generateRSAPublicKey(Base64.decode(publicKey)));
            JWTValidator.of(token).validateAlgorithm(jwtSigner).validateDate();
        } catch (ValidateException validateException) {
            return false;
        }
        return true;
    }

    public Pair<String,String> generateKey(){
        RSA rsa = new RSA();
        String privateKeyBase64 = rsa.getPrivateKeyBase64();
        String publicKeyBase64 = rsa.getPublicKeyBase64();
        return Pair.of(privateKeyBase64,publicKeyBase64);
    }
}
