package com.fxiaoke.open.erpsyncdata.dbproxy.node;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import groovy.util.logging.Slf4j;

/**
 * nodeProcessor抽象类，context作为属性
 * <p>
 * 改了很久，，，感觉太抽象了很难理解，这里只是定义一些接口，不做其他操作
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
public abstract class AbsNodeProcessor<T extends NodeContext<T>> {
    protected final DataNodeNameEnum dataNodeNameEnum;

    protected AbsNodeProcessor(DataNodeNameEnum dataNodeNameEnum) {
        this.dataNodeNameEnum = dataNodeNameEnum;
    }

    /**
     * 后处理，无论process是否成功都执行,AOP捕获并吞掉异常
     */
    public void postProcess(NodeContext<?> nodeContext) {
        //noinspection unchecked
        postReport((T) nodeContext);
    }


    /**
     * 节点结束后上报日志，无论是否成功都继续往下执行，AOP捕获并吞掉异常
     */
    protected void postReport(T ctx) {

    }

    /**
     * 上报延迟节点日志，无论是否成功都继续往下执行，AOP捕获并吞掉异常
     */
    protected void preReport(T ctx) {

    }


    /**
     * 前处理，无论是否成功都继续往下执行，AOP捕获并吞掉异常
     */
    public void preProcess(NodeContext<?> context) {
        //noinspection unchecked
        preReport((T) context);
    }


    /**
     * 是否执行
     */
    public boolean needProcess(T ctx) {
        return true;
    }

    public abstract T processMessage(T syncDataContextEvent);
}
