package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.DistributeCustomerUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.admin.arg.DistributeCustomerArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * distributeCustomer APL函数逻辑
 *
 * <AUTHOR>
 * @date 2023.12.07
 */
@Slf4j
@Service("distributeCustomer")
public class DistributeCustomerServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ApiClientHolder apiClientHolder;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("DistributeCustomerServiceImpl.executeLogic,arg={}", arg);

        if (arg == null || StringUtils.isEmpty(arg.getTenantId()) || StringUtils.isEmpty(arg.getParams())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        DistributeCustomerArg model = JSONObject.parseObject(arg.getParams(), DistributeCustomerArg.class);
        if (model == null
                || StringUtils.isEmpty(model.getTenantId())
                || StringUtils.isEmpty(model.getDataCenterId())
                || StringUtils.isEmpty(model.getUseOrgNumber())
                || StringUtils.isEmpty(model.getCustomerNumber())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(model.getTenantId(), model.getDataCenterId());
        if (connectInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(model.getTenantId(),
                connectInfoEntity.getConnectParams(),
                model.getDataCenterId());
        if (StringUtils.isNotEmpty(DistributeCustomerUtils.getCustomerId(apiClient, model.getCustomerNumber(), model.getUseOrgNumber()))) {
            return Result.newError(ResultCodeEnum.CUSTOMER_EXIST_IN_CURRENT_ORG);
        }

        String customerId = DistributeCustomerUtils.getCustomerId(apiClient, model.getCustomerNumber());
        if (StringUtils.isEmpty(customerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_CUSTOMER);
        }

        String orgId = DistributeCustomerUtils.getOrgId(apiClient, model.getUseOrgNumber());
        if (StringUtils.isEmpty(customerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_ORG);
        }

        DistributeArg distributeArg = new DistributeArg();
        distributeArg.setDataIds(customerId);
        distributeArg.setOrganizationIds(orgId);
        Result<String> result = apiClient.distribute("BD_Customer", distributeArg);
        log.info("DistributeCustomerServiceImpl.executeLogic,result={}", result);
        if (!result.isSuccess()) {
            return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
        }
        String newCustomerId = null;
        DistributeResult distributeResult = JSONObject.parseObject(result.getData(), DistributeResult.class);
        if (CollectionUtils.isNotEmpty(distributeResult.getResult().getResponseStatus().getSuccessEntitys())) {
            newCustomerId = distributeResult.getResult().getResponseStatus().getSuccessEntitys().get(0).getId();
        }
        return Result.newSuccess(newCustomerId);
    }
}
