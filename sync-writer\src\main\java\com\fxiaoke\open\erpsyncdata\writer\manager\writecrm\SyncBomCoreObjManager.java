package com.fxiaoke.open.erpsyncdata.writer.manager.writecrm;

import cn.hutool.core.collection.ListUtil;
import com.facishare.paas.pod.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmDataManager;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class SyncBomCoreObjManager {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private CrmDataManager crmDataManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    public void beforeWrite(SyncDataContextEvent syncDataContextEvent) {
        Integer destEventType = syncDataContextEvent.getDestEventType();
        if (!EventTypeEnum.UPDATE.match(destEventType)) {
            return;
        }
        String tenantId = syncDataContextEvent.getTenantId();
        String snapshotId = syncDataContextEvent.tryGetSnapshotId();
        Integer dataReceiveType = syncDataContextEvent.getDataReceiveType();
        SyncPloyDetailSnapshotEntity entryBySnapshotId = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, snapshotId);
        Optional<DetailObjectMappingsData.DetailObjectMappingData> first = entryBySnapshotId.getSyncPloyDetailData().getDetailObjectMappings().stream().filter(v -> ObjectApiNameEnum.FS_BOM_OBJ.match(v.getDestObjectApiName())).findFirst();
        if (!first.isPresent()) {
            return;
        }
        String sourceMainId = syncDataContextEvent.getSourceData().getId();
        DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData = first.get();
        LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
        Map<String, ObjectData> detailDataIdMap = new HashMap<>();
        for (ObjectData objectData : destDetailSyncDataIdAndDestDataMap.values()) {
            if (ObjectApiNameEnum.FS_BOM_OBJ.match(objectData.getApiName())) {
                detailDataIdMap.put(objectData.getId(), objectData);
            }
        }
        //根据目标数据是否存在，填充actionType。对于destData不存在，目标存在的，则增加删除的明细。
        List<SyncDataMappingsEntity> createdDetailMappings = syncDataMappingsDao.listCreatedDetailMappingByMasterDataId(tenantId, SyncObjectAndTenantMappingData.newInstance(tenantId, detailObjectMappingData.getSourceObjectApiName(), detailObjectMappingData.getDestObjectApiName()), sourceMainId);
        //key dataId,value mappingId
        Map<String, SyncDataMappingsEntity> detailMappingIdMap = new HashMap<>();
        for (SyncDataMappingsEntity createdDetailMapping : createdDetailMappings) {
            detailMappingIdMap.put(createdDetailMapping.getDestDataId(), createdDetailMapping);
        }
        List<String> createdMappingDestDataIdsAll = new ArrayList<>(detailMappingIdMap.keySet());

        //记录新增的明细，即没有查到的
        Set<String> newDetailIds = new HashSet<>(detailDataIdMap.keySet());
        try {
            for (List<String> createdMappingDestDataIds : ListUtil.split(createdMappingDestDataIdsAll, 2000)) {
                //查询CRM的数据，这里只是检查是否存在，需要补充数据的在写那里补充
                List<com.fxiaoke.crmrestapi.common.data.ObjectData> crmObjList = crmDataManager.listCRMObjsByIds(tenantId, detailObjectMappingData.getDestObjectApiName(), createdMappingDestDataIds, ListUtil.of("_id", "is_deleted", "life_status"));
                for (com.fxiaoke.crmrestapi.common.data.ObjectData crmObj : crmObjList) {
                    String crmObjId = crmObj.getId();
                    newDetailIds.remove(crmObjId);
                    ObjectData detailObjData = detailDataIdMap.get(crmObjId);
                    if (detailObjData == null) {
                        //destData不存在，认为需要删除。仅适用于全量传输明细，如果有不全量传输的，需要另外思考方案。
                        String syncDataId = IdUtil.generateId();
                        SyncDataMappingsEntity mapping = detailMappingIdMap.get(crmObjId);
                        String mappingId = mapping.getId();
                        ObjectData sourceData = new ObjectData();
                        sourceData.putTenantId(tenantId);
                        sourceData.putApiName(detailObjectMappingData.getSourceObjectApiName());
                        sourceData.putId(mapping.getSourceDataId());
                        ObjectData destData = new ObjectData();
                        destData.putTenantId(tenantId);
                        destData.putApiName(detailObjectMappingData.getDestObjectApiName());
                        destData.putId(mapping.getDestDataId());
                        destData.put("action_type", "delete");
                        //创建syncData
                        SyncDataEntity detailSyncData = syncDataManager.createSyncDataWithSnapshotEntity(tenantId,syncDataContextEvent.getSourceContextUserId(), syncDataId, mappingId, EventTypeEnum.INVALID.getType(),
                                TenantType.ERP, sourceData, destData, SyncDataStatusEnum.BE_PROCESS.getStatus(),
                                entryBySnapshotId,
                                crmObjId, EventTypeEnum.INVALID.getType(), detailObjectMappingData.getDestObjectApiName(), tenantId,
                                TenantType.CRM, null, i18NStringManager.getByEi(I18NStringEnum.s966,tenantId), I18NStringEnum.s966.name(), null, dataReceiveType,syncDataContextEvent.getDataVersion());
                        destDetailSyncDataIdAndDestDataMap.put(syncDataId, destData);
                    } else {
                        //目标数据和crm数据都存在，认为是修改，增加action_type
                        detailObjData.put("action_type", "update");
                    }
                }
            }
            for (String newDetailId : newDetailIds) {
                //新增
                detailDataIdMap.get(newDetailId).put("action_type", "create");
            }
        } catch (Exception e) {
            log.error("find crm data error", e);
        }
    }
}
