package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/10
 */
public class K3Model extends LinkedHashMap<String, Object>{
    private static final long serialVersionUID = 5334358421821577082L;

    /**
     * 支持多种key的get
     * @param keys
     * @return
     */
    public Object get(String... keys) {
        Object o = null;
        for (String key : keys) {
            o = this.get(key);
            if (o!=null){
                break;
            }
        }
        return o;
    }

    /**
     * 支持多个key的删除
     * @param keys
     * @return
     */
    public Object delete(String... keys){
        for (String key : keys) {
            Object remove = this.remove(key);
            if (remove!=null){
                return remove;
            }
        }
        return null;
    }

    public Object put(String key, Object value, ErpFieldTypeEnum fieldType) {
        switch (fieldType) {
            case date_time:
                // TODO: 2020/11/10 日期转换
                break;
            default:
                break;
        }
        return super.put(key, value);
    }

    public List<K3Model> getDetails(String detailApiName) {
        Object obj = this.get(detailApiName);
        if (obj == null) {
            return new ArrayList<>();
            //因为这个异常上层老是被吞掉，无法返回错误信息，直接不抛异常了。
//            throw new ErpSyncDataException("获取明细异常");
        }
        return JacksonUtil.fromJson(JacksonUtil.toJson(obj), new TypeReference<List<K3Model>>() {
        });
    }

    public String getString(String key) {
        Object obj = this.get(key);
        return JacksonUtil.toJson(obj);
    }

    public BigDecimal getBigDecimal(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Double) {
            return BigDecimal.valueOf((Double) value);
        }
        return new BigDecimal(value.toString());
    }


    public Double getDouble(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Double) {
            return (Double) value;
        }
        return Double.valueOf(value.toString());
    }


    public Integer getInt(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        return Integer.valueOf(value.toString());
    }

// 这个是map，不需要单独实现
//    @Override
//    public long ramBytesUsed(int depth) {
//        long totalObjBytesSize = 0;
//        for(String key: this.keySet()) {
//                Object obj = this.get(key);
//                totalObjBytesSize +=  RamUsageEstimateUtil.sizeOfObject(obj, depth);
//        }
//        return totalObjBytesSize;
//    }
}
