package com.fxiaoke.open.erpsyncdata.apiproxy.engine;

import com.fxiaoke.open.erpsyncdata.apiproxy.engine.directives.NumberDirective2;
import com.jfinal.template.Engine;

public class EngineUtils {
    public static Engine getEngine() {
        Engine engine = Engine.use();
        engine.getEngineConfig().removeDirective("number");
        engine.addDirective("number", NumberDirective2.class);
        return engine;
    }
}
