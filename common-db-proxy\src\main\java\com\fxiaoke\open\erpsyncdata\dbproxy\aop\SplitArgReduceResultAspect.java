package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import cn.hutool.core.collection.ListUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.SplitArgReduceResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TransactionTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BinaryOperator;

/**
 * <AUTHOR>
 * @date 2024/12/11 11:48:54
 * <p>
 * 为了减少数据库的压力,减少超长sql,将入参为list的,按配置的数量每组拆分,最后结果在做reduce
 * 考虑到以后可能mongo或者其他的Service也需要该需求,做成aop,而不是mybatis的拦截器
 * 
 * 注意:以下类型的方法不支持使用该注解:
 * 1. 带order by的查询方法 - 因为拆分后结果顺序会乱
 * 2. 带group by的统计方法 - 因为分组会被打散
 * 3. 带offset的分页方法 - 因为offset会错位
 * 4. 带max/min聚合的方法 - 因为极值会丢失
 * 5. 带复杂子查询的方法 - 因为可能导致结果不准确
 * 6. 返回值不能reduce的方法 - 只支持数字和List类型的返回值
 * 
 * 适用场景:
 * 1. 批量更新/删除操作
 * 2. 简单的List查询
 * 3. 简单的count统计
 *
 * //IgnoreI18nFile 有catch,只会打印日志,不会抛出到用户
 * @see SplitArgReduceResult
 */
@Slf4j
@Aspect
@Component
public class SplitArgReduceResultAspect {

    /**
     * 方法参数索引缓存
     * key: 方法
     * value: 参数索引
     * 注意:方法的参数索引在编译时就确定了,运行时不会改变,所以这里使用ConcurrentHashMap而不是LoadingCache
     */
    private static final Map<Method, Integer> methodIndexMap = new ConcurrentHashMap<>();

    /**
     * PostgreSQL事务模板(默认druid数据源)
     */
    @Autowired
    @Qualifier("transactionTemplate")
    private TransactionTemplate pgTransactionTemplate;

    /**
     * PostgreSQL事务模板(hikari数据源)
     */
    @Autowired
    @Qualifier("hikariTransactionTemplate")
    private TransactionTemplate pgHikariTransactionTemplate;

    /**
     * ClickHouse事务模板
     */
    @Autowired
    @Qualifier("chTransactionTemplate")
    private TransactionTemplate chTransactionTemplate;

    /**
     * 事务模板映射
     */
    private final Map<TransactionTypeEnum, TransactionTemplate> transactionTemplateMap = new EnumMap<>(TransactionTypeEnum.class);

    @PostConstruct
    public void init() {
        transactionTemplateMap.put(TransactionTypeEnum.PG, pgTransactionTemplate);
        transactionTemplateMap.put(TransactionTypeEnum.PG_HIKARI, pgHikariTransactionTemplate);
        transactionTemplateMap.put(TransactionTypeEnum.CLICKHOUSE, chTransactionTemplate);
        log.info("SplitArgReduceResultAspect initialized with transaction templates: {}", transactionTemplateMap.keySet());
    }

    /**
     * 需要回滚的数据库操作异常
     */
    public static class DbOperationException extends RuntimeException {
        public DbOperationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 不需要回滚的reduce处理异常
     */
    public static class ReduceOperationException extends RuntimeException {
        public ReduceOperationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao.*(..))")
    public Object splitArgReduceResult(ProceedingJoinPoint jp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final SplitArgReduceResult annotation = methodSignature.getMethod().getAnnotation(SplitArgReduceResult.class);
        if (Objects.isNull(annotation)) {
            return jp.proceed();
        }
        return splitArgReduceResult(jp, annotation);
    }

    //    @Around("execution(@annotation(annotation))")
    public Object splitArgReduceResult(ProceedingJoinPoint jp, SplitArgReduceResult annotation) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final String methodName = methodSignature.getName();

        // 参数校验
        final ValidateResult validateResult = validateParameters(jp, annotation, methodSignature);
        if (!validateResult.isValid()) {
            return jp.proceed();
        }

        final Object[] args = jp.getArgs();
        List<Object> originalArg = (List<Object>) args[validateResult.getIndex()];
        
        // 获取拆分数量
        final int num = annotation.num() == 0 ? ConfigCenter.SPLIT_ARG_DEFAULT_NUM : annotation.num();
        if (CollectionUtils.isEmpty(originalArg) || originalArg.size() <= num) {
            log.debug("List size {} is not larger than split size {}, skipping split for method {}", 
                    originalArg == null ? 0 : originalArg.size(), num, methodName);
            return jp.proceed();
        }

        List<List<Object>> splitLists = ListUtil.split(originalArg, num);

        try {
            return processWithTransaction(jp, methodName, args, validateResult.getIndex(), 
                    splitLists, annotation, originalArg);
        } finally {
            args[validateResult.getIndex()] = originalArg; // 恢复原始参数
        }
    }

    /**
     * 参数校验结果
     */
    private static class ValidateResult {
        private final boolean valid;
        private final Integer index;

        public ValidateResult(boolean valid, Integer index) {
            this.valid = valid;
            this.index = index;
        }

        public boolean isValid() {
            return valid;
        }

        public Integer getIndex() {
            return index;
        }
    }

    /**
     * 校验参数
     */
    private ValidateResult validateParameters(ProceedingJoinPoint jp, SplitArgReduceResult annotation, 
            MethodSignature methodSignature) {
        final String methodName = methodSignature.getName();
        final Object[] args = jp.getArgs();
        final String listName = annotation.value();

        Integer index = getSplitArgIndex(methodSignature, args, listName);
        if (Objects.isNull(index)) {
            log.warn("Parameter validation failed: list parameter {} not found in method {}", 
                    listName, methodName);
            return new ValidateResult(false, null);
        }

        return new ValidateResult(true, index);
    }

    /**
     * 使用事务处理拆分后的列表
     */
    private Object processWithTransaction(ProceedingJoinPoint jp, String methodName, Object[] args, 
            Integer index, List<List<Object>> splitLists, SplitArgReduceResult annotation,
            List<Object> originalArg) throws Throwable {
        final BinaryOperator<Object> reduceFunc = (BinaryOperator<Object>) annotation.reduce().getReduceFunc();
        final int limit = annotation.limit();
        final TransactionTypeEnum transactionType = annotation.transactionType();

        try {
            // 如果不需要事务,直接执行
            if (transactionType == TransactionTypeEnum.NONE) {
                return processSplitLists(jp, methodName, args, index, splitLists, reduceFunc, limit, null);
            }

            // 获取对应的事务模板
            TransactionTemplate transactionTemplate = getTransactionTemplate(transactionType);
            if (transactionTemplate == null) {
                log.warn("Transaction template not found for type: {}, method: {}, will execute without transaction", 
                        transactionType, methodName);
                return processSplitLists(jp, methodName, args, index, splitLists, reduceFunc, limit, null);
            }

            return transactionTemplate.execute(status -> 
                processSplitLists(jp, methodName, args, index, splitLists, reduceFunc, limit, status));
        } catch (Exception e) {
            // 如果是reduce处理出错,尝试使用原始参数重新执行
            if (e instanceof ReduceOperationException || 
                (e.getCause() instanceof ReduceOperationException)) {
                log.info("Reduce operation failed, retrying with original args for method {}", methodName);
                args[index] = originalArg;
                return jp.proceed();
            }
            if (e instanceof DbOperationException){
                throw e.getCause();//原始报错
            }
            throw e;
        }
    }

    /**
     * 处理拆分后的列表
     * @param status 如果不为null,表示在事务中执行
     */
    private Object processSplitLists(ProceedingJoinPoint jp, String methodName, Object[] args, Integer index,
                                   List<List<Object>> splitLists, BinaryOperator<Object> reduceFunc, int limit,
                                   TransactionStatus status) {
        Object result = null;
        int processedCount = 0;
        
        for (List<Object> list : splitLists) {
            args[index] = list;
            Object partialResult;
            try {
                partialResult = jp.proceed(args);
                processedCount += list.size();
            } catch (Throwable e) {
                String errorMsg = String.format("Processing error%s for method %s with %d items at index %d: %s",
                        status != null ? " in transaction" : "", methodName, list.size(), processedCount, e.getMessage());
                log.error(errorMsg, e);
                if (status != null) {
                    status.setRollbackOnly();
                }
                throw new DbOperationException("数据库操作失败,触发事务回滚", e);
            }

            try {
                if (result == null) {
                    result = partialResult;
                } else {
                    result = reduceFunc.apply(result, partialResult);
                }
                if (limit > 0 && getNumber(result) >= limit) {
                    return getLimit(result, limit);
                }
            } catch (Exception e) {
                String warnMsg = String.format("Reduce operation failed%s for method %s: %s",
                        status != null ? " in transaction" : "", methodName, e.getMessage());
                log.warn(warnMsg, e);
                if (status != null) {//因为后面会用原参数重试，所以先回滚了操作
                    status.setRollbackOnly();
                }
                throw new ReduceOperationException("Reduce操作失败,将使用原始参数重试", e);
            }
        }
        return result;
    }

    /**
     * 根据事务类型获取对应的事务模板
     */
    private TransactionTemplate getTransactionTemplate(TransactionTypeEnum type) {
        return transactionTemplateMap.get(type);
    }

    /**
     * 获取方法参数中List参数的索引
     */
    private Integer getSplitArgIndex(MethodSignature methodSignature, Object[] args, String listName) {
        final Method method = methodSignature.getMethod();
        return methodIndexMap.computeIfAbsent(method, k -> {
            final String[] parameterNames = InvokeErrorMonitorAspect.getParameterNames(methodSignature);
            for (int i = 0; i < parameterNames.length; i++) {
                final String name = parameterNames[i];
                if (Objects.equals(listName, name)) {
                    final Object o = args[i];
                    if (!(o instanceof List)) {
                        log.warn("Parameter {} in method {} is not a List", listName, methodSignature.getName());
                        return null;
                    }
                    return i;
                }
            }
            return null;
        });
    }

    private Object getLimit(Object result, int limit) {
        if (result instanceof Number) {
            return limit;
        }
        if (result instanceof List) {
            return ((List<?>) result).subList(0, Math.min(limit, ((List<?>) result).size()));
        }
        return result;
    }

    private int getNumber(Object result) {
        if (result instanceof Number) {
            return ((Number) result).intValue();
        }
        if (result instanceof List) {
            return ((List<?>) result).size();
        }
        return 0;
    }
}
