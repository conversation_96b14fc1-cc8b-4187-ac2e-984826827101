package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.common.ibatis.BaseMapper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.github.mybatis.mapper.ITenant;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/11
 */
public interface BaseTenantMapper<T,C> extends BaseMapper<T>, ITenant<C> {
    default C setGlobalTenant(String tenantId) {
        return ITenant.super.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId));
    }
}
