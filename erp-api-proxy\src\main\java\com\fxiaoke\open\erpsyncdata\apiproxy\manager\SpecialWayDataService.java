package com.fxiaoke.open.erpsyncdata.apiproxy.manager;


import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 14:09 2020/12/7
 * @Desc:
 */
public interface SpecialWayDataService {
    Result<StandardData> getErpObjDataByIdWithDB(ErpIdArg erpIdArg,String dataCenterId);

    Result<List<StandardData>> getReSyncObjDataByIdWithDB(ErpIdArg erpIdArg,String dataCenterId);

    Result<StandardListData> listErpObjDataByTimeWithDB(TimeFilterArg timeFilterArg,String dataCenterId);

    @InvokeMonitor(tenantId = "#erpIdArg.tenantId", dcId = "#erpObjGroovyEntity.dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", objAPIName = "#erpIdArg.objAPIName", dataId = "#erpIdArg.dataId", sourceEventType = "#erpIdArg.sourceEventType", snapshotId = "#erpIdArg.syncPloyDetailSnapshotId", action = ActionEnum.GET, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#erpIdArg.tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#erpIdArg.objAPIName", outDataCount = "1",outSideObjId = "#erpIdArg.dataId",sourceSystemType = "2",operationType = CommonConstant.READ_OPERATE_TYPE,operationTypeDetail =  ErpObjInterfaceUrlEnum.queryMasterById , operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardData> getErpObjDataByIdWithGroovy(ErpIdArg erpIdArg, ErpObjGroovyEntity erpObjGroovyEntity);

    @InvokeMonitor(tenantId = "#erpIdArg.tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "#result?.getData()?.size()?:0", objAPIName = "#erpIdArg.objAPIName", dataId = "#erpIdArg.dataId", sourceEventType = "#erpIdArg.sourceEventType", snapshotId = "#erpIdArg.syncPloyDetailSnapshotId", action = ActionEnum.GET_LIST_BY_ID, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#erpIdArg.tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#erpIdArg.objAPIName", outDataCount = "#result?.getData()?.size()?:0",outSideObjId = "#erpIdArg.dataId",sourceSystemType = "2",operationType = CommonConstant.READ_OPERATE_TYPE,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<List<StandardData>> getReSyncObjDataByIdWithGroovy(ErpIdArg erpIdArg, ErpObjGroovyEntity erpObjGroovyEntity,String dataCenterId);

    @InvokeMonitor(tenantId = "#timeFilterArg.tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "#result?.getData()?.dataList?.size()?:0", objAPIName = "#timeFilterArg.objAPIName", snapshotId = "#timeFilterArg.snapshotId", action = ActionEnum.LIST, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#timeFilterArg.tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#timeFilterArg.objAPIName", outDataCount = "#result?.getData()?.dataList?.size()?:0",sourceSystemType = "2",operationType = CommonConstant.READ_OPERATE_TYPE,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardListData> listErpObjDataByTimeWithGroovy(TimeFilterArg timeFilterArg, ErpObjGroovyEntity erpObjGroovyEntity,String dataCenterId);

    @InvokeMonitor(tenantId = "#doWriteMqData.tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", data = "#standardData", objAPIName = "#standardData.objAPIName", dataId = "#result?.data?.masterDataId", action = ActionEnum.CREATE, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#doWriteMqData.tenantId", dataCenterId = "#dataCenterId",outSideObjApiName = "#standardData.objAPIName",outSideObjId = "#result?.data?.masterDataId", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operationTypeDetail = ErpObjInterfaceUrlEnum.create,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<ErpIdResult> createErpObjDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                   StandardData standardData,
                                                   ErpObjGroovyEntity erpObjGroovyEntity,
                                                   String dataCenterId);
    @InvokeMonitor(tenantId = "#doWriteMqData.tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", data = "#standardDetailData", objAPIName = "#standardDetailData.objAPIName", dataId = "#standardDetailData.masterDataId", snapshotId = "#doWriteMqData.syncPloyDetailSnapshotId", action = ActionEnum.CREATE_DETAIL, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#doWriteMqData.tenantId", dataCenterId = "#dataCenterId",outSideObjApiName = "#standardData.objAPIName",outSideObjId = "#result?.data?.masterDataId", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operationTypeDetail = ErpObjInterfaceUrlEnum.createDetail,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardDetailId> createErpObjDetailDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                              StandardDetailData standardDetailData,
                                                              ErpObjGroovyEntity erpObjGroovyEntity,
                                                              String dataCenterId);
    @InvokeMonitor(tenantId = "#doWriteMqData.tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", data = "#standardData", objAPIName = "#standardData.objAPIName", dataId = "#standardData.masterFieldVal.getId()", action = ActionEnum.UPDATE, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#doWriteMqData.tenantId", dataCenterId = "#dataCenterId",outSideObjApiName = "#standardData.objAPIName",outSideObjId = "#standardData?.masterFieldVal.getId()", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operationTypeDetail = ErpObjInterfaceUrlEnum.update,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<ErpIdResult> updateErpObjDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                   StandardData standardData,
                                                   ErpObjGroovyEntity erpObjGroovyEntity,
                                                   String dataCenterId);
    @InvokeMonitor(tenantId = "#doWriteMqData.tenantId", dcId = "#erpObjGroovyEntity.dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", data = "#standardDetailData", objAPIName = "#standardDetailData?.objAPIName", dataId = "#standardDetailData?.masterDataId", snapshotId = "#doWriteMqData.syncPloyDetailSnapshotId", action = ActionEnum.UPDATE_DETAIL, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#doWriteMqData.tenantId", dataCenterId = "#erpObjGroovyEntity.dataCenterId",outSideObjApiName = "#standardDetailData.objAPIName",outSideObjId = "#standardDetailData?.masterDataId", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operationTypeDetail = ErpObjInterfaceUrlEnum.updateDetail,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardDetailId> updateErpObjDetailDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                              StandardDetailData standardDetailData,
                                                              ErpObjGroovyEntity erpObjGroovyEntity);
    @InvokeMonitor(tenantId = "#erpObjGroovyEntity.tenantId", dcId = "#erpObjGroovyEntity.dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", objAPIName = "#standardInvalidData?.objAPIName", dataId = "#standardInvalidData?.masterFieldVal?.getId()", action = ActionEnum.INVALID)
    @DataMonitorScreen(tenantId = "#erpObjGroovyEntity.tenantId", dataCenterId = "#erpObjGroovyEntity.dataCenterId",outSideObjApiName = "#standardInvalidData.objAPIName",outSideObjId = "#standardInvalidData?.masterDataId", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operationTypeDetail = ErpObjInterfaceUrlEnum.invalid,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<String> invalidErpObjDataWithGroovy(StandardInvalidData standardInvalidData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId);

    @InvokeMonitor(tenantId = "#erpObjGroovyEntity.tenantId", dcId = "#erpObjGroovyEntity.dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", objAPIName = "#standardInvalidData.objAPIName", dataId = "#standardInvalidData?.masterFieldVal?.getId()", action = ActionEnum.INVALID)
    @DataMonitorScreen(tenantId = "#erpObjGroovyEntity.tenantId", dataCenterId = "#erpObjGroovyEntity.dataCenterId",outSideObjApiName = "#standardInvalidData.objAPIName",outSideObjId = "#standardInvalidData?.masterDataId", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operationTypeDetail = ErpObjInterfaceUrlEnum.invalidDetail,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<String> invalidErpObjDetailDataWithGroovy(StandardInvalidData standardInvalidData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId);

    Result<String> executeCustomFunction(String tenantId,
                                         String dataCenterId,
                                         String funcApiName,
                                         Map arg,
                                         ErpObjInterfaceUrlEnum interfaceUrl, final String syncPloyDetailSnapshotId,String objApiName,String objDataName);

    @InvokeMonitor(tenantId = "#erpObjGroovyEntity.tenantId", dcId = "#erpObjGroovyEntity.dataCenterId", invokeType = InvokeTypeEnum.FUNC, count = "1", objAPIName = "#standardRecoverData.objAPIName", dataId = "#standardRecoverData.masterFieldVal.getId()", action = ActionEnum.RECOVER)
    @DataMonitorScreen(tenantId = "#erpObjGroovyEntity.tenantId", dataCenterId = "#erpObjGroovyEntity.dataCenterId",outSideObjApiName = "#standardRecoverData.objAPIName",outSideObjId = "#standardRecoverData?.masterFieldVal.getId()", sourceSystemType = "1",operationType = CommonConstant.WRITE_OPERATE_TYPE,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<String> recoverErpObjDataWithGroovy(StandardRecoverData standardRecoverData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId);

    Result<String> deleteErpObjDataWithGroovy(StandardInvalidData standardDeleteData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId);
}
