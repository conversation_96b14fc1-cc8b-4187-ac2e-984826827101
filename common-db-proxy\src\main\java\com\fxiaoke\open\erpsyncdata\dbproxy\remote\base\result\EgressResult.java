package com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.result;

import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/2 10:17:44
 */
@Data
public
class EgressResult<T> implements Serializable {
    public static final int SUCCESS_CODE = 200;

    private int code;
    private String message;
    private T data;


    public T getData() {
        if (code != SUCCESS_CODE) {
            throw new CrmBusinessException(code, message);
        }
        return data;
    }

    public boolean isSuccess() {
        return this.code == SUCCESS_CODE;
    }
}
