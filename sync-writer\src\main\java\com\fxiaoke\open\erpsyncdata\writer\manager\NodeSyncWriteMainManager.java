package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.multi.SetValueMap;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.NodeDataStatus;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.SyncDataErrLog;
import com.fxiaoke.open.erpsyncdata.common.util.SandboxUtil;
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MappingCreatedData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UpdateMapping;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ErrorUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SimpleSyncData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ObjectMappingVo;
import com.fxiaoke.ps.ProtostuffUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.arg.CompleteDataWriteArg.OUT_ERROR_CODE;

/**
 * 写数据统一入口
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/12/28
 */
@Service
@Slf4j
public class NodeSyncWriteMainManager extends AbsMainNodeProcessor {
    @Autowired
    private DoWrite2CrmManager doWrite2CrmManager;
    @Autowired
    private DoWrite2ErpManager doWrite2ErpManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private PloyBreakManager ployBreakManager;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private AsyncReTryIfFailedManager asyncReTryIfFailedManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private EIEAConverter eieaConverter;
    public NodeSyncWriteMainManager() {
        super(DataNodeNameEnum.DataWriteDest);
    }

    @CompareSyncField(syncType = SyncCompareConstant.SYNC_DATA_WRITE)
    public SyncDataContextEvent processMessage(final SyncDataContextEvent doWriteMqData) {
        if (TenantType.CRM.equals(doWriteMqData.getDestTenantType())) {
            //写CRM,批量写不需要执行complete,批量写有可能在当前线程执行。
            doWrite2CrmManager.writeToCRM(doWriteMqData);
        } else {
            doWrite2ErpManager.writeToERP(doWriteMqData);
        }
        if (!doWriteMqData.getStop() || doWriteMqData.isInterceptRepeatSync()) {
            completeWriteUpdateStatus(doWriteMqData);
        }
        return doWriteMqData;
    }

    @Override
    protected void postReport(SyncDataContextEvent ctx) {
        //更新中间表成功后，即进行上报写成功或失败的日志
        SyncDataContextEvent.WriteResult writeResult = ctx.getWriteResult();
        if (writeResult != null) {
            int count = 1;
            if (ctx.getDetailWriteResults() != null) {
                count += ctx.getDetailWriteResults().size();
            }
            Long tpm = null;
            if (Objects.equals(ctx.getSourceTenantType(), TenantType.ERP)){
                //速度限制写入到上报数据中
                tpm = Double.valueOf(speedLimitManager.getTenantTpm(ctx.getTenantId(), SpeedLimitTypeEnum.TO_CRM)).longValue();
            }
            if (writeResult.isSuccess()) {
                //主成功都算成功,失败都算失败。实际上有些主从分开的可能会不一致。
                NodeHelper.addStatusRecord(NodeDataStatus.WRITE_SUCCESS, ctx.getMainIdIfExist(), count, tpm);
            } else {
                NodeHelper.addStatusRecord(NodeDataStatus.WRITE_FAILED, ctx.getMainIdIfExist(), count, tpm);
            }
        }
    }

    //更新中间表
    /**
     * 先将主从的数据都准备好，然后一次性批量更新mapping
     *
     * @param message
     */
    public void completeWriteUpdateStatus(SyncDataContextEvent message) {
        //更新中间表在节点监控中也作为一个节点了
        message.setCurrentDataNodeName(DataNodeNameEnum.DataIdMapping);
        monitorReportManager.sendProcessNodeMsgByCtx(message, DataNodeNameEnum.DataIdMapping);

        //原来代码有两种更新方式，这里也保留两种批量更新方式
        List<UpdateMapping.BySyncDataIdArg> bySyncDataIdArgs = new ArrayList<>();
        List<UpdateMapping.BySourceArg> bySourceArgs = new ArrayList<>();
        //构建明细的结果
        buildDetailUpdateArgs(message, bySyncDataIdArgs, bySourceArgs);
        //构建主数据结果
        buildMainUpdateArg(message, bySyncDataIdArgs, bySourceArgs);
        //分成两类更新是不想更改原来的代码逻辑
        bulkUpdateBySource(message.getTenantId(), bySourceArgs);
        bulkUpdateBySyncDataId(message.getTenantId(), bySyncDataIdArgs);
    }

    public void batchCompleteWriteUpdateStatus(List<SyncDataContextEvent> syncDataContextEvents){
        for (SyncDataContextEvent syncDataContextEvent : syncDataContextEvents) {
            completeWriteUpdateStatus(syncDataContextEvent);
        }
    }

    private void buildDetailUpdateArgs(SyncDataContextEvent message,
                                       List<UpdateMapping.BySyncDataIdArg> bySyncDataIdArgs,
                                       List<UpdateMapping.BySourceArg> bySourceArgs) {
        String tenantId = message.getTenantId();
        Integer destEventType = message.getDestEventType();
        List<SyncDataContextEvent.WriteResult> detailWriteResults = message.getDetailWriteResults();
        if (CollUtil.isEmpty(detailWriteResults)) {
            return;
        }
        //更新syncData
        for (SyncDataContextEvent.WriteResult detailWriteResult : detailWriteResults) {
            updateSyncDataStatus(tenantId, destEventType, false, detailWriteResult);
        }
        List<SyncDataContextEvent.WriteResult> failedDetailResults = detailWriteResults.stream().filter(v -> !v.isSuccess()).collect(Collectors.toList());
        List<SyncDataContextEvent.WriteResult> successDetailResults = detailWriteResults.stream().filter(SyncDataContextEvent.WriteResult::isSuccess).collect(Collectors.toList());
        for (SyncDataContextEvent.WriteResult writeResult : failedDetailResults) {
            //失败明细数据处理
            UpdateMapping.BySyncDataIdArg arg = UpdateMapping.BySyncDataIdArg.builder()
                    .syncDataId(writeResult.getSyncDataId())
                    .lastSyncStatus(SyncDataStatusEnum.WRITE_FAILED.getStatus())
                    .destDataName(writeResult.getSimpleSyncData().getDestDataName())
                    .remark(writeResult.getErrMsg())
                    .build();
            bySyncDataIdArgs.add(arg);
        }
        if (CollUtil.isEmpty(successDetailResults)) {
            return;
        }
        //成功明细处理
        if (destEventType == EventTypeEnum.ADD.getType()) {
            //目标新增时
            for (SyncDataContextEvent.WriteResult writeResult : successDetailResults) {
                SimpleSyncData simpleSyncData = writeResult.getSimpleSyncData();
                String destDataName = simpleSyncData.getDestDataName();
                UpdateMapping.BySourceArg arg = UpdateMapping.BySourceArg.builder()
                        .syncDataId(writeResult.getSyncDataId())
                        .sourceObjectApiName(simpleSyncData.getSourceObjectApiName())
                        .destObjectApiName(simpleSyncData.getDestObjectApiName())
                        .sourceDataId(simpleSyncData.getSourceDataId())
                        .destDataId(writeResult.getDestDataId())
                        .lastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus())
                        .destDataName(destDataName)
                        .remark(writeResult.getErrMsg()).build();
                bySourceArgs.add(arg);
            }
        } else {
            //目标更新时
            buildSuccessDetailArg(bySyncDataIdArgs, bySourceArgs, tenantId, successDetailResults);
        }

    }

    private void buildMainUpdateArg(SyncDataContextEvent message,
                                    List<UpdateMapping.BySyncDataIdArg> bySyncDataIdArgs,
                                    List<UpdateMapping.BySourceArg> bySourceArgs) {
        SyncDataContextEvent.WriteResult writeResult = message.getWriteResult();
        if (ObjectUtils.isEmpty(writeResult)) {
            return;
        }
        Integer destEventType = message.getDestEventType();
        SimpleSyncData mainSyncData = writeResult.getSimpleSyncData();
        String destDataName = StrUtil.firstNonBlank(message.getDestDataNameAfterWrite(), mainSyncData.getDestDataName());
        String remark = writeResult.getErrMsg();
        updateSyncDataStatus(message.getTenantId(), destEventType, true, writeResult);
        if (!writeResult.isSuccess()) {
            //失败
            UpdateMapping.BySyncDataIdArg arg = UpdateMapping.BySyncDataIdArg.builder()
                    .syncDataId(writeResult.getSyncDataId())
                    .lastSyncStatus(SyncDataStatusEnum.WRITE_FAILED.getStatus())
                    .destDataName(destDataName)
                    .remark(remark)
                    .build();
            bySyncDataIdArgs.add(arg);
        } else {
            //成功
            if (destEventType == EventTypeEnum.ADD.getType()) {
                //新增
                UpdateMapping.BySourceArg arg = UpdateMapping.BySourceArg.builder()
                        .syncDataId(writeResult.getSyncDataId())
                        .sourceObjectApiName(mainSyncData.getSourceObjectApiName())
                        .destObjectApiName(mainSyncData.getDestObjectApiName())
                        .sourceDataId(mainSyncData.getSourceDataId())
                        .destDataId(writeResult.getDestDataId())
                        .lastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus())
                        .destDataName(destDataName)
                        .remark(remark).build();
                bySourceArgs.add(arg);
            } else {
                if (EventTypeEnum.INVALID.getType() == destEventType) {
                    remark = i18NStringManager.getByEi(I18NStringEnum.s1152, message.getTenantId());//后面有用到
                }
                UpdateMapping.BySyncDataIdArg arg = UpdateMapping.BySyncDataIdArg.builder()
                        .syncDataId(writeResult.getSyncDataId())
                        .lastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus())
                        .destDataName(destDataName)
                        .remark(remark)
                        .build();
                bySyncDataIdArgs.add(arg);
            }
        }
    }
    private void buildSuccessDetailArg(List<UpdateMapping.BySyncDataIdArg> bySyncDataIdArgs, List<UpdateMapping.BySourceArg> bySourceArgs, String tenantId, List<SyncDataContextEvent.WriteResult> successDetailResults) {
        //明细需要查一次中间表,根据sourceData信息查
        //分组,<objMapping,srcId>
        SetValueMap<ObjectMappingVo, String> objMappingSrcIdsMap = new SetValueMap<>();
        for (SyncDataContextEvent.WriteResult writeResult : successDetailResults) {
            SimpleSyncData simpleSyncData = writeResult.getSimpleSyncData();
            objMappingSrcIdsMap.putValue(ObjectMappingVo.of(simpleSyncData.getSourceObjectApiName(), simpleSyncData.getDestObjectApiName()), simpleSyncData.getSourceDataId());
        }
        //查询后，保留不存在或未创建成功的srcId，即移除创建成功了的srcId
        for (ObjectMappingVo objectMappingVo : objMappingSrcIdsMap.keySet()) {
            Set<String> srcIds = objMappingSrcIdsMap.get(objectMappingVo);
            List<MappingCreatedData> srcMappings = syncDataMappingsDao
                    .listCreatedBySource(tenantId,
                            objectMappingVo.getSourceObjectApiName(),
                            objectMappingVo.getDestObjectApiName(),
                            srcIds);
            //移除创建成功了的srcId
            srcMappings.stream().filter(v -> v.getIsCreated())
                    .forEach(v -> srcIds.remove(v.getSourceDataId()));
        }
        //再次循环，构建更新mapping参数
        for (SyncDataContextEvent.WriteResult writeResult : successDetailResults) {
            SimpleSyncData simpleSyncData = writeResult.getSimpleSyncData();
            String destDataName = simpleSyncData.getDestDataName();
            String remark = writeResult.getErrMsg();
            Set<String> notCreatedSrcIds = objMappingSrcIdsMap.get(ObjectMappingVo.of(simpleSyncData.getSourceObjectApiName(), simpleSyncData.getDestObjectApiName()));
            if (notCreatedSrcIds.contains(simpleSyncData.getSourceDataId())) {
                //当原来的数据是未创建成功的，更新destDataId
                UpdateMapping.BySourceArg arg = UpdateMapping.BySourceArg.builder()
                        .syncDataId(writeResult.getSyncDataId())
                        .sourceObjectApiName(simpleSyncData.getSourceObjectApiName())
                        .destObjectApiName(simpleSyncData.getDestObjectApiName())
                        .sourceDataId(simpleSyncData.getSourceDataId())
                        .destDataId(writeResult.getDestDataId())
                        .lastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus())
                        .destDataName(destDataName)
                        .remark(i18NStringManager.getByEi(I18NStringEnum.s6, tenantId)).build();
                bySourceArgs.add(arg);
            } else {
                //仅更新状态和destDataName，不更新destDataId
                UpdateMapping.BySyncDataIdArg arg = UpdateMapping.BySyncDataIdArg.builder()
                        .syncDataId(writeResult.getSyncDataId())
                        .lastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus())
                        .destDataName(destDataName)
                        .remark(i18NStringManager.getByEi(I18NStringEnum.s6, tenantId))
                        .build();
                bySyncDataIdArgs.add(arg);
            }
        }
    }


    /**
     * 根据source信息批量更新mapping表数据
     *
     * @param tenantId
     * @param bySourceArgs
     */
    private void bulkUpdateBySource(String tenantId, List<UpdateMapping.BySourceArg> bySourceArgs) {
        if (bySourceArgs.isEmpty()) {
            return;
        }
        try {
            //区分destDataId为null和不为null的情况
            List<UpdateMapping.BySourceArg> noDestDataNameArgs = bySourceArgs.stream()
                    .filter(v -> v.getDestDataId() == null)
                    .collect(Collectors.toList());
            //pg限制,一条语句的参数数量不能超过32767,所以拆分为每1000个保存一次
            if (!noDestDataNameArgs.isEmpty()) {
                ListUtils.partition(noDestDataNameArgs, 1000).forEach(list -> syncDataMappingsDao.bulkUpdateDestBySourceArgs(tenantId, false, list));
            }
            List<UpdateMapping.BySourceArg> updateDestDataNameArgs = bySourceArgs.stream().filter(v -> v.getDestDataId() != null).collect(Collectors.toList());
            if (!updateDestDataNameArgs.isEmpty()) {
                ListUtils.partition(updateDestDataNameArgs, 1000).forEach(list -> syncDataMappingsDao.bulkUpdateDestBySourceArgs(tenantId, true, list));
            }
        } catch (Exception exception) {
            log.warn("Exception bulkUpdateDestBySourceArgs,args={}:{}", JSONObject.toJSONString(bySourceArgs), exception);
            for (UpdateMapping.BySourceArg bySyncDataIdArg : bySourceArgs) {
                syncDataManager.updateToError(tenantId, bySyncDataIdArg.getSyncDataId(),
                        SyncDataStatusEnum.WRITE_FAILED.getStatus(),
                        i18NStringManager.getByEi(I18NStringEnum.s964, tenantId) + bySyncDataIdArg.getDestDataId(), I18NStringEnum.s964.name());
            }
            // 区分是pg的超时链接，标记不需要聚合框架重试。记录中间表映射。
            if(ErrorUtils.judgeException(exception)){
                LogIdUtil.setNeedRetry(false);
                asyncReTryIfFailedManager.retryUpsertMappingBySource(tenantId,bySourceArgs);
                log.info("handler exception size:{}",bySourceArgs.size());
            }
        }
    }

    /**
     * 更具syncDataId批量更新mapping表数据
     *
     * @param tenantId
     * @param bySyncDataIdArgs
     */
    public void bulkUpdateBySyncDataId(String tenantId, List<UpdateMapping.BySyncDataIdArg> bySyncDataIdArgs) {
        if (bySyncDataIdArgs.isEmpty()) {
            return;
        }
        try {
            //区分destDataName为null和不为null的情况
            List<UpdateMapping.BySyncDataIdArg> noDestDataNameArgs = bySyncDataIdArgs.stream().filter(v -> v.getDestDataName() == null).collect(Collectors.toList());
            if (!noDestDataNameArgs.isEmpty()) {
                syncDataMappingsDao.bulkUpdateBySyncDataId(tenantId, false, noDestDataNameArgs);
            }
            List<UpdateMapping.BySyncDataIdArg> updateDestDataNameArgs = bySyncDataIdArgs.stream().filter(v -> v.getDestDataName() != null).collect(Collectors.toList());
            if (!updateDestDataNameArgs.isEmpty()) {
                syncDataMappingsDao.bulkUpdateBySyncDataId(tenantId, true, updateDestDataNameArgs);
            }
        } catch (Exception exception) {
            log.warn("Exception bulkUpdateBySyncDataId,args=:{}:{}" , JSONObject.toJSONString(bySyncDataIdArgs), exception);
            for (UpdateMapping.BySyncDataIdArg bySyncDataIdArg : bySyncDataIdArgs) {
                syncDataManager.updateToError(tenantId, bySyncDataIdArg.getSyncDataId(),
                        SyncDataStatusEnum.WRITE_FAILED.getStatus(),
                        i18NStringManager.getByEi(I18NStringEnum.s963, tenantId), I18NStringEnum.s963.name());
            }
            // 区分是pg的超时链接，标记不需要聚合框架重试。记录中间表映射。
            if(ErrorUtils.judgeException(exception)){
                LogIdUtil.setNeedRetry(false);
                asyncReTryIfFailedManager.retryUpsertMappingBySyncData(tenantId,bySyncDataIdArgs);
                log.info("handler exception size:{}",bySyncDataIdArgs.size());
            }
        }
    }

    /**
     * 更新syncData
     *
     * @param tenantId
     * @param writeResult
     */
    private void updateSyncDataStatus(String tenantId, Integer destEventType, boolean isMain, SyncDataContextEvent.WriteResult writeResult) {

        if (writeResult != null) {
            syncDataManager.updateNodeMsgBySyncDataId(tenantId,writeResult.getSyncDataId(), PloyDetailNodeEnum.WRITE,writeResult.getErrMsg(),null);
            if (writeResult.isSuccess()) {
                if (destEventType == EventTypeEnum.ADD.getType()) {
                    syncDataFixDao.updateStatusAndDestDataIdBySuccess(tenantId,
                            writeResult.getSyncDataId(),
                            writeResult.getDestDataId(),
                            SyncDataStatusEnum.WRITE_SUCCESS.getStatus(),
                            writeResult.getErrMsg()
                    );//上层已经做了多语
                } else {
                    //原来明细更新时还需要判断原来是否已新建成功，这里不判断了。
                    syncDataFixDao.updateStatus(tenantId,
                            writeResult.getSyncDataId(),
                            SyncDataStatusEnum.WRITE_SUCCESS.getStatus(),
                            writeResult.getErrMsg(),
                            null);
                }
            } else {
                syncDataFixDao.updateStatus(tenantId, writeResult.getSyncDataId(), SyncDataStatusEnum.WRITE_FAILED.getStatus(), writeResult.getErrMsg(), String.valueOf(writeResult.getErrCode()));
                if (isMain) {
                    //改为只有主才调用
                    ployBreakManager.incrFailedSyncDataNum(tenantId, writeResult.getSyncDataId());
                }
            }
        }

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName(writeResult.getSimpleSyncData().getDestObjectApiName()).build();
            if(writeResult != null && writeResult.isSuccess()) {
                dumpLog.setErrType(0);
            }else {
                if(writeResult.getErrCode()==OUT_ERROR_CODE) {
                    dumpLog.setErrType(1);
                } else {
                    dumpLog.setErrType(-1);
                }
                dumpLog.setSyncErrMsg(writeResult.getErrMsg());
            }
            if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        }catch (Exception e){}
    }


}
