package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity.Fields;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 * 数据集成通知dao
 * <AUTHOR>
 * @date 2023.08.07
 */
@Slf4j
@Repository
public class DataIntegrationNotificationDao extends BaseMongoStore<DataIntegrationNotificationEntity> {
    @Qualifier("erpSyncDataLogMongoStore")
    @Autowired
    private DatastoreExt store;
    private String DATABASE;

    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    protected DataIntegrationNotificationDao() {
        super(CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build())));
    }

    @Override
    public MongoDatabase getDatabase(String tenantId) {
        return store.getMongo().getDatabase(DATABASE);
    }

    @Override
    public String getCollName(String tenantId) {
        return "data_integration_notification";
    }

    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> indexModels = new ArrayList<>();
        indexModels.add(new IndexModel(Indexes.ascending(Fields.tenantId,
                Fields.dataCenterId,
                Fields.ployDetailIdList,
                Fields.notificationType,
                Fields.time),
                new IndexOptions().name("idx_tenant_id_dc_ploy_1").background(true)));
        indexModels.add(new IndexModel(Indexes.ascending(Fields.tenantId,
                Fields.dataCenterId,
                Fields.ployDetailIdList,
                Fields.alarmType,
                Fields.alarmLevel,
                Fields.time),
                new IndexOptions().name("idx_tenant_id_dc_ploy_2").background(true)));

        indexModels.add(new IndexModel(Indexes.ascending(Fields.createTime),
                new IndexOptions().expireAfter(90L, TimeUnit.DAYS).background(true)));

        return indexModels;
    }

    private DataIntegrationNotificationEntity buildData(String tenantId,
                                                        String dataCenterId,
                                                        List<String> ployDetailIdList,
                                                        AlarmRuleType alarmRuleType,
                                                        String alarmRuleName,
                                                        AlarmType alarmType,
                                                        AlarmLevel alarmLevel,
                                                        String msg,
                                                        List<Integer> userIdList,
                                                        List<String> notifyType) {
        NotificationType notificationType = null;
        switch (alarmType) {
            case POLLING_ERP_API_EXCEPTION:
            case SYNC_EXCEPTION:
                notificationType = NotificationType.ALERT;
                break;
            case INTEGRATION_STREAM_BREAK:
            case GET_BY_ID_API_BREAK:
                notificationType = NotificationType.BREAK;
                break;
            case SUPER_ADMIN:
                notificationType = NotificationType.SUPER_ADMIN;
                break;
            default:
                notificationType = NotificationType.OTHER;
                break;
        }
        DataIntegrationNotificationEntity entity = DataIntegrationNotificationEntity.builder()
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .ployDetailIdList(ployDetailIdList)
                .notificationType(notificationType)
                .alarmRuleType(alarmRuleType)
                .alarmRuleName(alarmRuleName)
                .alarmType(alarmType)
                .alarmLevel(alarmLevel)
                .time(new Date().getTime())
                .msg(msg)
                .recover(false)
                .userIdList(userIdList)
                .notifyType(notifyType)
                .traceId(TraceUtil.get())
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        return entity;
    }

    public void insert(String tenantId,
                       String dataCenterId,
                       List<String> ployDetailIdList,
                       AlarmRuleType alarmRuleType,
                       String alarmRuleName,
                       AlarmType alarmType,
                       AlarmLevel alarmLevel,
                       String msg,
                       List<Integer> userIdList,
                       List<String> notifyType) {
        DataIntegrationNotificationEntity entity = buildData(tenantId,
                dataCenterId,
                ployDetailIdList,
                alarmRuleType,
                alarmRuleName,
                alarmType,
                alarmLevel,
                msg,
                userIdList,
                notifyType);
        insert(entity);
    }
    @DataMonitorScreen(tenantId = "#entity.tenantId", dataCenterId = "#entity.dataCenterId",ployDetailId = "#entity.objAPIName",skipSend="#entity?.ployDetailIdList!=null?'false':'true'",operationType = CommonConstant.ALERT_INTEGRATION_STREAM)
    private void insert(DataIntegrationNotificationEntity entity) {
        try {
            MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(entity.getTenantId());
            collection.insertOne(entity);
        } catch (Exception e) {
            log.warn("DataIntegrationNotificationDao.insertIgnore,exception={}", e.getMessage());
        }
    }

    public DataIntegrationNotificationModel getDataListByPage(String tenantId,
                                                              String dataCenterId,
                                                              List<String> ployDetailIdList,
                                                              Integer userId,
                                                              NotificationType notificationType,
                                                              AlarmType alarmType,
                                                              AlarmLevel alarmLevel,
                                                              Date startTime,
                                                              Date endTime,
                                                              int pageSize,
                                                              int page) {
        List<Bson> filterList = new ArrayList<>();

        filterList.add(Filters.eq(Fields.tenantId, tenantId));

        filterList.add(Filters.ne(Fields.dataCenterId, "-"));
        if(StringUtils.isNotEmpty(dataCenterId)) {
            filterList.add(Filters.eq(Fields.dataCenterId, dataCenterId));
        }

        filterList.add(Filters.ne(Fields.ployDetailIdList, "-"));
        if(CollectionUtils.isNotEmpty(ployDetailIdList)) {
            filterList.add(Filters.in(Fields.ployDetailIdList, ployDetailIdList));
        }

        if(notificationType!=null) {
            filterList.add(Filters.eq(Fields.notificationType, notificationType.name()));
        }

        if(alarmType!=null) {
            filterList.add(Filters.eq(Fields.alarmType, alarmType.name()));
        }

        if(alarmLevel!=null) {
            filterList.add(Filters.eq(Fields.alarmLevel, alarmLevel.name()));
        }

        if (startTime != null && endTime != null) {
            filterList.add(Filters.gte(Fields.time, startTime.getTime()));
            filterList.add(Filters.lt(Fields.time, endTime.getTime()));
        }

        if(userId!=null) {
            filterList.add(Filters.in(Fields.userIdList, userId));
        }

        Bson filters = and(filterList);
        DataIntegrationNotificationModel dataList = getDataList(tenantId, filters, pageSize, page);
        return dataList;
    }

    private DataIntegrationNotificationModel getDataList(String tenantId,
                                                         Bson filters,
                                                         int pageSize,
                                                         int page) {
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);

        //最多count 10W条数据，最大计算时间为10s
        long total = collection.countDocuments(filters, new CountOptions().maxTime(10, TimeUnit.SECONDS).limit(10 * 10000));
        log.info("DataIntegrationNotificationDao.getDataList,tenantId={},total={},filters={}", tenantId, total,filters);

        List<DataIntegrationNotificationEntity> dataList = new ArrayList<>();
        collection.find(filters).sort(Sorts.orderBy(Sorts.descending(Fields.time))).skip(page * pageSize).limit(pageSize).into(dataList);

        return new DataIntegrationNotificationModel(total, dataList, null);
    }

    public List<DataIntegrationNotificationEntity> pageByDcId(String tenantId, String dataCenterId, ObjectId maxId, int pageSize) {
        final Bson filter = and(Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dataCenterId),
                Filters.gt(Fields.id, maxId));
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);
        return collection.find(filter).sort(Sorts.orderBy(Sorts.ascending(Fields.id))).limit(pageSize).into(new ArrayList<>());

    }

    public void deleteByDataCenterId(String tenantId, String dcId) {
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);
        final Bson filter = and(Filters.eq(Fields.tenantId, tenantId), Filters.eq(Fields.dataCenterId, dcId));
        collection.deleteMany(filter);
    }

    /**
     * 每个集成流只返回一条记录，多条记录会返回最新的
     */
    public List<DataIntegrationNotificationEntity> distinct(String tenantId,
                                                            String dataCenterId,
                                                            List<String> ployDetailIdList) {
        List<Bson> filterList = new ArrayList<>();

        filterList.add(Filters.eq(Fields.tenantId, tenantId));

        if (StringUtils.isNotEmpty(dataCenterId)) {
            filterList.add(Filters.eq(Fields.dataCenterId, dataCenterId));
        }

        if (CollectionUtils.isNotEmpty(ployDetailIdList)) {
            filterList.add(Filters.in(Fields.ployDetailIdList, ployDetailIdList));
        }

        filterList.add(Filters.eq(Fields.recover, false));

        Bson filters = and(filterList);
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);
        List<DataIntegrationNotificationEntity> dataList = new ArrayList<>();
        collection.find(filters).into(dataList);
        //根据集成流去重，保留最新的
        Map<String, DataIntegrationNotificationEntity> map = new LinkedHashMap<>();
        dataList.sort(Comparator.comparingLong(v -> v.getTime() == null ? 0 : v.getTime() * -1));
        for (DataIntegrationNotificationEntity entity : dataList) {
            if (CollectionUtils.isEmpty(entity.getPloyDetailIdList())) continue;
            String ployDetailId = entity.getPloyDetailIdList().get(0);
            if (!map.containsKey(ployDetailId)) {
                map.put(ployDetailId, entity);
            }
        }
        return map.values().stream().collect(Collectors.toList());
    }

    public List<String> getDataListByDcId(String tenantId) {
        List<Bson> filterList = new ArrayList<>();

        filterList.add(Filters.eq(Fields.tenantId, tenantId));
        filterList.add(Filters.ne(Fields.dataCenterId, "-"));

        Bson filters = and(filterList);

        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection("");

        Set<String> set = new LinkedHashSet<>();
        int offset = 0;
        int limit = 100;
        while (true) {
            List<DataIntegrationNotificationEntity> dataList = new ArrayList<>();
            collection.find(filters).skip(offset).limit(limit).into(dataList);
            if(CollectionUtils.isEmpty(dataList)) break;
            for(DataIntegrationNotificationEntity entity : dataList) {
                if(!set.contains(entity.getDataCenterId())) {
                    set.add(entity.getDataCenterId());
                }
            }
            if(dataList.size()==limit) {
                offset += limit;
            } else {
                break;
            }
        }
        return new ArrayList<>(set);
    }

    public long deleteMany(String tenantId) {
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);
        Bson filters = and(
                Filters.ne(Fields.tenantId, tenantId));

        DeleteResult result = collection.deleteMany(filters);
        log.info("DataIntegrationNotificationDao.deleteMany,tenantId={},result={}", tenantId,
                result);
        return result.getDeletedCount();
    }

    public long deleteByPloyDetailId(String tenantId, String ployDetailId) {
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);
        Bson filters = and(Filters.eq(Fields.tenantId, tenantId), Filters.in(Fields.ployDetailIdList, Lists.newArrayList(ployDetailId)));

        DeleteResult result = collection.deleteMany(filters);
        log.info("DataIntegrationNotificationDao.deleteByPloyDetailId,tenantId={},ployDetailId={},result={}", tenantId, ployDetailId, result);
        return result.getDeletedCount();
    }

    public long updateAlarmStatus(String tenantId, String ployDetailId, AlarmType alarmType, boolean recover) {
        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);
        Bson filters = and(Filters.eq(Fields.tenantId, tenantId),
                Filters.in(Fields.ployDetailIdList, Lists.newArrayList(ployDetailId)),
                Filters.eq(Fields.alarmType, alarmType.name()));

        List<Bson> updates = new ArrayList<>();
        updates.add(set(Fields.recover, recover));

        Bson update = combine(updates);

        UpdateResult result = collection.updateMany(filters, update, new UpdateOptions().upsert(false));
        log.info("DataIntegrationNotificationDao.updateAlarmStatus,tenantId={},ployDetailId={},result={}", tenantId, ployDetailId, result);
        return result.getModifiedCount();
    }

    public boolean hasData(String tenantId, Integer userId) {
        List<Bson> filterList = new ArrayList<>();

        filterList.add(Filters.eq(Fields.tenantId, tenantId));
        if(userId!=null) {
            filterList.add(Filters.in(Fields.userIdList, userId));
        }

        Bson filters = and(filterList);

        MongoCollection<DataIntegrationNotificationEntity> collection = getOrCreateCollection(tenantId);

        //取一条满足查询条件的数据即可，不用精确计算
        long total = collection.countDocuments(filters,new CountOptions().maxTime(5,TimeUnit.SECONDS).limit(1));
        return total > 0;
    }
}
