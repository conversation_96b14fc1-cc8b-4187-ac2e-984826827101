package com.fxiaoke.open.erpsyncdata.writer.manager


import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailObjectMappingsData
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService
import spock.lang.Specification
import spock.lang.Unroll

import static com.fxiaoke.open.erpsyncdata.common.constant.FieldType.OBJECT_REFERENCE
import static com.fxiaoke.open.erpsyncdata.common.constant.FieldType.TEXT
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmObjectApiName.ACCOUNT_API_NAME

class DoWrite2CrmManagerTest extends Specification {
    ObjectData needReturnData = null;
    DoWrite2CrmManager monitor = new DoWrite2CrmManager(
            syncDataManager: Mock(SyncDataManager) {
                updateNeedReturnData((String) _, (String) _, (ObjectData) _) >> { args -> needReturnData = args[2] }
            }
    )


    def "DoDetailNeedReturnData2SyncData"() {

    }

    @Unroll
    def "#name-是否需填充外部负责人"() {
        setup:
        def objApiName = "TestName"
        DoWrite2CrmManager manager = new DoWrite2CrmManager(
                syncPloyDetailSnapshotService: Mock(SyncPloyDetailSnapshotService) {
                    def syncPloyDetailData
                    if (master) {
                        syncPloyDetailData = [destObjectApiName: objApiName, fieldMappings: fields] as SyncPloyDetailData2
                    } else {
                        syncPloyDetailData = [destObjectApiName: "master1", detailObjectMappings: [new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName: "detail1"), new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName: "detail2"), new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName: "detail3"), new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName: objApiName, fieldMappings: fields)]] as SyncPloyDetailData2
                    }
                    getSyncPloyDetailSnapshotBySnapshotId(*_) >> Result2.newSuccess([syncPloyDetailData: syncPloyDetailData] as SyncPloyDetailSnapshotData2)
                },
                sfaApiManager: Mock(SfaApiManager) {
                    haveOutDataPrivilege(*_) >> outDataPrivilege
                }
        )


        when:
        def fill = manager.haveOutDataPrivilegeField("1", 1, objApiName, "123", object as com.fxiaoke.crmrestapi.common.data.ObjectData)


        then:
        fill == result

        where:
        name                 || master | fields                                                                                                       | object          | outDataPrivilege || result
        "没有字段映射"       || true   | []                                                                                                           | [:]             | true              | false

        "主对象没有字段映射" || true   | [new FieldMappingData(destType: OBJECT_REFERENCE), new FieldMappingData(destType: TEXT)] | [:] | true | false
        "从对象没有字段映射" || false  | [new FieldMappingData(destType: OBJECT_REFERENCE), new FieldMappingData(destType: TEXT)]                     | [:]             | true              | false

        "主对象字段没值"     || true   | [new FieldMappingData(destType: OBJECT_REFERENCE, destTargetApiName: ACCOUNT_API_NAME, destApiName: "test")] | ["asd": "asd"]  | true              | false
        "从对象字段没值"     || false  | [new FieldMappingData(destType: OBJECT_REFERENCE, destTargetApiName: ACCOUNT_API_NAME, destApiName: "test")] | ["asd": "asd"]  | true              | false

        "主对象没有外部权限" || true   | [new FieldMappingData(destType: OBJECT_REFERENCE, destTargetApiName: ACCOUNT_API_NAME, destApiName: "test")] | ["test": "asd"] | false             | false
        "从对象没有外部权限" || false  | [new FieldMappingData(destType: OBJECT_REFERENCE, destTargetApiName: ACCOUNT_API_NAME, destApiName: "test")] | ["test": "asd"] | false             | false


        "主对象有外部权限"   || true   | [new FieldMappingData(destType: OBJECT_REFERENCE, destTargetApiName: ACCOUNT_API_NAME, destApiName: "test")] | ["test": "asd"] | true              | true
        "从对象有外部权限"   || false  | [new FieldMappingData(destType: OBJECT_REFERENCE, destTargetApiName: ACCOUNT_API_NAME, destApiName: "test")] | ["test": "asd"] | true              | true

    }

//    def "DoMasterNeedReturnData2SyncData"() {
//        com.fxiaoke.crmrestapi.common.data.ObjectData objectData = JacksonUtil.fromJson(json, com.fxiaoke.crmrestapi.common.data.ObjectData.class);
//        List<String> needReturnFieldApiNames = JacksonUtil.fromJson(field, List.class);
//        SyncPloyDetailData2 syncPloyDetailData = new SyncPloyDetailData2()
//        syncPloyDetailData.setIntegrationStreamNodes(new IntegrationStreamNodesData())
//        syncPloyDetailData.getIntegrationStreamNodes().setObjApiName2NeedReturnFieldApiName(Maps.newHashMap())
//        syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName().put(objApiName, needReturnFieldApiNames)
//        when:
//        monitor.doMasterNeedReturnData2SyncData("", "", objectData, syncPloyDetailData)
//
//        then:
//        if (needReturnIsNull) {
//            needReturnData==null
//        } else {
//            for (String str : needReturnFieldApiNames) {
//                println needReturnData.get(str)
//                (needReturnData.get(str).equals(objectData.get(str))) || (needReturnData.get(str) == null && objectData.get(str) == null)
//            }
//        }
//
//        where:
//        needReturnIsNull | objApiName | json                                                                                              | field
//        false            | "obj1"     | "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"object_describe_api_name\":\"obj1\"}" | "[\"tenant_id\",\"modifiedTime\"]"
//        false            | "obj1"     | "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"object_describe_api_name\":\"obj1\"}" | "[\"tenant_id\"]"
//        true             | "obj2"     | "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"object_describe_api_name\":\"obj1\"}" | "[\"tenant_id\",\"modifiedTime\",\"field_hZkQe__c\"]"
//        true             | "obj2"     | "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"object_describe_api_name\":\"obj1\"}" | "[\"tenant_id\",\"modifiedTime\",\"field_hZkQe__c\",\"adfasaf\"]"
//    }
//
//    def "SetNeedReturnData2SyncData"() {
//        com.fxiaoke.crmrestapi.common.data.ObjectData objectData = JacksonUtil.fromJson(json, com.fxiaoke.crmrestapi.common.data.ObjectData.class);
//        List<String> needReturnFieldApiNames = JacksonUtil.fromJson(field, List.class);
//        when:
//        monitor.setNeedReturnData2SyncData("", "", objectData, needReturnFieldApiNames)
//
//        then:
//        for (String str : needReturnFieldApiNames) {
//            println needReturnData.get(str)
//            (needReturnData.get(str).equals(objectData.get(str))) || (needReturnData.get(str) == null && objectData.get(str) == null)
//        }
//        where:
//        json                                                                                   | field
//        "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"field_hZkQe__c\":\"yes\"}" | "[\"tenant_id\",\"modifiedTime\"]"
//        "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"field_hZkQe__c\":\"yes\"}" | "[\"tenant_id\"]"
//        "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"field_hZkQe__c\":\"yes\"}" | "[\"tenant_id\",\"modifiedTime\",\"field_hZkQe__c\"]"
//        "{\"tenant_id\":\"626246\",\"modifiedTime\":1697438078901,\"field_hZkQe__c\":\"yes\"}" | "[\"tenant_id\",\"modifiedTime\",\"field_hZkQe__c\",\"adfasaf\"]"
//    }
}
