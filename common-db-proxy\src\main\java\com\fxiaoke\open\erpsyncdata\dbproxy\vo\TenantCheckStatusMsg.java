package com.fxiaoke.open.erpsyncdata.dbproxy.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 17:43 2023/3/16
 * @Desc:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantCheckStatusMsg implements Serializable {
    private String tenantId; // 租户ei信息
    private long createTime; // 日志上报时间
    private String checkUrl; //检查地址
    private String msg; //返回信息
    private Boolean isSuccess ; //是否成功
}
