package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.dbproxy.DBProxyV2Handler;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 */
@Service("dbProxyExecutor")
public class DbProxyExecutorService implements CustomFunctionCommonService {
    @Autowired
    private DBProxyV2Handler dbProxyV2Handler;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Override
    public Result<Dict> executeLogic(CustomFunctionCommonArg arg) {
        String tenantId = arg.getTenantId();
        String params = arg.getParams();
        Dict dict = JacksonUtil.fromJson(params, Dict.class);
        String dcId = dict.getStr("dcId");
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        Result<Dict> execute = dbProxyV2Handler.execute(connectInfoEntity, params);
        return execute;
    }
}
