{"type": "page", "title": "对象自定义函数使用情况", "name": "findObjFuncPage", "toolbar": [], "body": [{"type": "crud", "name": "findObjFuncUsage", "initFetch": false, "api": {"method": "post", "url": "../integrationstream/findObjFuncUsagePloy", "data": {"tenantIds": "${IF(${tenantIds} == nil || ${tenantIds} == '', [], ${tenantIds|split:,})}", "objApiName": "${objApiName}", "detail": "${detail}", "status": "${status}", "type": "${type}"}}, "filter": {"type": "group", "columnCount": 3, "mode": "horizontal", "body": [{"type": "radios", "name": "type", "label": "对象类型", "selectFirst": true, "options": [{"label": "CRM", "value": 1}, {"label": "ERP", "value": 2}]}, {"type": "radios", "name": "detail", "optionType": "button", "label": "主从", "selectFirst": true, "options": [{"label": "主对象", "value": false}, {"label": "从对象", "value": true}]}, {"type": "radios", "name": "status", "label": "状态", "selectFirst": true, "options": [{"label": "所有", "value": 0}, {"label": "启用", "value": 1}, {"label": "停用", "value": 2}]}, {"type": "input-text", "clearable": true, "trimContents": true, "name": "objApiName", "label": "对象ApiName"}, {"type": "input-text", "name": "tenantIds", "clearable": true, "label": "企业列表", "placeholder": "查询的企业列表,不填为所有企业 分隔符为,", "trimContents": true}]}, "defaultParams": {"perPage": 100}, "columns": [{"name": "ployTenantId", "label": "企业id"}, {"name": "id", "label": "集成流id"}, {"name": "ployName", "label": "集成流名称"}, {"name": "sourceObjectApiName", "label": "源对象"}, {"name": "destObjectApiName", "label": "目标对象"}, {"name": "status", "type": "status", "label": "是否启用", "sortable": "${status} == 1 ? true : false"}, {"name": "beforeFuncApiName", "label": "同步前函数", "onEvent": {"click": {"actions": [{"expression": "${beforeFuncApiName != null && beforeFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${beforeFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}, {"name": "duringFuncApiName", "label": "同步中函数", "onEvent": {"click": {"actions": [{"expression": "${duringFuncApiName != null && duringFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${duringFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}, {"name": "afterFuncApiName", "label": "同步后函数", "onEvent": {"click": {"actions": [{"expression": "${afterFuncApiName != null && afterFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${afterFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "combineNum": 0}]}