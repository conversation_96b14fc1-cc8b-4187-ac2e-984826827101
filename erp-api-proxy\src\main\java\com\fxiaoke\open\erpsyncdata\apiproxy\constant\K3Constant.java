package com.fxiaoke.open.erpsyncdata.apiproxy.constant;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/15
 */
public interface K3Constant {
    /**
     * 仓库和仓位分隔符
     * 仓位仓库的id使用[仓库编码||仓位1编码.仓位2编码...]
     * 不允许修改
     */
    String WAREHOUSE_LOC_SEPARATOR = "||";

    /**
     * 仓位编码分割符
     * 不允许修改
     */
     String LOC_SEPARATOR = ".";

    /**
     * 辅助属性分割符
     * 不允许修改
     */
    String AUX_PROPERTY_SEPARATOR = ".";

    /**
     * id分隔符
     * 不允许修改
     */
    String ID_SEPARATOR = "#";

    String SON = "套件子项";   // ignoreI18n  可能有二开K3c,先不改

    String SESSION_INFO_HAS_BEEN_LOST = "会话信息已丢失";   // ignoreI18n
}
