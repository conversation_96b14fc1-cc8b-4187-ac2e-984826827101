package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAConnectInfoEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 9:58 2021/2/22
 * @Desc:
 */
@Repository
public interface OAConnectInfoDao extends ErpBaseDao<OAConnectInfoEntity>, ITenant<OAConnectInfoDao> {

    /**
     * 获取企业连接信息
     *
     * @param tenantId
     * @return
     */
    OAConnectInfoEntity getByTenantId(@Param("tenantId")String tenantId);


    /**
     * 根据事件类型新增，编辑，删除返回对应的状态api
     *
     * @param tenantId
     * @return
     */
    OAConnectInfoEntity getByTenantIdSingleEvent(@Param("tenantId")String tenantId);


    /**
     * 获取企业连接信息
     *
     * @param tenantId
     * @return
     */
    @Cached(expire = 30,cacheType = CacheType.LOCAL)
    OAConnectInfoEntity getByTenantIdWithCache(@Param("tenantId")String tenantId);

    /**
     * 获取所有企业ei
     *
     * @return
     */
    List<String> listTenantId();

}