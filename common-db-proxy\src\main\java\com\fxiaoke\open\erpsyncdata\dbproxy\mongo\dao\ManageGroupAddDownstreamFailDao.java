package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ManageGroupAddDownstreamFailEntity;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13 11:58:19
 */
@Repository
public class ManageGroupAddDownstreamFailDao extends BaseDao<ManageGroupAddDownstreamFailEntity> {
    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }

    public List<ManageGroupAddDownstreamFailEntity> findAllByTaskId(String taskId, String queryStr, Integer offset, Integer limit) {
        final Query<ManageGroupAddDownstreamFailEntity> query = createQuery(ImmutableMap.of("task_id", taskId));
        if (StringUtils.isNotBlank(queryStr)) {
            query.field("downstream_name").contains(queryStr);
        }
        return query
                .offset(offset).limit(limit)
                .order("-create_time")
                .asList();
    }

    public int countByTaskId(String taskId, String queryStr) {
        final Query<ManageGroupAddDownstreamFailEntity> query = createQuery(ImmutableMap.of("task_id", taskId));
        if (StringUtils.isNotBlank(queryStr)) {
            query.field("downstream_name").contains(queryStr);
        }
        return Math.toIntExact(count(query));
    }
}
