package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OASyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.OaSyncLogDaoArg;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/9
 * @Desc:
 */
@Repository
public interface OASyncLogDao extends ErpBaseDao<OASyncLogEntity> , ITenant<OASyncLogDao> {


    List<OASyncLogEntity> queryOALog(@Param("tenantId") String tenantId, @Param("objApiName") String objApiName, @Param("id") String id,
                                     @Param("status") String status, @Param("dataId") String dataId, @Param("eventType") String eventType, @Param(
            "receiverId") String receiverId);

    List<OASyncLogEntity> queryOALogGroup(@Param("oaSyncLogDaoArg") OaSyncLogDaoArg oaSyncLogDaoArg, @Param("limit") Integer pageSize, @Param("offset") int offset);

    int countOALogGroup(@Param("oaSyncLogDaoArg") OaSyncLogDaoArg oaSyncLogDaoArg);

    List<String> queryByTypeApi(@Param("tenantId") String tenantId, @Param("eventType") String eventType, @Param("objApiName") String objApiName,
                                @Param("time") Long time);

    int getLogExceprionNum(@Param("tenantId") String tenantId, @Param("dataId") String dataId, @Param("eventType") String eventType);
}