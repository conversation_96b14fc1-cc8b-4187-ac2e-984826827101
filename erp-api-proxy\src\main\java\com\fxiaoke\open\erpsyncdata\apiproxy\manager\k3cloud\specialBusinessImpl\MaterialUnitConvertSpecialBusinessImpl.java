package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * k3的物料单位换算特殊逻辑。
 *
 * <AUTHOR>
 * @Date: 09:20 2021/3/11
 * @Desc:
 */
@Slf4j
@Component("BD_MATERIALUNITCONVERT")
public class MaterialUnitConvertSpecialBusinessImpl implements SpecialBusiness {

    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        if(standardData.getMasterFieldVal().get("FConvertDenominator")!=null&&standardData.getMasterFieldVal().get("FConvertNumerator")!=null){
            log.info("FConvertDenominator={},FConvertNumerator={}",standardData.getMasterFieldVal().get("FConvertDenominator"),standardData.getMasterFieldVal().get("FConvertNumerator"));
            Double convertDenominator=(Double)standardData.getMasterFieldVal().get("FConvertDenominator");
            Double convertNumerator=(Double)standardData.getMasterFieldVal().get("FConvertNumerator");//分子
            standardData.getMasterFieldVal().put("CrmConvertNumber",convertNumerator/convertDenominator);
        }
    }

}
