package com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MonitorLogModule;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RetryDataEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UpdateMapping;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RetrySendMqDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class AsyncReTryIfFailedManager {
    @Autowired
    @Lazy
    public AllModelDubboService allModelDubboService;
    @Autowired
    public RetrySendMqDao retrySendMqDao;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    public TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ApplicationContext applicationContext;

    public Result2<Void> reTryBatchSendEventData2DispatcherMqByContext(List<SyncDataContextEvent> syncDataContextEvents, String resultMsg) {
        String tenantId = syncDataContextEvents.get(0).getSourceData().getTenantId();
        ReTrySendMq reTrySendMq = new ReTrySendMq();
        reTrySendMq.setTenantId(tenantId);
        reTrySendMq.setMqMsg(JacksonUtil.toJson(syncDataContextEvents));
        reTrySendMq.setResultMsg(resultMsg);
        reTrySendMq.setStatus(0);
        reTrySendMq.setRetryDataEnum(RetryDataEnum.PAAS_DATA_RETRY_MQ.name());
        reTrySendMq.setCreateTime(new Date());
        reTrySendMq.setUpdateTime(reTrySendMq.getCreateTime());
        //先入库是为了防止没入库就异常了。
        String mongoId = retrySendMqDao.createReTrySendMq(reTrySendMq);
        ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder = MonitorBizLogUtil.builder(MonitorLogModule.send_mqdata_fail_log, RetryDataEnum.PAAS_DATA_RETRY_MQ.getDataType(), tenantId);
        builder.label1(RetryDataEnum.PAAS_DATA_RETRY_MQ.getDesc());
        builder.num1(syncDataContextEvents.size());
        // 上报biz_log
        MonitorBizLogUtil.send(builder.build());
        return Result2.newSuccess();
    }


    public Result2<Integer> retryOriginMQ(List<Object> messages){
        if(CollectionUtils.isNotEmpty(messages)){
            List<ReTrySendMq> sendMqs=Lists.newArrayList();
            for (Object message : messages) {
                ReTrySendMq reTrySendMq=new ReTrySendMq();
                reTrySendMq.setRetryDataEnum(RetryDataEnum.PAAS_ORIGIN_DATA_RETRY_MQ.name());
                reTrySendMq.setStatus(0);
                reTrySendMq.setCrm2erp(true);
                reTrySendMq.setMqMsg(JSONObject.toJSONString(message));
                reTrySendMq.setCreateTime(new Date());
                reTrySendMq.setUpdateTime(reTrySendMq.getCreateTime());
                sendMqs.add(reTrySendMq);
            }
            retrySendMqDao.batchUpsertRetryData(sendMqs);
        }
        return Result2.newSuccess(messages.size());
    }

    public Result2<Integer> retryUpsertMappingBySyncData(String tenantId,List<UpdateMapping.BySyncDataIdArg> bySyncDataIdArgs){
        if(CollectionUtils.isNotEmpty(bySyncDataIdArgs)){
            List<ReTrySendMq> sendMqs=Lists.newArrayList();
            ReTrySendMq reTrySendMq=new ReTrySendMq();
            reTrySendMq.setTenantId(tenantId);
            reTrySendMq.setRetryDataEnum(RetryDataEnum.RETRY_BULK_UPDATE_BY_SYNC_DATA_ID.name());
            reTrySendMq.setStatus(0);
            reTrySendMq.setCrm2erp(true);
            reTrySendMq.setMqMsg(JSONObject.toJSONString(bySyncDataIdArgs));
            reTrySendMq.setCreateTime(new Date());
            reTrySendMq.setUpdateTime(reTrySendMq.getCreateTime());
            sendMqs.add(reTrySendMq);
            retrySendMqDao.batchUpsertRetryData(sendMqs);
        }
        return Result2.newSuccess(bySyncDataIdArgs.size());
    }

    public Result2<Integer> retryUpsertMappingBySource(String tenantId,List<UpdateMapping.BySourceArg> bySourceArgs){
        if(CollectionUtils.isNotEmpty(bySourceArgs)){
            List<ReTrySendMq> sendMqs=Lists.newArrayList();
            ReTrySendMq reTrySendMq=new ReTrySendMq();
            reTrySendMq.setTenantId(tenantId);
            reTrySendMq.setRetryDataEnum(RetryDataEnum.RETRY_BULK_UPDATE_BY_SOURCE.name());
            reTrySendMq.setStatus(0);
            reTrySendMq.setCrm2erp(true);
            reTrySendMq.setMqMsg(JSONObject.toJSONString(bySourceArgs));
            reTrySendMq.setCreateTime(new Date());
            reTrySendMq.setUpdateTime(reTrySendMq.getCreateTime());
            sendMqs.add(reTrySendMq);
            retrySendMqDao.batchUpsertRetryData(sendMqs);
        }
        return Result2.newSuccess(bySourceArgs.size());
    }



//    public Result2<Void> reTrySendMqFromMongo(String ei,String retryType, Long startTime, Long endTime, List<Integer> status, Integer limit,Integer offset) {
//        service.submit(() -> {
//            try {
//                doReTrySendMq(ei,retryType,startTime,endTime,status,limit,offset);
//            } catch (Exception e) {
//                log.error("reTrySendMqFromMongo Exception", e);
//            }
//        });
//        return Result2.newSuccess();
//    }

//    public Result2<Void> doReTrySendMq(String ei,String retryType, Long startTime, Long endTime, List<Integer> status, Integer limit,Integer offset) {
//        boolean getEiLock = false;
//        String lockName = CommonConstant.lockMongoReTryMq;
//        try {
//            //抢锁不等待。没抢到就等下一次触发
//            getEiLock = redissonClient.getLock(lockName).tryLock();
//        } catch (Exception e) {
//            log.info("get lockMongoReTryMq lock  exception", e);
//        }
//        if (!getEiLock) {
//            return Result2.newSuccess();
//        }
//        TraceUtil.initTrace(String.format("J-E.doReTrySendMq.-10000-erp%s", TimeUtil.hms()));
//
//        if(ObjectUtils.isEmpty(startTime)){
//            startTime= 0L;
//        }
//        if(ObjectUtils.isEmpty(endTime)){
//            endTime = System.currentTimeMillis() - 1000 * 60 * 9L;//;
//        }
//        if(ObjectUtils.isEmpty(limit)){
//            limit=200;
//        }
//        if(ObjectUtils.isEmpty(offset)){
//            offset= 0;
//        }
//        try {
//            Long count = retrySendMqDao.getCollectionCounts();//集合总数，并不是需要扫描的重试的数量
//            //避免重试的时候，依然会失败，重试10条数据都失败，那就跳过此任务。
//            boolean retryData=true;
//            log.info("reTryFromMongo start,ei={},startTime={},endTime={},status={},collectionAllCounts={}", ei, startTime, endTime, status, count);
//            for (int i = 0; i < 500&&retryData; i++) {
//                List<ReTrySendMq> reTrySendMqs = retrySendMqDao.listRetrySendMqIdList(ei, retryType,startTime, endTime, status, limit,offset);
//                retryData=retryData(reTrySendMqs);
//                if (reTrySendMqs.size() < limit) {
//                    break;
//                }
//                if (i > 10) {
//                    log.info("reTryFromMongo too many i={} pageSize={}", i, limit);
//                }
//            }
//            log.info("reTryFromMongo end");
//        } catch (
//                Throwable t) {
//            log.error("reTryFromMongo Throwable", t);
//        } finally {
//            log.info("lockMongoReTryMq relese lock");
//            redissonClient.getLock(lockName).unlock();
//        }
//        return Result2.newSuccess();
//    }

//    public boolean retryData(List<ReTrySendMq> reTrySendMqs )  {
//        boolean continueFail=false;//重试任务的数据发送，依然失败，那就不继续发送，等待下一次触发
//        List<ObjectId> needDeleteIds = Lists.newArrayList();
//        List<ObjectId> needUpdateIds = Lists.newArrayList();
//        for (ReTrySendMq reTrySendMq : reTrySendMqs) {
//            //根据类型type。匹配对应的方法执行。
//            try {
//                RetryDataEnum retryDataEnum=RetryDataEnum.valueOf(reTrySendMq.getRetryDataEnum());
//                if(retryDataEnum.getErpModule().contains("task")){
//                    if(retryDataEnum.getArgType()==List.class){
//                        List<?> objects = JSONArray.parseArray(reTrySendMq.getMqMsg(), retryDataEnum.getArgType());
//                        Object result = invokeMethod(retryDataEnum.getHandlerMethod(), objects);
//                    }
//
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            List<SyncDataContextEvent> events = JacksonUtil.fromJson(reTrySendMq.getMqMsg(), new TypeReference<List<SyncDataContextEvent>>() {
//            });
//            if (CollectionUtils.isNotEmpty(events)) {
//                Result2<Void> result2 = allModelDubboService.batchSendEventData2DispatcherMqByContext(events);
//                if (result2.isSuccess()) {
//                    needDeleteIds.add(reTrySendMq.getId());
//                } else {
//                    needUpdateIds.add(reTrySendMq.getId());
//                }
//            } else {
//                needDeleteIds.add(reTrySendMq.getId());
//            }
//        }
//        if (CollectionUtils.isNotEmpty(needDeleteIds)) {
//            retrySendMqDao.deleteByIds(needDeleteIds);
//            needDeleteIds.clear();
//        }
//        if (CollectionUtils.isNotEmpty(needUpdateIds)) {
//            continueFail=needDeleteIds.size()>10;
//            retrySendMqDao.updateStatusById(needUpdateIds, 2);
//            needUpdateIds.clear();
//        }
//        return continueFail;
//    }

    public Object invokeMethod(String fullMethodName, Object... args) throws Exception {
        // 分割类名和方法名
        int lastIndex = fullMethodName.lastIndexOf("#");
        String className = fullMethodName.substring(0, lastIndex);
        String methodName = fullMethodName.substring(lastIndex + 1);

        // 加载类
        Class<?> clazz = Class.forName(className);

        // 获取Spring管理的bean
        Object bean = applicationContext.getBean(clazz);

        // 获取方法，这里假设方法没有重载，即方法名唯一
        Method method = findMethod(clazz, methodName, args);
        // 调用方法
        return method.invoke(bean, args);
    }
    private Method findMethod(Class<?> clazz, String methodName, Object[] args) throws NoSuchMethodException {
        for (Method method : clazz.getMethods()) {
            if (method.getName().equals(methodName) && method.getParameterCount() == args.length) {
                return method;
            }
        }
        throw new NoSuchMethodException("Method " + methodName + " with " + args.length + " parameters not found in " + clazz);
    }

}