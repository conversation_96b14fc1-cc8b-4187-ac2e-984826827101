package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * 旗舰版api模板管理器
 * <AUTHOR>
 * @date 2023-09-08
 */
@Component
public class K3UltimateApiTemplateManager {
    @Autowired
    private List<K3UltimateBaseApiTemplate> apiTemplateList;
    @Autowired
    private ErpK3UltimateApiTemplateManager erpK3UltimateApiTemplateManager;

    private LinkedHashMap<String,K3UltimateBaseApiTemplate> apiMap = new LinkedHashMap<>();

    @PostConstruct
    private void init() {
        for(K3UltimateBaseApiTemplate apiTemplate : apiTemplateList) {
            apiMap.put(apiTemplate.getObjApiName(),apiTemplate);
        }
    }

    public K3UltimateBaseApiTemplate getApiTemplate(String tenantId, String dataCenterId,String erpObjApiName) {
        K3UltimateBaseApiTemplate apiTemplate = erpK3UltimateApiTemplateManager.findApiTemplate(tenantId, dataCenterId, erpObjApiName);
        if(apiTemplate==null) {
            apiTemplate = apiMap.get(erpObjApiName.toLowerCase());
            if (apiTemplate == null) {
                K3UltimateBaseApiTemplate k3UltimateBaseApiTemplate = new K3UltimateBaseApiTemplate();
                return k3UltimateBaseApiTemplate;
            }
        }
        return apiTemplate;
    }
}
