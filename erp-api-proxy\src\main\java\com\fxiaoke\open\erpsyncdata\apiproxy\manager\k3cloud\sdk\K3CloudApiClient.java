package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk;

import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3Constant;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ExceptionToStringUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.StrUtil2;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryCombineQtyArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.K3CloudAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.K3CloudApiConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryInventoryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StopWatch;

import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 不使用spring bean管理，可直接新建运行
 */
@Slf4j
public class K3CloudApiClient extends ApiClient {
    @Setter
    @Getter
    private String tenantId;

    private K3CloudConnectParam connectParam;

    @Setter
    @Getter
    private String dataCenterId;//数据中心id，数据库中存储了一套连接信息的唯一标识

    /**
     * 唯一码，目前使用baseurl+dbid拼接，
     * 用于序列化
     */
    @Getter
    @Setter
    private String key;

    @Getter
    private final Map<String, String> headers = new ConcurrentHashMap<>();

    /**
     * 配置
     */
    private final K3CloudApiConfig config = new K3CloudApiConfig();

    /**
     * 当连接参数相等时，证明是连接同一套环境。
     *
     * @param o
     * @return
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        K3CloudApiClient apiClient = (K3CloudApiClient) o;
        return Objects.equals(key, apiClient.key);
    }

    /**
     * @return
     */
    @Override
    public int hashCode() {
        return Objects.hashCode(connectParam);
    }

    public static final String SAVE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save";
    public static final String BATCH_SAVE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.BatchSave";
    /**
     * 审核
     */
    public static final String AUDIT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit";
    public static final String DELETE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete";
    /**
     * 反审核
     */
    public static final String UN_AUDIT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit";
    /**
     * 撤销
     */
    public static final String CANCEL_ASSIGN = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CancelAssign";
    /**
     * 提交
     */
    public static final String SUBMIT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit";
    public static final String VIEW = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View";
    public static final String BILL_QUERY = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery";
    public static final String INVENTORY_QUERY = "Kingdee.K3.SCM.WebApi.ServicesStub.InventoryQueryService.GetInventoryData.common.kdsvc";
    /**
     * 暂存
     */
    public static final String DRAFT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Draft";
    public static final String GET_DATA_CENTER_LIST = "Kingdee.BOS.ServiceFacade.ServicesStub.Account.AccountService.GetDataCenterList";
    public static final String DISTRIBUTE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Allocate";
    public static final String UN_DISTRIBUTE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CancelAllocate";
    /**
     * 元数据信息
     */
    public static final String BUSINESS_INFO = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryBusinessInfo";
    /**
     * 执行业务操作：作废、关闭......
     */
    public static final String EXCUTE_OPERATION = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExcuteOperation";
    /**
     * 登录
     */
    public static final String LOGIN = "Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser";
    /**
     * 应用登陆
     */
    public static final String APP_AUTH = "Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginByAppSecret.common.kdsvc";
    /**
     * 销售订单新变更单新增
     *
     */
    public static final String CREATEXORDER = "Kingdee.K3.SCM.WebApi.ServicesStub.SaveXSaleOrderWebApi.SaveXSaleOrder";

    /**
     * K3上传文件接口
     */
    public static final String K3_UPLOAD_ATTACHMENT = "FileUpLoadServices/FileService.svc/upload2attachment/?fileName=%s&fileId=%s&token=%s&last=%b";

    /**
     * k3 新上传文件接口
     */
    public static final String K3_COMMON_UPLOAD_ATTACHMENT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UploadFile.common.kdsvc";

    public static final String K3_DOWNLOAD_URL = "FileUpLoadServices/download.aspx?fileId=%s&token=%s&ticks=%s&nail=%d";

    public void setConnectParam(K3CloudConnectParam connectParam) {
        this.connectParam = connectParam;
        this.setKey(connectParam.getBaseUrl() + connectParam.getDbId());
        BeanUtils.copyProperties(connectParam.newConfigIfNull(), this.config);
        //if (!this.config.isUseAppToken()){
            this.loginThrowException();
        //}
    }

    /**
     * 新实例，进行一次登录操作
     */
    public static K3CloudApiClient newInstance(final String tenantId, String connectParamJson, String dataCenterId) {
        K3CloudConnectParam connectParam = GsonUtil.fromJson(connectParamJson, K3CloudConnectParam.class);
        return newInstance(tenantId, connectParam, dataCenterId);
    }

    /**
     * 新实例，进行一次登录操作
     */
    public static K3CloudApiClient newInstance(final String tenantId, K3CloudConnectParam connectParam, String dataCenterId) {
        if (connectParam == null || connectParam.getBaseUrl() == null) {
            throw new ErpSyncDataException(I18NStringEnum.s152,tenantId);
        }
        K3CloudApiClient apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
        apiClient.setDataCenterId(dataCenterId);
        apiClient.setConnectParam(connectParam);
        apiClient.setTenantId(tenantId);
        return apiClient;
    }
    /**
     * 登录，如果失败直接抛出异常
     */
    public void loginThrowException() {
        try {
            loginK3();
        } catch (Exception e) {
            //重试一次
            try {
                TimeUnit.SECONDS.sleep(10L);
            } catch (InterruptedException ignored) {
            }
            loginK3();
        }
    }

    public boolean loginK3() {
        boolean loginSuccess;
        String responseBody = null;
        try {
                Object[] loginInfo = null;
                HttpRspLimitLenUtil.ResponseBodyModel ret = null;
                int lcid = I18NStringManager.lang2lcid(connectParam.getLang());
                if (connectParam.getAuthType().equals(K3CloudAuthType.APPLICATION)) {
                    //应用授权
                    loginInfo = new Object[]{connectParam.getDbId(), connectParam.getUserName(), connectParam.getAppId(), connectParam.getPassword(), lcid};
                    ret = executeJudgeMethod(APP_AUTH, loginInfo);
                } else {
                    loginInfo = new Object[]{connectParam.getDbId(), connectParam.getUserName(), connectParam.getPassword(), lcid};
                    ret = executeJudgeMethod(LOGIN, loginInfo);
                }
                responseBody = ret.getBody();
                JSONObject jsonObject = new JSONObject(responseBody);
                if(jsonObject.get("Context")!=null&&!"null".equals(jsonObject.get("Context").toString())){
                    this.loginContext=jsonObject.getJSONObject("Context");
                }
                //userToken附件接口需要
                int result = jsonObject.getInt("LoginResultType");
                if (result == 1) {
                    this.userToken = this.loginContext.getString("UserToken");
                    if(jsonObject.has("KDSVCSessionId")&&StringUtils.isNotBlank(jsonObject.optString("KDSVCSessionId"))){
                        this.kdServiceSessionId = String.valueOf(jsonObject.get("KDSVCSessionId"));
                    }
                }
                loginSuccess = result == 1;
        } catch (Exception e) {
            log.info("login k3 error responseBody:{}", responseBody, e);
            tryThrowOTherException(e);
            throw new ErpSyncDataException(ResultCodeEnum.THIRD_SYSTEM_AUTH_FAILED,tenantId);
        }
        if (!loginSuccess) {
            log.info("login k3 error responseBody:{}", responseBody);
            throw new ErpSyncDataException(ResultCodeEnum.THIRD_SYSTEM_AUTH_FAILED,tenantId);
        }
        return loginSuccess;
    }

    //尝试抛出更具体和可读性更好的异常
    private void tryThrowOTherException(Exception e) {
        if(ExceptionToStringUtil.getExceptionToString(e).contains("Connection timed out")) {
            throw new ErpSyncDataException(ResultCodeEnum.LOGIN_K3C_TIMEOUT,tenantId);
        }
        if(ExceptionToStringUtil.getExceptionToString(e).contains("java.security.cert.CertificateExpiredException")) {
            throw new ErpSyncDataException(ResultCodeEnum.VISIT_OUT_HTTPS_CERTIFICATE_EXPIRE,tenantId);
        }
    }

    /**
     * 应用授权
     */
    public boolean appLogin() {
        //应用授权
        Object[] loginInfo = {connectParam.getDbId(),connectParam.getUserName(),connectParam.getAppId(),connectParam.getPassword(),2052};
        HttpRspLimitLenUtil.ResponseBodyModel ret = null;
        try {
            ret = executeJudgeMethod(APP_AUTH, loginInfo);
            JSONObject jsonObject = new JSONObject(ret.getBody());
            if (jsonObject.get("Context") instanceof JSONObject){
                this.loginContext = jsonObject.getJSONObject("Context");
            }
            int result = jsonObject.getInt("LoginResultType");
            if (result == 1) return Boolean.TRUE;
            return Boolean.FALSE;
        } catch (Throwable e) {
            throw ErpSyncDataException.wrap(e);
        }
    }


    public K3CloudApiClient(String serverUrl) {
        super(serverUrl);
        connectParam = new K3CloudConnectParam();
        connectParam.setBaseUrl(serverUrl);
        if ("https://api.kingdee.com/galaxyapi/".equals(StringUtils.appendIfMissing(serverUrl, "/"))) {
            config.setUseAppToken(true);
        }
    }

    /**
     * 获取版本
     */
    public String getDisplayVersion() {
        if(this.loginContext!=null){
            return loginContext.getString("DisplayVersion");
        }
        return null;
    }

    private <T> Result<T> doAndLog(final String objApiName, final Object parameters, ErpObjInterfaceUrlEnum type, Supplier<Result<T>> supplier) {
        return doAndLog(objApiName, parameters, type, supplier, null);
    }

    private <T> Result<T> doAndLog(final String objApiName, final Object parameters, ErpObjInterfaceUrlEnum type, Supplier<Result<T>> supplier, TimeFilterArg timeFilterArg) {
        if (java.util.Objects.isNull(BaseErpDataManager.getInterfaceMonitorManager())) {
            // 没有值，说明没有启动 Spring, 说明是测试,直接返回
            return supplier.get();
        }

        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();

        Result<T> result = null;
        try {
            result = supplier.get();
            return result;
        } finally {
            stopWatch.stop();
            Integer status = java.util.Objects.nonNull(result) && BooleanUtils.isTrue(result.isSuccess()) ? 1 : 2;
            BaseErpDataManager.saveErpInterfaceMonitor(tenantId, dataCenterId, objApiName, type.name(),
                    parameters, JSON.toJSONString(result, SerializerFeature.WriteMapNullValue), status, callTime, System.currentTimeMillis(),
                    "", TraceUtil.get(), stopWatch.getTotalTimeMillis(), timeFilterArg);
        }
    }

    /**
     * 创建销售订单新变更单
     */
    public Result<SaveResult> createXOrder(CreateXOrderBySalesOrderNoArg arg) {
        return doAndLog(K3CloudForm.SAL_XORDER, arg, ErpObjInterfaceUrlEnum.createXOrder, () -> createXOrder(CREATEXORDER, arg, new TypeReference<SaveResult>() {
        }));
    }
    /**
     * >=8.0.277.10 版本返回值有改动
     */
    public Result<NewSaveResult> newCreateXOrder(CreateXOrderBySalesOrderNoArg arg) {
        return doAndLog(K3CloudForm.SAL_XORDER, arg, ErpObjInterfaceUrlEnum.createXOrder, () -> createXOrder(CREATEXORDER, arg, new TypeReference<NewSaveResult>() {
        }));
    }

    public Result<SaveResult> save(final ErpObjInterfaceUrlEnum urlEnum, String formid, SaveArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, urlEnum, () -> executeWrap(SAVE, parameters, new TypeReference<SaveResult>() {
        }));
    }

    public Result<String> batchSave(String formid, String data) {
        return executeWrap(BATCH_SAVE, new Object[]{formid, data}, new TypeReference<String>() {
        });
    }

    public Result<Submit.Result> audit(String formid, Submit.BaseArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.audit, () -> executeWrap(AUDIT, parameters, new TypeReference<Submit.Result>() {
        }));
    }

    public Result<DeleteResult> delete(String formid, DeleteArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.delete, () -> executeWrap(DELETE, parameters, new TypeReference<DeleteResult>() {
        }));
    }

    public Result<ExcuteOperationArg.Result> excuteOperation(ErpObjInterfaceUrlEnum urlEnum, final String formId, Object[] data) {
        return doAndLog(formId, data, urlEnum, () -> executeWrap(EXCUTE_OPERATION, data, new TypeReference<ExcuteOperationArg.Result>() {
        }));
    }

    public Result<Submit.Result> cancelAssign(String formid, Submit.BaseArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, data, ErpObjInterfaceUrlEnum.cancelAssign, () -> executeWrap(CANCEL_ASSIGN, parameters, new TypeReference<Submit.Result>() {
        }));
    }

    public Result<Submit.Result> unAudit(String formid, Submit.BaseArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.unAudit, () -> executeWrap(UN_AUDIT, parameters, new TypeReference<Submit.Result>() {
        }));
    }

    public Result<Submit.Result> submit(String formid, Submit.BaseArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.submit, () -> executeWrap(SUBMIT, parameters, new TypeReference<Submit.Result>() {
        }));
    }

    public Result<ViewResult> view(String formid, ViewArg data) {
        return view(ErpObjInterfaceUrlEnum.view, formid, data);
    }

    public Result<ViewResult> view(ErpObjInterfaceUrlEnum urlEnum, String formid, ViewArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, urlEnum, () -> executeWrap(VIEW, parameters, new TypeReference<ViewResult>() {
        }));
    }

    public Result<List<List<Object>>> executeBillQuery(String logFormId,QueryArg data, TimeFilterArg timeFilterArg) {
        final Object[] parameters = {data};
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if(timeFilterArg!=null&&timeFilterArg.getOperationType()!=null&& EventTypeEnum.INVALID.getType()==timeFilterArg.getOperationType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
        }
        logFormId=StringUtils.isBlank(logFormId)?data.getFormId():logFormId;
        Result<List<List<Object>>> listResult = doAndLog(logFormId, parameters, type, () -> executeWrap(BILL_QUERY, parameters, new TypeReference<List<List<Object>>>() {
        }), timeFilterArg);
        return handleBillQueryResult(listResult);
    }

    public Result<List<List<Object>>> executeBillQuery(QueryArg data) {
        return executeBillQuery(null,data, null);
    }

    public Result<QueryBusinessInfoResult> queryBusinessInfo(QueryBusinessInfoArg arg) {
        final Object[] parameters = {arg};
        Result<QueryBusinessInfoResult> result = executeWrap(BUSINESS_INFO, parameters,
                new TypeReference<QueryBusinessInfoResult>() {
                });
        return handleQueryBusinessInfoResult(result);
    }

    private Result<QueryBusinessInfoResult> handleQueryBusinessInfoResult(Result<QueryBusinessInfoResult> result) {
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        QueryBusinessInfoResult queryBusInfoRes = result.getData();
        if (queryBusInfoRes == null || queryBusInfoRes.getResult() == null) {
            log.warn("queryBusInfoRes result error,result:{}", queryBusInfoRes);
            return new Result<>(ResultCodeEnum.RESULT_ERROR, "Result is empty");

//            return Result.newError(ResultCodeEnum.RESULT_ERROR.getErrCode(), "Result为空");
        }
        ResponseStatus responseStatus = queryBusInfoRes.getResult().getResponseStatus();
        if (responseStatus != null) {
            //成功情况下为空
            if (!responseStatus.getIsSuccess()) {
                //接口返回失败
                result.setErrCode(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrCode());
                result.setErrMsg(responseStatus.printErrors());
            }
        }
        return result;
    }

    public Result<List<K3Model>> queryAll(QueryArg queryArg) {
        int offset = 0;
        int limit = 2000;
        int currentPageSize = -1;
        Integer largeSize = ConfigCenter.ERP_LIST_MAX_NUM;
        List<K3Model> allList = new ArrayList<>(64);
        while (currentPageSize != 0 && offset < largeSize) {
            queryArg.setLimit(limit);
            queryArg.setStartRow(offset);
            Result<List<K3Model>> result = queryReturnMap(queryArg);
            if (!result.isSuccess()) {
                return result;
            }
            allList.addAll(result.getData());
            currentPageSize = result.getData().size();
            offset += currentPageSize;
        }
        return Result.newSuccess(allList);
    }

    /**
     * 查询所有并去重
     *
     * @param queryArg
     * @return
     */
    public Result<List<K3Model>> queryAllNoDup(QueryArg queryArg) {
        int offset = 0;
        int limit = 2000;
        int currentPageSize = -1;
        //防止出问题拉爆内存
        Integer largeSize = ConfigCenter.ERP_LIST_MAX_NUM;
        LinkedHashSet<K3Model> allSet = new LinkedHashSet<>(64);
        while (currentPageSize != 0 && allSet.size() < largeSize) {
            queryArg.setLimit(limit);
            queryArg.setStartRow(offset);
            Result<List<K3Model>> result = queryReturnMap(queryArg);
            if (!result.isSuccess()) {
                return result;
            }
            allSet.addAll(result.getData());
            currentPageSize = result.getData().size();
            offset += currentPageSize;
        }
        return Result.newSuccess(new ArrayList<>(allSet));
    }

    public Result<List<K3Model>> queryReturnMap(QueryArg queryArg, TimeFilterArg timeFilterArg) {
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if(timeFilterArg.getOperationType()!=null&& EventTypeEnum.INVALID.getType()==timeFilterArg.getOperationType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
        }
        return queryReturnMap(null,queryArg, timeFilterArg, type);
    }

    private Result<List<K3Model>> queryReturnMap(String logFormId,QueryArg queryArg, TimeFilterArg timeFilterArg, ErpObjInterfaceUrlEnum urlEnum) {
        //这里对字段做去重，不在更底层做是怕上层逻辑使用了字段列表。而这个方法是返回map的。
        queryArg.removeDuplicateFieldKeys();
        logFormId=StringUtils.isBlank(logFormId)?queryArg.getFormId():logFormId;
        final Object[] parameters = {queryArg};
        return doAndLog(logFormId, parameters, urlEnum, () -> {
            Result<List<List<Object>>> listResult = executeWrap(BILL_QUERY, parameters, new TypeReference<List<List<Object>>>() {
            });
            listResult = handleBillQueryResult(listResult);
            if (!listResult.isSuccess()) {
                return Result.copy(listResult);
            }
            List<List<Object>> dataList = listResult.getData();
            List<String> fieldKeys = queryArg.getFieldKeyList();
            List<K3Model> result = new ArrayList<>();
            for (List<Object> data : dataList) {
                K3Model k3Model = new K3Model();
                for (int i = 0; i < fieldKeys.size(); i++) {
                    k3Model.put(fieldKeys.get(i), data.get(i));
                }
                result.add(k3Model);
            }
            return Result.newSuccess(result);
        }, timeFilterArg);
    }

    public Result<List<K3Model>> queryReturnMap(QueryArg queryArg, ErpObjInterfaceUrlEnum type) {
        return queryReturnMap(null,queryArg, null, type);
    }

    public Result<List<K3Model>> queryReturnMap(QueryArg queryArg) {
        return queryReturnMap(queryArg, ErpObjInterfaceUrlEnum.queryMasterById);
    }
    public Result<List<K3Model>> queryReturnMap(String logFormId,QueryArg queryArg) {
        return queryReturnMap(logFormId,queryArg,null, ErpObjInterfaceUrlEnum.queryMasterById);
    }

    public Result<QueryInventoryResult.CombineInventoryResult> specialStockQuery(QueryCombineQtyArg queryCombineQtyArg) {
        Result<QueryInventoryResult.CombineInventoryResult> listResult = executeWrap(INVENTORY_QUERY, new Object[]{queryCombineQtyArg}, new TypeReference<QueryInventoryResult.CombineInventoryResult>() {
        });
        return listResult;
    }


    public Result<String> distribute(String formid, DistributeArg data) {
        final Object[] parameters = {formid, data};
        Result<String> result = doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.distribute, () -> executeWrap(DISTRIBUTE, parameters));
        return result;
    }

    public Result<String> unDistribute(String formid, UnDistributeArg data) {
        final Object[] parameters = {formid, data};
        Result<String> result = doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.unDistribute, () -> executeWrap(UN_DISTRIBUTE, parameters));
        return result;
    }

    public Result<SaveResult> draft(String formid, SaveArg data) {
        final Object[] parameters = {formid, data};
        return doAndLog(formid, parameters, ErpObjInterfaceUrlEnum.draft, () -> executeWrap(DRAFT, parameters, new TypeReference<SaveResult>() {
        }));
    }

    public Result<List<BaseObjResult>> queryDataCenterList() {
        return executeWrap(GET_DATA_CENTER_LIST, new Object[]{}, new TypeReference<List<BaseObjResult>>() {
        });
    }

    /**
     * 注；这里返回的结果，只保证调用K3接口并反序列化正常，具体请求K3的结果是否报错需要根据返回内容判断。
     *
     * @param serviceName
     * @param parameters
     * @return
     */
    public Result<String> executeWrap(String serviceName, Object[] parameters) {
        HttpRspLimitLenUtil.ResponseBodyModel strResult = null;
        try {
            strResult = executeJudgeMethod(serviceName, parameters);
            if (strResult.isReachLengthLimit()) {
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR, "contentlen limit");
            }
            //有可能为response_error开头的
            if (strResult.getBody().startsWith("response_error")) {
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, "response_error");
            }
        } catch (Exception e) {
            log.warn("execute k3 failed,serviceName:{},parameters:{}", serviceName, JacksonUtil.toJson(parameters), e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getClass().getName());
        } finally {
            log.info("execute k3 finish,serviceName:{},parameters:{},result:{}", serviceName, JacksonUtil.toJson(parameters), JacksonUtil.toJson(strResult));
        }
        return Result.newSuccess(strResult.getBody());
    }

    /**
     * 注；这里返回的结果，只保证调用K3接口并反序列化正常，具体请求K3的结果是否报错需要根据返回内容判断。
     *
     * @param serviceName
     * @param parameters
     * @return
     */
    public <T> Result<T> executeWrap(String serviceName, Object[] parameters, TypeReference<T> typeReference) {
        T k3Result;
        HttpRspLimitLenUtil.ResponseBodyModel strResult = null;
        try {
            strResult = executeJudgeMethod(serviceName, parameters);
            if (strResult.isReachLengthLimit()) {
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR, "contentlen limit");
            }
            if (strResult.getBody().contains(K3Constant.SESSION_INFO_HAS_BEEN_LOST)) {
                this.loginThrowException();
                strResult = executeJudgeMethod(serviceName, parameters);
            }
            //有可能为response_error开头的
            if (strResult.getBody().startsWith("response_error")) {
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, "response_error");
            }
            k3Result = JacksonUtil.fromJson(strResult.getBody(), typeReference);
        } catch (ErpSyncDataException e) {
            log.warn("execute k3 failed,serviceName:{},parameters:{},result:{}", serviceName, JacksonUtil.toJson(parameters), JacksonUtil.toJson(strResult), e);
            return new Result<>(e.getErrCode(), e.getErrMsg(), null);
        } catch (Exception e) {
            String resultStr=JacksonUtil.toJson(strResult);
            log.warn("execute k3 failed,serviceName:{},parameters:{},result:{}", serviceName, JacksonUtil.toJson(parameters), resultStr, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getClass().getName()+"。returns results："+resultStr);
        } finally {
            long length = HttpRspLimitLenUtil.ResponseBodyModel.getSize(strResult);
            log.debug("execute k3 finish,length:{},serviceName:{},parameters:{},result:{}", length, serviceName, JacksonUtil.toJson(parameters), Objects.isNull(strResult) ? null : StringUtils.substring(strResult.getBody(), 0, 10000));
        }
        return Result.newSuccess(k3Result);
    }

    private HttpRspLimitLenUtil.ResponseBodyModel  executeJudgeMethod(String serviceName, Object[] parameters) throws Exception {
        String baseUrl = this.connectParam.getBaseUrl();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        if (!serviceName.endsWith(".common.kdsvc")) {
            serviceName = serviceName + ".common.kdsvc";
        }
        String url = baseUrl + serviceName;
        Map<String, Object[]> body = Collections.singletonMap("parameters", parameters);
        String jsonBody = JacksonUtil.toJson(body);
        Map<String, String> headers = getHeaders(url);
        if (config.isEnableDebug()) {
            //打印curl命令
            StringBuilder headerCurl = new StringBuilder("--header 'Content-Type: application/json'");
            headers.forEach((k, v) -> {
                headerCurl.append(String.format(" --header '%s:%s'", k, v));
            });
            log.info(String.format("curl --location --request POST '%s' %s --data-raw '%s'", url, headerCurl, jsonBody));
        }
        if (config.isRemoveZeroWidthChar()){
            jsonBody = StrUtil2.removeZeroWidthChar(jsonBody);
        }
        return FsHttpClient4K3.defaultClient().postUseCookie(url, jsonBody, headers, config.isUseNewResponseReader());
    }

    private Map<String, String> getHeaders(String url) {
        Map<String, String> headers;
        if (this.config.isUseAppToken()){
            headers = K3AppHeaderUtil.buildAppToken(url, connectParam);
            if(StringUtils.isNotEmpty(this.kdServiceSessionId)) {
                headers.put("SID",this.kdServiceSessionId);
                headers.put("Cookie","kdservice-sessionid="+this.kdServiceSessionId);
            }
        }else {
            headers = this.headers;
        }
        return headers;
    }

    private <T> Result<T> createXOrder(String serviceName, CreateXOrderBySalesOrderNoArg arg,TypeReference<T> typeReference){
        T k3Result;
        HttpRspLimitLenUtil.ResponseBodyModel strResult = null;
        try {
            String baseUrl = this.connectParam.getBaseUrl();
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            if (!serviceName.endsWith(".common.kdsvc")) {
                serviceName = serviceName + ".common.kdsvc";
            }
            String url = baseUrl + serviceName;
            String jsonBody = JacksonUtil.toJson(arg);
            Map<String, String> headers = getHeaders(url);
            if (config.isEnableDebug()) {
                //打印curl命令
                StringBuilder headerCurl = new StringBuilder("--header 'Content-Type: application/json'");
                headers.forEach((k, v) -> {
                    headerCurl.append(String.format(" --header '%s:%s'", k, v));
                });
                log.info(String.format("curl --location --request POST '%s' %s --data-raw '%s'", url, headerCurl, jsonBody));
            }
            if (config.isRemoveZeroWidthChar()){
                jsonBody = StrUtil2.removeZeroWidthChar(jsonBody);
            }
            strResult = FsHttpClient4K3.defaultClient().postUseCookie(url, jsonBody, headers, config.isUseNewResponseReader());
            //有可能为response_error开头的
            if (strResult.getBody().startsWith("response_error")) {
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, "response_error");
            }
            k3Result = JacksonUtil.fromJson(strResult.getBody(), typeReference);
        } catch (Exception e) {
            log.warn("execute k3 failed,serviceName:{},parameters:{}", serviceName, JacksonUtil.toJson(arg), e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getClass().getName());
        } finally {
            log.info("execute k3 finish,serviceName:{},parameters:{},result:{}", serviceName, JacksonUtil.toJson(arg), JacksonUtil.toJson(strResult));
        }
        return Result.newSuccess(k3Result);
    }

    private Result<List<List<Object>>> handleBillQueryResult(Result<List<List<Object>>> result) {
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        List<List<Object>> resultList = result.getData();
        if (resultList.size() == 1) {
            List<Object> dataList = resultList.get(0);
            if (dataList.size() == 1) {
                Object value = dataList.get(0);
                if (value instanceof Map) {
                    result.setErrCode(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrCode());
                    try {
                        ViewResult viewResult = JacksonUtil.fromJson(JacksonUtil.toJson(value), ViewResult.class);
                        result.setErrMsg(viewResult.getResult().getResponseStatus().printErrors());
                    } catch (Exception e) {
                        log.error("parse error,value:{}", value);
                    }
                }
            }
        }
        return result;
    }

    public Result<Map<String, Object>> commonUploadFile(String fileName, String fileId, boolean end, InputStream inputStream, double fileSize) {
        log.info("K3CloudApiClient.commonUploadFile,fileName={}",fileName);
        Map<String, String> hearsMap = Maps.newHashMap();
        hearsMap.put("Content-type", "application/json; charset=utf-8");
        hearsMap.put("Cookie","kdservice-sessionid=" + this.kdServiceSessionId);
        String baseUrl = this.connectParam.getBaseUrl();
        if(StringUtils.isNotEmpty(this.connectParam.getOldBaseUrl())) {
            baseUrl = this.connectParam.getOldBaseUrl();
            log.info("K3CloudApiClient.commonUploadFile,oldBaseUrl={}",baseUrl);
        }
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        String url = baseUrl + K3_COMMON_UPLOAD_ATTACHMENT;

        HttpRspLimitLenUtil.ResponseBodyModel result = null;
        Map<String, Object> upload2AttachmentResult = Maps.newHashMap();
        upload2AttachmentResult.put("FileName", fileName);
        upload2AttachmentResult.put("FileSize", (int) fileSize);
        boolean success = false;
        int MAXSIZE = 1024 * 1024;//1M
        try {
            K3CommonUploadFileArg k3CommonUploadFileArg = new K3CommonUploadFileArg();
            K3CommonUploadFileArg.CommonUploadFileArg commonUploadFileArg = new K3CommonUploadFileArg.CommonUploadFileArg();
            commonUploadFileArg.setFileName(URLEncoder.encode(fileName, "UTF-8"));
            k3CommonUploadFileArg.setData(commonUploadFileArg);
            //如果文件的长度大于最大上传长度，分块进行上传
            while (fileSize > 0) {
                byte[] bytes;
                if (fileSize > MAXSIZE) {
                    bytes = IoUtil.readBytes(inputStream,MAXSIZE);
                } else {
                    log.info("K3CloudApiClient.commonUploadFile,fileSize={}",fileSize);
                    bytes = IoUtil.readBytes(inputStream);
                    k3CommonUploadFileArg.getData().setIsLast(Boolean.TRUE);
                }
                fileSize -= MAXSIZE;
                k3CommonUploadFileArg.getData().setFileId(fileId);
                k3CommonUploadFileArg.getData().setSendByte(Base64.getEncoder().encodeToString(bytes));
                result = FsHttpClient4K3.defaultClient().postUrl2(url, k3CommonUploadFileArg, hearsMap);
                if (StringUtils.isNotEmpty(result.getBody())) {
                    log.info("K3CloudApiClient.commonUploadFile,result={}",result);
                    ObjectMapper k3CommonUploadFileObjectMapper = new ObjectMapper();
                    K3CommonUploadFileResult k3CommonUploadFileResult = k3CommonUploadFileObjectMapper.readValue(result.getBody(), K3CommonUploadFileResult.class);
                    if (ObjectUtils.isEmpty(k3CommonUploadFileResult.getResult())) {
                        return Result.newError(I18NStringEnum.s3636, result.getBody());
                    }

                    K3CommonUploadFileResult.CommonUploadFileResult commonUploadFileResult = k3CommonUploadFileResult.getResult();
                    if (ObjectUtils.isEmpty(commonUploadFileResult)) {
                        return Result.newError(I18NStringEnum.s3636, result.getBody());
                    }

                    //获取服务器返回的数据中的fileId
                    success = commonUploadFileResult.getResponseStatus().getIsSuccess();
                    fileId = commonUploadFileResult.getFileId();
                    upload2AttachmentResult.put("Message", commonUploadFileResult.getMessage());
                    upload2AttachmentResult.put("Success", success);
                    upload2AttachmentResult.put("FileId", fileId);
                }
            }
            log.info("k3 common upload file result:{}",result);
        } catch (Exception e) {
            log.info("K3cloud client common upload file message result={} e={}", result,e.getMessage());
            return Result.newError(I18NStringEnum.s3637, e.getMessage(), ObjectUtils.isNotEmpty(result) ? result.getBody() : "");
        }
        if (success) {
            return Result.newSuccess(upload2AttachmentResult);
        }
        //返回错误消息
        if (upload2AttachmentResult.get("Message") != null) {
            return Result.newError(I18NStringEnum.s3636, upload2AttachmentResult.get("Message").toString());
        }
        return Result.newSystemError(I18NStringEnum.s40);
    }

    /**
     * 参考K3渠道文件上传下载 https://vip.kingdee.com/article/43303
     *
     * @param fileName
     * @param fileId
     * @param end
     * @param
     * @return
     */

    public Result<Map<String, Object>> uploadFile(String fileName, String fileId, boolean end, InputStream inputStream, double fileSize, Boolean isNeedNewFileInterface) {
        //有版本限制，版本号大于 8.0.100.202202，才能走新接口
        int compareVersion = VersionComparator.INSTANCE.compare(getDisplayVersion(), "8.0.100.202202");
        boolean needFillDeleteInfo = compareVersion > 0;
        if (needFillDeleteInfo && isNeedNewFileInterface) {
            return commonUploadFile(fileName, fileId, end, inputStream, fileSize);
        }

        //TODO 对于大文件是否需要拦截？
        Map<String, String> hearsMap = Maps.newHashMap();
        hearsMap.put("Content-type", "UTF-8");
        String baseUrl = this.connectParam.getBaseUrl();
        HttpRspLimitLenUtil.ResponseBodyModel result=null;
        Map<String, Object> upload2AttachmentResult = Maps.newHashMap();
        boolean success = false;
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        if(StringUtils.isNotEmpty(this.connectParam.getOldBaseUrl())) {
            baseUrl = this.connectParam.getOldBaseUrl();
            log.info("K3CloudApiClient.uploadFile,oldBaseUrl={}",baseUrl);
        }
        String url = baseUrl + K3_UPLOAD_ATTACHMENT;
        int MAXSIZE = 1024 * 1024;//1M
        try {
            //如果文件的长度大于最大上传长度，分块进行上传
            final String encodeFileName = URLEncoder.encode(fileName, "UTF-8");
            while (fileSize > MAXSIZE) {
                fileSize -= MAXSIZE;
                byte[] bytes = IoUtil.readBytes(inputStream,MAXSIZE);//
                String completeUrl = String.format(url, encodeFileName, fileId, this.getUserToken(), false);
                log.info("uploadFile,completeUrl.1={}",completeUrl);
                result = FsHttpClient4K3.defaultClient().postUrl(completeUrl, bytes, hearsMap);
                if (StringUtils.isNotEmpty(result.getBody())) {
                    JSONObject resultJson = new JSONObject(result.getBody());
                    if(!resultJson.has("Upload2AttachmentResult")){
                        return Result.newError(I18NStringEnum.s3636, result.getBody());
                    }
                    //获取服务器返回的数据中的fileId
                    if (java.util.Objects.nonNull(resultJson.get("Upload2AttachmentResult")) || !"".equals(resultJson.get("Upload2AttachmentResult"))) {
                        upload2AttachmentResult = com.alibaba.fastjson.JSONObject.parseObject(String.valueOf(resultJson.get("Upload2AttachmentResult")), HashMap.class);
                        success = (Boolean) upload2AttachmentResult.get("Success");
                        fileId = upload2AttachmentResult.get("FileId").toString();
                    }
                }
            }
            if (fileSize <= MAXSIZE && fileSize > 0) {
                String completeUrl = String.format(url, encodeFileName, fileId, this.getUserToken(),true);
                log.info("uploadFile,completeUrl.2={}",completeUrl);
                byte[] bytes = IoUtil.readBytes(inputStream);
                result = FsHttpClient4K3.defaultClient().postUrl(completeUrl, bytes, hearsMap);
                if (StringUtils.isNotEmpty(result.getBody())) {
                    log.info("k3 uploadFile result:{}", result.getBody());
                    JSONObject resultJson = new JSONObject(result.getBody());
                    if(!resultJson.has("Upload2AttachmentResult")){
                        return Result.newError(I18NStringEnum.s3636, result.getBody());
                    }
                    //获取服务器返回的数据中的fileId
                    if (java.util.Objects.nonNull(resultJson.get("Upload2AttachmentResult")) || !"".equals(resultJson.get("Upload2AttachmentResult"))) {
                        upload2AttachmentResult = com.alibaba.fastjson.JSONObject.parseObject(String.valueOf(resultJson.get("Upload2AttachmentResult")), HashMap.class);
                        success = (Boolean) upload2AttachmentResult.get("Success");
                    }
                }
            }
            log.info("k3 upload file result:{}",result);
        } catch (Exception e) {
            log.info("K3cloud client message result={} e={}",result,e);
            return Result.newError(I18NStringEnum.s3637, e.getMessage(), result.getBody());
        }
        if (success == true) {
            return Result.newSuccess(upload2AttachmentResult);
        }
        //返回错误消息
        if (upload2AttachmentResult.get("Message") != null) {
            return Result.newError(I18NStringEnum.s3636, upload2AttachmentResult.get("Message").toString());
        }
        return Result.newSystemError(I18NStringEnum.s40);
    }




    /**
     * K3渠道下载文件
     *
     * @param field
     * @return
     */
    public Result<StoneFileUploadResponse> downLoadAndUploadFile(String field,Function<Response,Result<StoneFileUploadResponse>> function) {
        K3DownFileResult fileResult = new K3DownFileResult();
        InputStream inputStream=null;
        try {
            String baseUrl = this.connectParam.getBaseUrl();
            if(StringUtils.isNotEmpty(this.connectParam.getOldBaseUrl())) {
                baseUrl = this.connectParam.getOldBaseUrl();
                log.info("K3CloudApiClient.downLoadFile,oldBaseUrl={}",baseUrl);
            }
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            String url = baseUrl + K3_DOWNLOAD_URL;
            String completeUrl = String.format(url, field, this.getUserToken(), new Date().getTime(), 0);
            log.info("downLoadFile,completeUrl={}", completeUrl);
            //定义上传的函数
            Result<StoneFileUploadResponse> stoneFileUploadResponseResult = FsHttpClient4K3.defaultClient().sendOkHttp3GetFile(completeUrl, headers, function);
            return stoneFileUploadResponseResult;
        } catch (Exception e) {
            log.warn("downLoadFile error field: {}", field, e);
            return null;
        }
    }

    /**
     * 新变更单所需字段，使用billQuery转ViewResult，兼容后面的逻辑
     * @param queryArg
     * @return
     */
    public Result<ViewResult> queryViewResult(QueryArg queryArg) {
        int offset = 0;
        int limit = 500;
        int currentPageSize = -1;

        K3Model k3Model = new K3Model();
        List<Map<String, Object>> saleOrderEntries = new ArrayList<>();
        List<Map<String, Object>> saleOrderPlans = new ArrayList<>();
        HashSet<String> uniqueEntryKeys = new HashSet<>();
        HashSet<String> uniquePlanKeys = new HashSet<>();
        while (currentPageSize != 0) {
            queryArg.setLimit(limit);
            queryArg.setStartRow(offset);
            Result<List<List<Object>>> result = executeBillQuery(queryArg);
            if (!result.isSuccess()) {
                return Result.newError(result.getErrCode(), result.getErrMsg());
            }
            // 基础字段
            if(k3Model.isEmpty()) {
                k3Model.put("Id", result.getData().get(0).get(0));
                k3Model.put("BillNo", result.getData().get(0).get(1));
                k3Model.put("PKIDX", result.getData().get(0).get(2));
            }

            //组转数据
            for (List<Object> rawData : result.getData()) {
                // SaleOrderEntry
                String entryIdForEntry = rawData.get(3).toString();
                String pkidxForEntry = rawData.get(4).toString();
                String uniqueKeyForEntry = entryIdForEntry + "-" + pkidxForEntry;

                if (!uniqueEntryKeys.contains(uniqueKeyForEntry)) {
                    Map<String, Object> saleOrderEntry = new HashMap<>();
                    saleOrderEntry.put("Id", entryIdForEntry);
                    saleOrderEntry.put("PKIDX", pkidxForEntry);
                    saleOrderEntries.add(saleOrderEntry);
                    uniqueEntryKeys.add(uniqueKeyForEntry);
                }

                // SaleOrderPlan
                String entryIdForPlan = rawData.get(5).toString();
                String pkidxForPlan = rawData.get(6).toString();
                String uniqueKeyForPlan = entryIdForPlan + "-" + pkidxForPlan;

                if (!uniquePlanKeys.contains(uniqueKeyForPlan)) {
                    Map<String, Object> saleOrderPlan = new HashMap<>();
                    saleOrderPlan.put("Id", entryIdForPlan);
                    saleOrderPlan.put("PKIDX", pkidxForPlan);
                    saleOrderPlans.add(saleOrderPlan);
                    uniquePlanKeys.add(uniqueKeyForPlan);
                }
            }

            currentPageSize = result.getData().size();
            offset += currentPageSize;
        }
        k3Model.put("SaleOrderEntry", saleOrderEntries);
        k3Model.put("SaleOrderPlan", saleOrderPlans);

        ViewResult viewResult = new ViewResult();
        ViewResult.Result result = new ViewResult.Result();
        result.setResult(k3Model);
        viewResult.setResult(result);

        return Result.newSuccess(viewResult);
    }
}




