package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.TimeUtils;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpU8EaiConfigDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpU8EaiConfigEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpU8EaiConfigExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8EaiConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>EAI代理服务器专用组件</p>
 * @dateTime 2021-11-16 19:58
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_U8_EAI)
public class U8EaiDataManager extends BaseErpDataManager {

    private final String labelFormat="<%s>%s</%s>\n";

    @Autowired
    ProxyHttpClient proxyHttpClient;

    @Resource(name = "okHttpSupport")
    private OkHttpSupport okHttpSupport;

    @Autowired
    private ErpU8EaiConfigDao erpU8EaiConfigDao;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static List<String> filterFileds=new ArrayList<String>(){{
        add("tenant_id");
        add("_id");
        add("object_describe_api_name");
        add("masterId");
        add("owner");
        add("created_by");
        add("detailId");
        add("masterDetail");
    }};

    /**
     * <p>map转xml</p>
     * @dateTime 2021-11-16 19:59
     * <AUTHOR>
     * @version 1.0
     */
    private String mapToXml(String label,Map<String,Object>dataMap){
        StringBuilder body=new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(label)){
            body.append(String.format("<%s>",label));
        }
        dataMap.forEach((key,v)->{
            if (!filterFileds.contains(key)){
                if (v!=null){
                    body.append(String.format(labelFormat, key,v,key));
                }else{
                    body.append(String.format(labelFormat, key,"",key));
                }
            }
        });
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(label)) {
            body.append(String.format("</%s>", label));
        }
        return body.toString();
    }

    private ErpU8EaiConfigEntity loadErpU8EaiConfigByTenantIdAndObjApiName(String tenantId,String objApiName,String dataCenterId){
        ErpU8EaiConfigEntity eaiConfigEntity =
          erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getEaiConfigByTenantAndObjApiName(tenantId, objApiName, dataCenterId);
        List<ErpU8EaiConfigEntity> detailEeiConfigEntities =
          erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryEaiConfigByTenantAndParentObjApiName(tenantId, objApiName, dataCenterId);
        if (!CollectionUtils.isEmpty(detailEeiConfigEntities)){
            for (ErpU8EaiConfigEntity entity : detailEeiConfigEntities) {
                eaiConfigEntity.getDetails().put(entity.getObjApiName(),entity);
            }
        }
        return eaiConfigEntity;
    }

    /**
     * <p>标准格式数据转xml</p>
     * @dateTime 2021-11-16 19:59
     * <AUTHOR>
     * @version 1.0
     */
    private String dataToXml(ErpU8EaiConfigEntity eaiConfigEntity,String sender,String receiver,StandardData standardData,String opreration){
        Map<String,Object> masterFieldVal = standardData.getMasterFieldVal();

        String headerLable = eaiConfigEntity.getLabel();
        String headerBodyWrap=headerLable;
        if (headerLable.contains(".")){
            String[] split = headerLable.split("\\.");
            headerLable=split[1];
            headerBodyWrap=split[0];
        }else {
            headerLable=null;
        }

        StringBuilder requestBody=new StringBuilder(String.format("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"
          + "<ufinterface sender=\"%s\" receiver=\"%s\" roottag=\"%s\" proc=\"%s\">",sender,receiver,headerBodyWrap,opreration));

        requestBody.append(String.format("<%s>",headerBodyWrap));

        String masterBody = mapToXml(headerLable,masterFieldVal);
        requestBody.append(masterBody);

        Map<String, List<ObjectData>> detailFieldVals = standardData.getDetailFieldVals();
        for (Map.Entry<String, ErpU8EaiConfigEntity> eaiConfigEntry : eaiConfigEntity.getDetails().entrySet()) {
            ErpU8EaiConfigEntity eaiConfig=eaiConfigEntry.getValue();

            String label = eaiConfig.getLabel();
            StringBuilder detailBody=new StringBuilder();
            String detailBodyWrap=null;
            if (label.contains(".")){
                String[] split = label.split("\\.");
                label=split[1];
                detailBodyWrap=split[0];
                detailBody.append(String.format("<%s>",detailBodyWrap));
            }
            List<ObjectData> dataMaps = detailFieldVals.get(eaiConfigEntry.getKey());
            if (CollectionUtils.isEmpty(dataMaps)){
                detailBody.append(String.format("</%s>",detailBodyWrap));
                requestBody.append(detailBody);
                continue;
            }
            for (Map<String, Object> dataMap : dataMaps) {
                String body = mapToXml(label, dataMap);
                detailBody.append(body);
            }
            if (detailBodyWrap!=null){
                detailBody.append(String.format("</%s>",detailBodyWrap));
            }
            requestBody.append(detailBody);
        }
        requestBody.append(String.format("</%s>",headerBodyWrap));
        requestBody.append("</ufinterface>");
        return requestBody.toString();
    }

    /**
     * <p>构建查询参数</p>
     * @dateTime 2021-11-16 20:13
     * <AUTHOR>
     * @version 1.0
     */
    private U8EaiRequestArg buildQueryRequestArg(ErpU8EaiConfigEntity eaiConfigEntity,Long startTime,Long endTime,Integer offset,Integer limit,String dataId){

        U8EaiRequestArg requestArg=new U8EaiRequestArg();
        requestArg.setStrategy("DB");
        String conditionFields = eaiConfigEntity.getDateTimeConditionField();
        if (StringUtils.isEmpty(conditionFields)){
            //从缓存拉取数据
            requestArg.setQueryDataFromCache(true);
        }

        String sql=eaiConfigEntity.getQuerySql();
        if (StringUtils.isEmpty(dataId)){
            requestArg.setOffset(offset);
            requestArg.setLimit(limit);
            requestArg.setEndTime(endTime);
            requestArg.setStartTime(startTime);
            String startTimeStr= TimeUtils.getDateTime(startTime);
            String endTimeStr= TimeUtils.getDateTime(endTime);
            if (!StringUtils.isEmpty(conditionFields)){
                StringBuilder conditons=new StringBuilder();
                String[] dateTimeConditionField = conditionFields.split(",");
                for (String field : dateTimeConditionField) {
                    if (conditons.length()==0){
                        conditons.append(field).append(">'").append(startTimeStr).append("' and ").append(field).append("<='").append(endTimeStr).append("'");
                    }else {
                        conditons.append(" or ").append(field).append(">'").append(startTimeStr).append("' and ").append(field).append("<='").append(endTimeStr).append("'");
                    }
                }
                sql=eaiConfigEntity.getQuerySql()+" and ("+conditons+" )";
            }
        }else {
            StringBuilder conditons=new StringBuilder();
            conditons.append(eaiConfigEntity.getDbKey()).append("='").append(dataId).append("'");
            sql=eaiConfigEntity.getQuerySql()+" and ("+conditons+" )";
        }
        requestArg.setMasterDataIdField(eaiConfigEntity.getIdField());
        requestArg.setMasterDataSql(sql);
        requestArg.setObjApiName(eaiConfigEntity.getObjApiName());

        Map<String,List<String>>detailSqls=new HashMap<>();

        eaiConfigEntity.getDetails().forEach((detailObjApiName,detail)->{
            detailSqls.put(detailObjApiName, Arrays.asList(detail.getQuerySql()));
        });
        requestArg.setDetailDataSql(detailSqls);

        return requestArg;
    }

    /**
     * <p>构建更新请求参数</p>
     * @dateTime 2021-11-16 20:13
     * <AUTHOR>
     * @version 1.0
     */
    private U8EaiRequestArg buildUpdateRequestArg(ErpU8EaiConfigEntity eaiConfigEntity, ErpConnectInfoEntity connectInfo,StandardData standardData,String operation){
        U8EaiConnectParam connectParam=JacksonUtil.fromJson(connectInfo.getConnectParams(),U8EaiConnectParam.class);
        U8EaiRequestArg requestArg=new U8EaiRequestArg();
        String eaiBody =dataToXml(eaiConfigEntity, connectParam.getSender(), connectParam.getReciver(),standardData,operation);
        requestArg.setStrategy("EAI");
        requestArg.setObjApiName(standardData.getObjAPIName());
        requestArg.setRequestEaiBody(eaiBody);
        requestArg.setSender(connectParam.getSender());
        requestArg.setReceiver(connectParam.getReciver());
        requestArg.setMasterDataIdField(eaiConfigEntity.getIdField());
        requestArg.setUseCombine(eaiConfigEntity.getUseCombine());
        Map<String, ErpU8EaiConfigEntity> details = eaiConfigEntity.getDetails();
        Map<String,List<String>>detailObjSqls=new HashMap<>();
        for (Map.Entry<String, ErpU8EaiConfigEntity> entry : details.entrySet()) {
            if (!standardData.getDetailFieldVals().containsKey(entry.getKey())){
                continue;
            }
            String selectIdByKeySql = entry.getValue().getQueryIdSql();
            List<String> condtionFields = getCondtionFields(selectIdByKeySql);
            List<String>detailSqls=new ArrayList<>();
            for (ObjectData objectData : standardData.getDetailFieldVals().get(entry.getKey())) {
                String detailSql=String.valueOf(selectIdByKeySql);
                for (String field : condtionFields) {
                    String key=field.replace("#","");
                    Object fieldV = objectData.get(key);
                    if(StringUtils.isEmpty(fieldV)){
                        log.warn("明细【{}】必填字段【{}】为空",entry.getKey(),field);
                        detailSql=detailSql.replaceAll(field,"");
                        continue;
                    }
                    detailSql=detailSql.replaceAll(field,fieldV.toString());
                }
                detailSqls.add(detailSql);
            }
            detailObjSqls.put(entry.getKey(),detailSqls);
        }
        requestArg.setDetailIdQuerySql(detailObjSqls);
        explainExtendConfig(requestArg,operation,eaiConfigEntity,standardData);
        return requestArg;
    }

    private void explainExtendConfig(U8EaiRequestArg requestArg,String operation, ErpU8EaiConfigEntity erpU8EaiConfigEntity, StandardData standardData){

        List<String> exceuteSqlList = getExceuteSqlList(operation, erpU8EaiConfigEntity.getExtend());
        exceuteSqlList=exceuteSqlList.stream().filter(s->!StringUtils.isEmpty(s)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(exceuteSqlList)){
            List<String>masterSqls=new ArrayList<>();
            ObjectData objectData=standardData.getMasterFieldVal();
            for (String sql : exceuteSqlList) {
                masterSqls.addAll(fillSqlValues(sql,Arrays.asList(objectData)));
            }
            requestArg.setExcuteMasterSqlList(masterSqls);
            requestArg.setStrategy("DB");
            List<String> sqls = fillSqlValues(erpU8EaiConfigEntity.getQueryIdSql(), Arrays.asList(objectData));
            if (!CollectionUtils.isEmpty(sqls)){
                requestArg.setMasterIdQuerySql(sqls.get(0));
            }
        }

        Map<String,List<String>>detailSqlMaps=new HashMap<>();

        for (Map.Entry<String, ErpU8EaiConfigEntity> entityEntry : erpU8EaiConfigEntity.getDetails().entrySet()) {
            List<String> exceuteDetailSqlList = getExceuteSqlList(operation, entityEntry.getValue().getExtend());
            List<String>detailSqls=new ArrayList<>();
            exceuteDetailSqlList=exceuteDetailSqlList.stream().filter(s->!StringUtils.isEmpty(s)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(exceuteDetailSqlList)){
                List<ObjectData> objectDatas=standardData.getDetailFieldVals().get(entityEntry.getKey());
                for (String sql : exceuteDetailSqlList) {
                    detailSqls.addAll(fillSqlValues(sql,objectDatas));
                }
                requestArg.setStrategy("DB");
            }
            detailSqlMaps.put(entityEntry.getKey(),detailSqls);
        }
        if (!CollectionUtils.isEmpty(detailSqlMaps)){
            requestArg.setExcuteDetailSqlList(detailSqlMaps);
        }
    }

    private List<String> fillSqlValues(String sql, List<ObjectData> objectDatas){
        List<String>sqls=new ArrayList<>();
        if (StringUtils.isEmpty(sql)){
            return Collections.emptyList();
        }
        for (ObjectData objectData : objectDatas) {
            List<String> fields = getCondtionFields(sql);
            String newSql=sql;
            for (String field : fields) {
                String key=field.replace("#","");
                Object fieldV = objectData.get(key);
                newSql=fieldV==null?newSql.replace(field,null):newSql.replace(field,fieldV.toString());
            }
            sqls.add(newSql);
        }
        return sqls;
    }

    private List<String> getExceuteSqlList(String operation, String extend){
        if (StringUtils.isEmpty(extend)){
            return Collections.emptyList();
        }
        ErpU8EaiConfigExtendDto erpU8EaiConfigExtendDto=JacksonUtil.fromJson(extend,ErpU8EaiConfigExtendDto.class);
        List<String>exceteSql=Collections.emptyList();
        switch (operation){
            case "add":
                exceteSql = erpU8EaiConfigExtendDto.getInsertSqlList();
                break;
            default:
                exceteSql = erpU8EaiConfigExtendDto.getUpdateSqlList();
                break;
        }
        return exceteSql;
    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if (timeFilterArg.getOperationType()== EventTypeEnum.INVALID.getType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
            return Result.newSuccess(new StandardListData());
        }
        U8EaiConnectParam connectParam=JacksonUtil.fromJson(connectInfo.getConnectParams(),U8EaiConnectParam.class);
        ErpU8EaiConfigEntity u8EaiConfigEntity =loadErpU8EaiConfigByTenantIdAndObjApiName(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(), connectInfo.getId());
        U8EaiRequestArg requestArg =
          buildQueryRequestArg(u8EaiConfigEntity, timeFilterArg.getStartTime(), timeFilterArg.getEndTime(), timeFilterArg.getOffset(), timeFilterArg
            .getLimit(),null);
        String requestBody = JacksonUtil.toJson(requestArg);

        InterfaceMonitorData
          interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(timeFilterArg.getTenantId(),
                connectInfo.getId(),
                timeFilterArg.getObjAPIName(),
          type.name());

        interfaceMonitorData.setTimeFilterArg(timeFilterArg);

        String requestUrl=connectParam.getBaseUrl()+"/queryMasterBatch";
        String loginUrl=connectParam.getBaseUrl()+"/login";
        return postWithMonitor(loginUrl,connectParam.getUserName(),connectParam.getPassword(),
          requestUrl, requestBody, new HashMap<>(), interfaceMonitorData,
                new TypeReference<Result<StandardListData>>() {},timeFilterArg, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT,timeFilterArg.getTenantId());
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        U8EaiConnectParam connectParam=JacksonUtil.fromJson(connectInfo.getConnectParams(),U8EaiConnectParam.class);

        ErpU8EaiConfigEntity u8EaiConfigEntity =loadErpU8EaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(),standardData.getObjAPIName(), connectInfo.getId());

        U8EaiRequestArg requestArg = buildUpdateRequestArg(u8EaiConfigEntity, connectInfo, standardData, "add");
        String requestBody = JacksonUtil.toJson(requestArg);

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(connectInfo.getTenantId(),
                connectInfo.getId(),
                standardData.getObjAPIName(),
          ErpObjInterfaceUrlEnum.create.name());
        String loginUrl=connectParam.getBaseUrl()+"/login";
        return postWithMonitor(loginUrl,connectParam.getUserName(),connectParam.getPassword(),connectParam.getBaseUrl()+"/create",
                requestBody,new HashMap<>(),interfaceMonitorData,new TypeReference<Result<ErpIdResult>>(){},null, null,connectInfo.getTenantId());
    }

    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    private List<String> getCondtionFields(String sql){
        List<String>fields=new ArrayList<>();
        String rule="'.*?'";
        Pattern pattern = Pattern.compile(rule);
        Matcher matcher = pattern.matcher(sql);
        while (matcher.find()){
            String field = matcher.group();
            field=field.substring(1,field.length()-1);
            fields.add(field);
        }
        fields.remove("#pid");
        fields.remove("#parentId");
        return fields;
    }


    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        U8EaiConnectParam connectParam=JacksonUtil.fromJson(connectInfo.getConnectParams(),U8EaiConnectParam.class);

        ErpU8EaiConfigEntity u8EaiConfigEntity =loadErpU8EaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(),standardData.getObjAPIName(), connectInfo.getId());

        U8EaiRequestArg requestArg = buildUpdateRequestArg(u8EaiConfigEntity, connectInfo, standardData, "edit");
        String requestBody = JacksonUtil.toJson(requestArg);

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(connectInfo.getTenantId(),
                connectInfo.getId(),
                standardData.getObjAPIName(),
          ErpObjInterfaceUrlEnum.update.name());
        String loginUrl=connectParam.getBaseUrl()+"/login";
        return postWithMonitor(loginUrl,connectParam.getUserName(),connectParam.getPassword(),connectParam.getBaseUrl()+"/update",
                requestBody,new HashMap<>(),interfaceMonitorData,new TypeReference<Result<ErpIdResult>>(){},null,
                null,connectInfo.getTenantId());
    }

    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**审核erp对象*/
    public Result<String> verifyErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        if (StringUtils.isEmpty(erpIdArg.getDataId())){
            return Result.newSystemError(I18NStringEnum.s37);
        }
        U8EaiConnectParam connectParam=JacksonUtil.fromJson(connectInfo.getConnectParams(),U8EaiConnectParam.class);

        ErpU8EaiConfigEntity u8EaiConfigEntity =loadErpU8EaiConfigByTenantIdAndObjApiName(erpIdArg.getTenantId(),erpIdArg.getObjAPIName(), connectInfo.getId());
        U8EaiRequestArg requestArg =
          buildQueryRequestArg(u8EaiConfigEntity, null,null, null, null,erpIdArg.getDataId());
        String requestBody = JacksonUtil.toJson(requestArg);
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectInfo.getId(),
                erpIdArg.getObjAPIName(),
          ErpObjInterfaceUrlEnum.queryMasterById.name());
        String loginUrl=connectParam.getBaseUrl()+"/login";
        return postWithMonitor(loginUrl,connectParam.getUserName(),connectParam.getPassword(),connectParam.getBaseUrl()+"/queryMasterById",
          requestBody,new HashMap<>(),interfaceMonitorData,new TypeReference<Result<StandardData>>(){},null,
                ConfigCenter.CONTENT_LENGTH_LIMIT,connectInfo.getTenantId());
    }

    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 5)
    public String getCookie(String url, String username, String password, int tries, String tenantId) throws IOException {
        FormBody.Builder formb = new FormBody.Builder();
        formb.add("username", username);
        formb.add("password", password);
        Request request = new Request.Builder()
                .url(url)
                .post(formb.build())
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        try {
            Object result = this.okHttpSupport.syncExecute(request, new SyncCallback() {
                @Override
                public Object response(Response response) {
                    if (!response.isSuccessful()) {
                        log.warn("登陆失败错误：{}", response.message());
                        return Result.newErrorByI18N(ResultCodeEnum.SYSTEM_ERROR,
                                String.format(I18NStringEnum.s1211.getI18nValue(), response.message()),
                                I18NStringEnum.s1211.getI18nKey(),
                                Lists.newArrayList(response.message()));
                    } else {
                        Headers requestHeader = response.headers();
                        return Result.newSuccess(requestHeader.get("Set-Cookie"));
                    }
                }
            });
            Result<String> cookieRs = (Result<String>) result;
            if (StringUtils.isEmpty(cookieRs.getData()) && tries == 1) {
                return getCookie(url, username, password, 2, tenantId);
            }
            return cookieRs.getData();
        } catch (Exception e) {
            log.warn("登陆u8 eai 获取cookie失败：{}", e);
            return "";
        }
    }

    private <T>Result<T> postWithMonitor(String loginUrl,String username,String password,String requestUrl,String requestBody,
                                         Map<String,String> header,InterfaceMonitorData interfaceMonitorData,TypeReference<Result<T>>  t,
                                         TimeFilterArg timeFilterArg,Long rspReadLimitLenByte, String tenantId){
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        Integer status = 1;
        String rspStr = "";
        HttpRspLimitLenUtil.ResponseBodyModel response=null;
        String traceId= TraceUtil.get();
        if (StringUtils.isEmpty(traceId)){
            traceId="";
        }
        header.put("traceId",traceId);
        try {
            String cookie = getCookie(loginUrl, username, password,1,tenantId);
            if (StringUtils.isEmpty(cookie)){
                return Result.newSystemError(I18NStringEnum.s42);
            }
            header.put("Cookie",cookie);
            response=postUrl(requestUrl,requestBody,header,rspReadLimitLenByte);
            if(response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}",tenantId);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            rspStr = response.getBody();
            Result<T>  result = JacksonUtil.fromJson(response.getBody(), t);
            if (!result.isSuccess()){
                status=2;
                //请求失败
                String resultMsg = result.getErrMsg();
                log.warn("request failed,requestUrl:{},headerMap:{},requestBody:{},response:{}", requestUrl,header, requestBody,  response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, result.getErrCode() + "," + resultMsg);
            }
            return result;
        }catch (Exception e){
            status=2;
            rspStr = e.getMessage();
            log.error("request error,requestUrl:{},headerMap:{},requestBody:{},response:{}", requestUrl, header, requestBody, response, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        }finally {
            stopWatch.stop();
            if(null != response) {
                rspStr = response.toString();
            }
            BaseErpDataManager.saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(),
                    interfaceMonitorData.getDcId(), interfaceMonitorData.getObjApiName(),
                    interfaceMonitorData.getType(), requestBody, rspStr, status, callTime, System.currentTimeMillis(),
              "", TraceUtil.get(), stopWatch.getTotalTimeMillis(), timeFilterArg);
        }
    }

    private HttpRspLimitLenUtil.ResponseBodyModel postUrl(String requestUrl, Object requestBody, Map<String, String> header,Long rspReadLimitLenByte) {
        HttpRspLimitLenUtil.ResponseBodyModel response=null;
        try {
            response=proxyHttpClient.postUrl(requestUrl,requestBody,header, rspReadLimitLenByte);
        } catch (Exception e1) {
            log.info("U8EaiDataManager.postUrl,e1={}",e1.getMessage());
            try {
                response=proxyHttpClient.postUrl(requestUrl,requestBody,header,rspReadLimitLenByte);
            } catch (Exception e2) {
                log.info("U8EaiDataManager.postUrl,e2={}",e2.getMessage());
                response=proxyHttpClient.postUrl(requestUrl,requestBody,header,rspReadLimitLenByte);
            }
        }
        return response;
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if(erpObjDataResult!=null){
            Result<List<StandardData>> dataList=new Result<>();
            if(erpObjDataResult.getData()!=null){
                dataList.setData(Lists.newArrayList(erpObjDataResult.getData()));
                return dataList;
            }else {
                dataList.setData(Lists.newArrayList());
                return dataList;
            }
        }
        return null;
    }

}
