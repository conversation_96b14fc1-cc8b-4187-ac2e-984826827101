package com.fxiaoke.open.erpsyncdata.web.interceptor;

import com.alibaba.fastjson.JSON;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.fxiaoke.open.erpsyncdata.web.interceptor.SuperAdminTokenUtil.erpDssToken;

@Slf4j
public class UserInterceptors extends HandlerInterceptorAdapter {
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private UserRoleManager userRoleManager;

    // 解除熔断接口,会发企信消息,所以不能修改url.需要管理员权限
    public static final String BREAK_ID_PREFIX = "erp/syncdata/out/break/getbyid";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //保险一点，每次解析身份前先清空身份
        UserContextHolder.remove();
        String requestURI = request.getRequestURI();
        if (requestURI.startsWith("/cep")) {
            //cep校验
            return checkCep(request);
        }
        // 免权限url,直接返回
        if (notNeedUser(requestURI)) {
            return true;
        }
        //先尝试从cookie获取身份，再去做检查
        UserVo userVo = tryGetUserFromCookie(request);
        if (requestURI.startsWith("/erp/syncdata/superadmin")) {
            //校验超级管理员身份
            userVo = checkSuperAdminUser(request, response, userVo);
            if (userVo != null) {
                //具有超级管理员身份
                UserContextHolder.setUserVo(userVo);
                return true;
            } else {
                return responseNoUserResult(request, response);
            }
        }
        if (requestURI.startsWith("/erp/syncdata/fstool")) {
            //校验纷享人员身份
            userVo = checkFsUser(userVo);
            if (userVo != null) {
                //具有纷享员工身份身份
                UserContextHolder.setUserVo(userVo);
                return true;
            } else {
                return responseNoUserResult(request, response);
            }
        }
        //校验企业管理员身份
        return checkTenantUser(request, response, userVo);
    }


    private static boolean checkCep(HttpServletRequest request) {
        //cep解析身份
        UserVo userVo = new UserVo();
        userVo.setEnterpriseAccount(request.getHeader("X-fs-Enterprise-Account"));
        String eiHeader = request.getHeader("X-fs-Enterprise-Id");
        if (eiHeader != null) {
            //正常调用都不会传空ei
            userVo.setEnterpriseId(Integer.valueOf(eiHeader));
        }
        String employeeId = request.getHeader("X-fs-Employee-Id");
        if (Objects.nonNull(employeeId)) {
            userVo.setEmployeeId(Integer.valueOf(employeeId));
        } else {
            userVo.setEmployeeId(-10000);
        }
        UserContextHolder.setUserVo(userVo);
        return true;
    }

    /**
     * 校验企业用户身份
     */
    private boolean checkTenantUser(HttpServletRequest request, HttpServletResponse response, UserVo userVo) {
        //无身份或身份不合法
        if (userVo == null) {
            return responseNoUserResult(request, response);
        }
        if (StringUtils.isBlank(userVo.getEnterpriseAccount())) {
            return responseNoUserResult(request, response);
        }
        if (userVo.getEmployeeId() == null || userVo.getEmployeeId() < 0) {
            return responseNoUserResult(request, response);
        }
        //校验环境
        String tenantId = String.valueOf(userVo.getEnterpriseId());
        // 临时校验角色信息(crm管理员/系统管理员),后续需要改为cep调用接口,使用cep鉴权
        final String employeeId = String.valueOf(userVo.getEmployeeId());
        if (!userRoleManager.checkAdminAuth(tenantId, employeeId)) {
            return errorResponse(request, response, ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        //设置线程变量, 填充dcId
        UserContextHolder.setUserVo(userVo);
        fillDcId(request);
        return true;
    }

    private UserVo tryGetUserFromCookie(HttpServletRequest request) {
        String fsAuthXcookie = getFsAuthXCookie(request.getCookies());
        log.info("get cookie = {}", fsAuthXcookie);
        if (fsAuthXcookie != null) {
            CookieToAuth.Argument argument = new CookieToAuth.Argument();
            argument.setCookie(fsAuthXcookie);
            argument.setFsToken(null);
            argument.setIp(null);
            try {
                CookieToAuth.Result<AuthXC> cookieToAuthResult = activeSessionAuthorizeService.cookieToAuthXC(argument);
                if (cookieToAuthResult.isSucceed()
                        && ValidateStatus.NORMAL.equals(cookieToAuthResult.getValidateStatus())) {
                    //成功从cookie获取身份
                    AuthXC authXC = cookieToAuthResult.getBody();
                    log.info("authXC={}", authXC);
                    return new UserVo(authXC);
                } else {
                    log.warn("activeSessionAuthorizeService.cookieToAuthXC failed, argument={}, resultUser=[{}]", argument, cookieToAuthResult);
                    return null;
                }
            } catch (Exception e) {
                //降级
                log.warn("activeSessionAuthorizeService.cookieToAuthXC exception, argument={}", argument, e);
                return null;
            }
        }
        return null;
    }

    private UserVo checkSuperAdminUser(HttpServletRequest request, HttpServletResponse response, UserVo userVo) {
        if (userVo != null) {
            //通过fsCookie获取的身份验证
            String fullUser = userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
            if (ConfigCenter.SUPER_ADMINS.contains(fullUser)) {
                return userVo;
            }
        }
        //线下环境通过。需要：1. 设置jvm参数-Derpdss.skip.super.auth=true, 2. 设置系统变量ENVIRONMENT_TYPE=firstshare
        if (Objects.equals(SystemUtils.getRuntimeEnv(), SystemUtils.RuntimeEnv.FIRSTSHARE)
                && Objects.equals(System.getProperty("erpdss.skip.super.auth"), "true")) {
            if (userVo == null) {
                //如果用户为空，赋值一个默认用户防止后面报错
                //赋值一个默认身份，不然一些逻辑有问题
                userVo = new UserVo();
                userVo.setEnterpriseAccount("83952");
                userVo.setEnterpriseId(83952);
                userVo.setEmployeeId(1000);
            }
            return userVo;
        }
        //通过token验证
        Cookie oldCookie = findCookie(request.getCookies(), erpDssToken);
        if (oldCookie == null || oldCookie.getValue() == null) {
            return null;
        }
        userVo = SuperAdminTokenUtil.parseToken(oldCookie.getValue());
        if (userVo == null) {
            return null;
        }
        int maxAge = (int) TimeUnit.MINUTES.toSeconds(10L);
        //鉴权成功，设置到cookie里面。
        //检查cookie超时时间，
        int currentAge = oldCookie.getMaxAge();
        if (currentAge < maxAge - 60) {
            //已经使用超过1分钟，重新设置一下cookie
            String newToken = SuperAdminTokenUtil.generateToken(userVo);
            Cookie cookie = new Cookie(erpDssToken, newToken);
            cookie.setMaxAge(maxAge);
            cookie.setPath("/erp/syncdata/superadmin");
            response.addCookie(cookie);
        }
        //token验证通过
        return userVo;
    }


    private UserVo checkFsUser(UserVo userVo) {
        //ei为1的通过
        if (userVo != null && userVo.getEnterpriseId() == 1) {
            return userVo;
        }
        //112环境任意身份可通过。
        if (Objects.equals(System.getProperty("process.profile"), "fstest")) {
            if (userVo == null) {
                //如果用户为空，赋值一个默认用户防止后面报错
                //赋值一个默认身份，不然一些逻辑有问题
                userVo = new UserVo();
                userVo.setEnterpriseAccount("83952");
                userVo.setEnterpriseId(83952);
                userVo.setEmployeeId(1000);
            }
            return userVo;
        }
        return null;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        //防止内存泄露
        UserContextHolder.remove();
    }

    /**
     * 装填数据中心id
     *
     * @param request
     */
    private void fillDcId(HttpServletRequest request) {
        String header = request.getHeader("esd-dcid");
        UserContextHolder.getUserVo().setDataCenterId(header);
    }

    /**
     * 获取用户信息失败时返回json
     *
     * @param request  request
     * @param response response
     */
    private boolean responseNoUserResult(HttpServletRequest request, HttpServletResponse response) {
        if (notNeedUser(request.getRequestURI())) {
            return true;
        }
        final ResultCodeEnum resultCode = ResultCodeEnum.NO_USER;
        return errorResponse(request, response, resultCode);
    }

    private static boolean errorResponse(final HttpServletRequest request, final HttpServletResponse response, final ResultCodeEnum resultCode) {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        Result<Void> result = new Result<>(resultCode);
        PrintWriter writer;
        try {
            writer = response.getWriter();
        } catch (IOException e) {
            log.warn("httpServletResponse error. uri[{}]", request.getRequestURI(), e);
            return false;
        }
        writer.append(JSON.toJSONString(result));
        writer.flush();
        writer.close();
        return false;
    }

    /**
     * 是否不需要登录身份
     *
     * @param uri
     * @return
     */
    private boolean notNeedUser(String uri) {
        log.info("uri={}", uri);
        return uri.startsWith("/dataCompare")//monitor模块的
                || uri.startsWith("/erp/syncdata/probedata")//task模块的
                || uri.startsWith("/erp/syncdata/noAuth")
                || uri.startsWith("/swagger")
                || uri.startsWith("/webjars/springfox-swagger-ui")
                || uri.startsWith("/inner")
                || uri.startsWith("/erp/syncdata/static")
                || uri.startsWith("/erp/syncdata/open")
                || (uri.startsWith("/erp/syncdata/out")
                && !uri.startsWith(BREAK_ID_PREFIX));
    }


    /**
     * cookies中获取FsAuthX
     *
     * @param cookies
     * @return
     */
    private Cookie findCookie(Cookie[] cookies, String cookieName) {
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookieName.equals(cookie.getName())) {
                    return cookie;
                }
            }
        }
        return null;
    }

    /**
     * cookies中获取FsAuthX
     *
     * @param cookies
     * @return
     */
    private String getFsAuthXCookie(Cookie[] cookies) {
        if (cookies == null) {
            return null;
        } else {
            for (Cookie cookie : cookies) {
                if ("FSAuthXC".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
            return null;
        }
    }
}
