package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryFieldDescArg;
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.admin.service.FsCrmObjectService;
import com.fxiaoke.open.erpsyncdata.admin.utils.ResultConversionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("queryFieldDesc")
public class QueryFieldDesc implements CustomFunctionCommonService {

    @Autowired
    private FsCrmObjectService fsCrmObjectService;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpObjectService erpObjectService;
    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId=commonArg.getTenantId();
        QueryFieldDescArg arg = JsonUtil.fromJson(commonArg.getParams(), QueryFieldDescArg.class);

        if (TenantType.CRM.equals(arg.getObjectType())) {//crm
            Result<ListObjectFieldsResult> crmObjectResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, arg.getObjectApiName(),null);
            return Result.newSuccess(JsonUtil.toJson(crmObjectResult.getData()));

        } else if (TenantType.ERP.equals(arg.getObjectType())) {//erp
            Result<ErpObjectDescResult> erpObjectDescResult = erpObjectService.queryErpObjectByObjApiName(tenantId, arg.getDataCenterId(), -10000, arg.getObjectApiName());
            if (!erpObjectDescResult.isSuccess() || Objects.isNull(erpObjectDescResult.getData())) {
                return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
            }
            ErpObjectApiNameArg queryArg = new ErpObjectApiNameArg();
            queryArg.setErpObjectApiName(arg.getObjectApiName());
            Result<List<ErpObjectFieldResult>> listResult = erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(tenantId, -10000, queryArg, arg.getDataCenterId());
            ListObjectFieldsResult listObjectFieldsResult = new ListObjectFieldsResult();
            if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                listObjectFieldsResult = ResultConversionUtil.converseErpObjFelds(tenantId,listResult.getData());
                listObjectFieldsResult.setObjectName(erpObjectDescResult.getData().getErpObjectName());
                listObjectFieldsResult.setObjectApiName(erpObjectDescResult.getData().getErpObjectApiName());
            }
            return Result.newSuccess(JsonUtil.toJson(listObjectFieldsResult));
        } else {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
    }

}
