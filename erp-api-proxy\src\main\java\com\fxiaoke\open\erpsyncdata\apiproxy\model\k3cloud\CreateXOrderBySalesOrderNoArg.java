package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:12 2021/7/2
 * @Desc:
 */
@Data
public class CreateXOrderBySalesOrderNoArg implements Serializable {
    @SerializedName("saveXSaleOrderArgs")
    @JsonProperty("saveXSaleOrderArgs")
    private SaveXSaleOrderArgs saveXSaleOrderArgs=new SaveXSaleOrderArgs();

    @Data
    public static class SaveXSaleOrderArgs {

//        @SerializedName("SaleOrderBillNo")
//        @JsonProperty("SaleOrderBillNo")
//        private String saleOrderBillNo;

        @SerializedName("SaleOrderBillId")
        @JsonProperty("SaleOrderBillId")
        private String saleOrderBillId;

//        @SerializedName("SOEntryIds")
//        @JsonProperty("SOEntryIds")
//        private List<String> sOEntryIds= Lists.newArrayList();

    }
}
