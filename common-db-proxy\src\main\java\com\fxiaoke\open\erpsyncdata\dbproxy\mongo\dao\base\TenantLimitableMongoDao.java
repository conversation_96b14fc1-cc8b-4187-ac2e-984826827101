package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base;

import cn.hutool.core.date.DateTime;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;

import java.util.Date;

/**
 * 可根据企业id限量的mongo dao
 *
 * <AUTHOR> (^_−)☆
 * @date 2023-06-29
 */
public interface TenantLimitableMongoDao {

    String getCollPrefix();
    /**
     * 获取集合统计信息
     */
    CollStat getCollStat(String tenantId);

    /**
     * 查找集合最早时间，过期时间字段
     */
    DateTime findMinDate(String tenantId);

    /**
     * 根据时间段删除集合
     *
     * @return 删除的数量
     */
    Long deleteBetween(String tenantId, Date beginDate, Date endDate);

}
