package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/23
 **/
public class DBExcelUtils {

    public static List<List<String>> map2DataList(List<String> fields, List<Map<String, Object>> dataMapList){
        List<List<String>> dataList = new ArrayList<>();
        for (Map<String, Object> dataMap : dataMapList) {
            List<String> data = new ArrayList<>();
            dataList.add(data);
            for (String field : fields) {
                data.add(dataMap.getOrDefault(field,"--").toString());
            }
        }
        return dataList;
    }

    /**
     * 默认样式
     * 内容居中
     * @return
     */
    public static HorizontalCellStyleStrategy getDefaultStyle() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }

    /**
     * 对SheetName进行特殊处理，解决SheetName过长导致的问题
     * SheetName最长支持32个字符，超过会被截断
     * @param sheetName
     * @return
     */
    public static String getSheetName(String sheetName) {
        if(sheetName.length()<=32) return sheetName;
        int len = sheetName.length();
        int tailLen = 6;
        int headLen = 32 - tailLen-2;
        String tail = sheetName.substring(len-tailLen,len);
        String head = sheetName.substring(0,headLen);

        return head+"@"+tail;
    }

    public static void main(String[] args) {
        String str = "object_3521fabcdefghigklmnopqrst__c";
        String sheetName = getSheetName(str);
        System.out.println(sheetName);
    }
}
