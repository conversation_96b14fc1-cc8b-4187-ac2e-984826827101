package com.fxiaoke.open.erpsyncdata.dbproxy.dao;


import cn.hutool.core.lang.Dict;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface DDLDao extends BaseTenantDao<Void,DDLDao> {
    List<Dict> listIndexes(@Param("tableName") String tableName);
    int createIndexes(@Param("table") String table,@Param("index") String index,@Param("where") String where);
    int dropIndexes(@Param("index") String index);
}
