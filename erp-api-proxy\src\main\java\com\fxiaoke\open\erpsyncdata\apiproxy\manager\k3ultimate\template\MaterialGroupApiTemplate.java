package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import org.springframework.stereotype.Component;

@Component
public class MaterialGroupApiTemplate extends K3UltimateBaseApiTemplate {
    @Override
    public String getObjApiName() {
        return K3UltimateObjApiName.bd_materialgroup;
    }

    @Override
    public String getBatchQueryApi() {
        return "/kapi/v2/basedata/bd_materialgroup/batchQuery";
    }

    @Override
    public String getBatchAddApi() {
        return "/kapi/v2/basedata/bd_materialgroup/batchAdd";
    }

    @Override
    public String getBatchUpdateApi() {
        return "/kapi/v2/basedata/bd_materialgroup/batchUpdate";
    }

    @Override
    public String getBatchSubmitApi() {
        return null;
    }

    @Override
    public String getBatchUnSubmitApi() {
        return null;
    }

    @Override
    public String getBatchAuditApi() {
        return null;
    }

    @Override
    public String getBatchUnAuditApi() {
        return null;
    }

    @Override
    public String getBatchEnableApi() {
        return null;
    }

    @Override
    public String getBatchDisableApi() {
        return null;
    }

    @Override
    public String getBatchDeleteApi() {
        return null;
    }
}
