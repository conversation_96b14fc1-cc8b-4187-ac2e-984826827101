package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.DynamicThresholdFilter;
import ch.qos.logback.core.spi.FilterReply;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.slf4j.Marker;

/**
 * <AUTHOR>
 * @create 2023/8/1 14:28
 * @desc 区别历史数据同步，不打印日志
 *
 */
public class LogConsoleStrategyFilter extends DynamicThresholdFilter {
    private static String HISTORY_VALUE_KEY="erp_data_receive_type";
    private static String NEED_PRINT_LOG_TENANT="need_print_log_tenant";

    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {
        // 判断 dataReceiverType,历史刷数据的则deny
        String dataReceiverType = getDataReceiverType();
        if (String.valueOf(DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType()).equals(dataReceiverType)&&disableExecuteByTenantId()) {
            return FilterReply.DENY; // 将日志降级为 DEBUG 级别
        }
        return FilterReply.NEUTRAL; // 保持中立，继续后续处理
    }

    private String getDataReceiverType() {
        // 从 MDC 获取 HISTORY_VALUE_KEY 的逻辑
        String history_value_key = MDC.get(HISTORY_VALUE_KEY);
        return history_value_key;
    }
    private Boolean disableExecuteByTenantId() {
        // 从 MDC 获取 HISTORY_VALUE_KEY 的逻辑
        String tenantId = MDC.get(NEED_PRINT_LOG_TENANT);
        if(StringUtils.isNotEmpty(tenantId)){
            return false;
        }
        return true;
    }
    public static  void putDataReceiveType(Integer dataReceiveType) {
        // 从 MDC 保存 HISTORY_VALUE_KEY 的逻辑
       MDC.put(HISTORY_VALUE_KEY,String.valueOf(dataReceiveType));

    }
    public static  void putTenantId(String tenantId) {
        // 从 MDC 保存 NEED_PRINT_LOG_TENANT 的逻辑
        MDC.put(NEED_PRINT_LOG_TENANT,tenantId);

    }
    public static  void removeDataReceiveType() {
        // 从 MDC 删除 HISTORY_VALUE_KEY 的逻辑
        MDC.remove(HISTORY_VALUE_KEY);
        MDC.remove(NEED_PRINT_LOG_TENANT);

    }
}
