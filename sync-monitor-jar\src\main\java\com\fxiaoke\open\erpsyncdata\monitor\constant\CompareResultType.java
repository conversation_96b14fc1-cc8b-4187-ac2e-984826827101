package com.fxiaoke.open.erpsyncdata.monitor.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@AllArgsConstructor
public enum CompareResultType {
    SUCCESS("成功比对", I18NStringEnum.s969.getI18nKey()),
    GET_SRC_DATA_FAILED("获取源数据失败", I18NStringEnum.s970.getI18nKey()),
    GET_DEST_DATA_FAILED("获取目标数据失败", I18NStringEnum.s971.getI18nKey()),
    ;

    /**
     * 描述
     */
    public final String description;
    public final String i18nKey;
}
