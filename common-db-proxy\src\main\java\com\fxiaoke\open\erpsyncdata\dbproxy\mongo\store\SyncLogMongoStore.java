package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseExpireIndexLogMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseLogMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Repository
@Slf4j
public class SyncLogMongoStore extends BaseExpireIndexLogMongoStore<SyncLog> {

    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;

    protected final static String SYNC_LOG_PREFIX = "sync_log_";

    protected SyncLogMongoStore() {
        super(SingleCodecHolder.codecRegistry);
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(SyncLog.class, ErpTempData.class)
                        .automatic(true).build()));
    }

    @Override
    public String getCollName(String tenantId) {
        return SYNC_LOG_PREFIX + tenantId;
    }

    @Override
    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> toBeCreate = Lists.newArrayList();
        //过期自动清理时间,30天
        Bson idxExpire = expireIndex();
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(getExpireDay(tenantId), TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending("realObjApiName")
                , Indexes.ascending("streamId")
                , Indexes.ascending("type")
                , Indexes.ascending("status")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("index_filters").background(true)));

        Bson idxType2 = Indexes.compoundIndex(Indexes.ascending("realObjApiName")
                , Indexes.ascending("type")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxType2, new IndexOptions().name("index_filters2").background(true)));

        Bson idxLogId = Indexes.ascending("logId");
        toBeCreate.add(new IndexModel(idxLogId, new IndexOptions().name("index_log_id").background(true)));

        //临时库的id索引
        Bson tempFilter = Filters.eq("type", SyncLogTypeEnum.TEMP.name());
        Bson idxTempDataId = Indexes.ascending("erpTempData.dataId");
        toBeCreate.add(new IndexModel(idxTempDataId, new IndexOptions().name("index_temp_data_id")
                .partialFilterExpression(tempFilter).background(true)));

        Bson idxTempDataNumber = Indexes.ascending("erpTempData.dataNumber");
        toBeCreate.add(new IndexModel(idxTempDataNumber, new IndexOptions().name("index_temp_data_number")
                .partialFilterExpression(tempFilter).background(true)));

        Bson idxTempDataNumber2 = Indexes.compoundIndex(
                Indexes.ascending("erpTempData.taskNum")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxTempDataNumber2, new IndexOptions().name("index_temp_task_num")
                .partialFilterExpression(tempFilter).background(true)));

        //数据范围的id索引,必须带streamId
        Bson typeFilter = Filters.eq("type", SyncLogTypeEnum.DATA_SYNC_FILTER.name());
        toBeCreate.add(new IndexModel(
                Indexes.compoundIndex(
                        Indexes.ascending("erpTempData.dataId"),
                        Indexes.ascending("realObjApiName"),
                        Indexes.ascending("streamId"),
                        Indexes.descending("updateTime")
                ),
                new IndexOptions().partialFilterExpression(typeFilter).background(true))
        );
        toBeCreate.add(new IndexModel(
                Indexes.compoundIndex(
                        Indexes.ascending("erpTempData.dataNumber"),
                        Indexes.ascending("realObjApiName"),
                        Indexes.ascending("streamId"),
                        Indexes.descending("updateTime")
                ),
                new IndexOptions().partialFilterExpression(typeFilter).background(true))
        );

        Bson idxTempDataTenantId = Indexes.compoundIndex(
                Indexes.ascending("erpTempData.tenantId"));
        toBeCreate.add(new IndexModel(idxTempDataTenantId, new IndexOptions().name( "index_temp_data_tenant_id")
                .background(true)));

        return toBeCreate;
    }

    @Override
    public Bson expireIndex() {
        return Indexes.descending("createTime");
    }
}
