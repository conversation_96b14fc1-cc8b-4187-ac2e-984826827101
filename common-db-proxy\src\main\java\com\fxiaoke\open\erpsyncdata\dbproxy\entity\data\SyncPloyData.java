package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncPloyData extends BaseData implements Serializable {
    private String id;
    private Integer type;
    private String appId;
    private String tenantId;
    private String name;
    private String objectApiName;
    private DetailObjectApiNamesData detailObjectApiNames = new DetailObjectApiNamesData();
    private String remark;

    public void setDetailObjectApiNamesAsList(List<String> detailObjectApiNames) {
        if (detailObjectApiNames == null) {
            return;
        }
        if (this.detailObjectApiNames == null) {
            this.detailObjectApiNames = new DetailObjectApiNamesData();
        }
        this.detailObjectApiNames.clear();
        this.detailObjectApiNames.addAll(detailObjectApiNames);
    }
}
