package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.result.DeleteResult;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Repository
@DependsOn("erpSyncDataLogMongoStore")
public class UserOperatorLogDao {


    private DatastoreExt store;

    private String DATABASE = "erp_sync_data";

    private MongoCollection<Document> collectionList;

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    public void setStore(DatastoreExt store) {
        this.store = store;
        getOrCreateCollection();
    }

    UserOperatorLogDao(){
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                                      .get("syncLogDbName", "fs-erp-sync-data");
    }


    public String save(UserOperatorLog userOperatorLog) {

        Key<UserOperatorLog> save = store.save(userOperatorLog);
        return save.getId().toString();
    }
    public Long delete(String id) {
        DeleteResult deleteResult=this.getOrCreateCollection().deleteOne(Filters.eq("_id",new ObjectId(id)));
        return deleteResult.getDeletedCount();
    }

    public String saveUserOperatorLog(UserOperatorLog userOperatorLog) {
        Key<UserOperatorLog> save = store.save(userOperatorLog);
        return save.getId().toString();
    }

    public MongoCollection<Document> getOrCreateCollection(){
        if (collectionList!=null){
            return collectionList;
        }
        collectionList = store.getMongo().getDatabase(DATABASE).getCollection("user_operator_log");
        FindIterable query = collectionList.find().limit(1);
        MongoCursor iterator = query.iterator();
        while (iterator.hasNext()){
            return collectionList;
        }

        List<IndexModel> toBeCreate = Lists.newArrayList();
        List<String> exists = Lists.newArrayList();
        String key = "index_ei_module_id";
        if (!exists.remove(key)) {
            Bson index_ei_obj_type_id = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("data_center_id"), Indexes.ascending("module"),
              Indexes.ascending("module_id"));
            toBeCreate.add(new IndexModel(index_ei_obj_type_id, new IndexOptions().name(key).background(true)));
        }

        key = "index_expire_time";//过期时间，
        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending("expire_time");
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(ConfigCenter.USER_OPERATOR_LOG_MONGO_EXPIRE_TIME, TimeUnit.DAYS)));
        }

        List<String> created = collectionList.createIndexes(toBeCreate);
        log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        return collectionList;
    }

    public List<UserOperatorLog> queryUserOperatorLogs(String tenantId,
                                                       String datacenterId,
                                                       String module,
                                                       String moduleId,
                                                       int offset,
                                                       int limit) {
        Query<UserOperatorLog> userOperatorLogs = store.find(UserOperatorLog.class).field("tenantId").equal(tenantId)
                                                       .field("dataCenterId").equal(datacenterId);
        if (StringUtil.isNotEmpty(module)){
            userOperatorLogs.field("module").equal(module);
        }
        if (StringUtil.isNotEmpty(moduleId)){
            userOperatorLogs.field("moduleId").equal(moduleId);
        }
        Query<UserOperatorLog> query = userOperatorLogs.order("-operatorTime").offset(offset).limit(limit);
        return query.asList();

    }

}
