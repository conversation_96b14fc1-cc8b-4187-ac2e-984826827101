package com.fxiaoke.open.erpsyncdata.web.mq.consumer.base;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.util.List;

@Slf4j
public abstract class AbstractMQConsumer<T> implements MessageListenerConcurrently {

    protected final Class<T> entityClazz;

    @Setter
    protected String configName;

    @Setter
    protected String sectionName;

    public AbstractMQConsumer() {
        entityClazz = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public AbstractMQConsumer(Class<T> entityClazz) {
        this.entityClazz = entityClazz;
    }

    public AbstractMQConsumer(String configName, String sectionName) {
        this();
        this.configName = configName;
        this.sectionName = sectionName;
    }

    private AutoConfMQPushConsumer autoConfMQPushConsumer;

    @PostConstruct
    public void init() {
        startConsumer();
    }

    protected final void startConsumer() {
        autoConfMQPushConsumer = new AutoConfMQPushConsumer(configName, sectionName, this);
        autoConfMQPushConsumer.start();
        log.info("MQConsumer initialized, configName: {} sectionName: {}", configName, sectionName);
    }

    @Override
    public final ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (int currentIndex = 0; currentIndex < list.size(); currentIndex++) {
            MessageExt messageExt = list.get(currentIndex);
            String text = null;
            try {
                MessageHelper.fillContextFromMessage(TraceContext.get(), messageExt);
                text = new String(messageExt.getBody());
                final T t = parseEvent(text);
                consumeOneMessage(t);
            } catch (Throwable ex) {
//                重试次数小于10次的打印warn日志，否则打印error日志
                if (messageExt.getReconsumeTimes() < 10) {
                    log.warn("consumeOneMessage Error, body:{}", text, ex);
                } else {
                    log.error("consumeOneMessage Error, body:{}", text, ex);
                }
//                从失败的消息开始重新消费
                if (currentIndex == 0) {
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } else {
                    consumeConcurrentlyContext.setAckIndex(currentIndex - 1);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
            } finally {
                TraceContext.remove();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


    protected T parseEvent(String text) {
        T t = JSON.parseObject(text, entityClazz);
        log.info("[{}] [parseEvent] [event:{}]", this.getClass().getName(), text);
        return t;
    }

    protected abstract void consumeOneMessage(T t) throws Exception;
}
