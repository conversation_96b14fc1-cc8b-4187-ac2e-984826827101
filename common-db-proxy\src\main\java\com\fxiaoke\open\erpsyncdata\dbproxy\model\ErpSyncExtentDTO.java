package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class ErpSyncExtentDTO extends ErpSyncTimeEntity implements Serializable {
    private static final long serialVersionUID = -9024883767459763881L;
    /**
     * 策略快照id
     */
    private String snapshotId;
    /** 策略详情数据封装 */
    private SyncPloyDetailData syncPloyDetailData;
    /**
     * 策略快照创建时间
     */
    private Long snapshotCreateTime;
}
