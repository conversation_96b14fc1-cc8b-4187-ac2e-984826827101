package com.fxiaoke.open.erpsyncdata.main.service

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager
import org.apache.commons.lang3.tuple.Pair
import org.springframework.dao.DuplicateKeyException
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class SyncDataMappingServiceImplTest extends Specification {

    SyncDataMappingServiceImpl impl
    def tenantId = "123456"

    def "test existByTwoWay"() {
        given:
        def manager = Mock(SyncDataMappingManager) {
            existByTwoWay(*_) >> true
        }
        impl = new SyncDataMappingServiceImpl(syncDataMappingManager: manager)
        expect:
        impl.existByTwoWay(tenantId, "", "apiName", "", "", "").isSuccess()
    }

    def "test getSyncDataMapping - #name"() {
        given:
        def manager = Mock(SyncDataMappingManager) {
            getMappingFirstBySource(*_) >> res
        }
        impl = new SyncDataMappingServiceImpl(syncDataMappingManager: manager)
        expect:
        impl.getSyncDataMapping(tenantId, null,null,null,null,null).isSuccess()
        where:
        name            | res
        "全空"           | Pair.of(null, null)
        "仅源为空"        | Pair.of(null, new SyncDataMappingsEntity())
        "仅目标为空"      | Pair.of(new SyncDataMappingsEntity(), null)
    }

    def "test updateDataIdByMergeInfo"() {
        given:
        def manager = Mock(SyncDataMappingManager) {
        }
        def dao = Mock(SyncDataMappingsDao) {
            setTenantId(_) >> it
            listByDestInfo(*_) >> {
                def entity = new SyncDataMappingsEntity(id: "test")
                // 处理空数据触发异常
                def entity1 = new SyncDataMappingsEntity(id: null)
                return [entity, entity1]
            }
            listBySourceInfo(*_) >> {
                def entity = new SyncDataMappingsEntity(id: "test")
                // 处理空数据触发异常
                def entity1 = new SyncDataMappingsEntity(id: null)
                return [entity, entity1]
            }
            mergeSourceDataId(*_) >> { args ->
                def list = args[1] as List
                println(list)
                if (list[0] == null) {
                    throw new DuplicateKeyException("")
                }
                return 1
            }
            mergeDestDataId(*_) >> { args ->
                def list = args[1] as List
                println(list)
                if (list[0] == null) {
                    throw new DuplicateKeyException("")
                }
                return 1
            }
        }
        impl = new SyncDataMappingServiceImpl(syncDataMappingManager: manager, syncDataMappingsDao: dao)
        expect:
        impl.updateDataIdByMergeInfo(tenantId, "test", ["test1"], "test").isSuccess()
    }

    def "test getDataByMasterId"() {
        given:
        def dao = Mock(SyncDataMappingsDao) {
            setTenantId(_) >> it
            findDetailByMasterId(*_) >> {
                SyncDataMappingsEntity e = new SyncDataMappingsEntity()
                return [e]
            }
        }
        impl = new SyncDataMappingServiceImpl(syncDataMappingsDao: dao)
        expect:
        impl.getDataByMasterId(tenantId, null,null,null,null).isSuccess()
    }

    def "test getSyncDataMappingByDest"() {
        given:
        def dao = Mock(SyncDataMappingsDao) {
            setTenantId(_) >> it
            getByUninKeyByDestId(*_) >> new SyncDataMappingsEntity()
        }
        impl = new SyncDataMappingServiceImpl(syncDataMappingsDao: dao)
        expect:
        impl.getSyncDataMappingByDest(tenantId, null,null,null,null,null).isSuccess()
    }

    def "test getMappingByDestObjApiNameAndId"() {
        given:
        def dao = Mock(SyncDataMappingsDao) {
            setTenantId(_) >> it
            getMappingByDestObjApiNameAndId(*_) >> {
                SyncDataMappingsEntity e = new SyncDataMappingsEntity()
                return [e]
            }
        }
        impl = new SyncDataMappingServiceImpl(syncDataMappingsDao: dao)
        expect:
        impl.getMappingByDestObjApiNameAndId(tenantId, null,null).isSuccess()
    }
}
