package com.fxiaoke.open.erpsyncdata.dbproxy.monitor

import com.facishare.converter.EIEAConverter
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.github.benmanes.caffeine.cache.Caffeine
import com.github.jedis.support.MergeJedisCmd
import com.github.jedis.support.PipelineCallback
import com.github.jedis.support.PipelineCmd
import com.google.common.collect.Sets
import org.apache.commons.lang3.time.DateFormatUtils
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import redis.clients.jedis.Tuple
import spock.lang.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit
import java.util.stream.Collectors

/**
 * <AUTHOR> 
 * @date 2022/10/21 11:39:38
 */
class CheckProductErpDataMonitorTest extends Specification {
    PipelineCmd pipe
    MergeJedisCmd jedis
    CheckProductErpDataMonitor monitor
    TenantConfigurationManager tenantConfigurationManager

    void setup() {
        pipe = Mock(PipelineCmd)
        jedis = Mock(MergeJedisCmd) {
            pipeline(_) >> { pipeline ->
                def callback = (PipelineCallback) pipeline[0]
                callback.pipeline(pipe)
            }
        }

        tenantConfigurationManager = Mock(TenantConfigurationManager)
        monitor = new CheckProductErpDataMonitor(
                jedisSupport: jedis,
                tenantConfigurationManager: tenantConfigurationManager,
                eieaConverter: Mock(EIEAConverter) {
                    enterpriseIdToAccount((Integer) _) >> { args -> String.valueOf(args[0]) }
                },
                notificationService: Mock(NotificationService) {
                    sendTenantAdminNotice(*_) >> { args ->
                        println args[0]["msg"]
                        return Result.newSuccess()
                    }
                },
                redissonClient: Mock(RedissonClient) {
                    getLock(_) >> Mock(RLock) {
                        tryLock() >> true
                    }
                },
                // 需要i18NStringManager
                i18NStringManager: new I18NStringManager()
        )

        monitor.afterPropertiesSet()
    }

    @Unroll
    def "生成数据"() {
        setup:
        String tenantId = "1"
        Set<String> redisData = new HashSet<>();
        jedis.sadd((String) _, (String[]) _) >> { args ->
            def strings = (String[]) args[1]
            redisData.addAll(strings)
            return strings.size()
        }
        tenantConfigurationManager.findOne(*_) >> new ErpTenantConfigurationEntity(configuration: config)

        def erpTempData = apiNames.stream().map({ s -> [objApiName: s] as ErpTempData }).collect(Collectors.toList())

        when:
        monitor.erpDateProduct(tenantId, erpTempData)
        def empty = redisData.isEmpty()
        monitor.destroy()

        then:
        empty
        redisData == Sets.newHashSet(result)

        where:
//        产生的数据                   |  需要监控的配置                            || redis保存的数据
        apiNames                     | config                                   || result
        ["123", "qwe"]               | ""                                       || []
        ["123", "qwe"]               | "testErpObj:00:00-00:00"                 || []
        ["123", "qwe", "testErpObj"] | "testErpObj:00:00-00:00"                 || ["1:testErpObj"]
        ["123", "qwe", "testErpObj"] | "testErpObj:00:00-00:00,123:4:00-6:00"   || ["1:testErpObj"]
        ["123", "qwe", "testErpObj"] | "testErpObj:00:00-00:00,123:00:00-00:00" || ["1:testErpObj", "1:123"]
        ["asdafsadf", "testErpObj"]  | "testErpObj:4:00-6:00"                   || []
    }

    @Unroll
    def "初始化key"() {
        setup:
        String tenantId = "1"
        Set<String> redisData = new HashSet<>();
        long time
        jedis.sadd((String) _, (String[]) _) >> { args ->
            def strings = (String[]) args[1]
            redisData.addAll(strings)
            return strings.size()
        }
        jedis.expire(*_) >> { args ->
            time = args[1]
            return 1
        }
        tenantConfigurationManager.findOne(*_) >> new ErpTenantConfigurationEntity(configuration: config)

        when:
        monitor.checkInit(tenantId)

        then:
        println monitor.map.get(tenantId)
        redisData == Sets.newHashSet(result)
        if (time > 0) {
            assert time > 24 * 3600
            assert time < 48 * 3600
        }


        where:
        // 需要监控的配置             || redis保存的数据
        config                      || result
        ""                          || []
        "123:04:00-04:10"           || []
        "123:" + after20 + "-00:00" || ["-1:"]
    }

    private static String getTimeFormat(Long time) {
        return DateFormatUtils.format(time, "HH:mm");
    }

    public static final String after20 = getTimeFormat(System.currentTimeMillis() + 20 * 60 * 1000)
    public static final String before20 = getTimeFormat(System.currentTimeMillis() - 20 * 60 * 1000)

    @Ignore
    @Unroll
    def "发出企信消息"() {
        setup:
        String tenantId = "1"
        Set<String> redisData = Sets.newHashSet(initdata);
        Set<String> zsetData = new HashSet<>();

        long time
        jedis.sadd((String) _, (String[]) _) >> { args ->
            def strings = (String[]) args[1]
            redisData.addAll(strings)
            return strings.size()
        }
        jedis.srem((String) _, (String[]) _) >> { args ->
            def strings = (String[]) args[1]
            redisData.removeAll(strings)
            return strings.size()
        }
        jedis.exists(_) >> true
        jedis.sismember((String) _, (String) _) >> { args ->
            return redisData.contains((String) args[1])
        }
        pipe.sadd((String) _, (String[]) _) >> { args ->
            def strings = (String[]) args[1]
            redisData.addAll(strings)
            return null
        }
        pipe.srem((String) _, (String[]) _) >> { args ->
            def strings = (String[]) args[1]
            redisData.removeAll(strings)
            return null
        }

        jedis.zadd((String) _, (Map<String, Double>) _) >> { args ->
            def map = (Map<String, Double>) args[1]
            time = map.values().stream().findFirst().orElse(1)
            zsetData.addAll(map.keySet())
            return map.size()
        }
        jedis.zrangeByScoreWithScores(*_) >> {
            return zsetData.stream().map({
                def scope = System.currentTimeMillis() / 1000 * 1000
                new redis.clients.jedis.Tuple(it, scope)
            }).collect(Collectors.toList())
        }
        tenantConfigurationManager.findOne(*_) >> new ErpTenantConfigurationEntity(configuration: config)
        monitor.map =  Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(1000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build({ monitor.initErpDataCheck(it) });

        monitor.map.get(tenantId)

        when:
        monitor.checkErpDataProduct()

        then:
        redisData == Sets.newHashSet(result)
        zsetData == Sets.newHashSet(zset)
        if (time > 0) {
            assert time < System.currentTimeMillis()
        }


        where:
        // 产生的数据  |需要监控的配置                                      || redis保存的数据 | 需要通知的数据
        initdata  | config                                             || result    | zset
        ["1:qwe"] | ""                                                 || ["1:qwe"] | []
        ["1:qwe"] | "qwe:04:00-04:10"                                  || ["1:qwe"] | []
        ["1:qwe"] | "qwe:00:00-" + before20                            || ["1:ALL"] | []
        ["1:qwe"] | "qwe:00:00-" + after20                             || ["1:qwe"] | []
        []        | "asd:00:00-" + before20                            || ["1:ALL"] | ["1/asd/00:00/" + before20 + "/1800000/4"]
        ["1:qwe"] | "qwe:00:00-" + before20 + ",asd:00:00-" + before20 || ["1:ALL"] | ["1/asd/00:00/" + before20 + "/1800000/4"]
    }

    @Ignore
    @Unroll
    def "测试发送消息"() {
        setup:
        Set<String> zremData = new HashSet<>();
        long time

        def collect = tuples.entrySet().stream().map({ new Tuple(it.getKey(), it.getValue()) }).collect(Collectors.toSet());
        jedis.zrangeByScoreWithScores(*_) >> collect

        jedis.zrem(*_) >> { args ->
            def strings = (String[]) args[1]
            zremData.addAll(strings)
            return null
        }
        int inc
        Set<String> incData = new HashSet<>();
        pipe.zincrby(*_) >> { args ->
            inc = args[1]
            def strings = args[2]
            incData.add(strings)
            return null
        }

        when:
        monitor.notifyCsmWarn()

        then:
        zremData == Sets.newHashSet(remset)
        incData == Sets.newHashSet(incSet)
        if (time > 0) {
            assert time < System.currentTimeMillis()
        }
        if (inc > 0) {
            assert inc == incTime
        }

        where:
//        需要通知的数据
        tuples << [
                ["1/asd/00:00/00:00/1000/4": 1235465000L],
                ["1/asd/00:00/00:00/1000/4": 1235465003L],
                ["1/qwe/00:00/00:00/2000/4": 1235465002L, "1/asd/00:00/00:00/1000/4": 1235465003L],
                [:],
        ]

//      通知后需要删除的数据 | 需要下次通知的数据 | 通知间隔+通知次数
        remset | incSet | incTime
        [] | ["1/asd/00:00/00:00/1000/4"] | 1001
        ["1/asd/00:00/00:00/1000/4"] | [] | _
        ["1/asd/00:00/00:00/1000/4"] | ["1/qwe/00:00/00:00/2000/4"] | 2001
        [] | [] | _
    }
}
