package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import lombok.Data;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.Serializable;

@Data
public class CellStyleData implements Serializable {
    private IndexedColors indexedColors;
    private HorizontalAlignment horizontalAlignment;
    private VerticalAlignment verticalAlignment;
    private FillPatternType fillPatternType;
}
