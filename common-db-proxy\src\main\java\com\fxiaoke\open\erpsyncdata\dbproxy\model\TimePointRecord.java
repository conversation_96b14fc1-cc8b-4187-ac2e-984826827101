package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import cn.hutool.core.lang.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/2
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimePointRecord {
    private String tenantId;
    private String objApiName;
    private String sourceDataId;
    private String traceId;
    private Long listenTime;
    private Long firstParseTime;
    /**
     * parse前最近的发送mq时间
     */
    private Long lastSendMqTime;
    private Long allFinishTime;
    /**
     * 名称，时间戳
     */
    private LinkedList<SyncDataTimePoint> syncDataTimePoints;


    @Getter
    @Setter
    @ToString
    public static class SyncDataTimePoint {
        private String syncDataId;
        private Integer count;//主+从总数量,在写步骤赋值
        private Boolean reWriteFailed;//回写失败
        private String reWriteFailedMsg;//reWriteFailed异常信息
        private Boolean afterSyncFailed;//同步后失败
        private String afterSyncFailedMsg;//afterSyncFailed异常信息
        private Boolean syncStepException;//是否SyncStepException异常
        private String syncStepExceptionMsg;//SyncStepException异常信息
        private Boolean throwable;//是否Throwable异常
        private String throwableMsg;//Throwable异常信息
        private List<Pair<String, Long>> timePoints = new ArrayList<>();
    }


    public SyncDataTimePoint getLastSyncDataTimePoint() {
        return syncDataTimePoints.isEmpty() ? null : syncDataTimePoints.getLast();
    }

    public void addSyncDataTimePoint() {
        syncDataTimePoints.add(new SyncDataTimePoint());
    }

    public void addTimePoint(String pointName) {
        SyncDataTimePoint lastSyncDataTimePoint = getLastSyncDataTimePoint();
        if (lastSyncDataTimePoint!=null){
            lastSyncDataTimePoint.getTimePoints().add(Pair.of(pointName, System.currentTimeMillis()));
        }
    }

    public void setSyncDataId(String syncDataId) {
        getLastSyncDataTimePoint().setSyncDataId(syncDataId);
    }

}
