package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3Constant;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.StrUtil2;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpOrganizationObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpOrganizationObj;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 库存模块公共方法
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/12/15
 */
@Slf4j
public class CommonStockBusinessImpl {
    @Autowired
    private ErpOrganizationObjManager erpOrganizationObjManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;
    /**
     * 仓位字段名称
     */
    protected static final String FLEX_VALUE_FIELD_KEY = "FFlexNumber";
    private static final String FID = "FID";
    private static final String FNUMBER = "FNumber";
    private static final String FNUMBER_SUFFIX = ".FNumber";
    private static final String FNAME_SUFFIX = ".FName";
    private static final String FLOT_ENTRY_ID = "FLOTID";


    /**
     * 如果同一家企业的多个组织只有一个组织的仓库需要同步，允许两个仓库有相同的仓库编码，同步仓库时，需要设置只有一个组织机构允许仓库同步
     *
     * @param tenantId
     * @param dcId
     * @return
     */
    protected List<ErpOrganizationObj> getSyncStockOrgs(String tenantId,String dcId) {
        List<ErpOrganizationObj> erpOrganizationList = erpOrganizationObjManager.queryDcErpOrganizationObj(tenantId,dcId);
        if (erpOrganizationList.isEmpty()) {
            throw new ErpSyncDataException(ResultCodeEnum.SYNC_WAREHOUSE_ORG_NOT_FOUND,tenantId);
        }
        List<ErpOrganizationObj> syncStockOrgs = erpOrganizationList.stream()
                .filter(ErpOrganizationObj::getNeedSyncWarehouse)
                .collect(Collectors.toList());
        return syncStockOrgs;
    }

    protected List<String> getSyncStockOrgNos(String tenantId,String dcId) {
        return getSyncStockOrgs(tenantId, dcId).stream().map(ErpOrganizationObj::getOrgNumber).collect(Collectors.toList());
    }

    /**
     * 获取仓位值集数据
     *
     * @param apiClient
     * @return key: 仓位值集编码，value，仓位值信息列表，包含仓位值集信息
     */
    protected LinkedHashMap<String, List<K3Model>> getFlexMap(K3CloudApiClient apiClient) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("BD_FLEXVALUES");
        queryArg.setFieldKeys("FName,FNumber,FFlexNumber,FEntity_FEntryId,FFlexValueNumber,FFlexValueName");
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s123.getI18nKey(),
                    apiClient.getTenantId(),
                    i18NStringManager.getByEi2(I18NStringEnum.s123, apiClient.getTenantId(),result.getErrMsg()),
                    Lists.newArrayList(result.getErrMsg())),
                    null,
                    null);
        }
        List<K3Model> flexValues = result.getData();
        LinkedHashMap<String, List<K3Model>> flexMap = flexValues.stream().collect(
                Collectors.groupingBy(v -> v.getString(FNUMBER), LinkedHashMap::new, Collectors.toList()));
        return flexMap;
    }


    /**
     * 获取仓库、仓位组合的id
     * 格式：仓位仓库的id使用[仓库编码||仓位1编码.仓位2编码...]
     * 暂时使用单个locid获取，批量操作接口后续观察是否需要做
     *
     * @param warehouseNumber
     * @param locId
     * @param apiClient
     * @return
     */
    public String getWarehouseComId(String warehouseNumber, String locId, K3CloudApiClient apiClient) {

        boolean hasLocId = judgeValue(locId);//是否有仓位
        String locNumber = Strings.EMPTY;
        if (hasLocId) {
            locNumber = getLocNumber(locId, apiClient, warehouseNumber);
        }
        warehouseNumber = StringUtils.isEmpty(locNumber) ? warehouseNumber : locNumber;
        return warehouseNumber;
    }

    protected String getBatchComId(String stockComId, String flotId, K3CloudApiClient apiClient) {
        boolean hasFlotId = judgeValue(flotId);//是否有批次
        Joiner warehouseLocJoiner = Joiner.on(K3Constant.ID_SEPARATOR).useForNull("");
        if (hasFlotId) {
            stockComId = warehouseLocJoiner.join(stockComId, getBatchNumber(flotId, apiClient));
        }
        return stockComId;
    }

    private Boolean judgeValue(String value) {
        if (StringUtils.isBlank(value) || "0".equals(value)) return false;
        return true;
    }

    private String getLocNumber(String locId, K3CloudApiClient apiClient, String warehouseNumber) {
        //获取仓位
        List<String> flexValueFields = getFlexValueFields(warehouseNumber, apiClient);
        //去重
        flexValueFields=flexValueFields.stream().distinct().collect(Collectors.toList());
        List<String> locQueryFieldKeys = new ArrayList<>();
        locQueryFieldKeys.add(FID);
        for (String flexValueField : flexValueFields) {
            locQueryFieldKeys.add(flexValueField + FNAME_SUFFIX);
            locQueryFieldKeys.add(flexValueField + FNUMBER_SUFFIX);
        }
        //根据locId查找仓位值集编码
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.FLEX_VALUES_DETAIL);
        queryArg.setFieldKeysByList(locQueryFieldKeys);
        queryArg.appendEqualFilter(FID, locId);
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s124.getI18nKey(),
                    apiClient.getTenantId(),
                    i18NStringManager.getByEi2(I18NStringEnum.s3686, apiClient.getTenantId(), locId, result.getErrMsg()),
                    Lists.newArrayList(locId, result.getErrMsg())),
                    null,
                    null);
        } else if (result.getData().isEmpty()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s125.getI18nKey(),
                    apiClient.getTenantId(),
                    i18NStringManager.getByEi2(I18NStringEnum.s3687, apiClient.getTenantId(), locId),
                    Lists.newArrayList(locId)),
                    null,
                    null);
        } else if (result.getData().size() > 1) {
            log.error("仓位值组合明细超过一条,result:{}", result);
        }
        K3Model k3Model = result.getData().get(0);
        List<String> locNumbers = new ArrayList<>();
        for (String flexValueField : flexValueFields) {
            locNumbers.add(k3Model.getString(flexValueField + FNUMBER_SUFFIX));
        }
        //组合编码
        Joiner warehouseLocJoiner = Joiner.on(K3Constant.WAREHOUSE_LOC_SEPARATOR).useForNull("");
        Joiner locJoiner = Joiner.on(K3Constant.LOC_SEPARATOR).useForNull("");
        String comWarehouseId = warehouseLocJoiner.join(warehouseNumber, locJoiner.join(locNumbers));
        return comWarehouseId;
    }

    private String getBatchNumber(String flotId, K3CloudApiClient apiClient) {
        //获取批次
        //根据flotID查找批号信息
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BATCH_OBJ);
        queryArg.setFieldKeysByList(configCenterConfig.getBATCH_FIELDS());
        queryArg.appendEqualFilter(FLOT_ENTRY_ID, flotId);
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s126.getI18nKey(),
                    apiClient.getTenantId(),
                    i18NStringManager.getByEi2(I18NStringEnum.s3688, apiClient.getTenantId(), flotId, result.getErrMsg()),
                    Lists.newArrayList(flotId, result.getErrMsg())),
                    null,
                    null);
        } else if (result.getData().isEmpty()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s127.getI18nKey(),
                    apiClient.getTenantId(),
                    i18NStringManager.getByEi2(I18NStringEnum.s3689, apiClient.getTenantId(), flotId),
                    Lists.newArrayList(flotId)),
                    null,
                    null);
        }
        K3Model k3Model = result.getData().get(0);
        return k3Model.get("FNumber").toString();
    }


    /**
     * 获取值集弹性域的字段编号
     *
     * @param warehouseNumber
     * @param apiClient
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 2, name = "getFlexValueFields")
    public List<String> getFlexValueFields(String warehouseNumber, K3CloudApiClient apiClient) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BD_STOCK);
        queryArg.setFieldKeys("FStockId,FName,FNumber,FIsOpenLocation,FFlexId.FName,FFlexId.FFLEXNUMBER,FStockFlexItem_FSeq");
        queryArg.appendEqualFilter(FNUMBER, warehouseNumber);
//        queryArg.appendEqualFilter("FIsOpenLocation","true");k3不支持这种筛选
        queryArg.setOrderString("FStockFlexItem_FSeq");
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        ErpSyncDataException.checkResult(result);
        List<String> flexValueFields = result.getData().stream()
                .map(v -> v.getString("FFlexId.FFLEXNUMBER"))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return flexValueFields;
    }

    /**
     * 获取库存虚拟Id
     *
     * @param warehouseComId
     * @param materialNumber
     * @return
     */
    protected String getStockComIdNoBatch(String warehouseComId, String materialNumber) {
        return Joiner.on(K3Constant.ID_SEPARATOR).join(warehouseComId, materialNumber);
    }

    /**
     * 获取物料已启用的辅助属性列表
     */
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 2, name = "getMaterialAuxPropertyFieldList")
    protected List<String> getMaterialAuxPropertyFieldList(K3CloudApiClient apiClient, String fid) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.STK_Inventory);
        queryArg.setFieldKeys("FMaterialId.FNumber");
        queryArg.setFilterString("FID='"+fid+"'");
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        ErpSyncDataException.checkResult(result);
        if(CollectionUtils.isEmpty(result.getData())) return new ArrayList<>();

        String materialNumber = result.getData().get(0).getString("FMaterialId.FNumber");

        return getMaterialAuxPropertyFieldListByMaterialNumber(apiClient, materialNumber);
    }

    public List<String> getMaterialAuxPropertyFieldListByMaterialNumber(final K3CloudApiClient apiClient, final String materialNumber) {

        final QueryArg queryMaterialArg = new QueryArg();
        queryMaterialArg.setFormId(K3CloudForm.BD_MATERIAL);
        queryMaterialArg.setFieldKeys("FIsEnable1,FAuxPropertyID,FAuxPropertyId.FValueType");
        queryMaterialArg.setFilterString(StrUtil2.convertSpecSql("FNumber",materialNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryMaterialArg);
        final Result<List<K3Model>> result = apiClient.queryAll(queryMaterialArg);
        final List<K3Model> k3Models = result.getData();
        Set<String> auxPropertyList = new HashSet<>();
        for(K3Model k3Model : k3Models) {
            Boolean isEnabled = (Boolean) k3Model.get("FIsEnable1");
            if(BooleanUtils.isTrue(isEnabled)) {
                String id = k3Model.get("FAuxPropertyID").toString();
                String valueType = k3Model.get("FAuxPropertyId.FValueType").toString();

                if("2".equalsIgnoreCase(valueType)) {
                    auxPropertyList.add("FAuxPropId.FF"+id);
                } else {
                    auxPropertyList.add("FAuxPropId.FF"+id);
                    auxPropertyList.add("FAuxPropId.FF"+id+".FNumber");
                    if("1".equalsIgnoreCase(valueType)) {
                        auxPropertyList.add("FAuxPropId.FF"+id+".FDataValue");
                    }
                }
            }
        }
        return Lists.newArrayList(auxPropertyList);
    }

    /**
     * 获取已启用的辅助属性字段列表
     */
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 2, name = "getAuxPropertyFieldList")
    protected List<String> getAuxPropertyFieldList(K3CloudApiClient apiClient) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BD_FLEXAUXPROPERTY);
        queryArg.setFieldKeys("FFlexNumber,FName,FNumber,FValueType,FValueSource");
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        ErpSyncDataException.checkResult(result);

        List<String> auxPropertyList = new ArrayList<>();
        for(K3Model k3Model : result.getData()) {
            String flexNumber = k3Model.getString("FFlexNumber");
            String valueType = k3Model.getString("FValueType");
            //来源，关联的对象
            String valueSource = k3Model.getString("FValueSource");
            if(StringUtils.isNotEmpty(flexNumber) && StringUtils.isNotBlank(flexNumber)) {
                if("2".equalsIgnoreCase(valueType)) { //手动输入
                    auxPropertyList.add("FAuxPropId."+flexNumber);
                } else { //基础资料和辅助资料类型
                    auxPropertyList.add("FAuxPropId."+flexNumber);
                    //其实对于编号字段，可能不同的单据可能是不一样的（大部分是FNumber）。这里因为是员工的有问题，先处理这个单。
                    if (Objects.equals(valueSource,K3CloudForm.BD_Empinfo)){
                        auxPropertyList.add("FAuxPropId."+flexNumber+".FStaffNumber");
                    }else {
                        auxPropertyList.add("FAuxPropId."+flexNumber+".FNumber");
                    }
                    if("1".equalsIgnoreCase(valueType)) {
                        auxPropertyList.add("FAuxPropId."+flexNumber+".FDataValue");
                    }
                }
            }
        }
        return auxPropertyList;
    }

    /**
     * 对辅助属性关联的基础资料的ID字段进行特殊处理
     * @param k3Model
     */
    protected void updateAuxProperty(K3Model k3Model) {
        for(String key : k3Model.keySet()) {
            if(key.contains("FAuxPropId")) {
                List<String> items = Splitter.on(".").splitToList(key);
                if(items.size()==2) {
                    Object value = k3Model.get(key);
                    if(value instanceof Integer && (Integer) value==0) {
                        k3Model.put(key,null);
                    }
                }
            }
        }
    }
}
