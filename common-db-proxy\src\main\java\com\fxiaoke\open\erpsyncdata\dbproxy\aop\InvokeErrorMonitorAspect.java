package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MonitorLogModule;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DBMonitorBizLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:43:42
 */
@Slf4j
public class InvokeErrorMonitorAspect {
    public Object invokeErrorMonitor(ProceedingJoinPoint proceedingJoinPoint, InvokeErrorType errorType) throws Throwable {
        final long l = System.currentTimeMillis();
        try {
            return proceedingJoinPoint.proceed();
        } catch (Throwable e) {
            try {
                sendErrorLog(proceedingJoinPoint, errorType.name(), l, e);
            } catch (Exception ex) {
                log.warn("{} error", proceedingJoinPoint.getSignature().getName(), e);
                log.warn("DBErrorMonitorAspect send error log error", ex);
            }
            throw e;
        }
    }

    public enum InvokeErrorType {
        PG,
        MONGO,
        FUNC
    }

    public void sendErrorLog(ProceedingJoinPoint proceedingJoinPoint, String type, long startTime, Throwable e) {
        final DBMonitorBizLog dbMonitorBizLog = buildDBMonitorBizLog(proceedingJoinPoint, e);
        sendBizLog(startTime, type, dbMonitorBizLog);
    }

    protected void sendBizLog(long startTime, String type, DBMonitorBizLog dbMonitorBizLog) {
        try {
            //上报
            ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder = MonitorBizLogUtil.builder(MonitorLogModule.invoke_error_monitor, dbMonitorBizLog.getClassName() + "." + dbMonitorBizLog.getMethod(), dbMonitorBizLog.getTenantId(), "-1", type);
            builder.label1(dbMonitorBizLog.getException());
            builder.label2(dbMonitorBizLog.getErrorMessage());
            builder.label3(dbMonitorBizLog.getObjectApiName());
            builder.label4(dbMonitorBizLog.getDataId());
            builder.label5(dbMonitorBizLog.getAllArgs());
            builder.cost1(System.currentTimeMillis() - startTime);
            MonitorBizLogUtil.send(builder.build());
        } catch (Exception e) {
            log.error("sendBizLog exception", e);
        }
    }

    public DBMonitorBizLog buildDBMonitorBizLog(ProceedingJoinPoint proceedingJoinPoint, Throwable e) {
        final DBMonitorBizLog dbErrorMonitorBizLog = buildBaseDBMonitorBizLog(proceedingJoinPoint, e);

        final Object[] args = proceedingJoinPoint.getArgs();
        if (ArrayUtils.isEmpty(args)) {
            dbErrorMonitorBizLog.setObjectApiName(LogIdUtil.getRealObjApiName());
            return dbErrorMonitorBizLog;
        }

        String objApiName = getObjApiNameByParameter(proceedingJoinPoint);
        dbErrorMonitorBizLog.setObjectApiName(objApiName);

        String dataId = getDataIdByParameter(proceedingJoinPoint);
        dbErrorMonitorBizLog.setDataId(dataId);

        return dbErrorMonitorBizLog;
    }

    public DBMonitorBizLog buildBaseDBMonitorBizLog(ProceedingJoinPoint proceedingJoinPoint, Throwable e) {
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        final String simpleName = methodSignature.getDeclaringType().getSimpleName();
        final String method = methodSignature.getMethod().getName();
        final DBMonitorBizLog dbErrorMonitorBizLog = DBMonitorBizLog.builder()
                .tenantId(TraceContext.get().getEi())
                .className(simpleName)
                .method(method)
                .exception(Objects.isNull(e) ? null : e.getClass().getSimpleName())
                .errorMessage(Objects.isNull(e) ? null : e.getMessage())
                .build();

        if (StringUtils.isBlank(dbErrorMonitorBizLog.getTenantId())) {
            dbErrorMonitorBizLog.setTenantId(getTenantIdByParameter(proceedingJoinPoint));
        }

        return dbErrorMonitorBizLog;
    }

    private static final Pattern ObjApiNamePattern = Pattern.compile("(\\w)*([oO])bj(ect)?Api[Nn]ame");

    public static final Map<Method, Function<Object[], String>> MethodObjApiNameMap = new ConcurrentHashMap<>();

    public static final Map<Method, Function<Object[], String>> MethodTenantIdMap = new ConcurrentHashMap<>();
    public static final Map<Method, Function<Object[], String>> MethodDataIdMap = new ConcurrentHashMap<>();

    private static String getObjApiNameByParameter(ProceedingJoinPoint proceedingJoinPoint) {
        return getByCache(proceedingJoinPoint, MethodObjApiNameMap, InvokeErrorMonitorAspect::getObjApiNameFunction);
    }

    private static String getTenantIdByParameter(ProceedingJoinPoint proceedingJoinPoint) {
        return getByCache(proceedingJoinPoint, MethodTenantIdMap, InvokeErrorMonitorAspect::getTenantIdFunction);
    }

    private static String getDataIdByParameter(ProceedingJoinPoint proceedingJoinPoint) {
        return getByCache(proceedingJoinPoint, MethodDataIdMap, InvokeErrorMonitorAspect::getDataIdFunction);
    }

    public static <T> T getByCache(ProceedingJoinPoint proceedingJoinPoint, Map<Method, Function<Object[], T>> cache, Function<MethodSignature, Function<Object[], T>> func) {
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        final Method method = methodSignature.getMethod();

        final Function<Object[], T> objApiNameFunction = cache.computeIfAbsent(method, k -> func.apply(methodSignature));
        return objApiNameFunction.apply(proceedingJoinPoint.getArgs());
    }

    /**
     * 获取对象apiName的优先级 参数注解 > 参数名称 > 参数类注解 > LogIdUtil.getRealObjApiName()
     */
    private static Function<Object[], String> getObjApiNameFunction(MethodSignature methodSignature) {
        final Function<Object[], String> func = getFuncByParameterAnn(methodSignature, ObjApiName.class);
        if (Objects.nonNull(func)) {
            return func;
        }

        final String[] parameterNames = getParameterNames(methodSignature);
        final Class[] parameterTypes = methodSignature.getParameterTypes();
        for (int i = 0; i < parameterNames.length; i++) {
            if (parameterTypes[i].equals(String.class) && ObjApiNamePattern.matcher(parameterNames[i]).matches()) {
                int index = i;
                return p -> (String) p[index];
            }
        }

        final Function<Object[], String> funcByParameterTypeAnn = getFuncByParameterTypeAnn(methodSignature, ObjApiName.class, ObjApiName::value);
        if (Objects.nonNull(funcByParameterTypeAnn)) {
            return funcByParameterTypeAnn;
        }

        return p -> LogIdUtil.getRealObjApiName();
    }

    public static String[] getParameterNames(MethodSignature methodSignature) {
        String[] parameterNames = methodSignature.getParameterNames();
        if (ArrayUtils.isEmpty(parameterNames) && ArrayUtils.isNotEmpty(methodSignature.getMethod().getParameters())) {
            // mybatis修改了声明,导致无法获取参数名称,获取到的值为null
            return Arrays.stream(methodSignature.getMethod().getParameters())
                    .map(p -> {
                        final Param annotation = p.getAnnotation(Param.class);
                        return Objects.nonNull(annotation) ? annotation.value() : "";
                    }).toArray(String[]::new);
        }
        return parameterNames;
    }

    /**
     * 获取企业id的优先级 TraceContext > 参数名称 > 参数注解 > 参数类注解
     */
    private static Function<Object[], String> getTenantIdFunction(MethodSignature methodSignature) {
        final String[] parameterNames = getParameterNames(methodSignature);
        final Class[] parameterTypes = methodSignature.getParameterTypes();
        for (int i = 0; i < parameterNames.length; i++) {
            if (parameterTypes[i].equals(String.class) && StringUtils.containsIgnoreCase(parameterNames[i], "tenantId")) {
                int index = i;
                return p -> (String) p[index];
            }
        }

        final Function<Object[], String> func = getFuncByParameterAnnSpel(methodSignature, TenantID.class, TenantID::value);
        if (Objects.nonNull(func)) {
            return func;
        }

        final Function<Object[], String> funcByParameterTypeAnn = getFuncByParameterTypeFieldAnn(methodSignature, TenantID.class);
        if (Objects.nonNull(funcByParameterTypeAnn)) {
            return funcByParameterTypeAnn;
        }

        return p -> null;
    }

    /**
     * 获取数据id的优先级 参数注解 > 参数名称 > 参数类注解
     */
    private static Function<Object[], String> getDataIdFunction(MethodSignature methodSignature) {
        final Function<Object[], String> func = getFuncByParameterAnn(methodSignature, DataId.class);
        if (Objects.nonNull(func)) {
            return func;
        }

        final String[] parameterNames = getParameterNames(methodSignature);
        final Class[] parameterTypes = methodSignature.getParameterTypes();
        for (int i = 0; i < parameterNames.length; i++) {
            if (parameterTypes[i].equals(String.class) && StringUtils.containsIgnoreCase(parameterNames[i], "DataId")) {
                int index = i;
                return p -> (String) p[index];
            }
        }

        final Function<Object[], String> funcByParameterTypeAnn = getFuncByParameterTypeAnn(methodSignature, DataId.class, DataId::value);
        if (Objects.nonNull(funcByParameterTypeAnn)) {
            return funcByParameterTypeAnn;
        }

        return p -> null;
    }

    private static <A extends Annotation> Function<Object[], String> getFuncByParameterAnn(MethodSignature methodSignature, Class<A> annotationClass) {
        final Method method = methodSignature.getMethod();
        final Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            final Annotation annotation = AnnotationUtils.findAnnotation(parameters[i], annotationClass);
            if (Objects.nonNull(annotation)) {
                int index = i;
                return p -> (String) p[index];
            }
        }
        return null;
    }

    private static <A extends Annotation> Function<Object[], String> getFuncByParameterAnnSpel(MethodSignature methodSignature, Class<A> annotationClass, Function<A, String> spelFunc) {
        final Method method = methodSignature.getMethod();
        final Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            final A annotation = AnnotationUtils.findAnnotation(parameters[i], annotationClass);
            if (Objects.nonNull(annotation)) {
                final String spel = spelFunc.apply(annotation);
                if (StringUtils.isEmpty(spel)) {
                    int index = i;
                    return p -> (String) p[index];
                }

                final String[] parameters1 = AspectSpelUtil.getParameters(method);
                return p -> {
                    final StandardEvaluationContext context = AspectSpelUtil.getStandardEvaluationContext(parameters1, p, null);
                    return AspectSpelUtil.getSpelValue(spel, context);
                };
            }
        }
        return null;
    }

    private static <A extends Annotation> Function<Object[], String> getFuncByParameterTypeAnn(MethodSignature methodSignature, Class<A> annotationClass, Function<A, String> spelFunc) {
        final Class<?>[] parameterTypes = methodSignature.getParameterTypes();
        for (int i = 0; i < parameterTypes.length; i++) {
            if (parameterTypes[i].isPrimitive() || parameterTypes[i].equals(String.class) || Collection.class.isAssignableFrom(parameterTypes[i])) {
                continue;
            }

            final A annotation = AnnotationUtils.findAnnotation(parameterTypes[i], annotationClass);
            if (Objects.nonNull(annotation)) {
                final String apply = spelFunc.apply(annotation);
                int index = i;
                return p -> {
                    try {
                        return (String) PropertyUtils.getProperty(p[index], apply);
                    } catch (Exception e) {
                        log.warn("getProperty error class:{} annotation:{}", p[index].getClass().getSimpleName(), annotation, e);
                        return null;
                    }
                };
            }
        }

        return null;
    }

    private static <A extends Annotation> Function<Object[], String> getFuncByParameterTypeFieldAnn(MethodSignature methodSignature, Class<A> annotationClass) {
        final Class<?>[] parameterTypes = methodSignature.getParameterTypes();
        for (int i = 0; i < parameterTypes.length; i++) {
            if (parameterTypes[i].isPrimitive() || parameterTypes[i].equals(String.class) || Collection.class.isAssignableFrom(parameterTypes[i])) {
                continue;
            }

            final Field[] declaredFields = parameterTypes[i].getDeclaredFields();
            if (Objects.isNull(declaredFields) || declaredFields.length == 0) {
                continue;
            }

            final String s = Arrays.stream(declaredFields)
                    .filter(field -> field.isAnnotationPresent(annotationClass))
                    .findFirst()
                    .map(Field::getName)
                    .orElse(null);
            if (StringUtils.isBlank(s)) {
                continue;
            }

            int index = i;
            return p -> {
                try {
                    return (String) PropertyUtils.getProperty(p[index], s);
                } catch (Exception e) {
                    log.warn("getProperty error class:{} fieldName:{}", p[index].getClass().getSimpleName(), s, e);
                    return null;
                }
            };
        }

        return null;
    }
}
