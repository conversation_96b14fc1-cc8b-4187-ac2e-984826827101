package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFeatureSyncEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR> (^_−)☆
 * @date 2021/5/20
 */
public interface ErpFeatureSyncDao extends ErpBaseDao<ErpFeatureSyncEntity>, ITenant<ErpFeatureSyncDao> {

    List<String> queryDistinctTenantId();

    List<ErpFeatureSyncEntity> queryAllByTenantId(@Param("tenantId")String tenantId);
}