package com.fxiaoke.open.erpsyncdata.web.transfer.handler;

import com.facishare.transfer.handler.page.PageTransferArg;
import com.facishare.transfer.handler.page.PageTransferResult;
import com.facishare.transfer.handler.page.TargetDataSource;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.InitLastSyncTimeService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:52:39
 */
@Component
public class ResetSyncTimeHandler extends ErpRateLimitPageTransferHandler<String, SyncPloyDetailEntity, Integer> {
    @Autowired
    private InitLastSyncTimeService initLastSyncTimeService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;


    @Override
    protected TargetDataSource<String, Integer> getTargetDataSource(int enterpriseId) throws Throwable {
        return new TargetDataSource<String, Integer>(enterpriseId) {
            @Override
            protected Integer targetData(String key) throws Throwable {
                return null;
            }
        };
    }

    @Override
    protected void update(int enterpriseId, String key, SyncPloyDetailEntity entity, Integer targetData) throws Throwable {
        final String tenantId = String.valueOf(enterpriseId);
        adminSyncPloyDetailService.doLastSyncTime(tenantId, entity.getSourceDataCenterId(), entity.getSourceObjectApiName(), entity.getSyncRules());
    }

    @Override
    protected PageTransferResult<String, SyncPloyDetailEntity> getSourceDataPage(PageTransferArg<String> arg) {
        final String tenantId = String.valueOf(arg.getEnterpriseId());
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setGlobalTenant(tenantId).listBySourceType(tenantId, TenantType.ERP);
        final Map<String, SyncPloyDetailEntity> collect = syncPloyDetailEntities.stream()
//                停用的集成流,sync_time也需要重置
//                .filter(entity -> Objects.equals(entity.getStatus(), SyncPloyDetailStatusEnum.ENABLE.getStatus()))
                .peek(entity -> {
                    if (StringUtils.isNotEmpty(entity.getSyncRules().getSyncType()) && CollectionUtils.isEmpty(entity.getSyncRules().getSyncTypeList())) {
                        entity.getSyncRules().setSyncTypeList(Lists.newArrayList(entity.getSyncRules().getSyncType()));
                    }
                })
                .collect(Collectors.toMap(SyncPloyDetailEntity::getId, Function.identity()));
        return new PageTransferResult<>(collect, null, false);
    }
}
