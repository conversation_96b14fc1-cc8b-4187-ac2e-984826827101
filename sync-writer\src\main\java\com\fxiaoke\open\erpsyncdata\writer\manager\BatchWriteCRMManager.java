package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.converter.manager.ReSyncDataNodeManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LocalDispatcherUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.writer.model.BatchDoWriteData;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**hardy: 22.8.23
 *
 * 批量写CRM
 * */

@Slf4j
@Service
public class BatchWriteCRMManager {
    @Autowired
    private DoWrite2CrmManager doWrite2CrmManager;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private ReSyncDataNodeManager reSyncDataNodeManager;
    @Autowired
    private SyncLogManager syncLogManager;

    private final LocalDispatcherUtil<BatchDoWriteData> localDispatcherUtil = new LocalDispatcherUtil<>((key,dataList)->{
        //批量处理的代码放这里
        batchDoWriteNotRepeated(dataList);
    });

    private void batchDoWriteNotRepeated(List<BatchDoWriteData> dataList) {
        List<List<BatchDoWriteData>> notRepeatedList=getNotRepeatedIdDataList(dataList);
        for(List<BatchDoWriteData> list:notRepeatedList){
            batchDoWrite(list);
        }
    }

    @PostConstruct
    public void postInit() {
        IConfigFactory factory = ConfigFactory.getInstance();
        IChangeableConfig config = factory.getConfig("erp-sync-data-all", iConfig -> {
            String queueCapacity = iConfig.get("BATCH_WRITE_CRM_QUEUE_CAPACITY", "100");
            String threadSize = iConfig.get("BATCH_WRITE_CRM_THREAD_SIZE", "3");
            String timeLimitSecond = iConfig.get("BATCH_WRITE_CRM_TIME_LIMIT_SECOND", "5");
            localDispatcherUtil.setQueueCapacity(Integer.valueOf(queueCapacity));
            localDispatcherUtil.setBatchProcessTimeLimitInSecond(Integer.valueOf(timeLimitSecond));
            log.info("reload BatchWriteCRMManager  cms queueCapacity:{},threadSize:{},timeLimitSecond:{} ", queueCapacity,threadSize,timeLimitSecond);
        });
    }

    public void putData(BatchDoWriteData data) {
        SyncDataContextEvent mainContext = data.getMainContext();
        String key = mainContext.getDestTenantId() + mainContext.getDestObjectApiName() + mainContext.getDestEventType();
        //按照企业+对象+动作分组
        localDispatcherUtil.produceData(key, data, data.count());
    }

    /**
     * 填充线程变量
     */
    public void fillLocal(List<BatchDoWriteData> batchDoWriteDataList){
        try {
            //后面初始化了logId
//            SyncLogBaseInfo syncLogBaseInfo = firstMain.getSyncLogBaseInfo();
//            //批量写时，只能取第一条的logId了。
//            LogIdUtil.setBaseLog(syncLogBaseInfo);
            for (BatchDoWriteData batchDoWriteData : batchDoWriteDataList) {
                SyncDataContextEvent mainEvent = batchDoWriteData.getMainContext();
                syncDataFixDao.insertCache(JacksonUtil.fromJson(mainEvent.getSyncDataEntityStr(), new TypeReference<SyncDataEntity>() {
                }));
                if (batchDoWriteData.getDetailContextList() != null) {
                    for (SyncDataContextEvent detailEvent : batchDoWriteData.getDetailContextList()) {
                        syncDataFixDao.insertCache(JacksonUtil.fromJson(detailEvent.getSyncDataEntityStr(), new TypeReference<SyncDataEntity>() {
                        }));
                    }
                }
            }
        } catch (Throwable th) {
            log.error("batch do write 2 crm ,fill local error", th);
        }
    }

    public void batchDoWrite(List<BatchDoWriteData> batchDoWriteDataList){
        SyncDataContextEvent firstMain=batchDoWriteDataList.get(0).getMainContext();
        String tenantId = firstMain.getTenantId();
        String destObjectApiName = firstMain.getDestObjectApiName();
        Integer destEventType = firstMain.getDestEventType();
        //填充logId，syncdata等线程变量
        fillLocal(batchDoWriteDataList);
        List<SyncDataContextEvent> allCtxList = new ArrayList<>();
        List<SyncDataContextEvent> mainCtxList = batchDoWriteDataList.stream().map(v -> v.getMainContext()).collect(Collectors.toList());
        try{
            final String objectApiName1 = destObjectApiName;
            // 初始化logId,traceId
            final String logId = syncLogManager.initLogId(tenantId, objectApiName1 + "BatchWrite2Crm");
            TraceUtil.initTrace(logId);
            TraceUtil.setEi(tenantId);
            //执行主对象数据
            allCtxList.addAll(mainCtxList);
            Result<Void> mainResult = doWrite2CrmManager.batchDoWriteAndAfter(tenantId, destObjectApiName, destEventType, mainCtxList);
            //可能存在部分成功，所以需要分别取出主成功的和失败的数据
            List<BatchDoWriteData> successCtxList = new ArrayList<>();
            List<BatchDoWriteData> failedCtxList = new ArrayList<>();
            for (BatchDoWriteData batchDoWriteData : batchDoWriteDataList) {
                if (batchDoWriteData.getMainContext().getWriteResult() != null
                        && batchDoWriteData.getMainContext().getWriteResult().isSuccess()) {
                    successCtxList.add(batchDoWriteData);
                } else {
                    failedCtxList.add(batchDoWriteData);
                }
            }
            //主成功的，从执行批量写
            Map<String, List<SyncDataContextEvent>> mainSuccessDetailCtxMap = successCtxList.stream()
                    .flatMap(v -> CollUtil.emptyIfNull(v.getDetailContextList()).stream())
                    .collect(Collectors.groupingBy(v -> v.getDestObjectApiName()));
            mainSuccessDetailCtxMap.forEach((detailObj, detailsCtxList) -> {
                doWrite2CrmManager.batchDoWriteAndAfter(tenantId, detailObj, destEventType, detailsCtxList);
                allCtxList.addAll(detailsCtxList);
            });
            for (BatchDoWriteData mainFailedCtx : failedCtxList) {
                //从的直接修改为失败
                if (mainFailedCtx.getDetailContextList() != null) {
                    for (SyncDataContextEvent detailCtx : mainFailedCtx.getDetailContextList()) {
                        detailCtx.newError(destEventType, detailCtx.getSyncDataId(), I18NStringEnum.kMainBatchWriteFailed.getText());

                        doWrite2CrmManager.afterBatchDoWrite(tenantId, mainFailedCtx.getDetailContextList());
                    }
                }
            }
        } finally {
            for (SyncDataContextEvent syncStepData : mainCtxList) {
                //只有主对象数据需要完成
                if (syncStepData.getSyncDataId() != null) {
                    TimePointRecorderStatic.recordSync("allFinish", syncStepData.getSyncDataId());
                }
            }
            batchSaveErrorSyncDataByCache(tenantId, allCtxList);
            syncDataFixDao.removeCacheAndInsertDb(tenantId);
            LogIdUtil.clear();
        }
    }

    public List<List<BatchDoWriteData>> getNotRepeatedIdDataList(List<BatchDoWriteData> tenantObjEvent) {
        if(CollectionUtils.isEmpty(tenantObjEvent)){
            return Lists.newArrayList();
        }
        List<List<BatchDoWriteData>> dataList = Lists.newArrayList();
        //destData,destData.id为空的放在第一页（大概率不会有这种数据）
        List<BatchDoWriteData> onePage = tenantObjEvent.stream()
                .filter(v -> v.getMainContext().getDestData() == null || v.getMainContext().getDestData().getId() == null).collect(Collectors.toList());
        if (onePage == null) {
            onePage = Lists.newArrayList();
        }
        dataList.add(onePage);
        Map<String, List<BatchDoWriteData>> collect = tenantObjEvent.stream()
                .filter(v -> v.getMainContext().getDestData() != null && v.getMainContext().getDestData().getId() != null)
                .collect(Collectors.groupingBy(v -> v.getMainContext().getDestData().getId()));
        for (List<BatchDoWriteData> list : collect.values()) {
            for (int i = 0; i < list.size(); i++) {
                if (dataList.size() == i) {
                    dataList.add(Lists.newArrayList());
                }
                dataList.get(i).add(list.get(i));
            }
        }
        return dataList;
    }

    private void batchSaveErrorSyncDataByCache(String tenantId, List<SyncDataContextEvent> syncStepDataList) {
        log.debug("batchSaveErrorSyncDataByCache,syncStepDataList.size={}", CollectionUtils.isNotEmpty(syncStepDataList) ? syncStepDataList.size():0);
        //从缓存取出SyncData，保存错误的SyncData到重试集合,删除成功的重试数据
        Map<String, SyncDataEntity> tenantSyncDataCache = syncDataFixDao.getTenantSyncDataCache(tenantId);
        log.debug("batchSaveErrorSyncDataByCache,tenantSyncDataCache={},dataList={}",tenantSyncDataCache);
        for(SyncDataContextEvent syncDataContextEvent:syncStepDataList){
            SyncDataEntity syncDataEntity=tenantSyncDataCache.get(syncDataContextEvent.getSyncDataId());
            log.debug("batchSaveErrorSyncDataByCache,syncDataEntity={},syncStepDataList={}",syncDataEntity,syncStepDataList);
            if(syncDataEntity!=null){
                reSyncDataNodeManager.saveErrorSyncDataByCache(syncDataContextEvent, Lists.newArrayList(syncDataEntity));
            }
        }
    }
}
