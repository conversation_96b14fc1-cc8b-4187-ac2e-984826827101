package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViewArg {

    @SerializedName("CreateOrgId")
    @JsonProperty("CreateOrgId")
    private Integer createOrgId;

    @SerializedName("Number")
    @JsonProperty("Number")
    private String number;
    /**
     * K3Id，编码和id同时传时会使用id查找
     */
    @SerializedName("Id")
    @JsonProperty("Id")
    private String id;
}
