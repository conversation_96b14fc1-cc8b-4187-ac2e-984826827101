package com.fxiaoke.open.erpsyncdata.web.interceptor;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;


/**
 * <AUTHOR>
 */
public class UserContextHolder {

    /**
     * alibaba开源的支持线程池传递的线程变量
     * 注：需要修饰线程池才能使用参考com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController
     */
    private static final TransmittableThreadLocal<UserVo> USER_VO_HOLDER = new TransmittableThreadLocal<>();
    /**
     * 代管企业的holder，单独出来，不影响原线程变量。
     * 来源：request body的arg
     */
    private static final TransmittableThreadLocal<UserVo> HOSTED_USER_VO_HOLDER = new TransmittableThreadLocal<>();

    public static UserVo getUserVo() {
        return USER_VO_HOLDER.get();
    }

    public static void setUserVo(UserVo userVo) {
        USER_VO_HOLDER.set(userVo);
    }

    public static UserVo getHostedUserVo() {
        return HOSTED_USER_VO_HOLDER.get();
    }


    public static void setHostedUserVo(UserVo userVo) {
        HOSTED_USER_VO_HOLDER.set(userVo);
    }

    static void remove() {
        USER_VO_HOLDER.remove();
        HOSTED_USER_VO_HOLDER.remove();
    }
}
