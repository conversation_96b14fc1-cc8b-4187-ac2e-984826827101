package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UpstreamAlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25 16:28:27
 */
@Repository
public class UpstreamAlertAggregationDao extends BaseDao<UpstreamAlertAggregationEntity> {

    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }

    public UpstreamAlertAggregationEntity incAlarmLevel(String tenantId, String downstreamId, String dcId, List<String> ployDetailIdList, AlarmLevel alarmLevel) {
        final Query<UpstreamAlertAggregationEntity> query = createQuery(ImmutableMap.of("tenant_id", tenantId, "dc_id", dcId));
        final UpdateOperations<UpstreamAlertAggregationEntity> update = createUpdate("update_time", System.currentTimeMillis());

        String incField;
        String tenantField;
        String ployField;
        switch (alarmLevel) {
            case URGENT:
                incField = "urgent_count";
                tenantField = "urgent_tenant_ids";
                ployField = "urgent_ploy_ids";
                break;
            case IMPORTANT:
                incField = "important_count";
                tenantField = "important_tenant_ids";
                ployField = "important_ploy_ids";
                break;
            case GENERAL:
            default:
                incField = "general_count";
                tenantField = "general_tenant_ids";
                ployField = "general_ploy_ids";
                break;
        }
        update.inc(incField, 1);
        update.add(tenantField, downstreamId, false);
        if (CollectionUtils.isNotEmpty(ployDetailIdList)) {
            update.addAll(ployField, ployDetailIdList, false);
        }

        update.setOnInsert("create_time", System.currentTimeMillis());
        return datastore.findAndModify(query, update, false, true);
    }

    public UpstreamAlertAggregationEntity getAndDelete(String id) {
        final Query<UpstreamAlertAggregationEntity> query = createQuery("_id", new ObjectId(id));
        query.order("_id");
        return datastore.findAndDelete(query);
    }

    public UpstreamAlertAggregationEntity getFirstAndDelete(Integer upstreamAlertAggDelayTimeSeconds) {
        final Query<UpstreamAlertAggregationEntity> query = createQuery();
        query.field("create_time").lessThan(System.currentTimeMillis() - upstreamAlertAggDelayTimeSeconds * 1000);
        query.order("_id");

        return datastore.findAndDelete(query);
    }

    public UpstreamAlertAggregationEntity getAndDeleteLastByTenantId(String tenantId) {
        final Query<UpstreamAlertAggregationEntity> query = createQuery("tenant_id", tenantId);
        query.order("-_id");

        return datastore.findAndDelete(query);
    }
}
