<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <meta charset="UTF-8"/>
    <title>amis admin</title>
    <link rel="shortcut icon" href="${cdnBaseUrl}/amis-sdk-2.9.0/static-resource/erpdss.png" type="image/x-icon">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge"/>
    <%-- https://a.fspage.com/FSR/weex/erpdss --%>
    <link rel="stylesheet" href="${cdnBaseUrl}/amis-sdk-2.9.0/sdk.css"/>
    <link rel="stylesheet" href="${cdnBaseUrl}/amis-sdk-2.9.0/helper.css"/>
    <link rel="stylesheet" href="${cdnBaseUrl}/amis-sdk-2.9.0/iconfont.css"/>
    <script src="${cdnBaseUrl}/amis-sdk-2.9.0/sdk.js"></script>
    <script src="${cdnBaseUrl}/amis-sdk-2.9.0/static-resource/history.js"></script>
    <style>
        html,
        body,
        .app-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .content {
            position: relative;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }
        .waterMarker {
            position: absolute;
            font-size: 20px;
            opacity: 0.08;
            color: #aaa;
            z-index: 9999;
            transform: rotate(-45deg);
            white-space: nowrap;
            /*确保水印不会干扰页面上的其他元素的交互*/
            pointer-events: none;
            user-select: none;
            -webkit-user-select: none; /* Safari 3.1+ */
            -moz-user-select: none; /* Firefox 2+ */
            -ms-user-select: none; /* Internet Explorer 10+ */
        }
    </style>
</head>
<body>
<div id="root" class="content">
    <div id="amis" class="app-wrapper">
    </div>
</div>
<script>
    (function () {
        // window.enableAMISDebug = true;
        let amis = amisRequire('amis/embed');
        const match = amisRequire('path-to-regexp').match;

        // 如果想用 browserHistory 请切换下这处代码, 其他不用变
        // const history = History.createBrowserHistory();
        const history = History.createHashHistory();

        const app = {
            type: 'app',
            brandName: 'FS内部工具',
            logo: '${cdnBaseUrl}/amis-sdk-2.9.0/static-resource/erpdss.png',
            header: {
                type: 'tpl',
                inline: false,
                className: 'w-full',
                tpl: '<div class="flex justify-between">' +
                    '<div class="text-danger font-bold text-xl">仅供云星辰连接器客户自行查询云星辰对象列表</div>' +
                    '<div>使用<a href="https://aisuda.bce.baidu.com/amis/zh-CN/docs/index" target="_blank">amis</a>构建</div>' +
                    '</div>'
            },
            // footer: '<div class="p-2 text-center bg-light">底部区域</div>',
            // asideBefore: '<div class="p-2 text-center">菜单前面区域</div>',
            // asideAfter: '<div class="p-2 text-center">菜单后面区域</div>',
            api: './pages/index.json'
        };

        function normalizeLink(to, location = history.location) {
            to = to || '';

            if (to && to[0] === '#') {
                to = location.pathname + location.search + to;
            } else if (to && to[0] === '?') {
                to = location.pathname + to;
            }

            const idx = to.indexOf('?');
            const idx2 = to.indexOf('#');
            let pathname = ~idx
                ? to.substring(0, idx)
                : ~idx2
                    ? to.substring(0, idx2)
                    : to;
            let search = ~idx ? to.substring(idx, ~idx2 ? idx2 : undefined) : '';
            let hash = ~idx2 ? to.substring(idx2) : location.hash;

            if (!pathname) {
                pathname = location.pathname;
            } else if (pathname[0] != '/' && !/^https?\:\/\//.test(pathname)) {
                let relativeBase = location.pathname;
                const paths = relativeBase.split('/');
                paths.pop();
                let m;
                while ((m = /^\.\.?\//.exec(pathname))) {
                    if (m[0] === '../') {
                        paths.pop();
                    }
                    pathname = pathname.substring(m[0].length);
                }
                pathname = paths.concat(pathname).join('/');
            }

            return pathname + search + hash;
        }

        function isCurrentUrl(to, ctx) {
            if (!to) {
                return false;
            }
            const pathname = history.location.pathname;
            const link = normalizeLink(to, {
                ...location,
                pathname,
                hash: ''
            });

            if (!~link.indexOf('http') && ~link.indexOf(':')) {
                let strict = ctx && ctx.strict;
                return match(link, {
                    decode: decodeURIComponent,
                    strict: typeof strict !== 'undefined' ? strict : true
                })(pathname);
            }

            return decodeURI(pathname) === link;
        }

        let amisInstance = amis.embed(
            '#amis',
            app,
            {
                location: history.location
            },
            {
                // watchRouteChange: fn => {
                //   return history.listen(fn);
                // },
                responseAdaptor(api, payload, query, request, response) {
                    //适配平台结果解析
                    if (payload.errCode != null) {
                        if (payload.errCode !== "s106240000") {
                            payload.status = -1
                            payload.msg = payload.errMsg
                        } else {
                            payload.status = 0
                        }
                    }
                    return payload;
                },
                updateLocation: (location, replace) => {
                    location = normalizeLink(location);
                    if (location === 'goBack') {
                        return history.goBack();
                    } else if (
                        (!/^https?\:\/\//.test(location) &&
                            location ===
                            history.location.pathname + history.location.search) ||
                        location === history.location.href
                    ) {
                        // 目标地址和当前地址一样，不处理，免得重复刷新
                        return;
                    } else if (/^https?\:\/\//.test(location) || !history) {
                        return (window.location.href = location);
                    }

                    history[replace ? 'replace' : 'push'](location);
                },
                jumpTo: (to, action) => {
                    if (to === 'goBack') {
                        return history.goBack();
                    }

                    to = normalizeLink(to);

                    if (isCurrentUrl(to)) {
                        return;
                    }

                    if (action && action.actionType === 'url') {
                        action.blank === false
                            ? (window.location.href = to)
                            : window.open(to, '_blank');
                        return;
                    } else if (action && action.blank) {
                        window.open(to, '_blank');
                        return;
                    }

                    if (/^https?:\/\//.test(to)) {
                        window.location.href = to;
                    } else if (
                        (!/^https?\:\/\//.test(to) &&
                            to === history.pathname + history.location.search) ||
                        to === history.location.href
                    ) {
                        // do nothing
                    } else {
                        history.push(to);
                    }
                },
                isCurrentUrl: isCurrentUrl
            }
        );

        history.listen(state => {
            amisInstance.updateProps({
                location: state.location || state
            });
        });
    })();
</script>
<script>
    <%--增加水印，白色背景肉眼基本不可见--%>
    document.addEventListener("DOMContentLoaded", function () {
        var parent = document.getElementById('root');
        for (let i = 0; i < 8; i++) {
            for (let j = 0; j < 8; j++) {
                let newDiv = document.createElement('div');
                newDiv.innerText = "${userName}"
                newDiv.classList.add("waterMarker")
                let style = newDiv.style;
                style.top = (4 + 12 * i) + "%"
                style.left = (4 + 12 * j) + "%"
                parent.appendChild(newDiv)
            }
        }
    })

</script>
</body>
</html>
