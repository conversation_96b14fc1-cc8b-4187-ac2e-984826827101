{"type": "page", "body": [{"type": "form", "title": "创建索引", "api": {"url": "../ddl/pg/createIndexes", "method": "post", "responseData": {"result": "${a}"}}, "body": [{"type": "input-text", "name": "tenantId", "label": "企业Id（决定数据库路由）"}, {"type": "input-text", "name": "table", "label": "表名或表名前缀(仅允许sync_data_mappings开头，不传查所有sync_data_mappings表）"}, {"type": "input-text", "name": "indexPrefix", "label": "索引前缀（每个企业会拼上tenantId)"}, {"type": "input-text", "name": "index", "label": "索引语句，表名后的语句 如(id)"}, {"type": "input-text", "name": "tenantIdStr", "label": "限定企业（逗号分隔）"}, {"type": "checkbox", "name": "skipFailed", "label": "是否跳过失败的企业"}, {"type": "static", "name": "indexes", "visibleOn": "typeof data.result !== 'undefined'", "label": "结果"}]}, {"type": "form", "title": "删除索引", "api": {"url": "../ddl/pg/dropIndexes", "method": "post", "responseData": {"result": "${a}"}}, "body": [{"type": "input-text", "name": "tenantId", "label": "企业Id（决定数据库路由）"}, {"type": "input-text", "name": "table", "label": "表名或表名前缀(仅允许sync_data_mappings开头，不传查所有sync_data_mappings表）"}, {"type": "input-text", "name": "indexPrefix", "label": "索引前缀（每个企业会拼上tenantId)"}, {"type": "input-text", "name": "index", "label": "索引语句，表名后的语句 如(id)"}, {"type": "input-text", "name": "tenantIdStr", "label": "限定企业（逗号分隔）"}, {"type": "checkbox", "name": "skipFailed", "label": "是否跳过失败的企业"}, {"type": "static", "name": "indexes", "visibleOn": "typeof data.result !== 'undefined'", "label": "结果"}]}]}