package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * erp数据特征表
 * <AUTHOR> (^_−)☆
 * @date 2021/5/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objectApiName")
@Table(name = "erp_data_feature")
public class ErpDataFeatureEntity implements Serializable {
    @Id
    private String id;

    /**
    * 企业Id
    */
    @TenantID
    private String tenantId;

    /**
    * 中间对象apiName
    */
    private String objectApiName;

    /**
    * 数据Id
    */
    private String dataId;

    /**
    * 特征值
    */
    private String feature;

    private Date createTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;
}