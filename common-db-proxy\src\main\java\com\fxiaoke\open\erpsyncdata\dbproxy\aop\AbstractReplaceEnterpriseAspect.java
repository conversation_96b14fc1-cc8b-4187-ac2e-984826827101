package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.context.request.async.DeferredResult;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/2/21 09:50:22
 * <p>
 */
@Slf4j
public abstract class AbstractReplaceEnterpriseAspect {

    @Autowired
    @Lazy
    protected ConfigCenterConfig configCenterConfig;

    @Autowired
    protected RelationErpShardDao relationErpShardDao;

    @Autowired
    private I18NStringManager i18NStringManager;

    protected static final Pattern TenantIdPattern = Pattern.compile("(source|dest)?[Tt]enantId(s)?");
    protected static final Map<Method, Integer> MethodTenantIdIndexMap = new ConcurrentHashMap<>();
    protected static final Map<Method, Integer> MethodTenantIdsIndexMap = new ConcurrentHashMap<>();

    protected static final Map<Class<?>, ReplaceData<?>> typeReplaceDataMap = new ConcurrentHashMap<>();

    protected static final Set<Class<?>> wrapperClasses = Sets.newHashSet(
            Boolean.class,
            Byte.class,
            Character.class,
            Short.class,
            Integer.class,
            Long.class,
            Float.class,
            Double.class,
            String.class,
            Object.class
    );

    protected List<String> getTenantIdsByParameter(ProceedingJoinPoint jp) {
        final Integer index = getTenantIdsIndexByParameter(jp);
        if (Objects.isNull(index)) {
            return null;
        }

        return (List<String>) jp.getArgs()[index];
    }


    protected String getTenantIdByParameter(JoinPoint jp) {
        final Integer index = getTenantIdIndexByParameter(jp);
        if (Objects.isNull(index)) {
            return null;
        }

        return (String) jp.getArgs()[index];
    }

    /**
     * 检查是否为代管企业
     */
    protected boolean managedEnterprise(String tenantId) {
        return StringUtils.isNotBlank(tenantId) && configCenterConfig.isManagedEnterprise(tenantId, false);
    }

    /**
     * 获取模板企业信息
     */
    protected String getTemplateId(String tenantId) {
        final RelationErpShardDto relationErpShardDto = relationErpShardDao.queryFirstNormalByDownstreamId(tenantId);
        return Objects.isNull(relationErpShardDto) ? null : relationErpShardDto.getTemplateId();
    }

    protected Integer getTenantIdIndexByParameter(JoinPoint proceedingJoinPoint) {
        return getByCache(proceedingJoinPoint, MethodTenantIdIndexMap, this::getTenantIdIndex);
    }

    protected Integer getTenantIdsIndexByParameter(ProceedingJoinPoint proceedingJoinPoint) {
        return getByCache(proceedingJoinPoint, MethodTenantIdsIndexMap, this::getTenantIdsIndex);
    }

    public static <T> T getByCache(JoinPoint proceedingJoinPoint, Map<Method, T> cache, Function<MethodSignature, T> func) {
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        final Method method = methodSignature.getMethod();

        return cache.computeIfAbsent(method, k -> func.apply(methodSignature));
    }

    protected Integer getTenantIdIndex(MethodSignature methodSignature) {
        return getParameterIndex(methodSignature, String.class, TenantIdPattern);
    }

    protected Integer getTenantIdsIndex(MethodSignature methodSignature) {
        return getParameterIndex(methodSignature, List.class, TenantIdPattern);
    }

    protected Integer getParameterIndex(MethodSignature methodSignature, Class<?> type, Pattern pattern) {
        final String[] parameterNames = getParameterNames(methodSignature);
        final Class[] parameterTypes = methodSignature.getParameterTypes();
        for (int i = 0; i < parameterNames.length; i++) {
            if (parameterTypes[i].equals(type) && pattern.matcher(parameterNames[i]).matches()) {
                return i;
            }
        }

        return null;
    }

    protected String[] getParameterNames(MethodSignature methodSignature) {
        return InvokeErrorMonitorAspect.getParameterNames(methodSignature);
    }

    public Object replaceTenantId(ProceedingJoinPoint jp, String downstreamId) throws Throwable {
        final Integer index = getTenantIdIndexByParameter(jp);
        final String templateId = getTemplateId(downstreamId);
        if (Objects.isNull(templateId)) {
            return jp.proceed();
        }

        final Object[] args = jp.getArgs();
        args[index] = templateId;

        final Object proceed = jp.proceed(args);

        return replaceResultTenantId(proceed, downstreamId);
    }

    /**
     * 处理入参只有一个下游企业的情况,将返回值都改为下游企业id
     */
    protected Object replaceResultTenantId(Object proceed, String downstreamId) {
        if (Objects.isNull(proceed) || isPrimitive(proceed)) {
            return proceed;
        }

        if (proceed instanceof List && CollectionUtils.isNotEmpty((List<?>) proceed)) {
            ((List<?>) proceed).stream()
                    .filter(o -> Objects.nonNull(o) && !isPrimitive(o))
                    .forEach(o -> getReplaceData(o).getReplaceTenantIdFunc().accept(o, downstreamId));
        }

        getReplaceData(proceed).getReplaceTenantIdFunc().accept(proceed, downstreamId);

        return proceed;
    }

    public Object replaceTenantIdList(ProceedingJoinPoint jp, List<String> tenantIds) throws Throwable {
        // key: downstreamId, value: templateId
        final Map<String, String> downstreamIdMap = tenantIds.stream()
                .filter(ei -> Objects.nonNull(ei) && managedEnterprise(ei))
                .distinct()
                .map(ei -> {
                    final String templateId = getTemplateId(ei);
                    if (Objects.isNull(templateId)) {
                        return null;
                    }
                    return Pair.of(ei, templateId);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (a, b) -> a));
        // 替换为模板企业id
        final List<String> newTenantIds = Stream.concat(
                        tenantIds.stream().filter(ei -> !downstreamIdMap.containsKey(ei)),
                        downstreamIdMap.values().stream()
                ).distinct()
                .collect(Collectors.toList());

        final Integer index = getTenantIdsIndexByParameter(jp);
        final Object[] args = jp.getArgs();
        args[index] = newTenantIds;

        final Object proceed = jp.proceed(args);

        return replaceResultTenantIdList(proceed, tenantIds, downstreamIdMap);
    }


    /**
     * 处理入参的tenantId为多个企业的情况
     */
    protected Object replaceResultTenantIdList(Object proceed, List<String> tenantIds, Map<String, String> downstreamIdMap) {
        if (Objects.isNull(proceed) || !(proceed instanceof List) || CollectionUtils.isEmpty((List<?>) proceed) || ((List<?>) proceed).stream().allMatch(AbstractReplaceEnterpriseAspect::isPrimitive)) {
            return proceed;
        }

        List<Object> list = (List<Object>) proceed;
        // 返回值按企业Id分组
        final Map<String, List<Object>> tenantDataMap = list.stream()
                .filter(t -> Objects.nonNull(t) && !isPrimitive(t))
                .map(t -> {
                    final String tenantId = getReplaceData(t).getFindTenantIdFunc().apply(t);
                    if (Objects.isNull(tenantId)) {
                        return null;
                    }
                    return Pair.of(tenantId, t);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Pair::getKey, Collectors.mapping(Pair::getValue, Collectors.toList())));

        // key:模板企业 value:下游企业
        final Map<String, List<String>> downStreamMap = downstreamIdMap.entrySet().stream()
                .collect(Collectors.groupingBy(Map.Entry::getValue, Collectors.mapping(Map.Entry::getKey, Collectors.toList())));

        downStreamMap.forEach((manageEnterpriseId, downStreamTenantIds) -> {
            final List<?> manageData = tenantDataMap.get(manageEnterpriseId);
            if (CollectionUtils.isEmpty(manageData)) {
                return;
            }

            // 替换为下游企业id,加到返回值中
            manageData.forEach(e ->
                    downStreamTenantIds.stream().map(tenantId -> {
                        final Object copy = BeanUtil.deepCopy(e, e.getClass());
                        getReplaceData(copy).getReplaceTenantIdFunc().accept(copy, tenantId);
                        return copy;
                    }).forEach(list::add));

            // 入参没有模板企业,删除模板企业数据
            if (!tenantIds.contains(manageEnterpriseId)) {
                list.removeAll(manageData);
            }
        });

        return list;
    }

    protected static boolean isPrimitive(Object o) {
        return isPrimitive(o.getClass());
    }

    protected static boolean isPrimitive(Class<?> type) {
        return type.isPrimitive() || type.isEnum() || type.equals(String.class) || wrapperClasses.contains(type);
    }


    // 👇🏻初始化ReplaceData


    public <T> ReplaceData<T> getReplaceData(T type) {
        final Class<?> aClass = type.getClass();
        return (ReplaceData<T>) typeReplaceDataMap.computeIfAbsent(aClass, this::initReplaceData);
    }

    @NotNull
    protected <T> ReplaceData<T> initReplaceData(Class<T> type) {
        final ReplaceData<T> replaceData = new ReplaceData<>();
        replaceData.setType(type);
        final Field[] declaredFields = type.getDeclaredFields();
        if (declaredFields.length == 0) {
            setDefaultFunction(replaceData);
            return replaceData;
        }

        final List<List<Field>> tenantIdFields = findTenantIdFields(type);
        if (CollectionUtils.isEmpty(tenantIdFields)) {
            log.warn("未找到TenantID注解的字段,请检查{}类", type);
            setDefaultFunction(replaceData);
            return replaceData;
        }

        tenantIdFields.stream().flatMap(List::stream).distinct().forEach(field -> field.setAccessible(true));

        replaceData.setFindTenantIdFunc(t -> tenantIdFields.stream()
                .map(fields -> getFieldValue(t, fields))
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElse(null));

        replaceData.setReplaceTenantIdFunc((t, tenantId) -> tenantIdFields.forEach(fields -> {
            try {
                final int last = fields.size() - 1;
                final List<Field> list = Lists.newArrayList(fields);
                list.remove(last);
                final Object fieldValue = getFieldValue(t, list);
                if (Objects.nonNull(fieldValue)) {
                    fields.get(last).set(fieldValue, tenantId);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }));

        return replaceData;
    }

    @Nullable
    protected static Object getFieldValue(Object t, List<Field> fields) {
        Object tenantId = t;
        try {
            for (Field field : fields) {
                tenantId = field.get(tenantId);
                if (Objects.isNull(tenantId)) {
                    return null;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return tenantId;
    }

    /**
     * 查询对象上所有@TenantId的字段,包括嵌套的
     */
    public static List<List<Field>> findTenantIdFields(Class<?> clazz) {
        List<List<Field>> annotatedFields = new ArrayList<>();
        findTenantIdFields(clazz, annotatedFields, new ArrayList<>(), Sets.newHashSet(clazz));
        return annotatedFields;
    }

    protected static void findTenantIdFields(Class<?> clazz, List<List<Field>> annotatedFields, List<Field> currentPath, Set<Class<?>> checkedClasses) {
        for (Field field : clazz.getDeclaredFields()) {

            // 检查字段类型是否为String，并且是否有@TenantId注解
            final Class<?> type = field.getType();
            if (type.equals(String.class) && field.isAnnotationPresent(TenantID.class)) {
                List<Field> path = new ArrayList<>(currentPath);
                path.add(field);
                annotatedFields.add(path);
                continue;
            }

            // 如果字段是对象类型，则递归查找
            if (isPrimitive(type) || type.isAssignableFrom(Collection.class) || type.isAssignableFrom(Map.class) || !checkedClasses.add(type)) {
                continue;
            }

            currentPath.add(field);
            findTenantIdFields(type, annotatedFields, currentPath, checkedClasses);
            currentPath.remove(currentPath.size() - 1);
        }
    }

    /**
     * 设置默认值,防止报错
     */
    protected <T> void setDefaultFunction(ReplaceData<T> replaceData) {
        replaceData.setFindTenantIdFunc(t -> null);
        replaceData.setReplaceTenantIdFunc((t, id) -> {
        });
    }

    @Data
    public static class ReplaceData<T> {
        protected Class<T> type;
        protected Function<T, String> findTenantIdFunc;
        protected BiConsumer<T, String> replaceTenantIdFunc;
    }



    @NotNull
    protected Object errorResult(String tenantId, Class<?> returnType, ResultCodeEnum code) {
        if (returnType.equals(DeferredResult.class)) {
            final DeferredResult<Object> objectDeferredResult = new DeferredResult<>();
            final Result<Object> result = Result.newError(code);
            final String s = i18NStringManager.get(result.getI18nKey(), I18nUtil.getLocaleFromTrace(), tenantId, code.getErrMsg());
            result.setErrMsg(s);
            objectDeferredResult.setResult(result);
            return objectDeferredResult;
        }

        return Result.newError(code);
    }
}
