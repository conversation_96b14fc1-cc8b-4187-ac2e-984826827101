package com.fxiaoke.open.erpsyncdata.web.transfer;

import com.facishare.transfer.dao.DataTransferEntity;
import com.facishare.transfer.handler.TransferHandler;
import com.facishare.transfer.model.EnterpriseData;
import com.facishare.transfer.service.DataTransferMgrImpl;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/1 16:21:05
 */
@Slf4j
public class ErpDataTransferMgr extends DataTransferMgrImpl {

    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 迁移的时候是使用的线程池,所以不能使用ThreadLocal,先直接使用成员变量
     */
    @Setter
    private Long stopTime;

    @Override
    protected DataTransferEntity transfer(final boolean forceTransfer, final EnterpriseData meta, final TransferHandler handler, final boolean transferAnyway) {
        long currentTime = System.currentTimeMillis();
        final String enterpriseAccount = meta.getEnterpriseAccount();
        int enterpriseId = meta.getEnterpriseId();

        if (isIgnoreTransfer(forceTransfer, enterpriseAccount, handler)) {
            return new DataTransferEntity();
        }

        DataTransferEntity entity = DataTransferEntity.init(handler, currentTime, enterpriseAccount, enterpriseId);

        try {
            if (configCenterConfig.isStopTransfer() || (Objects.nonNull(stopTime) && System.currentTimeMillis() > stopTime)) {
                setEntityStop(entity,enterpriseId+"");
                return entity;
            }

            dataTransferDao.saveByEAEI(entity);
            handler.transfer(enterpriseId, transferAnyway);
            entity.success();
            log.info("[{}] [transfer] [finish] [enterpriseAccount:{}] [cost:{}ms]", Thread.currentThread().getName(), enterpriseAccount, entity.getCostTime());
        } catch (Throwable t) {
            log.warn("[" + Thread.currentThread().getName() + "] [transfer] [error] [enterpriseAccount:" + enterpriseAccount + "]", t);
            entity.fail(t);
        } finally {
            dataTransferDao.saveByEAEI(entity);
        }

        log.info("[transfer] [finish one enterprise] [enterpriseAccount:{}] [status:{}]", enterpriseAccount, entity.getResultCode());

        return entity;
    }

    private void setEntityStop(final DataTransferEntity entity, String tenantId) {
        String errorMessage = i18NStringManager.getByEi(I18NStringEnum.s1165,tenantId);
        entity.setResultCode(DataTransferEntity.ResultCode.fail.getCode());
        entity.setResultContent(errorMessage);
        long end = System.currentTimeMillis();
        entity.setCostTime(end - entity.getStartTime());
        entity.setEndTime(end);
    }
}
