package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.StrUtil2;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
@Component
@Slf4j
public class CommonBusinessManager {
    @ReloadableProperty("obj_number_filed_mapping")
    private String objNumberFieldMapping;
    @Autowired
    private ErpFieldManager erpFieldManager;
    /**
     * todo 需要做缓存
     * 根据number转成id
     *
     * @param formId
     * @param number
     * @param idFieldKey
     * @param numberFieldKey
     * @return
     */
    public String getIdByNumber(String formId, String number, String idFieldKey, String numberFieldKey, K3CloudApiClient apiClient) {
        return getIdByNumberAndFilter(formId,number,idFieldKey,numberFieldKey,apiClient, Maps.newHashMap());
    }


    public String getIdByNumberAndFilter(String formId, String number, String idFieldKey, String numberFieldKey, K3CloudApiClient apiClient, Map<String,String> eqFilter) {
        log.info("CommonBusinessManager.getIdByNumber,formId={},number={},idFieldKey={},numberFieldKey={}",formId,number,idFieldKey,numberFieldKey);
        if(StringUtils.isBlank(number)){
            return null;
        }
        //替换id
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(formId);
        queryArg.setFieldKeysByList(ImmutableList.of(idFieldKey, numberFieldKey));
        queryArg.setFilterString(StrUtil2.convertSpecSql(numberFieldKey,number));
        if(eqFilter!=null&&CollectionUtils.isNotEmpty(eqFilter.keySet())){
            for(String key:eqFilter.keySet()){
                queryArg.appendEqualFilter(key,eqFilter.get(key));
            }
        }
        fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);

        queryArg.setOrderString(idFieldKey + " ASC");
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        log.info("CommonBusinessManager.getIdByNumber,result={}",result);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new ErpSyncDataException(I18NStringEnum.s245,apiClient.getTenantId());
        }
        K3Model k3Model = result.getData().get(0);
        return k3Model.getString(idFieldKey);
    }

    public void fillQueryArgByViewExtend(String tenantId, String dataCenterId, QueryArg queryArg) {
        List<ErpFieldExtendEntity> byDefineType = erpFieldManager.queryIdField(tenantId, dataCenterId, queryArg.getFormId());
        if(CollectionUtils.isNotEmpty(byDefineType)){
            ErpFieldExtendEntity erpFieldExtendEntity = byDefineType.get(0);
            this.handleArgWithViewExtend(queryArg,erpFieldExtendEntity.getViewExtend());
        }
    }

    /**
     *  通过viewExtend处理queryArg
     * @param queryArg
     * @param viewExtend
     */
    public void handleArgWithViewExtend(QueryArg queryArg, String viewExtend){
        if(StringUtils.isBlank(viewExtend)){return;}
        try {
            Map<String,Object> viewExtendMap = JacksonUtil.fromJson(viewExtend, HashMap.class);
            log.debug("handleArgWithViewExtend viewExtendMap:{}", viewExtendMap);
            for (Map.Entry<String,Object> entry: viewExtendMap.entrySet()) {
                Class clz = queryArg.getClass();
                Field[] fields = clz.getDeclaredFields();
                for (Field field: fields) {
                    if(entry.getKey().equalsIgnoreCase(field.getName())){ // 配置的JSON字符串key必须与实体类字段名一致(忽略大小写)
                        field.setAccessible(true);
                        field.set(queryArg,entry.getValue());
                    }
                }
            }
            log.debug("handleArgWithViewExtend queryArg:{}", queryArg);
        }catch (Exception e){
            log.error("handleViewExtend error,viewExtend: {}, e:{}",viewExtend,e);
        }
    }

    /**
     * 根据对象编码获取对象id，这个是通用接口，需要配置中心支持
     *
     * @param formId
     * @param number
     * @return
     */
    public String getIdByNumber(String formId, String number, K3CloudApiClient apiClient) {
        log.info("CommonBusinessManager.getIdByNumber,formId={},number={},objNumberFieldMapping={}",formId,number,objNumberFieldMapping);
        HashMap<String,String> map = GsonUtil.fromJson(objNumberFieldMapping,HashMap.class);
        String id_number = map.get(formId);
        if(StringUtils.isEmpty(id_number)) return null;

        List<String> valueList = Splitter.on(",").splitToList(id_number);
        String idFieldKey = valueList.get(0);//id字段key
        String numberFieldKey = valueList.get(1);//编码字段key

        return getIdByNumber(formId,number,idFieldKey,numberFieldKey,apiClient);
    }

    /**
     * todo 需要做缓存
     * 根据number转成id
     *
     * @param formId
     * @param id
     * @param idFieldKey
     * @param numberFieldKey
     * @param apiClient
     * @return
     */
    public String getNumberById(String formId, String id, String idFieldKey, String numberFieldKey, K3CloudApiClient apiClient) {
        //替换id
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(formId);
        queryArg.setFieldKeysByList(ImmutableList.of(idFieldKey, numberFieldKey));
        queryArg.setFilterString(String.format("%s='%s'", idFieldKey, id));
        queryArg.setOrderString(idFieldKey + " ASC");
        fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new ErpSyncDataException(I18NStringEnum.s246,apiClient.getTenantId());
        }
        K3Model k3Model = result.getData().get(0);
        return k3Model.getString(numberFieldKey);
    }

    public String getFieldValue(String formId, String number,String numberFieldKey, String fieldKeys,String returnFieldKey, K3CloudApiClient apiClient) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(formId);
        queryArg.setFieldKeys(fieldKeys);
        queryArg.setFilterString(String.format("%s='%s'", numberFieldKey, number));
        fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new ErpSyncDataException(I18NStringEnum.s245,apiClient.getTenantId());
        }
        K3Model k3Model = result.getData().get(0);
        return k3Model.getString(returnFieldKey);
    }


}
