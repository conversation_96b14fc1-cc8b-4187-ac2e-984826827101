//package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;
//
//import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpAlarmRecordModel;
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseMongoStore;
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpAlarmRecordEntity;
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpAlarmRecordEntity.Fields;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
//import com.github.autoconf.ConfigFactory;
//import com.github.mongo.support.DatastoreExt;
//import com.mongodb.MongoClientSettings;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import com.mongodb.client.model.*;
//import com.mongodb.client.result.DeleteResult;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.bson.codecs.configuration.CodecRegistries;
//import org.bson.codecs.pojo.PojoCodecProvider;
//import org.bson.conversions.Bson;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.stereotype.Repository;
//
//import javax.annotation.PostConstruct;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//
//import static com.mongodb.client.model.Filters.and;
//
///**
// * 告警记录dao
// *
// * <AUTHOR>
// * @date 2023-11-23
// */
//@Slf4j
//@Repository
//public class ErpAlarmRecordDao extends BaseMongoStore<ErpAlarmRecordEntity> {
//    @Qualifier("erpSyncDataLogMongoStore")
//    @Autowired
//    private DatastoreExt store;
//    private String DATABASE;
//
//    @PostConstruct
//    void init() {
//        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
//                .get("syncLogDbName", "fs-erp-sync-data");
//    }
//
//    protected ErpAlarmRecordDao() {
//        super(CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
//                CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build())));
//    }
//
//    @Override
//    public MongoDatabase getDatabase(String tenantId) {
//        return store.getMongo().getDatabase(DATABASE);
//    }
//
//    @Override
//    public String getCollName(String tenantId) {
//        return "erp_alarm_record";
//    }
//
//    public List<IndexModel> buildIndexes(String tenantId) {
//        List<IndexModel> indexModels = new ArrayList<>();
//        indexModels.add(new IndexModel(Indexes.ascending(Fields.tenantId,
//                Fields.dataCenterId,
//                Fields.ployDetailId,
//                Fields.alarmType,
//                Fields.alarmLevel,
//                Fields.time),
//                new IndexOptions().background(true)));
//
//        indexModels.add(new IndexModel(Indexes.ascending(Fields.createTime),
//                new IndexOptions().expireAfter(90L, TimeUnit.DAYS).background(true)));
//
//        return indexModels;
//    }
//
//    private ErpAlarmRecordEntity buildData(String tenantId,
//                                           String dataCenterId,
//                                           String ployDetailId,
//                                           AlarmRuleType alarmRuleType,
//                                           String alarmRuleName,
//                                           AlarmType alarmType,
//                                           AlarmLevel alarmLevel,
//                                           Long time,
//                                           String msg,
//                                           List<Integer> userIdList) {
//        ErpAlarmRecordEntity entity = ErpAlarmRecordEntity.builder()
//                .tenantId(tenantId)
//                .dataCenterId(dataCenterId)
//                .ployDetailId(ployDetailId)
//                .alarmRuleType(alarmRuleType)
//                .alarmRuleName(alarmRuleName)
//                .alarmType(alarmType)
//                .alarmLevel(alarmLevel)
//                .time(time)
//                .msg(msg)
//                .userIdList(userIdList)
//                .traceId(TraceUtil.get())
//                .createTime(new Date())
//                .updateTime(new Date())
//                .build();
//        return entity;
//    }
//
//    public void insert(String tenantId,
//                       String dataCenterId,
//                       String ployDetailId,
//                       AlarmRuleType alarmRuleType,
//                       String alarmRuleName,
//                       AlarmType alarmType,
//                       AlarmLevel alarmLevel,
//                       Long time,
//                       String msg,
//                       List<Integer> userIdList) {
//        ErpAlarmRecordEntity entity = buildData(tenantId,
//                dataCenterId,
//                ployDetailId,
//                alarmRuleType,
//                alarmRuleName,
//                alarmType,
//                alarmLevel,
//                time,
//                msg,
//                userIdList);
//        insert(entity);
//    }
//
//    public void insert(ErpAlarmRecordEntity entity) {
//        try {
//            MongoCollection<ErpAlarmRecordEntity> collection = getOrCreateCollection(entity.getTenantId());
//            collection.insertOne(entity);
//        } catch (Exception e) {
//            log.warn("ErpAlarmRecordDao.insertIgnore,exception={}", e.getMessage());
//        }
//    }
//
//    public void batchInsert(String tenantId, List<ErpAlarmRecordEntity> entityList) {
//        MongoCollection<ErpAlarmRecordEntity> collection = getOrCreateCollection(tenantId);
//        collection.insertMany(entityList);
//    }
//
//    public ErpAlarmRecordModel getDataListByPage(String tenantId,
//                                                 String dcId,
//                                                 List<String> ployDetailIdList,
//                                                 AlarmType alarmType,
//                                                 AlarmLevel alarmLevel,
//                                                 Date startTime,
//                                                 Date endTime,
//                                                 int pageSize,
//                                                 int page) {
//        List<Bson> filterList = new ArrayList<>();
//
//        filterList.add(Filters.eq(Fields.tenantId, tenantId));
//
//        if (StringUtils.isNotEmpty(dcId)) {
//            filterList.add(Filters.eq(Fields.dataCenterId, dcId));
//        }
//
//        if (CollectionUtils.isNotEmpty(ployDetailIdList)) {
//            filterList.add(Filters.in(Fields.ployDetailId, ployDetailIdList));
//        }
//
//        if (alarmType != null) {
//            filterList.add(Filters.eq(Fields.alarmType, alarmType.name()));
//        }
//
//        if (alarmLevel != null) {
//            filterList.add(Filters.eq(Fields.alarmLevel, alarmLevel.name()));
//        }
//
//        if (startTime != null && endTime != null) {
//            filterList.add(Filters.gte(Fields.time, startTime.getTime()));
//            filterList.add(Filters.lt(Fields.time, endTime.getTime()));
//        }
//
//        Bson filters = and(filterList);
//        ErpAlarmRecordModel dataList = getDataList(tenantId, filters, pageSize, page);
//        return dataList;
//    }
//
//    private ErpAlarmRecordModel getDataList(String tenantId,
//                                            Bson filters,
//                                            int pageSize,
//                                            int page) {
//        MongoCollection<ErpAlarmRecordEntity> collection = getOrCreateCollection(tenantId);
//
//        //最多count 10W条数据，最大计算时间为10s
//        long total = collection.countDocuments(filters, new CountOptions().maxTime(10, TimeUnit.SECONDS).limit(10 * 10000));
//
//        List<ErpAlarmRecordEntity> dataList = new ArrayList<>();
//        collection.find(filters).sort(Sorts.orderBy(Sorts.descending(Fields.time))).skip(page * pageSize).limit(pageSize).into(dataList);
//
//        return new ErpAlarmRecordModel(total, dataList, null);
//    }
//
//    public List<String> getDataListByDcId(String tenantId) {
//        List<Bson> filterList = new ArrayList<>();
//
//        filterList.add(Filters.eq(Fields.tenantId, tenantId));
//        filterList.add(Filters.ne(Fields.dataCenterId, "-"));
//
//        Bson filters = and(filterList);
//
//        MongoCollection<ErpAlarmRecordEntity> collection = getOrCreateCollection("");
//
//        Set<String> set = new LinkedHashSet<>();
//        int offset = 0;
//        int limit = 100;
//        while (true) {
//            List<ErpAlarmRecordEntity> dataList = new ArrayList<>();
//            collection.find(filters).skip(offset).limit(limit).into(dataList);
//            if (CollectionUtils.isEmpty(dataList)) break;
//            for (ErpAlarmRecordEntity entity : dataList) {
//                if (!set.contains(entity.getDataCenterId())) {
//                    set.add(entity.getDataCenterId());
//                }
//            }
//            if (dataList.size() == limit) {
//                offset += limit;
//            } else {
//                break;
//            }
//        }
//        return new ArrayList<>(set);
//    }
//
//    public long deleteMany(String tenantId) {
//        MongoCollection<ErpAlarmRecordEntity> collection = getOrCreateCollection(tenantId);
//        Bson filters = and(
//                Filters.ne(Fields.tenantId, tenantId));
//
//        DeleteResult result = collection.deleteMany(filters);
//        log.info("ErpAlarmRecordDao.deleteMany,tenantId={},result={}", tenantId, result);
//        return result.getDeletedCount();
//    }
//}
