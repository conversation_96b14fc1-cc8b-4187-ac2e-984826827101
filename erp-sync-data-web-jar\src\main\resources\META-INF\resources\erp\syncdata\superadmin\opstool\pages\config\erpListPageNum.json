{"type": "page", "body": [{"type": "form", "initApi": "post:../config/getConfig", "api": "../config/upsertConfig", "title": "批量查询的条数（企业或对象）", "reload": "service", "data": {"tenantId": "0", "dataCenterId": "0", "channel": "ALL", "type": "SPECIAL_LIST_SIZE"}, "body": [{"type": "static", "name": "tenantId", "label": "tenantId"}, {"type": "static", "name": "dataCenterId", "label": "数据中心Id"}, {"type": "static", "name": "channel", "label": "渠道"}, {"type": "static", "name": "type", "label": "类型"}, {"type": "textarea", "name": "configuration", "label": "配置内容"}]}, {"type": "form", "initApi": "post:../config/getConfig", "api": "../config/upsertConfig", "title": "默认分页大小", "reload": "service", "data": {"tenantId": "0", "dataCenterId": "0", "channel": "ALL", "type": "ERP_LIST_PAGE_SIZE"}, "body": [{"type": "static", "name": "tenantId", "label": "tenantId"}, {"type": "static", "name": "dataCenterId", "label": "数据中心Id"}, {"type": "static", "name": "channel", "label": "渠道"}, {"type": "static", "name": "type", "label": "类型"}, {"type": "input-text", "name": "configuration", "label": "配置内容"}]}]}