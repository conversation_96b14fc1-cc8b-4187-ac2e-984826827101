package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/30 16:15
 * @Version 1.0
 */
@Component
@Slf4j
public class FieldDbManager {
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;

    public void convertIdArg2Erp(ErpIdArg erpIdArg) {
        String tenantId = erpIdArg.getTenantId();
        String objApiName = erpIdArg.getObjAPIName();
        String mainId = erpIdArg.getDataId();
        ErpObjectFieldEntity idField = getIdField(tenantId, objApiName);
        CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
        //原代码逻辑挪到了CompositeIdExtend里面
        erpIdArg.setDataId(compositeIdExtend.getSaveId(mainId));
//        if (compositeIdExtend.isComposite()) {
//            String viewIdField = compositeIdExtend.getViewIdField();
//            if (StringUtils.isNotBlank(viewIdField)) {
//                List<String> splitIds = Splitter.on(compositeIdExtend.getSeparator()).splitToList(mainId);
//                //兼容直接传真实Id的情况
//                if (splitIds.size()<=1){
//                    return;
//                }
//                String viewId = splitIds.get(compositeIdExtend.getSaveIdIndex());
//            }
//        }
    }

    /**
     * 判断某个企业的erp对象是用Id主键查询，还是number查询
     */
    public boolean hasUsedIdQuery(ErpIdArg erpIdArg,String dataCenterId){
        List<ErpFieldExtendEntity> erpFieldExtendEntities = erpFieldManager.queryIdField(erpIdArg.getTenantId(),dataCenterId, erpIdArg.getObjAPIName());
        if(CollectionUtils.isNotEmpty(erpFieldExtendEntities)){
            ErpFieldExtendEntity erpFieldExtendEntity=erpFieldExtendEntities.get(0);
            boolean useId = StringUtils.equalsIgnoreCase(erpFieldExtendEntity.getViewCode(), "Id");
            return useId;
        }
        return true;
    }

    /**
     * 获取关联对象
     * @param tenantId
     * @param objApiName
     * @return
     */
    public ErpObjectRelationshipEntity getRelation(String tenantId, String objApiName) {
        return erpObjManager.getRelation(tenantId, objApiName);
    }


    /**
     *
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    public ErpObjectFieldEntity getIdField(String tenantId, String objApiName) {
        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, objApiName);
        if (idField == null) {
            throw new ErpSyncDataException(I18NStringEnum.s142,tenantId);
        }
        return idField;
    }

}
