package com.fxiaoke.open.erpsyncdata.monitor.handler;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DelayDataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:01 2023/2/2
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "dataNodesTimeOutHandler")
public class DataNodesTimeOutHandler extends IJobHandler {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private DataNodeMsgDao dataNodeMsgDao;
    @Autowired
    private DelayDataNodeMsgDao delayDataNodeMsgDao;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;

    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam triggerParam) throws Exception {
        RLock rLock = redissonClient.getLock(CommonConstant.REDIS_LOCK_DATA_NODE_TIME_OUT);
        if (rLock.tryLock(2, 60 * 10, TimeUnit.SECONDS)) {
            try {
                ImmutableSet<String> topTenantSet = tenantConfigurationManager.getWhiteList(TenantConfigurationTypeEnum.TOP60_TENANTS, ",");
                Long now = System.currentTimeMillis();
                log.info("execute dataNodesTimeOutHandler,triggerParam:{}", triggerParam);
                Map<String, Long> tenantTime = tenantConfigurationManager.getDataNodesTimeOutConfig();
                Long defaultTime = tenantTime.getOrDefault("default", 20 * 60 * 1000L);
                tenantTime.remove("default");
                List<String> notDefaultTenantIds = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(tenantTime.keySet())) {
                    for (String tenantId : tenantTime.keySet()) {
                        notDefaultTenantIds.add(tenantId);
                        Long timeoutTime = tenantTime.get(tenantId);
                        doProcessTimeoutNodes(tenantId, null, timeoutTime, now, topTenantSet);
                    }
                }
                doProcessTimeoutNodes(null, notDefaultTenantIds, defaultTime, now, topTenantSet);
            } finally {
                rLock.unlock();
            }
        }


    }

    public void doProcessTimeoutNodes(String tenantId, List<String> notDefaultTenantIds, Long timeoutTime, Long now, Set<String> topTenantSet) {
        Integer limit = 500;
        Integer mostSize = 100000;
        while (true) {
            List<DataNodeMsgDoc> ltTimeNodeMsgList = dataNodeMsgDao.getLtTimeNodeMsgList(tenantId, notDefaultTenantIds, now - timeoutTime, limit);
            if (CollectionUtils.isEmpty(ltTimeNodeMsgList)) {
                break;
            }
            List<DataNodeMsgDoc> notEndList = Lists.newArrayList();
            List<String> endUniqueKeyLists=Lists.newArrayList();
            for (DataNodeMsgDoc msg : ltTimeNodeMsgList) {
                if (notNeedSendBizLog(msg.getTenantId(), msg.getObjApiName(), msg.getStreamId(), msg.getCreateTime().getTime())) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(msg.getNodeTypes()) && !msg.getNodeTypes().contains(DataNodeTypeEnum.end.name())) {
                    if (!msg.getNodeTypes().contains(DataNodeTypeEnum.start.name())) {
                        continue;
                    }
                    StringBuffer uniqueKey = new StringBuffer();
                    uniqueKey.append(msg.getTenantId()).append("_").append(msg.getObjApiName()).append("_").append(msg.getDataId()).append("_").append(msg.getStreamId());
                    msg.setNoVersionUniqueKey(uniqueKey.toString());
                    msg.setIsTop60(topTenantSet.contains(msg.getTenantId()));
                    notEndList.add(msg);
                }else{
                    StringBuffer uniqueKey = new StringBuffer();
                    uniqueKey.append(msg.getTenantId()).append("_").append(msg.getObjApiName()).append("_").append(msg.getDataId()).append("_").append(msg.getStreamId());
                    endUniqueKeyLists.add(uniqueKey.toString());
                }
            }
            List<ObjectId> objectIds = ltTimeNodeMsgList.stream().map(DataNodeMsgDoc::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notEndList)) {
                //插入延迟表
                delayDataNodeMsgDao.batchUpsertDataNodeMsgDoc(notEndList);
                List<DataNodeMsgDoc> list = dataNodeMsgDao.getByObjectIds(objectIds);//为了保证延迟表的数据正确，判断是否由于并发情况节点信息已删除
                List<ObjectId> newObjectIds = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(list)) {
                    newObjectIds.addAll(list.stream().map(DataNodeMsgDoc::getId).collect(Collectors.toList()));
                }
                List<DataNodeMsgDoc> deleteMsg = notEndList.stream().filter(msg -> !newObjectIds.contains(msg.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deleteMsg)) {
                    List<String> noVersionUniqueKeyList = deleteMsg.stream().map(DataNodeMsgDoc::getNoVersionUniqueKey).collect(Collectors.toList());
                    delayDataNodeMsgDao.deleteByNoVersionUniqueKey(noVersionUniqueKeyList);
                }
            }
            for (DataNodeMsgDoc msg : ltTimeNodeMsgList) {
                Boolean isEnd = false, isStart = false;
                if (CollectionUtils.isNotEmpty(msg.getNodeTypes())) {
                    isEnd = msg.getNodeTypes().contains(DataNodeTypeEnum.end.name());//包含结束节点
                    isStart = msg.getNodeTypes().contains(DataNodeTypeEnum.start.name());//包含开始节点
                }
                if (notNeedSendBizLog(msg.getTenantId(), msg.getObjApiName(), msg.getStreamId(), msg.getCreateTime().getTime())) {
                    continue;
                }
                if (!isStart && !isEnd) {//没有开始节点也没有结束节点的不上报
                    continue;
                }
                monitorReportManager.sendBizLog(msg.getTenantId(), msg.getObjApiName(), msg.getDataId(), msg.getVersion(), msg.getStreamId(), isEnd, msg);
            }
            dataNodeMsgDao.deleteByIds(objectIds);
            if(CollectionUtils.isNotEmpty(endUniqueKeyLists)){
                delayDataNodeMsgDao.deleteByNoVersionUniqueKey(endUniqueKeyLists);
            }
            if (ltTimeNodeMsgList == null || ltTimeNodeMsgList.size() < limit) {
                break;
            }
            mostSize = mostSize - limit;
            if (mostSize <= 0) {
                break;
            }
        }
    }

    /**
     * 超时上报，以下情况不上报
     * 1.如果当前集成流停用了
     * 2.集成流快照的创建时间比节点创建时间晚
     *
     * @param tenantId
     * @param sourceObjApiName
     * @param streamId
     * @param time
     * @return
     */
    @LogLevel(LogLevelEnum.TRACE)
    public boolean notNeedSendBizLog(String tenantId, String sourceObjApiName, String streamId, Long time) {
        SyncPloyDetailSnapshotEntity entity = getEnablePloyDetailSnapshot(tenantId, sourceObjApiName, streamId);
        if (entity == null) {
            return true;
        }
        if (entity.getCreateTime() > time) {
            return true;
        }
        return false;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 30, cacheType = CacheType.LOCAL, localLimit = 200)
    private SyncPloyDetailSnapshotEntity getEnablePloyDetailSnapshot(String tenantId, String sourceObjApiName, String streamId) {
        List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNewestBySourceTenantIdAndSrouceObjectApiName(tenantId, sourceObjApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list = list.stream().filter(entity -> entity.getSyncPloyDetailId().equals(streamId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

}
