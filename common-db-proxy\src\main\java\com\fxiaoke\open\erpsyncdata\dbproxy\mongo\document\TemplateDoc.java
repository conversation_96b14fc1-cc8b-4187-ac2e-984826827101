package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 模板
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/3/28
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
public class TemplateDoc {
    /**
     * id
     */
    @Id
    @JSONField(serializeUsing = ObjectIdSer.class,deserializeUsing = ObjectIdSer.class)
    private ObjectId id;
    /**
     * 版本
     */
    private String version;
    /**
     * 标题,唯一
     */
    private String title;

    /**
     * 描述信息，可能不使用
     */
    private String description;

    /**
     * 标签id,逗号分割
     */
    private List<String> tagIds;

    /**
     * 题头图，链接
     */
    private String headerImg;

    /**
     * 源系统,默认K3先
     */
    private ErpChannelEnum channel = ErpChannelEnum.ERP_K3CLOUD;

    /**
     * 场景id,逗号分割
     */
    private List<String> sceneIds;

    /**
     * 排序值
     */
    private Integer order = 65536;

    /**
     * 是否启用
     */
    private boolean enable;
    /**
     * 关联的前置条件
     */
    private List<String> preconditionIds;

    /**
     * 详情
     */
    private String detailStr;

    /**
     * 更新时间
     */
    private Date updateTime;
}
