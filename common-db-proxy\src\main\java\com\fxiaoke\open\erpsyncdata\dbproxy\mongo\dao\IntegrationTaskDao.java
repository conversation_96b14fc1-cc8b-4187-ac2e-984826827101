package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.IntegrationTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/22 19:44:29
 */
//@Component
public class IntegrationTaskDao extends BaseDao<IntegrationTaskEntity> {

    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }


    public IntegrationTaskEntity getById(String id) {
        return findOne("_id", new ObjectId(id));
    }

    public IntegrationTaskEntity startProcess(String id) {
        final Query<IntegrationTaskEntity> query = createQuery("_id", new ObjectId(id));
        final UpdateOperations<IntegrationTaskEntity> update = createUpdate(ImmutableMap.of("updateTime", System.currentTimeMillis(), "status", 1));
        return datastore.findAndModify(query, update);
    }

    public boolean allTaskDone(String taskId) {
        final Query<IntegrationTaskEntity> query = createQuery("taskId", taskId);
        query.field("status").in(Lists.newArrayList(0, 1));

        return Objects.isNull(query.get());
    }

    public long countSuccess(String taskId) {
        final Query<IntegrationTaskEntity> query = createQuery(ImmutableMap.of("taskId", taskId, "status", 2));
        return query.countAll();
    }

    public List<IntegrationTaskEntity> getAllFailTask(String taskId) {
        return find(ImmutableMap.of("taskId", taskId, "status", 3));
    }

    public IntegrationTaskEntity findOneByTaskId(String taskId) {
        return findOne("taskId", taskId);
    }

    public void updateStatus(String id, int status, String message) {
        final Query<IntegrationTaskEntity> query = createQuery("_id", new ObjectId(id));
        final UpdateOperations<IntegrationTaskEntity> update = createUpdate(ImmutableMap.of("updateTime", System.currentTimeMillis(), "status", status, "message", message));
        datastore.findAndModify(query, update);
    }
}
