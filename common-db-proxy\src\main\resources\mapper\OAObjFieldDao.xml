<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.OAObjFieldDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        obj_api_name,
        label,
        field_api_name,
        replace_name,
        create_time,
        update_time
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAObjFieldEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="field_api_name" jdbcType="VARCHAR" property="fieldApiName"/>
        <result column="replace_name" jdbcType="VARCHAR" property="replaceName"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>

    <select id="getByTenantIdAndObj" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_object_field
        where (tenant_id = #{tenantId,jdbcType=VARCHAR} or tenant_id = '0') and obj_api_name =
        #{objApiName,jdbcType=VARCHAR}
    </select>
</mapper>