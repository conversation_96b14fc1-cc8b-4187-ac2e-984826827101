<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="transferMongoService" class="com.fxiaoke.open.erpsyncdata.web.transfer.TransferMongoService"/>

    <bean id="dataTransferMgr" class="com.fxiaoke.open.erpsyncdata.web.transfer.ErpDataTransferMgr">
        <property name="enterpriseService">
            <bean class="com.fxiaoke.open.erpsyncdata.web.transfer.overwrite.EnterpriseServiceImpl"/>
        </property>
    </bean>

</beans>