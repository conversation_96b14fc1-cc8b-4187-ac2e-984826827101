package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("queryObjFieldDesc")
public class QueryObjFieldDesc implements CustomFunctionCommonService {
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        ErpObjectDescResult arg = JacksonUtil.fromJson(commonArg.getParams(), ErpObjectDescResult.class);
        //lang取null参考自UpdateSyncDataMappingServiceImpl
        ErpObjectRelationshipResult result = erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId(tenantId, -10000, arg, arg.getDataCenterId(),null).safeData();
        return Result.newSuccess(JacksonUtil.toJson(result));
    }
}
