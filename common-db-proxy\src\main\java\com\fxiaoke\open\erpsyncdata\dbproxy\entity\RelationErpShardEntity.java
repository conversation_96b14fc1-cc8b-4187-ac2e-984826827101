package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/2/21 11:57:00
 * <p>
 * 代管企业和模板企业关系
 * 每个downStreamId+dcId只能有一条记录！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "relation_erp_shard")
public class RelationErpShardEntity {
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 代管企业组id
     *
     * @see RelationManageGroupEntity#getId()
     */
    @Column(name = "group_id")
    private String groupId;

    /**
     * 下游企业id,同时也是代管企业Id
     */
    @Column(name = "down_stream_id")
    private String downstreamId;

    /**
     * @see com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum
     */
    @Column(name = "status")
    private Integer status;

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "update_time")
    private Long updateTime;
}
