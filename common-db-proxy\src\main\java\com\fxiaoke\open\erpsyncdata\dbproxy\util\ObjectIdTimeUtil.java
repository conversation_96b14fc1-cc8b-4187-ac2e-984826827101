package com.fxiaoke.open.erpsyncdata.dbproxy.util;


import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;

import java.util.List;


@Slf4j
public class ObjectIdTimeUtil {

    public static Pair<Long, Long> getObjectIdTime(String id) {
        try {
            if (StringUtil.isNotBlank(id)) {
                ObjectId objectId = new ObjectId(id);
                Long startLogTime = objectId.getTimestamp() * 1000L - 100000L, endLogTime = objectId.getTimestamp() * 1000L + 100000L;
                return Pair.of(startLogTime, endLogTime);
            }
        } catch (Exception e) {
            log.warn("getObjectIdTime e={}", e);
        }
        return Pair.of(System.currentTimeMillis() - 1000 * 60 * 60 * 24L, System.currentTimeMillis());
    }
    public static Pair<Long, Long> getObjectIdTime(ObjectId id) {
        try {
            if (id!=null) {
                Long startLogTime = id.getTimestamp() * 1000L - 100000L, endLogTime = id.getTimestamp() * 1000L + 100000L;
                return Pair.of(startLogTime, endLogTime);
            }
        } catch (Exception e) {
            log.warn("getObjectIdTime e={}", e);
        }
        return Pair.of(System.currentTimeMillis() - 1000 * 60 * 60 * 24L, System.currentTimeMillis());
    }

    public static Pair<Long, Long> getObjectStrIdsTime(List<String> ids) {
        try {
            if (ids != null && !ids.isEmpty()) {
                Long startLogTime = null, endLogTime = null;
                for (String id : ids) {
                    ObjectId objectId = new ObjectId(id);
                    Long timestamp = objectId.getTimestamp() * 1000L;
                    if (startLogTime == null || timestamp < startLogTime) {
                        startLogTime = timestamp - 100000L;
                    }
                    if (endLogTime == null || timestamp > endLogTime) {
                        endLogTime = timestamp + 100000L;
                    }
                }
                return Pair.of(startLogTime, endLogTime);
            }
        } catch (Exception e) {
            log.warn("getObjectIdTime e={}", e);
        }
        return Pair.of(System.currentTimeMillis() - 1000 * 60 * 60 * 24L, System.currentTimeMillis());
    }

    public static Pair<Long, Long> getObjectIdsTime(List<ObjectId> ids) {
        try {
            if (ids != null && !ids.isEmpty()) {
                Long startLogTime = null, endLogTime = null;
                for (ObjectId id : ids) {
                    Long timestamp = id.getTimestamp() * 1000L;
                    if (startLogTime == null || timestamp < startLogTime) {
                        startLogTime = timestamp - 100000L;
                    }
                    if (endLogTime == null || timestamp > endLogTime) {
                        endLogTime = timestamp + 100000L;
                    }
                }
                return Pair.of(startLogTime, endLogTime);
            }
        } catch (Exception e) {
            log.warn("getObjectIdTime e={}", e);
        }
        return Pair.of(System.currentTimeMillis() - 1000 * 60 * 60 * 24L, System.currentTimeMillis());
    }
}
