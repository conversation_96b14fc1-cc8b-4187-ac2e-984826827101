package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date: 20:07 2024/11/27
 * @Desc: 云星空旗舰版事件模型
 */
@Data
public class K3UltimateEventModelV2 implements Serializable {

    /**
     * 触发事件编码
     */
    private String eventNumber;
    /**
     * 订阅执行实例
     */
    private Long msgId;
    /**
     * 变化的业务对象编码
     */
    private String entityNumber;
    /**
     * 业务对象操作方式
     */
    private String operation;
    /**
     * 事件传递参数
     */
    private Map<String, Object> data;
}
