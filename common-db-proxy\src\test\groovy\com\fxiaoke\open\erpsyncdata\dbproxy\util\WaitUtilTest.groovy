package com.fxiaoke.open.erpsyncdata.dbproxy.util

import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.Callable

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Unroll
class WaitUtilTest extends Specification {

    def "test fetchResourceWithDelays "(List results, int callTimes) {
        given:
        def i = 0
        Callable<String> resourceFunction = {
            return results.get(i++)
        }
        List<Long> delays = [100L, 200L, 300L]

        when:
        String result = WaitUtil.fetchResourceWithDelays(resourceFunction, delays)

        then:
        result == results.get(results.size() - 1)
        i == callTimes

        where:
        results             | callTimes
        ["res"]             | 1
        [null, null, "res"] | 3
        [null, null, null]  | 3
    }
}
