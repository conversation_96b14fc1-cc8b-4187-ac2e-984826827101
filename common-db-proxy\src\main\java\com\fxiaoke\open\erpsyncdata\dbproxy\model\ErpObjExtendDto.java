package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.CommonConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * erp对象扩展数据
 * <AUTHOR> (^_−)☆
 * @date 2020/9/22
 */
@Data
public class ErpObjExtendDto {

    /**
     * erp对象真实apiName
     */
    private String realObjApiName;

    /**
     * 对象apiName,拆分的平台apiName
     */
    private String splitObjApiName;

    /**
     * 对象名称,不一定返回
     */
    private String objName;

    /**
     * 拆分类型
     */
    private ErpObjSplitTypeEnum splitType;

    /**
     * 扩展数据,从对象储存真实apiName
     */
    private String extentValue;

    /**
     * id字段
     */
    private String idFieldName;

    /**
     * 批次
     */
    private Integer splitSeq;

    public static ErpObjExtendDto findDtoBySplitType(List<ErpObjExtendDto> dtoList, ErpObjSplitTypeEnum splitType){
        ErpObjExtendDto dto = dtoList.stream().
                filter(v-> splitType.equals(v.getSplitType())).findAny().get();
        return dto;
    }

    public static ErpObjExtendDto findDtoByApiName(List<ErpObjExtendDto> dtoList, String erpObjApiName){
        ErpObjExtendDto dto = dtoList.stream().
                filter(v-> erpObjApiName.equals(v.getSplitObjApiName())).findAny().get();
        return dto;
    }

    public String parseDetailApiName(){
        String realApiCode = parseRealApiCode();
        if (StringUtils.isNotBlank(realApiCode)){
            return realApiCode;
        }
        return CommonConstants.DEFAULT_ENTRY_NAME;
    }

    /**
     * 获取真实的编码
     */
    public String parseRealApiCode(){
        if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(this.getSplitType())) {
            return this.getRealObjApiName();
        }
        if (ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL.equals(this.getSplitType())){
            SubDetailExtend extend = SubDetailExtend.parse(this.extentValue);
            if (extend != null) {
                return extend.getRealObjectApiName();
            }
        }
        return extentValue;
    }
}
