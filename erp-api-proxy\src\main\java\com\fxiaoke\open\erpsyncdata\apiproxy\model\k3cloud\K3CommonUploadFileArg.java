package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/10
 */
@Data
public class K3CommonUploadFileArg implements Serializable {
    private CommonUploadFileArg data;

    @Data
    public static class CommonUploadFileArg implements Serializable {
        @JSONField(name = "FileName")
        private String fileName;

        @JSONField(name = "IsLast")
        private Boolean isLast = Boolean.FALSE;

        @JSONField(name = "FileId")
        private String fileId;

        @JSONField(name = "SendByte")
        private String sendByte;
    }
}