package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class K3UltimateConfigModel implements Serializable {
    @Data
    public static class UpdateErpObjConfig implements Serializable {
        private boolean autoSubmit = true;
        private boolean autoAudit = true;
    }

    @Data
    public static class CreateErpObjConfig extends UpdateErpObjConfig {
        private boolean deleteBillWhenSubmitOrAuditFailed = false;
    }
}
