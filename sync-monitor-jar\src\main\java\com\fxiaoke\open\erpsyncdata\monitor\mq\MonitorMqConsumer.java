package com.fxiaoke.open.erpsyncdata.monitor.mq;

import com.fxiaoke.open.erpsyncdata.monitor.helper.MonitorMqProcessorHelper;
import com.fxiaoke.open.erpsyncdata.monitor.mq.processor.AbstractMonitorMqOrderlyProcessor;
import com.fxiaoke.open.erpsyncdata.monitor.mq.processor.AbstractMonitorMqProcessor;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@Slf4j
@Service
public class MonitorMqConsumer implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer concurrentlyConsumer;
    private AutoConfMQPushConsumer orderlyConsumer;

    //本地调试时不想启动mq消费可以设置jvm参数-Dmonitor.enableConsumer=false
    @Value("#{ systemProperties['monitor.enableConsumer'] ?: true} ")
    private Boolean enableConsumer = true;

    @ReloadableProperty("monitor.enableMqDebug")
    private Boolean enableMqDebug = false;

    @PostConstruct
    public void init() {
        concurrentlyConsumer = new AutoConfMQPushConsumer("erp-sync-data-monitor", "mq-consumer",
                (MessageListenerConcurrently) (msgs, context) -> {
                    if (!msgs.isEmpty()) {
                        if (enableMqDebug) {
                            log.info("concurrentlyConsumer receive msgs:{}", msgs);
                        }
                        //分tag
                        Map<String, List<MessageExt>> tagGroup = msgs.stream().collect(Collectors.groupingBy(v -> v.getTags()));
                        tagGroup.forEach((tag, groupMsgs) -> {
                            try {
                                // 取出traceContext
                                MessageHelper.fillContextFromMessage(TraceContext.get(), groupMsgs.get(0));
                                MonitorType monitorType = MonitorType.valueOf(tag);
                                AbstractMonitorMqProcessor<?> processor = MonitorMqProcessorHelper.get(monitorType);
                                if (processor != null) {
                                    processor.processMsgs(groupMsgs);
                                }
                            } catch (Exception e) {
                                //异常打印错误日志
                                log.error("process msg error,tag:{},msg:{}", tag, groupMsgs, e);
                            } finally {
                                // 务必清理context
                                TraceContext.remove();
                            }
                        });
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                });
        orderlyConsumer = new AutoConfMQPushConsumer("erp-sync-data-monitor", "mq-consumer-orderly",
                (MessageListenerOrderly) (msgs, context) -> {
                    if (!msgs.isEmpty()) {
                        if (enableMqDebug) {
                            log.info("orderlyConsumer receive msgs:{}", msgs);
                        }
                        //分tag
                        Map<String, List<MessageExt>> tagGroup = msgs.stream().collect(Collectors.groupingBy(v -> v.getTags()));
                        tagGroup.forEach((tag, groupMsgs) -> {
                            try {
                                // 取出traceContext
                                MessageHelper.fillContextFromMessage(TraceContext.get(), groupMsgs.get(0));
                                MonitorType monitorType = MonitorType.valueOf(tag);
                                AbstractMonitorMqOrderlyProcessor<?> processor = MonitorMqProcessorHelper.getOrderly(monitorType);
                                if (processor != null) {
                                    processor.processMsgs(groupMsgs);
                                }
                            } catch (Exception e) {
                                //异常打印错误日志
                                log.error("process msg error,tag:{},msg:{}", tag, groupMsgs, e);
                            } finally {
                                // 务必清理context
                                TraceContext.remove();
                            }
                        });
                    }
                    return ConsumeOrderlyStatus.SUCCESS;
                });
    }

    @PreDestroy
    public void close() {
        if (concurrentlyConsumer != null) {
            concurrentlyConsumer.close();
        }
        if (orderlyConsumer != null) {
            orderlyConsumer.close();
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            if (enableConsumer) {
                start();
                log.info("start monitor consumer,{}", enableMqDebug);
            } else {
                log.info("not start monitor consumer");
            }
        }
    }

    public void start() {
        if (concurrentlyConsumer != null) {
            concurrentlyConsumer.start();
        }
        if (orderlyConsumer != null) {
            orderlyConsumer.start();
        }
    }
}
