package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncDataFailedEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncDataFailedEntity.Fields;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * ERP同步失败数据表，主要用于同步失败数据的恢复告警逻辑
 * <AUTHOR>
 * @date 2023.07.25
 */
@Repository
@Slf4j
public class SyncDataFailedDao {

    @Qualifier("erpSyncDataLogMongoStore")
    @Autowired
    private DatastoreExt store;
    private String DATABASE;

    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
        createIndex();
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<SyncDataFailedEntity> collection = getCollection();

        // TTL过期索引
        Bson expireIdx = Indexes.ascending(Fields.createTime);

        Bson idx = Indexes.ascending(Fields.tenantId, Fields.dataCenterId, Fields.ployDetailId, Fields.sourceDataId);



        try {
            //同步失败数据有效期6小时
            collection.createIndex(expireIdx,new IndexOptions()
                    .expireAfter(24L,TimeUnit.HOURS));

            collection.createIndex(idx, new IndexOptions()
                    .unique(true));
        } catch (Exception e) {
            log.info("SyncFailedDataDao.createIndex,failed,exception={}",e.getMessage());
        }
    }


    private MongoCollection<SyncDataFailedEntity> getCollection() {
        MongoCollection<SyncDataFailedEntity> collection = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("sync_failed_data_1", SyncDataFailedEntity.class);
        return collection;
    }

    public List<SyncDataFailedEntity> getDataList(String tenantId, int limit) {
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId));

        return getDataList(filters,limit);
    }

    public List<SyncDataFailedEntity> getDataList(String tenantId, String dcId, int limit) {
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId));

        return getDataList(filters,limit);
    }

    public List<SyncDataFailedEntity> getDataList(String tenantId, String dcId, String ployDetailId, int limit) {
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId));

        return getDataList(filters,limit);
    }

    public Map<String,List<SyncDataFailedEntity>> getDataListMap(String tenantId, String dcId, String ployDetailId, int limit) {
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId));

        List<SyncDataFailedEntity> dataList = getDataList(filters, limit);
        return getTenantDcPloyDetailDataMap(dataList);
    }

    private Map<String,List<SyncDataFailedEntity>> getTenantDcPloyDetailDataMap(List<SyncDataFailedEntity> dataList) {
        Map<String,List<SyncDataFailedEntity>> dataMap = new HashMap<>();
        for(SyncDataFailedEntity dataEntity : dataList) {
            String key = dataEntity.getTenantId() + "-" + dataEntity.getDataCenterId() + "-" + dataEntity.getPloyDetailId();
            if(!dataMap.containsKey(key)) {
                List<SyncDataFailedEntity> list = new ArrayList<>();
                list.add(dataEntity);
                dataMap.put(key, list);
            } else {
                dataMap.get(key).add(dataEntity);
            }
        }

        return dataMap;
    }

    private List<SyncDataFailedEntity> getDataList(Bson filters, int limit) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        List<SyncDataFailedEntity> totalDataList = new ArrayList<>();
        int offset = 0;
        while (true) {
            List<SyncDataFailedEntity> dataList = new ArrayList<>();
            collection.find(filters).skip(offset).limit(limit).into(dataList);
            if(CollectionUtils.isEmpty(dataList)) break;
            totalDataList.addAll(dataList);
            if(dataList.size()==limit) {
                offset += limit;
            } else {
                break;
            }
        }

        return totalDataList;
    }

   public Map<String, SyncDataFailedEntity> getTenantDcPloyDetailDataMap() {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.exists(Fields.tenantId);

        MongoCursor<SyncDataFailedEntity> iterator = collection.find(filters).iterator();
        Map<String, SyncDataFailedEntity> map = new HashMap<>();
        while (iterator.hasNext()) {
            SyncDataFailedEntity entity = iterator.tryNext();
            if(entity==null) continue;
            String key = entity.getTenantId() + "-" + entity.getDataCenterId() + "-" + entity.getPloyDetailId();
            map.putIfAbsent(key,entity);
        }
        return map;
    }

    public SyncDataFailedEntity getData(String tenantId, String dcId, String ployDetailId, String sourceDataId) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.eq(Fields.sourceDataId, sourceDataId));
        SyncDataFailedEntity first = collection.find(filters).first();
        return first;
    }

    public void replace(String tenantId, String dcId, String ployDetailId, String sourceDataId) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.eq(Fields.sourceDataId, sourceDataId));

        SyncDataFailedEntity entity = new SyncDataFailedEntity();
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dcId);
        entity.setPloyDetailId(ployDetailId);
        entity.setSourceDataId(sourceDataId);
        entity.setTraceId(TraceUtil.get());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        UpdateResult result = collection.replaceOne(filters, entity, new ReplaceOptions().upsert(true));
        log.info("SyncFailedDataDao.replace,tenantId={},dcId={},ployDetailId={},sourceDataId={},result={}",tenantId,
                dcId,
                ployDetailId,
                sourceDataId,
                result);
    }

    public void insertMany(List<SyncDataFailedEntity> dataList) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        for(SyncDataFailedEntity entity : dataList) {
            if(StringUtils.isEmpty(entity.getTraceId())) {
                entity.setTraceId(TraceUtil.get());
            }
        }
        try {
            collection.insertMany(dataList);
            log.info("SyncFailedDataDao.insertMany,dataList.size={}",dataList.size());
        } catch (Exception e) {
            log.info("SyncFailedDataDao.insertMany,insert failed,exception={}",e.getMessage());
        }
    }

    public long delete(String tenantId, String dcId, String ployDetailId, String sourceDataId) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.eq(Fields.sourceDataId, sourceDataId));

        DeleteResult result = collection.deleteOne(filters);
        log.info("SyncFailedDataDao.delete,tenantId={},dcId={},ployDetailId={},sourceDataId={},result={}",tenantId,
                dcId,
                ployDetailId,
                sourceDataId,
                result);
        return result.getDeletedCount();
    }

    public long deleteMany(String tenantId) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId));

        DeleteResult result = collection.deleteMany(filters);
        log.info("SyncFailedDataDao.deleteMany,tenantId={},result={}",tenantId,
                result);
        return result.getDeletedCount();
    }

    public long deleteMany(String tenantId, String dcId, String ployDetailId) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId));

        DeleteResult result = collection.deleteMany(filters);
        log.info("SyncFailedDataDao.deleteMany,tenantId={},dcId={},ployDetailId={},result={}",tenantId,
                dcId,
                ployDetailId,
                result);
        return result.getDeletedCount();
    }

    public long deleteMany(String tenantId, String dcId, String ployDetailId,List<String> dataIdList) {
        MongoCollection<SyncDataFailedEntity> collection = getCollection();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.ployDetailId, ployDetailId),
                Filters.in(Fields.sourceDataId, dataIdList));

        DeleteResult result = collection.deleteMany(filters);
        log.info("SyncFailedDataDao.deleteMany,tenantId={},dcId={},ployDetailId={},result={},dataIdList={}",tenantId,
                dcId,
                ployDetailId,
                result,
                dataIdList);
        return result.getDeletedCount();
    }
}
