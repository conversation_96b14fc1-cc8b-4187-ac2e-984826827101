package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.open.erpsyncdata.web.model.InitFieldExtendDataModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * ERP对象字段相关工具接口
 * <AUTHOR>
 * @date 2023.03.19
 */
@Slf4j
@Api(tags = "重试数据相关接口")
@RestController("ErpObjFieldController")
@RequestMapping("erp/syncdata/superadmin/erpObjectFields")
public class ErpObjFieldController extends BaseController  {
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;

    @ApiOperation(value = "初始化字段扩展表")
    @RequestMapping(value = "/initFieldExtendData", method = RequestMethod.GET)
    public Result<String> initFieldExtendData(@RequestParam String tenantId,
                                              @RequestParam String erpObjApiName) {
        String traceId = TraceUtil.get();
        new Thread(()->{
            TraceUtil.initTrace(traceId);
            erpObjectFieldsService.initFieldExtendData(tenantId,erpObjApiName);
        }).start();
        return Result.newSuccess();
    }

    @ApiOperation(value = "初始化字段扩展表")
    @RequestMapping(value = "/initFieldExtendDataByEi", method = RequestMethod.POST)
    public Result<String> initFieldExtendDataByEi(@RequestBody InitFieldExtendDataModel arg) {
        String traceId = TraceUtil.get();
        new Thread(()->{
            TraceUtil.initTrace(traceId);
            erpObjectFieldsService.initFieldExtendData(arg.getEiList());
        }).start();
        return Result.newSuccess();
    }

    @ApiOperation(value = "初始化字段扩展表")
    @RequestMapping(value = "/initFieldExtendDataAll", method = RequestMethod.GET)
    public Result<String> initFieldExtendDataAll() {
        String traceId = TraceUtil.get();
        new Thread(()->{
            TraceUtil.initTrace(traceId);
            erpObjectFieldsService.initFieldExtendData();
        }).start();
        return Result.newSuccess();
    }

}
