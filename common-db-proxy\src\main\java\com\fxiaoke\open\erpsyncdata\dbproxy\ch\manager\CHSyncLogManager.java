package com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager;


import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.ErpSyncLogDTO;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.vo.LogId2CountVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ObjectIdTimeUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CommonConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CHSyncLogManager {
    @Autowired
    private CHSyncLogDao clickHouseSyncLogDao;
    @Autowired
    private TenantInfoManager tenantInfoManager;

    public void insert(String tenantId, SyncLog syncLog) {
        final CHSyncLogEntity chSyncLogEntity = buildCHSyncLogFromSyncLog(tenantId, syncLog);
        sendBizLog(chSyncLogEntity);
    }

    public void batchInsert(String tenantId, List<SyncLog> syncLogs) {//批量上报
        if (CollectionUtils.isNotEmpty(syncLogs)) {
            List<CHSyncLogEntity> chSyncLogEntities = buildCHSyncLogFromSyncLog(tenantId, syncLogs);
            sendBizLog(chSyncLogEntities);
        }
    }

    public int sendBizLog(List<CHSyncLogEntity> chSyncLogEntities) {
        if (CollectionUtils.isNotEmpty(chSyncLogEntities)) {
            for (CHSyncLogEntity data : chSyncLogEntities) {
                sendBizLog(data);
            }
        }
        return 0;
    }

    private static void sendBizLog(CHSyncLogEntity data) {
        ErpSyncLogDTO dto = ErpSyncLogDTO.builder()
                .appName(data.getAppName())// 服务名称
                .traceId(data.getTraceId()) // 分布式跟踪id
                .serverIp(data.getServerIp()) // 发出日志的ip
                .tenantId(data.getTenantId()) // 租户ei信息
                .createTime(data.getCreateTime().getTime()) // 日志上报时间
                .updateTime(data.getUpdateTime().getTime()) // 日志上报时间
                .expireTime(data.getExpireTime().getTime())//过期时间
                .logType(data.getLogType())//日志类型：sync_data\sync_log\interface_monitor
                .id(data.getId())//mongoId
                .logId(data.getLogId())//logId
                .type(data.getType())//sync_log类型
                .realObjApiName(data.getRealObjApiName())//CRM为CRM对象apiName
                .sourceObjectApiName(data.getSourceObjectApiName())//
                .streamId(data.getStreamId())//集成流id
                .data(data.getData())//数据
                .syncLogStatus(data.getSyncLogStatus())//节点数据同步状态
                .erpTempData(data.getErpTempData())//对象类数据需要代码设置字段
                .erpTempDataDataId(data.getErpTempDataDataId())
                .erpTempDataDataNumber(data.getErpTempDataDataNumber())
                .build();
        if(CollectionUtils.isNotEmpty(data.getErpTempDataTaskNum())){
            if(data.getErpTempDataTaskNum().size()>1){
                for(String taskNum: data.getErpTempDataTaskNum()){
                    if(!CommonConstants.PLOY_AUTO_TASK_KEY.equals(taskNum)){
                        dto.setErpTempDataTaskNum(taskNum);
                        break;
                    }
                }
            }else{
                dto.setErpTempDataTaskNum(data.getErpTempDataTaskNum().get(0));
            }
        }
        BizLogClient.send("biz_log_erp_sync_logs", Pojo2Protobuf.toMessage(dto, com.fxiaoke.log.ErpSyncLog.class).toByteArray());
    }

    /**
     * 根据LogId
     * 倒序
     *
     * @param tenantId
     * @param logIds
     * @return
     */
    public List<SyncLog> listByLogIds(String tenantId, List<String> logIds,Long startLogTime, Long endLogTime) {
        List<CHSyncLogEntity> result = clickHouseSyncLogDao.listByLogIds(tenantId, logIds,new Date(startLogTime),new Date(endLogTime));
        return this.changeClickHouseSyncLogEntityToSyncLogList(result);
    }

    private List<SyncLog> changeClickHouseSyncLogEntityToSyncLogList(List<CHSyncLogEntity> result) {
        List<SyncLog> syncLogs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(result)) {
            Set<String> ids= Sets.newHashSet();
            for (CHSyncLogEntity data : result) {
                if(ids.contains(data.getId())){//去掉id重复的
                    continue;
                }
                SyncLog syncLog = changeClickHouseSyncLogEntityToSyncLog(data);
                ids.add(data.getId());
                syncLogs.add(syncLog);
            }
        }
        return syncLogs;
    }

    private SyncLog changeClickHouseSyncLogEntityToSyncLog(CHSyncLogEntity data) {
        if (data == null) {
            return null;
        }
        SyncLog syncLog = new SyncLog();
        syncLog.setId(new ObjectId(data.getId()));
        syncLog.setLogId(data.getLogId());
        if (data.getType() != null) {
            syncLog.setType(SyncLogTypeEnum.valueOf(data.getType()));
        }
        syncLog.setRealObjApiName(data.getRealObjApiName());
        syncLog.setStreamId(data.getStreamId());
        syncLog.setData(data.getData());
        syncLog.setStatus(data.getSyncLogStatus());
        if (StringUtils.isNotBlank(data.getErpTempData())) {
            syncLog.setErpTempData(JacksonUtil.fromJson(data.getErpTempData(), ErpTempData.class));
        }
        if (data.getCreateTime() != null) {
            syncLog.setCreateTime(data.getCreateTime());
        }
        if (data.getUpdateTime() != null) {
            syncLog.setUpdateTime(data.getUpdateTime());
        }
        return syncLog;
    }


    /**
     * 根据LogId范围查询
     * 用于查询子Id
     *
     * @param tenantId
     * @param gt
     * @param lt
     * @return
     */
    public List<SyncLog> listBetweenLogId(String tenantId, String gt, String lt,Long startLogTime, Long endLogTime) {
        List<CHSyncLogEntity> result = clickHouseSyncLogDao.listBetweenLogId(tenantId, gt, lt, 1000,new Date(startLogTime),new Date(endLogTime));
        return this.changeClickHouseSyncLogEntityToSyncLogList(result);
    }

    public SyncLog getById(String tenantId, ObjectId id, String type) {
        if(id==null){
            return null;
        }
        Pair<Long, Long> objectIdTime = ObjectIdTimeUtil.getObjectIdTime(id);
        Long startLogTime=objectIdTime.getLeft(),endLogTime=objectIdTime.getRight();
        CHSyncLogEntity entity = clickHouseSyncLogDao.getById(tenantId, id.toString(),new Date(startLogTime),new Date(endLogTime));
        if (entity == null) {
            return null;
        }
        return this.changeClickHouseSyncLogEntityToSyncLog(entity);
    }


    public List<SyncLog> pageByFilters(SyncLogPageArg arg) {
        String tenantId = arg.getTenantId();
        String realObjApiName = arg.getRealObjApiName();
        String streamId = arg.getStreamId();
        List<String> type = arg.getTypes().stream().map(SyncLogTypeEnum::name).collect(Collectors.toList());
        Date beginUpdateTime = arg.getBeginUpdateTime();
        Date endUpdateTime = arg.getEndUpdateTime();
        List<String> logId = arg.getLogId();
        Integer status = arg.getStatus();
        Integer offset = arg.getOffset();
        Integer limit = arg.getLimit();
        String erpTempDataDataId = null;
        String erpTempDataDataNumber = null;
        String erpTempDataTaskNum = null;
        if (arg.getQueryTempDataFilterArg() != null) {
            if (arg.getQueryTempDataFilterArg().getDataId() != null) {
                erpTempDataDataId = arg.getQueryTempDataFilterArg().getDataId();
            }
            if (arg.getQueryTempDataFilterArg().getDataNum() != null) {
                erpTempDataDataNumber = arg.getQueryTempDataFilterArg().getDataNum();
            }
            if (arg.getQueryTempDataFilterArg().getTaskNum() != null) {
                erpTempDataTaskNum = arg.getQueryTempDataFilterArg().getTaskNum();
            }
        }
        List<CHSyncLogEntity> result = clickHouseSyncLogDao.pageByFilters(tenantId, realObjApiName, streamId, type, beginUpdateTime, endUpdateTime,
                logId, status==null?null:status.toString(), erpTempDataDataId, erpTempDataDataNumber, erpTempDataTaskNum, offset, limit);
        return this.changeClickHouseSyncLogEntityToSyncLogList(result);
    }

    public List<String> getAllLogId(@TenantID("#arg.getTenantId()") SyncLogPageArg arg) {
        String tenantId = arg.getTenantId();
        String realObjApiName = arg.getRealObjApiName();
        String streamId = arg.getStreamId();
        List<String> type = arg.getTypes().stream().map(SyncLogTypeEnum::name).collect(Collectors.toList());
        Date beginUpdateTime = arg.getBeginUpdateTime();
        Date endUpdateTime = arg.getEndUpdateTime();
        List<String> logId = arg.getLogId();
        Integer status = arg.getStatus();
        String erpTempDataDataId = null;
        String erpTempDataDataNumber = null;
        String erpTempDataTaskNum = null;
        if (arg.getQueryTempDataFilterArg() != null) {
            if (arg.getQueryTempDataFilterArg().getDataId() != null) {
                erpTempDataDataId = arg.getQueryTempDataFilterArg().getDataId();
            }
            if (arg.getQueryTempDataFilterArg().getDataNum() != null) {
                erpTempDataDataNumber = arg.getQueryTempDataFilterArg().getDataNum();
            }
            if (arg.getQueryTempDataFilterArg().getTaskNum() != null) {
                erpTempDataTaskNum = arg.getQueryTempDataFilterArg().getTaskNum();
            }
        }

        List<String> result = clickHouseSyncLogDao.getAllLogId(tenantId, realObjApiName, streamId, type, beginUpdateTime, endUpdateTime,
                logId, status==null?null:status.toString(), erpTempDataDataId, erpTempDataDataNumber, erpTempDataTaskNum, 1000);
        return result;
    }

    public Map<String, Integer> getCountByLogIds(final SyncLogPageArg arg, final List<SyncLogTypeEnum> types) {
        String tenantId = arg.getTenantId();
        String realObjApiName = arg.getRealObjApiName();
        String streamId = arg.getStreamId();
        List<String> type = types.stream().map(SyncLogTypeEnum::name).collect(Collectors.toList());
        Date beginUpdateTime = arg.getBeginUpdateTime();
        Date endUpdateTime = arg.getEndUpdateTime();
        List<String> logId = arg.getLogId();
        Integer status = arg.getStatus();
        String erpTempDataDataId = null;
        String erpTempDataDataNumber = null;
        String erpTempDataTaskNum = null;
        if (arg.getQueryTempDataFilterArg() != null) {
            if (arg.getQueryTempDataFilterArg().getDataId() != null) {
                erpTempDataDataId = arg.getQueryTempDataFilterArg().getDataId();
            }
            if (arg.getQueryTempDataFilterArg().getDataNum() != null) {
                erpTempDataDataNumber = arg.getQueryTempDataFilterArg().getDataNum();
            }
            if (arg.getQueryTempDataFilterArg().getTaskNum() != null) {
                erpTempDataTaskNum = arg.getQueryTempDataFilterArg().getTaskNum();
            }
        }
        List<LogId2CountVo> dataList = clickHouseSyncLogDao.getCountByLogIds(tenantId, realObjApiName, streamId, type, beginUpdateTime, endUpdateTime,
                logId, status==null?null:status.toString(), erpTempDataDataId, erpTempDataDataNumber, erpTempDataTaskNum, 1000);
        Map<String, Integer> result = new HashMap<>();
        dataList.forEach(document -> result.put(document.getType(), document.getCount()));
        return result;
    }


    public long countByFilters(@TenantID("#arg.getTenantId()") SyncLogPageArg arg, int limit) {
        String tenantId = arg.getTenantId();
        String realObjApiName = arg.getRealObjApiName();
        String streamId = arg.getStreamId();
        List<String> type = arg.getTypes().stream().map(SyncLogTypeEnum::name).collect(Collectors.toList());
        Date beginUpdateTime = arg.getBeginUpdateTime();
        Date endUpdateTime = arg.getEndUpdateTime();
        List<String> logId = arg.getLogId();
        Integer status = arg.getStatus();
        String erpTempDataDataId = null;
        String erpTempDataDataNumber = null;
        String erpTempDataTaskNum = null;
        if (arg.getQueryTempDataFilterArg() != null) {
            if (arg.getQueryTempDataFilterArg().getDataId() != null) {
                erpTempDataDataId = arg.getQueryTempDataFilterArg().getDataId();
            }
            if (arg.getQueryTempDataFilterArg().getDataNum() != null) {
                erpTempDataDataNumber = arg.getQueryTempDataFilterArg().getDataNum();
            }
            if (arg.getQueryTempDataFilterArg().getTaskNum() != null) {
                erpTempDataTaskNum = arg.getQueryTempDataFilterArg().getTaskNum();
            }
        }
        return clickHouseSyncLogDao.countByFilters(tenantId, realObjApiName, streamId, type, beginUpdateTime, endUpdateTime,
                logId, status==null?null:status.toString(), erpTempDataDataId, erpTempDataDataNumber, erpTempDataTaskNum, limit);
    }

    public long countByTenantId(String tenantId) {
        long startTime = System.currentTimeMillis();
        long count = clickHouseSyncLogDao.countByTenantId(tenantId);
        long costTime = System.currentTimeMillis() - startTime;
        log.info("SyncLogDao.countByType,costTime={}", costTime);
        return count;
    }

    public List<CHSyncLogEntity> buildCHSyncLogFromSyncLog(String tenantId, List<SyncLog> syncLogs) {
        List<CHSyncLogEntity> logs = Lists.newArrayList();
        for (SyncLog data : syncLogs) {
            final CHSyncLogEntity log = buildCHSyncLogFromSyncLog(tenantId, data);
            logs.add(log);
        }
        return logs;
    }

    private @NotNull CHSyncLogEntity buildCHSyncLogFromSyncLog(String tenantId, SyncLog data) {
        CHSyncLogEntity log = new CHSyncLogEntity();
        log.setServerIp(ProcessInfo2.serverIp);
        log.setAppName(ProcessInfo2.appName);
        if (data.getId() == null) {
            log.setId(new ObjectId().toString());
        } else {
            log.setId(data.getId().toString());
        }
        log.setTenantId(tenantId);
        log.setLogType("sync_log");
        log.setLogId(data.getLogId());
        if (data.getType() != null) {
            log.setType(data.getType().getType());
        }
        log.setRealObjApiName(data.getRealObjApiName());
        log.setSourceObjectApiName(data.getSourceObjectApiName());
        log.setStreamId(data.getStreamId());
        log.setData(data.getData());
        log.setSyncLogStatus(data.getStatus());
        if (data.getErpTempData() != null) {
            log.setErpTempData(JacksonUtil.toJson(data.getErpTempData()));
            log.setErpTempDataDataId(data.getErpTempData().getDataId());
            log.setErpTempDataDataNumber(data.getErpTempData().getDataNumber());
            if (CollectionUtils.isNotEmpty(data.getErpTempData().getTaskNum())) {
                log.setErpTempDataTaskNum(Lists.newArrayList(data.getErpTempData().getTaskNum()));
            }
        }
        if (data.getCreateTime() != null) {
            log.setCreateTime(data.getCreateTime());
        } else {
            log.setCreateTime(new Date());
        }
        if (data.getUpdateTime() != null) {
            log.setUpdateTime(data.getUpdateTime());
        } else {
            log.setUpdateTime(new Date());
        }
        log.setExpireTime(getExpireTimeByEi(tenantId, log.getCreateTime().getTime()));
        return log;
    }

    private Date getExpireTimeByEi(String tenantId, Long createTime) {
        Long expireTime = null;
        if (createTime != null) {
            expireTime = createTime + tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        } else {
            expireTime = System.currentTimeMillis() + tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        }
        return new Date(expireTime);
    }

    public Long findMinDate(String tenantId) {
        return clickHouseSyncLogDao.findMinDate(tenantId);
    }

    public Long deleteBetween(String tenantId, Date beginTime, Date endTime) {
        String lastId = null;
        Long size = 0L;
//        while(true){
//            List<CHSyncLogEntity> result=clickHouseSyncLogDao.listBetween(tenantId, beginTime, endTime,lastId,1000);
//            if(org.springframework.util.CollectionUtils.isEmpty(result)){
//                break;
//            }
//            lastId=result.get(result.size()-1).getId();
//            for(CHSyncLogEntity entity:result){
//                entity.setExpireTime(new Date());//利用合并，利用过期时间删除，因为不允许delete
//            }
//            sendBizLog(result);
//            size+=result.size();
//            if(result.size()<1000){
//                break;
//            }
//        }
        return size;
    }

    public List<CHSyncLogEntity> listLogByType(String tenantId, List<String> sourceObjectApiName, String sourceDataId,String sourceDataName, List<String> logTypeList, Long startLogTime, Long endLogTime) {
        return clickHouseSyncLogDao.listLogByType(tenantId, sourceObjectApiName, sourceDataId,sourceDataName, logTypeList, new Date(startLogTime),new Date(endLogTime));
    }
}
