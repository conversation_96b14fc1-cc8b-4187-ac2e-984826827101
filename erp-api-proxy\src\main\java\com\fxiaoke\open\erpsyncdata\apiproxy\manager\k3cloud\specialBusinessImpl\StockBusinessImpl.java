package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3Constant;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.TimeUtils;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryCombineQtyArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.InventoryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryInventoryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 即时库存特殊处理类
 * 仅获取固定字段，仓库编码、库存组织编码、物料名称、物料编码、库存量（基本单位）等
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/12/3
 */
@Slf4j
@Component("STK_Inventory")
public class StockBusinessImpl extends CommonStockBusinessImpl implements SpecialBusiness {


    @Autowired
    private ErpTenantConfigurationDao tenantConfigurationDao;

    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;

    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;

    @Autowired
    private ErpFieldManager erpFieldManager;

    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private  InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    /**
     * 仓库编码
     */
    private static final String WAREHOUSE_NUMBER = "FStockId.FNumber";
    //批次编码
    private static final String BATCH_FLOT_NUMBER = "FLot.FNumber";
    /**
     * 物料编码
     */
    private static final String MATERIAL_NUMBER = "FMaterialId.FNumber";
    /**
     * 仓位id
     */
    private static final String LOC_ID = "FStockLocID";
    /**
     * 库存虚拟id字段
     */
    private static final String STOCK_COM_ID = "stockComId";
    /**
     * 库存虚拟关联仓库Id字段
     */
    private static final String WAREHOUSE_COM_ID = "warehouseComId";
    /**
     * 库存量（基本单位）
     */
    private static final String FBASE_QTY = "FBaseQty";

    /**
     * 批号
     */
    private static final String FLOT = "FLot";

    /**
     * 批次库存虚拟字段
     */
    private static final String BATCH_COM_ID = "batchComId";

    /**
     * 冻结库存(虚拟字段)
     */
    private static final String BLOCK_STOCK_QTY = "BlockStockQTY";
    /**
     * 实际库存(虚拟字段)
     */
    private static final String REAL_STOCK_QTY = "RealStockQTY";

    /**k3c库存数据原始ID*/
    private static final String FID = "FID";

    private Map<String, String> COMBINE_INVENTORY_FIELD = Maps.newHashMap();

    /**
     *  缓存轮询的分页最后一条信息
     */
    private ThreadLocal<Map<String, K3DataManager.LastErpDataModel>> lastErpDataCache = ThreadLocal.withInitial(()-> new HashMap<>());


    @PostConstruct
    public void initField() {
        COMBINE_INVENTORY_FIELD.put("FLockQty", "FLockQty");//预留量主单位
        COMBINE_INVENTORY_FIELD.put("FSecLockQty", "FSecLockQty");//预留量辅单位
        COMBINE_INVENTORY_FIELD.put("FBaseLockQty", "FBaseLockQty");//预留量基本单位
        COMBINE_INVENTORY_FIELD.put("FBaseQty", "FBaseQty");//库存量基本单位
        COMBINE_INVENTORY_FIELD.put("FQty", "FQty");//库存量主单位
        COMBINE_INVENTORY_FIELD.put("FSecQty", "FSecQty");//库存量辅助单位
        COMBINE_INVENTORY_FIELD.put("FAVBQty", "FAVBQty");//可用量主单位
        COMBINE_INVENTORY_FIELD.put("FBaseAVBQty", "FBaseAVBQty");//可用量基本单位
        COMBINE_INVENTORY_FIELD.put("FSecAVBQty", "FSecAVBQty");//可用量库存辅助单位

    }

    /**
     * 独立获取数据接口
     *
     * @param key
     * @return
     */
    @Override
    public boolean needSpecialGetAndQuery(String key) {
        return true;
    }

    /**
     * 独立获取数据接口
     *
     * @param erpIdArg
     * @param apiClient
     * @return
     */
    @SuppressWarnings("UnstableApiUsage")
    @Override
    public Result<StandardData> specialGetErpObjData(ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        String dataId = erpIdArg.getDataId();
        List<String> split = Splitter.on(K3Constant.ID_SEPARATOR).splitToList(dataId);
        StandardData standardData = new StandardData();
        if (split.size() == 2 || split.size() == 3) {
            //size=2:库存id = 仓库id#物料编码
            //size=3:包含批次 #仓库id#物料编码#批次id
            //如果k3c上物料开启了保质期，那么  仓库id+物料编码 查询，接口会返回多条数据。会以 仓库id+物料编码 为key, 对库存量数据进行合并。
            //如果用户要看到多条数据，不做合并，那么库存ID不能用复合ID，而要用k3c接口返回的FID。
            String warehouseComId = split.get(0);
            String materialNumber = split.get(1);
            String batchId = split.size() == 3 ? split.get(2) : null;
            List<String> warehouseIds = Splitter.on(K3Constant.WAREHOUSE_LOC_SEPARATOR).splitToList(warehouseComId);
            String warehouseNumber = warehouseIds.get(0);
            //仓位编码字段
            List<String> locNumberFields = new ArrayList<>();
            //仓位编码值
            List<String> locNumbers = new ArrayList<>();
            List<String> flexValueFields = getFlexValueFields(warehouseNumber, apiClient);
            if (warehouseIds.size() == 2) {
                //仓库id = 仓库编码||仓位1编码.仓位2编码,需要增加仓位筛选条件
                String locNumberStr = warehouseIds.get(1);
                //仓位值字段列表
                locNumbers = Splitter.on(K3Constant.LOC_SEPARATOR).splitToList(locNumberStr);
                if (locNumbers.size() != flexValueFields.size()) {
                    return Result.newError(ResultCodeEnum.UNSUPPORTED_STOCK_ID);
                }
                for (String flexValueField : flexValueFields) {
                    locNumberFields.add(String.format(LOC_ID + ".%s.FNumber", flexValueField));
                }
            }
            List<String> syncOrgNos = getSyncStockOrgNos(erpIdArg.getTenantId(), apiClient.getDataCenterId());
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId("STK_Inventory");
            List<String> fieldKeys = Lists.newArrayList(configCenterConfig.getSTOCK_FIELDS());
            queryArg.appendEqualFilter(WAREHOUSE_NUMBER, warehouseNumber)
                    .appendEqualFilter(MATERIAL_NUMBER, materialNumber)
                    .appendInFilter("FStockOrgId.FNumber", syncOrgNos);
            if (ObjectUtils.isNotEmpty(batchId)) queryArg.appendEqualFilter(BATCH_FLOT_NUMBER, batchId);
            boolean isLocId = !locNumberFields.isEmpty();
            if (isLocId) {
                //该库存为仓位库存
                for (int i = 0; i < locNumberFields.size(); i++) {
                    queryArg.appendEqualFilter(locNumberFields.get(i), locNumbers.get(i));
                }
                fieldKeys.addAll(locNumberFields);
            }

            Map<String,String> fieldMap = new HashMap<>();

            queryArg.setFieldKeysByList(getAllStockFieldList(erpIdArg.getTenantId(),
                    apiClient.getDataCenterId(),
                    erpIdArg.getObjAPIName(),
                    fieldKeys,
                    erpIdArg.getSyncPloyDetailSnapshotId(),
                    fieldMap));
            commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
            Result<List<K3Model>> allStockRes = apiClient.queryAll(queryArg);
            if (!allStockRes.isSuccess()) {
                return Result.copy(allStockRes);
            } else if (allStockRes.getData().isEmpty()) {
                return Result.newError(ResultCodeEnum.STOCK_NOT_FOUND);
            }
            K3Model comStock = allStockRes.getData().get(0);
            Double fBaseQty;

            boolean parentStock = !isLocId && !flexValueFields.isEmpty();
            if (parentStock) {
                //不是仓位id，并且该仓库存在仓位，则为需要伪造的库存量为0的数据
                fBaseQty = 0.0;
            } else {
                //实际情况应该只查到一条记录。
                fBaseQty = allStockRes.getData().stream().mapToDouble(m -> m.getDouble("FBaseQty")).sum();
            }
            //转换可用库存量
            ErpTenantConfigurationEntity availableResult = tenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId())).findOne(erpIdArg.getTenantId(), apiClient.getDataCenterId(),
                    ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.INVENTORY_AVB_QTY.toString());
            if (ObjectUtils.isNotEmpty(availableResult)) {
                calculateCombineQTY(apiClient,
                        comStock.getString("FStockOrgId.FNumber"),
                        comStock.getString("FMaterialId.FNumber"),
                        comStock.getString("FStockId.FNumber"),
                        comStock.getString("FStockLocID"),
                        comStock.getString("FLot.FNumber"),
                        comStock,
                        parentStock,
                        comStock.getString("FID"));
            } else {
                comStock.put(FBASE_QTY, fBaseQty);
            }
            comStock.put(STOCK_COM_ID, erpIdArg.getDataId());
            comStock.put(WAREHOUSE_COM_ID, warehouseComId);
            //数据范围整数类型有问题，需要转换
            String flotId = comStock.getString(FLOT);
            comStock.put(FLOT, flotId);
            ObjectData masterData = convertData(comStock,fieldMap);
            standardData.setMasterFieldVal(masterData);
            return Result.newSuccess(standardData);
        } else if(split.size() == 1) {
            //库存ID没有使用erp集成平台生成的复合ID，而是使用k3c读回来的原始库存FID.
            List<String> syncOrgNos = getSyncStockOrgNos(erpIdArg.getTenantId(), apiClient.getDataCenterId());
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId("STK_Inventory");
            List<String> fieldKeys = Lists.newArrayList(configCenterConfig.getSTOCK_FIELDS());
            List<String> auxPropertyFieldList = getMaterialAuxPropertyFieldList(apiClient,split.get(0));
            Set<String> disableK3CInventoryAuxPropEiList = tenantConfigurationManager.getDisableK3CInventoryAuxPropEiList();
            if(!disableK3CInventoryAuxPropEiList.contains(erpIdArg.getTenantId())) {
                if(CollectionUtils.isNotEmpty(auxPropertyFieldList)) {
                    fieldKeys.addAll(auxPropertyFieldList);
                }
            }

            queryArg.appendEqualFilter(FID, split.get(0))
                    .appendInFilter("FStockOrgId.FNumber",syncOrgNos);

            Map<String,String> fieldMap = new HashMap<>();

            queryArg.setFieldKeysByList(getAllStockFieldList(erpIdArg.getTenantId(),
                    apiClient.getDataCenterId(),
                    erpIdArg.getObjAPIName(),
                    fieldKeys,
                    erpIdArg.getSyncPloyDetailSnapshotId(),
                    fieldMap));
            commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
            Result<List<K3Model>> allStockRes = apiClient.queryAll(queryArg);
            if(!allStockRes.isSuccess() || CollectionUtils.isEmpty(allStockRes.getData())) {
                log.info("StockBusinessImpl.specialGetErpObjData,allStockRes={}",allStockRes);
                return Result.newError(ResultCodeEnum.CAN_NOT_GET_STOCK_DATA_BY_FID);
            }
            K3Model comStock = allStockRes.getData().get(0);
            updateAuxProperty(comStock);
            Double fBaseQty = comStock.getDouble("FBaseQty");

            //获取库存可用量
            ErpTenantConfigurationEntity availableResult = tenantConfigurationManager.findOne(erpIdArg.getTenantId(), apiClient.getDataCenterId(),
                    ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.INVENTORY_AVB_QTY.toString());
            if (ObjectUtils.isNotEmpty(availableResult)) {
                calculateCombineQTY(apiClient,
                        comStock.getString("FStockOrgId.FNumber"),
                        comStock.getString("FMaterialId.FNumber"),
                        comStock.getString("FStockId.FNumber"),
                        comStock.getString("FStockLocID"),
                        comStock.getString("FLot.FNumber"),
                        comStock,
                        false,
                        comStock.getString("FID"));
            } else {
                comStock.put(FBASE_QTY, fBaseQty);
            }


            String warehouseNumber = comStock.getString(WAREHOUSE_NUMBER);
            String locId = comStock.getString(LOC_ID);
            String flotId = comStock.getString("FLot");//批次
            //boolean hasLoc = StringUtils.isNotBlank(locId) && !"0".equals(locId);
            boolean hasBatch = StringUtils.isNotBlank(flotId) && !"0".equals(flotId);
            ErpTenantConfigurationEntity combineStock = tenantConfigurationManager.findOne(erpIdArg.getTenantId(), apiClient.getDataCenterId(), ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.IGNORE_COMBINE_STOCK_ID.toString());
            if(ObjectUtils.isEmpty(combineStock)){//不需要的不计算，耗时还是有一定的时间
                String warehouseComId = getWarehouseComId(warehouseNumber, locId, apiClient);
                String material = comStock.getString(MATERIAL_NUMBER);
                String stockComId = getStockComId(apiClient, flotId, hasBatch, warehouseComId, material);
                comStock.put(STOCK_COM_ID, stockComId);
                comStock.put(WAREHOUSE_COM_ID, warehouseComId);
            }

            //数据范围整数类型有问题，需要转换
            //String flotId = comStock.getString(FLOT);
            comStock.put(FLOT, flotId);

            ObjectData masterData = convertData(comStock,fieldMap);
            standardData.setMasterFieldVal(masterData);
            return Result.newSuccess(standardData);
        }

        log.info("user stock  id not support ,erpIdArg:{}", erpIdArg);
        return Result.newError(ResultCodeEnum.UNSUPPORTED_STOCK_ID);
    }

    private ObjectData convertData(K3Model comStock,Map<String,String> fieldMap) {
        K3Model k3Model = new K3Model();
        k3Model.putAll(comStock);

        for(String fieldApiName : comStock.keySet()) {
            if(fieldMap.containsKey(fieldApiName)) {
                Object value = comStock.get(fieldApiName);
                k3Model.remove(fieldApiName);
                k3Model.put(fieldMap.get(fieldApiName),value);
            }
        }

        ObjectData masterData = ObjectData.convert(k3Model);
        return masterData;
    }

    /**
     * 获取集成流中配置的即时库存自定义字段并做字段拼接
     * @param tenantId
     * @param stockFieldList
     * @param ployDetailSnapshotId
     * @return
     */
    private List<String> getAllStockFieldList(String tenantId,
                                              String dataCenterId,
                                              String erpObjApiName,
                                              List<String> stockFieldList,
                                              String ployDetailSnapshotId,
                                              Map<String,String> fieldMap) {
        SyncPloyDetailSnapshotEntity snapshotEntity = null;
        if(StringUtils.isNotEmpty(ployDetailSnapshotId)) {
            snapshotEntity = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getById(tenantId, ployDetailSnapshotId);
        } else {
            List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findMasterByRealObjectApiName(tenantId, dataCenterId, erpObjApiName);
            List<SyncPloyDetailSnapshotEntity> snapshotEntityList = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listNewestBySourceTenantIdAndSrouceObjectApiName(tenantId,relationshipEntityList.get(0).getErpSplitObjectApiname(),1);
            if(CollectionUtils.isNotEmpty(snapshotEntityList)) {
                snapshotEntity = snapshotEntityList.get(0);
            } else {
                throw new SyncDataException(com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum.PLOY_WAS_DISABLED);
            }
        }
        List<String> fieldMappingList = snapshotEntity.getSyncPloyDetailData().getFieldMappings().stream()
                .filter((fieldMappingData -> fieldMappingData.getMappingType() == 1))
                .map(FieldMappingData::getSourceApiName)
                .collect(Collectors.toList());
        fieldMappingList.remove(WAREHOUSE_COM_ID);
        fieldMappingList.remove(STOCK_COM_ID);
        log.info("StockBusinessImpl.getAllStockFieldList,fieldMappingList={}",fieldMappingList);

        List<String> validFieldMappingList = new ArrayList<>();
        for(String fieldApiName : fieldMappingList) {
            ErpFieldExtendEntity fieldExtendEntity = erpFieldExtendDao.findOne(tenantId,dataCenterId,erpObjApiName,fieldApiName);
            if(fieldExtendEntity==null || StringUtils.isEmpty(fieldExtendEntity.getQueryCode())) {
                continue;
            }
            fieldMap.put(fieldExtendEntity.getQueryCode(),fieldApiName);
            validFieldMappingList.add(fieldExtendEntity.getQueryCode());
        }
        log.info("StockBusinessImpl.getAllStockFieldList,validFieldMappingList={}",validFieldMappingList);

        List<String> allStockFieldList = new ArrayList<>();
        allStockFieldList.addAll(stockFieldList);

        for(String fieldApiName : validFieldMappingList) {
            Optional<String> first = allStockFieldList.stream()
                    .filter((item) -> StringUtils.equalsIgnoreCase(item, fieldApiName))
                    .findFirst();
            if(first.isPresent()) continue;
            allStockFieldList.add(fieldApiName);
        }
        return allStockFieldList;
    }

    /**
     * 是否使用自定义库存对接，如果库存主键的ID=FID，刚认为是采用了自定义库存对接
     * @param tenantId
     * @param objApiName
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    private boolean isUseCustomStock(String tenantId, String dataCenterId,String objApiName) {
        List<ErpFieldExtendEntity> masterFieldExtends = erpFieldManager.queryAllFieldExtend(tenantId,dataCenterId, objApiName);
        try {
            ErpFieldExtendEntity idFieldExtend = K3DataConverter.getIdFieldExtend(tenantId,masterFieldExtends);
            if(idFieldExtend!=null && StringUtils.equalsIgnoreCase(idFieldExtend.getFieldApiName(),"FID")) {
                return true;
            }
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * 独立获取列表数据接口
     * 对于仓位的库存，会增加一条仓库库存，并且库存量为0
     *
     * @param timeFilterArg
     * @param apiClient
     * @return
     */
    @Override
    public Result<StandardListData> specialListErpObjData(TimeFilterArg timeFilterArg, K3CloudApiClient apiClient) {
        try {
            if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
                return Result.newError(ResultCodeEnum.UNSUPPORTED_CHANNEL);
            }

            List<String> fieldKeys = Lists.newArrayList(configCenterConfig.getSTOCK_FIELDS());

            Set<String> disableK3CInventoryAuxPropEiList = tenantConfigurationManager.getDisableK3CInventoryAuxPropEiList();
            if(!disableK3CInventoryAuxPropEiList.contains(timeFilterArg.getTenantId())) {
                fieldKeys.addAll(getAuxPropertyFieldList(apiClient));
            }

            Map<String,String> fieldMap = new HashMap<>();

            QueryArg queryArg = new QueryArg();
            queryArg.setFormId("STK_Inventory");
            queryArg.setFieldKeysByList(getAllStockFieldList(timeFilterArg.getTenantId(),
                    apiClient.getDataCenterId(),
                    timeFilterArg.getObjAPIName(),
                    fieldKeys,
                    timeFilterArg.getSnapshotId(),
                    fieldMap));
            //        如果是自定义历史任务,以filter为准
            if (StringUtils.isNotEmpty(timeFilterArg.getCustomFilterString())) {
                queryArg.setFilterString(timeFilterArg.getCustomFilterString());
            } else {
                String startDtf = TimeUtils.getDateTime(timeFilterArg.getStartTime());
                String endDtf = TimeUtils.getDateTime(timeFilterArg.getEndTime());
                queryArg.setFilterString(String.format("FUpdateTime >= {ts'%s'} and FUpdateTime < {ts'%s'}", startDtf, endDtf));
            }

            String lastErpId = null;
            String lastErpNum = null;
            Map<String, K3DataManager.LastErpDataModel> lastErpDataMap = lastErpDataCache.get();
            K3DataManager.LastErpDataModel lastErpDataModel = lastErpDataMap.get(timeFilterArg.getTenantId()+"_"+timeFilterArg.getObjAPIName());
            log.info("stock business K3DataManager.buildQueryParams,lastErpDataModel={}", lastErpDataModel);
            if(lastErpDataModel!=null) {
                lastErpId = lastErpDataModel.getErpId();
            }

            List<String> orgNos = getSyncStockOrgNos(timeFilterArg.getTenantId(), apiClient.getDataCenterId());
            queryArg.appendInFilter("FStockOrgId.FNumber", orgNos);
            if (timeFilterArg.getLimit() != null) {
                queryArg.setLimit(timeFilterArg.getLimit());
            }
            if(StringUtils.isEmpty(lastErpId)){
                //兼容原来的分页
                if (timeFilterArg.getOffset() != null) {
                    queryArg.setStartRow(timeFilterArg.getOffset());
                }
            }else{
                //当按照id排序的时候，start永远为0，因为id会取上一页最后一个id
                queryArg.setStartRow(0);
                queryArg.setFilterString(FID + " > '" + lastErpId  + "' and (" + queryArg.getFilterString() + ")");
            }
            //统一设置id排序字段
            queryArg.setOrderString(FID);
            Result<List<K3Model>> result = listAndLog(apiClient,queryArg,timeFilterArg);
            if (!result.isSuccess()) {
                manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),Lists.newArrayList());
                return Result.copy(result);
            }
            StandardListData standardListData = new StandardListData();
            standardListData.setTotalNum(result.getData().size());
            List<StandardData> dataList = new ArrayList<>(result.getData().size());
            Set<String> hadBuiltParentStock = new HashSet<>();
            for (K3Model datum : result.getData()) {
                updateAuxProperty(datum);
                StandardData standardData = new StandardData();
                String warehouseNumber = datum.getString(WAREHOUSE_NUMBER);
                String locId = datum.getString(LOC_ID);
                String flotId = datum.getString("FLot");//批次
                boolean hasLoc = StringUtils.isNotBlank(locId) && !"0".equals(locId);
                boolean hasBatch = StringUtils.isNotBlank(flotId) && !"0".equals(flotId);
                String material = datum.getString(MATERIAL_NUMBER);
                ErpTenantConfigurationEntity combineStock = tenantConfigurationManager.findOne(timeFilterArg.getTenantId(), apiClient.getDataCenterId(), ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.IGNORE_COMBINE_STOCK_ID.toString());
                if(ObjectUtils.isEmpty(combineStock)){//不需要的不计算，耗时还是有一定的时间
                    String warehouseComId = getWarehouseComId(warehouseNumber, locId, apiClient);
                    String stockComId = getStockComId(apiClient, flotId, hasBatch, warehouseComId, material);
                    datum.put(STOCK_COM_ID, stockComId);
                    datum.put(WAREHOUSE_COM_ID, warehouseComId);
                }
                //转成String类型
                datum.put("FLot", flotId);
                ErpTenantConfigurationEntity availableResult = tenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(timeFilterArg.getTenantId())).findOne(timeFilterArg.getTenantId(), apiClient.getDataCenterId(), ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.INVENTORY_AVB_QTY.toString());
                if (ObjectUtils.isNotEmpty(availableResult)) {
                    log.info("StockBusinessImpl.calculateCombineQTY=true,tenantId={}",timeFilterArg.getTenantId());
                    //添加配置的企业才进行可用量的计算
                    calculateCombineQTY(apiClient,
                            datum.getString("FStockOrgId.FNumber"),
                            datum.getString("FMaterialId.FNumber"),
                            datum.getString("FStockId.FNumber"),
                            datum.getString("FStockLocID"),
                            datum.getString("FLot.FNumber"), datum,
                            false,
                            datum.getString("FID"));
                }
                standardData.setMasterFieldVal(convertData(datum,fieldMap));
                dataList.add(standardData);
                log.info("StockBusinessImpl.standardData={}",standardData);

                boolean useCustomStock = isUseCustomStock(timeFilterArg.getTenantId(),apiClient.getDataCenterId(),timeFilterArg.getObjAPIName());
                log.info("StockBusinessImpl.useCustomStock={}",useCustomStock);

                //批次库存不需要添加库存量为0的库存
                if (hasLoc && !useCustomStock) {
                    String parentStockComId = getStockComId(apiClient, flotId, hasBatch, warehouseNumber, material);
                    log.info("StockBusinessImpl.parentStockComId={}",parentStockComId);
                    if (!hadBuiltParentStock.contains(parentStockComId)) {
                        //对于有仓位情况，需要伪造一条仓库的库存，库存量为0
                        convertZeroQty(datum);
                        ObjectData parentStock = ObjectData.convert(datum);
                        parentStock.put(WAREHOUSE_COM_ID, warehouseNumber);
                        parentStock.put(STOCK_COM_ID, parentStockComId);
                        StandardData parentStandardData = new StandardData();
                        parentStandardData.setMasterFieldVal(parentStock);
                        dataList.add(parentStandardData);
                        log.info("StockBusinessImpl.parentStandardData={}",parentStandardData);
                        hadBuiltParentStock.add(parentStockComId);
                    }
                }
            }
            standardListData.setDataList(dataList);
            manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),dataList);
            return new Result<>(standardListData);
        } catch (Exception e) {
            manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),new ArrayList<>());
            throw ErpSyncDataException.wrap(e, I18NStringEnum.s258, timeFilterArg.getTenantId());
        }
    }

    private Result<List<K3Model>> listAndLog(K3CloudApiClient apiClient,QueryArg queryArg,TimeFilterArg timeFilterArg){
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        Result<List<K3Model>> listResult = apiClient.queryReturnMap(queryArg, timeFilterArg);
        stopWatch.stop();
        //保存接口日志
        Integer status=(listResult==null||!listResult.isSuccess())?2:1;
        interfaceMonitorManager.saveErpInterfaceMonitor(timeFilterArg.getTenantId(),
                apiClient.getDataCenterId(), timeFilterArg.getObjAPIName(), ErpObjInterfaceUrlEnum.queryMasterBatch.name(),
                queryArg, GsonUtil.toJson(listResult), status, callTime, System.currentTimeMillis(),
                "", TraceUtil.get(), stopWatch.getTotalTimeMillis(),timeFilterArg);
        return listResult;
    }

    /**
     * 管理分页轮询最后一条数据的缓存
     * @param tenantId
     * @param erpObjApiName
     * @param standardDataList
     */
    private void manageLastErpDataCache(String tenantId,String erpObjApiName,List<StandardData> standardDataList) {
        String key = tenantId + "_" + erpObjApiName;

        String operation = "put";
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(standardDataList)) {
            StandardData lastStandardData = standardDataList.get(standardDataList.size()-1);

            K3DataManager.LastErpDataModel lastErpDataModel = new K3DataManager.LastErpDataModel();
            lastErpDataModel.setErpId(lastStandardData.getMasterFieldVal().getString("FID"));
            Map<String, K3DataManager.LastErpDataModel> lastErpDataMap = lastErpDataCache.get();
            lastErpDataMap.put(key,lastErpDataModel);
        } else {
            lastErpDataCache.get().remove(key);
            operation = "remove";
        }
        log.debug("K3DataManager.manageLastErpDataMap,stock,operation={},lastErpDataMap={}", operation , JSONObject.toJSONString(lastErpDataCache.get()));
    }


    private K3Model calculateCombineQTY(K3CloudApiClient k3CloudApiClient, String orgNumber, String materialNumber, String stockNumber,
                                        String stockLockId, String flotNumber, K3Model model, boolean parentStock,String fid) {
        return calculateCombineQTY(k3CloudApiClient, orgNumber, materialNumber, stockNumber, stockLockId, flotNumber, model, parentStock, fid, 100, 100);
    }

    protected K3Model calculateCombineQTY(K3CloudApiClient k3CloudApiClient, String orgNumber, String materialNumber, String stockNumber,
                                          String stockLockId, String flotNumber, K3Model model, boolean parentStock, String fid, Integer pageRows, Integer maxWhile) {
        if (parentStock) {
            return convertZeroQty(model);
        }
        // 特殊接口,没有排序,没有fid参数,只能翻页查找
        final QueryCombineQtyArg.QueryCombineQtyArgBuilder builder = QueryCombineQtyArg.builder().flotnumbers(flotNumber.equals("0") ? "" : flotNumber)
                .fmaterialnumbers(materialNumber)
                .fstockorgnumbers(orgNumber)
                .fstocknumbers(stockNumber)
                .pagerows(pageRows)
                .isshowauxprop(true)
                .isshowstockloc(true)
                .fstocklocid(stockLockId);
        InventoryResult inventoryResult = null;
        QueryCombineQtyArg queryCombineQtyArg = null;
        for (int page = 1; Objects.isNull(inventoryResult) && page <= maxWhile; page++) {
            queryCombineQtyArg = builder.pageindex(page).build();
            Result<QueryInventoryResult.CombineInventoryResult> combineInventoryResult = k3CloudApiClient.specialStockQuery(queryCombineQtyArg);
            if (!combineInventoryResult.isSuccess() || combineInventoryResult.getData().getRowcount() <= 0) {
                log.info("calculateCombineQTY not found, model:{},queryArg:{}", model, queryCombineQtyArg);
                return model;
            }
            inventoryResult = combineInventoryResult.getData().getData().stream()
                    .filter(result -> result.getFID().equalsIgnoreCase(fid))
                    .findFirst()
                    .orElse(null);
        }
            //如果stockLockNumber有值，过滤对应的仓位
//            InventoryResult inventoryResult = combineInventoryResult.getData().getData().get(0);
//            if (StringUtils.isNotEmpty(stockLockId) && !stockLockId.equals("0")) {
//                List<InventoryResult> filterResult = combineInventoryResult.getData().getData().stream().filter(item ->
//                        item.getFSTOCKLOCID().equals(stockLockId)).collect(Collectors.toList());
//                inventoryResult = filterResult.get(0);
//            }
        model.put(COMBINE_INVENTORY_FIELD.get("FLockQty"), inventoryResult.getFLOCKQTY());
        model.put(COMBINE_INVENTORY_FIELD.get("FBaseLockQty"), inventoryResult.getFBASELOCKQTY());
        model.put(COMBINE_INVENTORY_FIELD.get("FSecLockQty"), inventoryResult.getFSecLockQty());

        model.put(COMBINE_INVENTORY_FIELD.get("FAVBQty"), inventoryResult.getFAVBQTY());
        model.put(COMBINE_INVENTORY_FIELD.get("FSecAVBQty"), inventoryResult.getFSECAVBQTY());
        model.put(COMBINE_INVENTORY_FIELD.get("FBaseAVBQty"), inventoryResult.getFBASEAVBQTY());

        model.put(COMBINE_INVENTORY_FIELD.get("FBaseQty"), inventoryResult.getFBASEQTY());
        model.put(COMBINE_INVENTORY_FIELD.get("FQty"), inventoryResult.getFQTY());
        model.put(COMBINE_INVENTORY_FIELD.get("FSecQty"), inventoryResult.getFSecQty());

        log.info("calculateCombineQTY model:{},queryArg:{}",model,queryCombineQtyArg);
        return model;
    }

    private K3Model convertZeroQty(K3Model model) {
        //仓位伪造库存量为0
        model.put(COMBINE_INVENTORY_FIELD.get("FLockQty"), 0);
        model.put(COMBINE_INVENTORY_FIELD.get("FBaseLockQty"), 0);
        model.put(COMBINE_INVENTORY_FIELD.get("FSecLockQty"), 0);

        model.put(COMBINE_INVENTORY_FIELD.get("FAVBQty"), 0);
        model.put(COMBINE_INVENTORY_FIELD.get("FSecAVBQty"), 0);
        model.put(COMBINE_INVENTORY_FIELD.get("FBaseAVBQty"), 0);

        model.put(COMBINE_INVENTORY_FIELD.get("FBaseQty"), 0);
        model.put(COMBINE_INVENTORY_FIELD.get("FQty"), 0);
        model.put(COMBINE_INVENTORY_FIELD.get("FSecQty"), 0);
        return model;
    }


    private String getStockComId(K3CloudApiClient apiClient, String flotId, boolean hasBatch, String warehouseComId, String material) {
        String stockComId = getStockComIdNoBatch(warehouseComId, material);
        //批次库存id
        stockComId = hasBatch ? getBatchComId(stockComId, flotId, apiClient) : stockComId;
        return stockComId;
    }
}
