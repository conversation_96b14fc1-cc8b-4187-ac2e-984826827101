package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.UnDistributeCustomerArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CSaveAction;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.Submit;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.UnDistributeArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.UnDistributeResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.DistributeCustomerUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * unDistributeCustomer APL函数逻辑
 *
 * <AUTHOR>
 * @date 2023.12.07
 */
@Slf4j
@Service("unDistributeCustomer")
public class UnDistributeCustomerServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("UnDistributeCustomerServiceImpl.executeLogic,arg={}", arg);

        if (arg == null || StringUtils.isEmpty(arg.getTenantId()) || StringUtils.isEmpty(arg.getParams())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        UnDistributeCustomerArg model = JSONObject.parseObject(arg.getParams(), UnDistributeCustomerArg.class);
        if (model == null
                || StringUtils.isEmpty(model.getTenantId())
                || StringUtils.isEmpty(model.getDataCenterId())
                || StringUtils.isEmpty(model.getUseOrgNumber())
                || StringUtils.isEmpty(model.getCustomerNumber())
                || StringUtils.isEmpty(model.getCustomerId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(model.getTenantId(), model.getDataCenterId());
        if (connectInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(model.getTenantId(),
                connectInfoEntity.getConnectParams(),
                model.getDataCenterId());

        String customerId = model.getCustomerId();
        String useOrgCustomerId = DistributeCustomerUtils.getCustomerId(apiClient, model.getCustomerNumber(), model.getUseOrgNumber());
        if (StringUtils.isEmpty(useOrgCustomerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_CUSTOMER_IN_DISTRIBUTED_ORG);
        }
        Submit.BaseArg unSubmitArg = new Submit.BaseArg();
        unSubmitArg.setIds(useOrgCustomerId);
        setInterfaceArg(unSubmitArg,apiClient,"BD_Customer",K3CSaveAction.UN_AUDIT);
        Result<Submit.Result> unAudit = apiClient.unAudit("BD_Customer", unSubmitArg);
        log.info("UnDistributeCustomerServiceImpl.executeLogic,unAudit={}", unAudit);

        String orgId = DistributeCustomerUtils.getOrgId(apiClient, model.getUseOrgNumber());
        if (StringUtils.isEmpty(customerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_ORG);
        }

        UnDistributeArg unDistributeArg = new UnDistributeArg();
        unDistributeArg.setDataIds(customerId);
        unDistributeArg.setOrganizationIds(orgId);
        Result<String> result = apiClient.unDistribute("BD_Customer", unDistributeArg);
        log.info("UnDistributeCustomerServiceImpl.executeLogic,result={}", result);
        if (!result.isSuccess()) {
            return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
        }
        String newCustomerId = null;
        UnDistributeResult unDistributeResult = JSONObject.parseObject(result.getData(), UnDistributeResult.class);
        if (CollectionUtils.isNotEmpty(unDistributeResult.getResult().getResponseStatus().getSuccessEntitys())) {
            newCustomerId = unDistributeResult.getResult().getResponseStatus().getSuccessEntitys().get(0).getId();
        }
        return Result.newSuccess(newCustomerId);
    }
    private void setInterfaceArg(Submit.BaseArg arg, K3CloudApiClient apiClient, String objApiName, K3CSaveAction action) {
        Map<String,Map<String, Map<String,Object>>> interfaceArgSettings=tenantConfigurationManager.getK3InterfaceArg(apiClient.getTenantId(),apiClient.getDataCenterId());
        if(interfaceArgSettings.containsKey(objApiName)
                &&interfaceArgSettings.get(objApiName).containsKey(action.getType())
                &&interfaceArgSettings.get(objApiName).get(action.getType())!=null){
            arg.putAll(interfaceArgSettings.get(objApiName).get(action.getType()));
        }
    }
}
