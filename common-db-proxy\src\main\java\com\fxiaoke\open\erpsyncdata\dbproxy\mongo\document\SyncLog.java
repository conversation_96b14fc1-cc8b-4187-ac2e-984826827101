package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.Date;

/**
 * 全链路同步日志
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/6/2
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class SyncLog {
    @BsonId
    private ObjectId id;

    /**
     * 日志id
     */
    private String logId;
    /**
     * 类型
     */
    private SyncLogTypeEnum type;
    /**
     * sourceData里面的apiName
     */
    private String sourceObjectApiName;
    /**
     * CRM为CRM对象apiName
     */
    private String realObjApiName;
    /**
     * 集成流id
     */
    private String streamId;
    /**
     * 数据
     */
    private String data;
    /**
     * 节点数据同步状态
     */
    private Integer status;

    /**
     * 对象类数据需要代码设置字段
     *
     * 注意存进去的对象，key不能带.
     * 一般使用序列化成string后存到data字段里
     * 特殊类型有筛选需求时，使用这个储存，
     * 如果是自定义的Java POJO，需要增加Codec
     * @see com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.SyncLogMongoStore.SingleCodecHolder
     */
    private ErpTempData erpTempData;

    /**
     * 创建时间，储存时赋值
     */
    private Date createTime;

    /**
     * 更新时间，储存时赋值
     */
    private Date updateTime;

    public static SyncLog initSyncLog(String logId, String realObjApiName, String sourceObjApiName, String streamId, SyncLogTypeEnum type, Integer status, Object... logObjs) {
        String data;
        if (logObjs == null || logObjs.length == 0) {
            data = "";
        } else if (logObjs.length == 1) {
            //使用jackson不丢失原来对象的字段顺序,并保留null值
            data = JacksonUtil.toJson(logObjs[0]);
        } else {
            ArrayList<Object> objects = Lists.newArrayList(logObjs);
            data = JacksonUtil.toJson(objects);
        }
        return initSyncLog(logId, realObjApiName, sourceObjApiName, streamId, type, data, status);
    }


    public static SyncLog initSyncLog(String logId, String realObjApiName, String sourceObjApiName, String streamId, SyncLogTypeEnum type, String data, Integer status) {
        Date now = new Date();
        return SyncLog.builder()
                .logId(logId)
                .realObjApiName(realObjApiName)
                .sourceObjectApiName(sourceObjApiName)
                .streamId(streamId)
                .createTime(now)
                .updateTime(now)
                .type(type)
                .data(data)
                .status(status)
                .build();
    }
}
