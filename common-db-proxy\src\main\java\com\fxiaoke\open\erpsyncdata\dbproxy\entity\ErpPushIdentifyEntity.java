package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>erp接口数据</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @dateTime 2020/12/7 11:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "erp_push_identify")
public class ErpPushIdentifyEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;


    /**
     * 认证版本
     */
    private String version;

    /**
     * 认证字符串
     */
    private String token;


    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}