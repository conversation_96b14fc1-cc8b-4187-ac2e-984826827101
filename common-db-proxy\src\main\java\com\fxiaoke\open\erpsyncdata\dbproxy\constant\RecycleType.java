package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity;

/**
 * 回收资源类型
 * <AUTHOR> (^_−)☆
 * @date 2023/5/26
 */
public enum RecycleType {
    /**
     * 中间表
     */
    SYNC_DATA_MAPPING(SyncDataMappingsEntity.class),

    /**
     * erp对象
     */
    ERP_OBJECT(ErpObjectEntity.class),

    /**
     * erp对象关系
     */
    ERP_OBJECT_RELATIONSHIP(ErpObjectRelationshipEntity.class),

    /**
     * erp对象字段
     */
    ERP_OBJECT_FIELD(ErpObjectFieldEntity.class),

    /**
     * erp对象字段
     */
    ERP_FIELD_EXTEND(ErpFieldExtendEntity.class),

    /**
     * 集成流
     */
    PLOY_STREAM(SyncPloyDetailEntity.class),

    /**
     * 历史任务
     */
    HISTORY_DATA_TASK(ErpHistoryDataTaskEntity.class),

    /**
     * 告警规则
     */
    ERP_ALARM_RULE(ErpAlarmRuleEntity.class),

    TENANT_CONFIG(ErpTenantConfigurationEntity.class),

    /**
     * 告警记录
     */
    INTEGRATION_NOTIFICATION(DataIntegrationNotificationEntity.class),

    /**
     * 超级管理员刷库操作
     */
    SUPER_ADMIN_BULK_DB(Object.class),
    ;

    public Class<?> type;

    RecycleType(Class<?> type) {
        this.type = type;
    }
}
