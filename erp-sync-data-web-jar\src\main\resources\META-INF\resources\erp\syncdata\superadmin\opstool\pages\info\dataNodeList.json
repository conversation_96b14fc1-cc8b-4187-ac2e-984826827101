{"type": "page", "title": "延迟节点信息(最多获取500条)", "remark": null, "name": "dataNodeList", "toolbar": [], "body": [{"type": "crud", "name": "dataNodes", "api": "../dataNodeQuery", "loadDataOnce": true, "defaultParams": {"perPage": 50}, "primaryField": "id", "headerToolbar": ["export-csv", "reload", "bulkActions"], "bulkActions": [], "filter": {}, "columns": [{"name": "id", "label": "数据库id"}, {"name": "tenantId", "label": "企业id", "searchable": true}, {"name": "isTop60", "filterable": {"multiple": false, "options": [{"value": true, "label": "是"}, {"value": false, "label": "否"}]}, "label": "是否top60企业"}, {"name": "objApiName", "label": "对象apiName", "searchable": true}, {"name": "dataId", "label": "数据id", "searchable": true}, {"name": "streamId", "label": "集成流id"}, {"name": "nodeNames", "type": "json", "label": "节点名称列表"}, {"name": "nodeTimes", "type": "json", "label": "节点时间列表"}, {"name": "createTime", "label": "创建时间"}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "combineNum": 0, "bodyClassName": "panel-default"}]}