package com.fxiaoke.open.erpsyncdata.dbproxy.model;


import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 对象分发优先级配置
 *
 * <AUTHOR> (^_−)☆
 * @see com.fxiaoke.dispatcher.common.BaseEvent#priority
 */
@Data
@Accessors(chain = true)
public class ObjDispatchPriorityConfig implements Serializable {

    /**
     * 对象的默认优先级
     */
    private Integer priority = 50;

    /**
     * 表达式优先级配置
     */
    private List<PriorityExpr> priorityExprList;

    /**
     * crm的需要重新查询的字段，如果数据中不存在
     */
    @EqualsAndHashCode.Exclude
    private List<String> fields;


    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriorityExpr implements Serializable {
        /**
         * 优先级值，前端传值无效
         */
        private Integer priority;
        /**
         * 解析的表达式，前端传值无效
         */
        @EqualsAndHashCode.Exclude
        private String expr;

        /**
         * 用于前端展示的数据
         */
        private List<List<FilterData>> filters;

        public PriorityExpr(Integer priority, String expr) {
            this.priority = priority;
            this.expr = expr;
        }
    }
}
