package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.GetConfigValueByKeyArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.GetConfigValueByKeyResult;
import com.fxiaoke.crmrestapi.service.SkuSpuService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.BomManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 11:50 2021/11/2
 * @Desc:
 */
@Component
@Slf4j
public class K3CPQOrderManager {
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private SkuSpuService skuSpuService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager ;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private BomManager bomManager;
    @Autowired
    private I18NStringManager i18NStringManager;


    public Result<String> handleCpqSaleOrderProduct(StandardData standardData, K3CloudApiClient apiClient, SaveArg saveArg) {
        String tenantId = apiClient.getTenantId();
        if (tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.BOM_V1_TENANTS)) {
            //v1版本，无产品组合，只有bom_instance
            return handleCpqSaleOrderProductV1(standardData,apiClient,saveArg);
        }
        return Result.newSuccess();
    }

    public Result<String> handleCpqSaleOrderProductV1(StandardData standardData, K3CloudApiClient apiClient, SaveArg saveArg) {
        log.info("handleCpqSaleOrderProduct SaveArg before={}",saveArg);
        String tenantId=standardData.getMasterFieldVal().getTenantId();
        Result<String> result=Result.newSuccess();
        if(!isNeedHandleCpq(tenantId,apiClient.getDataCenterId())){
            return Result.newSuccess();
        }
        SyncPloyDetailEntity ployDetail = this.getOutputOrderPloyDetail(tenantId,apiClient.getDataCenterId());
        if(ployDetail==null){
            log.warn("ployDetail is null type={},tenantId={},objApiName={}",SyncPloyTypeEnum.OUTPUT.getType(), tenantId, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
            return result;
        }
        String prod_pkg_key=null,parent_prod_pkg_key=null,root_prod_pkg_key=null;
        String k3FakeSalesOrderProductObjApiName=null;//找到订单产品的k3侧的虚拟apiName
        String k3SalesOrderProductObjApiName=null;//找到订单产品的k3侧的apiName
        for(DetailObjectMappingsData.DetailObjectMappingData detailObj:ployDetail.getDetailObjectMappings()){
            if(ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName().equals(detailObj.getSourceObjectApiName())){
                k3FakeSalesOrderProductObjApiName=detailObj.getDestObjectApiName();
                for(FieldMappingData fieldMappingData:detailObj.getFieldMappings()){
                    if("prod_pkg_key".equals(fieldMappingData.getSourceApiName())){
                        prod_pkg_key=fieldMappingData.getDestApiName();
                    }else if("parent_prod_pkg_key".equals(fieldMappingData.getSourceApiName())){
                        parent_prod_pkg_key=fieldMappingData.getDestApiName();
                    }if("root_prod_pkg_key".equals(fieldMappingData.getSourceApiName())){
                        root_prod_pkg_key=fieldMappingData.getDestApiName();
                    }
                }
                break;
            }
        }
        if(k3FakeSalesOrderProductObjApiName==null){
            log.warn("k3FakeSalesOrderProductObjApiName is null ployDetail={}",ployDetail);
            return result;
        }
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Lists.newArrayList(k3FakeSalesOrderProductObjApiName));
        if(CollectionUtils.isNotEmpty(erpObjectEntities)){
            k3SalesOrderProductObjApiName=erpObjectEntities.get(0).getErpObjectExtendValue();
        }
        if(k3SalesOrderProductObjApiName==null){
            log.warn("k3SalesOrderProductObjApiName is null ployDetail={}",ployDetail);
            return result;
        }
        if(prod_pkg_key==null||parent_prod_pkg_key==null||root_prod_pkg_key==null){
            log.warn("some fields(prod_pkg_key、parent_prod_pkg_key、root_prod_pkg_key) must be binded but not binding ployDetail={}",ployDetail);
            result.setErrCode("-1");
            result.setErrMsg("some fields(prod_pkg_key、parent_prod_pkg_key、root_prod_pkg_key) must be binded but not binding");
            return result;
        }
        List<ObjectData> salesOrderProducts = standardData.getDetailFieldVals().get(k3SalesOrderProductObjApiName);//k3销售订单明细数据
        if(CollectionUtils.isEmpty(salesOrderProducts)){
            return result;
        }
        //如果同步订单时，对接了这个FRowType产品类型字段，后面会用这个字段把程序动态添加的parent，son等类型覆盖掉
        for(ObjectData orderProduct : salesOrderProducts) {
            String rowType = orderProduct.getString("FRowType");
            //用这个字段暂存FRowType,后面再用这个值把FRowType覆盖掉
            orderProduct.put("FRowType_old",rowType);
        }
        log.info("handleCpqSaleOrderProduct,salesOrderProducts={}", salesOrderProducts);

        List<String> bomNameList = salesOrderProducts.stream()
                .map(objectData -> objectData.getString("bom_id"))
                .collect(Collectors.toList());
        Map<String,String> bomMap = bomManager.getBomIdList(tenantId,bomNameList);
        //从CRM批量获取bom_id字段值并覆盖，因为默认的bom_id字段是BomObj对应的名称，需要转换成对应的id
        for(ObjectData data : salesOrderProducts) {
            data.put("bom_id",bomMap.get(data.getString("bom_id")));
        }

        Map<Object,ObjectData> pkg_key2Product= Maps.newHashMap();
        for(ObjectData salesOrderProduct:salesOrderProducts){
            if(salesOrderProduct.get(prod_pkg_key)!=null){
                pkg_key2Product.put(salesOrderProduct.get(prod_pkg_key),salesOrderProduct);
            }
        }
        Map<String,String> bomIdMapping=Maps.newHashMap();
        for(ObjectData salesOrderProduct:salesOrderProducts){
            if(salesOrderProduct.get(root_prod_pkg_key)!=null&&salesOrderProduct.get(parent_prod_pkg_key)==null){//父产品
                if(salesOrderProduct.get("FMaterialId.FNumber")!=null){
                    String parentProductNum = salesOrderProduct.get("FMaterialId.FNumber").toString();
                    String bom_instance_tree_id = salesOrderProduct.getString("bom_instance_tree_id");
                    String bom_id = salesOrderProduct.getString("bom_id");
                    if(StringUtils.isNotEmpty(bom_instance_tree_id)) {
                        String bom_instance_id = bomManager.getBomInstanceId(tenantId,bom_instance_tree_id,bom_id);
                        SyncDataMappingsEntity bomMappingEntity = bomManager.getBomInstanceMapping(tenantId,
                                bom_instance_id,
                                bom_instance_tree_id,
                                bom_id,
                                parentProductNum);
                        if(bomMappingEntity!=null) {
                            String bomVersion = BomUtils.getBomVersion(bomMappingEntity.getDestDataId());
                            salesOrderProduct.put("FRowType","Parent");
                            salesOrderProduct.put("FBomId.FNumber",bomVersion);
                            salesOrderProduct.put("FBOMEntryId",BomUtils.getBomEntryId(bomMappingEntity.getDestDataId()));
                            bomIdMapping.put(salesOrderProduct.get(prod_pkg_key).toString(),bomVersion);//父产品prod_pkg_key与bom版本映射
                        }
                    } else {
                        if(parentProductNum.contains("{")){
                            String bomId=parentProductNum.substring(parentProductNum.indexOf("{")+1,parentProductNum.indexOf("}"));
                            bomIdMapping.put(salesOrderProduct.get(prod_pkg_key).toString(),bomId);//父产品prod_pkg_key与bom版本映射
                        }
                    }
                }
            }
        }
        ErpTenantConfigurationEntity configurationValue = tenantConfigurationManager.findOne(tenantId,
                apiClient.getDataCenterId(),
                ErpChannelEnum.ERP_K3CLOUD.name(),
                TenantConfigurationTypeEnum.ONLY_SYNC_CPQ_PARENT.name());
        log.info("handleCpqSaleOrderProduct,configurationValue={}", configurationValue);
        // 只同步父项，原型客户：博创
        if(ObjectUtils.isNotEmpty(configurationValue)){

            for(ObjectData salesOrderProduct:salesOrderProducts){
                if(salesOrderProduct.get(root_prod_pkg_key)==null){//标准产品(不带cpq)
                    if (StringUtils.isEmpty(salesOrderProduct.getString("FRowType"))){
                        salesOrderProduct.put("FRowType","Standard");
                    }
                }else{
                    if(salesOrderProduct.get(parent_prod_pkg_key)==null){//CPQ父产品
                        salesOrderProduct.put("FRowType","Standard");
                        salesOrderProduct.put("FBomId.FNumber",bomIdMapping.get(salesOrderProduct.get(prod_pkg_key).toString()));
                    }else {
                        salesOrderProduct.clear();
                    }
                }
            }
            salesOrderProducts= salesOrderProducts.stream().filter(item->item.size()>0).collect(Collectors.toList());
            standardData.getDetailFieldVals().get(k3SalesOrderProductObjApiName).clear();
            standardData.getDetailFieldVals().get(k3SalesOrderProductObjApiName).addAll(salesOrderProducts);
            return onlySyncCpqParent(tenantId,apiClient.getDataCenterId(),standardData,saveArg);
        }

        for(ObjectData salesOrderProduct:salesOrderProducts){
            if(salesOrderProduct.get(root_prod_pkg_key)==null){//标准产品(不带cpq)
                salesOrderProduct.put("FRowType","Standard");
            }else{
                if(salesOrderProduct.get(parent_prod_pkg_key)==null){//CPQ父产品
                    salesOrderProduct.put("FRowType","Parent");
                    salesOrderProduct.put("FBomId.FNumber",bomIdMapping.get(salesOrderProduct.get(prod_pkg_key).toString()));
                }else {//CPQ子产品
                    String fBOMEntryId=salesOrderProduct.getString("FBOMEntryId");
                    String materialNumber = salesOrderProduct.getString("FMaterialId.FNumber");

                    ObjectData parentProduct=pkg_key2Product.get(salesOrderProduct.get(parent_prod_pkg_key));

                    String bom_instance_tree_id = salesOrderProduct.getString("bom_instance_tree_id");
                    String bom_id = salesOrderProduct.getString("bom_id");
                    String bom_instance_id = bomManager.getBomInstanceId(tenantId,bom_instance_tree_id,bom_id);

                    if(StringUtils.isNotEmpty(bom_instance_tree_id)) {
                        SyncDataMappingsEntity bomMappingEntity = bomManager.getBomInstanceMapping(tenantId,
                                bom_instance_id,
                                bom_instance_tree_id,
                                bom_id,
                                materialNumber);
                        if(bomMappingEntity!=null) {
                            //填充子产品关联的FBOMEntryId
                            salesOrderProduct.put("FBOMEntryId",BomUtils.getBomEntryId(bomMappingEntity.getDestDataId()));
                        }
                        String parentProductNum = parentProduct.get("FMaterialId.FNumber").toString();
                        //填充子产品关联的父产品编码
                        salesOrderProduct.put("FParentMatId.FNumber",parentProductNum);
                        //填充子产品的类型
                        salesOrderProduct.put("FRowType","Son");
                    } else {
                        String parentBomEntryId=null;
                        String sonBomEntryId=null;
                        if(StringUtils.isNotBlank(fBOMEntryId)&&fBOMEntryId.contains("{")&&fBOMEntryId.contains("}")){
                            parentBomEntryId=fBOMEntryId.substring(0,fBOMEntryId.indexOf("{"));
                            sonBomEntryId=fBOMEntryId.substring(fBOMEntryId.indexOf("{")+1,fBOMEntryId.indexOf("}"));
                            salesOrderProduct.put("FBOMEntryId",sonBomEntryId);
                        }

                        salesOrderProduct.put("FBomId.FNumber",bomIdMapping.get(salesOrderProduct.get(parent_prod_pkg_key)));//跟父产品一样
                        if(parentProduct!=null&&parentProduct.get("FMaterialId.FNumber")!=null){
                            parentProduct.put("FBOMEntryId",parentBomEntryId);
                            String parentProductNum = parentProduct.get("FMaterialId.FNumber").toString();
                            if(parentProductNum.contains("{")){
                                parentProductNum=parentProductNum.substring(0,parentProductNum.indexOf("{"));//去掉后面的{版本号}
                            }
                            parentProduct.put("FMaterialId.FNumber",parentProductNum);
                            salesOrderProduct.put("FParentMatId.FNumber",parentProductNum);//父产品
                        }
                        salesOrderProduct.put("FRowType","Son");
                    }
                }
            }
        }
        log.info("handleCpqSaleOrderProduct,standardData2={}", JSONObject.toJSONString(standardData));

        //如果客户指定了产品类型字段，用客户指定的覆盖程序动态生成的
        for(ObjectData saleOrderProduct : salesOrderProducts) {
            String fRowTypeOld = saleOrderProduct.getString("FRowType_old");
            if(StringUtils.isNotEmpty(fRowTypeOld)) {
                saleOrderProduct.put("FRowType",fRowTypeOld);
            }
            saleOrderProduct.remove("FRowType_old");
        }
        log.info("handleCpqSaleOrderProduct,standardData3={}", JSONObject.toJSONString(standardData));

        K3DataConverter saveConverter = k3DataManager.buildSaveConverter(tenantId, apiClient.getDataCenterId(), standardData);
        //重新转换参数
        saveConverter.fillSaveArg(standardData, saveArg,tenantId);
        log.info("handleCpqSaleOrderProduct SaveArg after={}",saveArg);
        return result;
    }
    /**
     * 是否需要走cpq逻辑
     * @param tenantId
     * @return
     */
    public Boolean isNeedHandleCpq(String tenantId,String dataCenterId) {
        ErpTenantConfigurationEntity needHandleCpq = tenantConfigurationManager.findOne(tenantId,dataCenterId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.saleOrderNeedHandleCpq.name());
        if(needHandleCpq==null){
            ErpTenantConfigurationEntity entity=new ErpTenantConfigurationEntity();
            entity.setId(idGenerator.get());
            entity.setTenantId(tenantId);
            entity.setDataCenterId(dataCenterId);
            entity.setChannel(ErpChannelEnum.ERP_K3CLOUD.name());
            entity.setType(TenantConfigurationTypeEnum.saleOrderNeedHandleCpq.name());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(entity.getCreateTime());
            HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
            GetConfigValueByKeyArg arg = new GetConfigValueByKeyArg();
            arg.setKey(CrmConfigKeyConstants.CPQ);
            GetConfigValueByKeyResult isOpenCpq = skuSpuService.getConfigValueByKey(headerObj, arg);
            if (isOpenCpq == null || isOpenCpq.getCode() != 0) {
                entity.setConfiguration("false");
            }else if (IsOpenCpqEnum.IsOpen.getValue().equals(isOpenCpq.getValue())) {
                entity.setConfiguration("true");
            }else{
                entity.setConfiguration("false");
            }
            tenantConfigurationManager.insert(tenantId,entity);
            return Boolean.valueOf(entity.getConfiguration());
        }
        return Boolean.valueOf(needHandleCpq.getConfiguration());
    }
    /**
     * SalesOrderObj->SAL_SaleOrder 只能有一个
     *
     * @param tenantId
     * @param erpDcId
     * @return
     */
    public SyncPloyDetailEntity getOutputOrderPloyDetail(String tenantId, String erpDcId) {
        List<SyncPloyDetailEntity> ployDetailEntities= adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantTypeAndObjApiName(tenantId, TenantType.CRM, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
        ployDetailEntities.removeIf(v->!erpDcId.equals(v.getDestDataCenterId()));
        if(CollectionUtils.isNotEmpty(ployDetailEntities)){
            for(SyncPloyDetailEntity syncPloyDetailEntity:ployDetailEntities){
                if(syncPloyDetailEntity.getDestObjectApiName()!=null&&syncPloyDetailEntity.getDestObjectApiName().contains(K3CloudForm.SAL_SaleOrder)){
                    return syncPloyDetailEntity;
                }
            }
        }
        return null;
    }

    /**
     * 同步CPQ父项
     */
    public Result<String> onlySyncCpqParent(String tenantId,String dataCenterId,StandardData standardData, SaveArg saveArg){
        K3DataConverter saveConverter = k3DataManager.buildSaveConverter(tenantId, dataCenterId, standardData);
        //重新转换参数
        saveConverter.fillSaveArg(standardData, saveArg,tenantId);
        log.info("handleCpqSaleOrderProduct SaveArg after={}",saveArg);
         Result<String> result=Result.newSuccess();
         return result;
    }

}
