package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AspectSpelUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SapConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;


/**
 * CH当前存储最大为1M,限制result为500K
 * 截断方式为: 保留第一个返回值元素,后面的只记录主键和主属性
 *
 * <AUTHOR>
 * @date 2024/11/15 16:57:34
 */
@Aspect
@Component
@Slf4j
public class TruncationInterfaceMonitorAspect {

    @Autowired
    private ErpFieldManager erpFieldManager;

    @Autowired
    private ErpObjManager erpObjManager;

    private final ThreadLocal<Pair<String, ErpConnectInfoEntity>> realApiNameConnectInfoEntityThreadLocal = new ThreadLocal<>();

    /**
     * 暂时只有批量获取接口超1M
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler.listErpObjDataByTime(..)) && args(timeFilterArg, connectInfo)")
    public Object saveConnectInfoCache(ProceedingJoinPoint joinPoint, TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) throws Throwable {
        try {
            realApiNameConnectInfoEntityThreadLocal.set(Pair.of(timeFilterArg.getObjAPIName(), connectInfo));
            return joinPoint.proceed();
        } finally {
            realApiNameConnectInfoEntityThreadLocal.remove();
        }
    }

    /**
     * K3是单个获取的,可能从对象过多会超限速
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager.*(..)) && args(erpIdArg, connectInfo)")
    public Object saveK3ConnectInfoCache(ProceedingJoinPoint joinPoint, ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) throws Throwable {
        final Pair<String, ErpConnectInfoEntity> pair = realApiNameConnectInfoEntityThreadLocal.get();
        if (Objects.nonNull(pair)) {
            return joinPoint.proceed();
        }

        try {
            realApiNameConnectInfoEntityThreadLocal.set(Pair.of(erpIdArg.getObjAPIName(), connectInfo));
            return joinPoint.proceed();
        } finally {
            realApiNameConnectInfoEntityThreadLocal.remove();
        }
    }

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager.saveErpInterfaceMonitor(..))")
    public Object saveErpInterfaceMonitor(ProceedingJoinPoint joinPoint) throws Throwable {
        final Pair<String, ErpConnectInfoEntity> pair = realApiNameConnectInfoEntityThreadLocal.get();
        if (Objects.isNull(pair)) {
            return joinPoint.proceed();
        }

        ErpConnectInfoEntity erpConnectInfo = pair.getRight();

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        final Method method = signature.getMethod();
        final String[] parameterNames = AspectSpelUtil.getParameters(method);
        Object[] args = joinPoint.getArgs();

        String result = null;
        Integer status = null;
        Integer resultIndex = null;
        for (int i = 0; i < parameterNames.length; i++) {
            if ("result".equals(parameterNames[i])) {
                result = (String) args[i];
                resultIndex = i;
            } else if ("status".equals(parameterNames[i])) {
                status = (int) args[i];
            }
        }

        if (StringUtils.isEmpty(result) || RamUsageEstimateUtil.sizeOfObjectIgnoreException(result) <= ConfigCenter.LIST_CONTENT_LOG_LENGTH_LIMIT) {
            return joinPoint.proceed();
        }

        args[resultIndex] = truncationJson(result, status, erpConnectInfo, pair);
        return joinPoint.proceed(args);
    }

    private String truncationJson(String result, Integer status, ErpConnectInfoEntity erpConnectInfo, Pair<String, ErpConnectInfoEntity> pair) throws Throwable {
        // status=1成功
        if (!Objects.equals(status, 1)) {
            // 失败的返回值无法确定,直接截断
            return abbreviatedString(result);
        }

        final ErpChannelEnum channel = erpConnectInfo.getChannel();
        // 海外连接器不确定返回的类型是什么,暂时先截断
        if (Objects.equals(channel, ErpChannelEnum.ERP_LINKEDIN) || Objects.equals(channel, ErpChannelEnum.ERP_FACEBOOK)) {
            return abbreviatedString(result);
        }

        final String realApiName = pair.getLeft();
        final String tenantId = erpConnectInfo.getTenantId();
        final String dataCenterId = erpConnectInfo.getId();
        final Set<String> fields = new HashSet<>();
        if (Objects.equals(channel, ErpChannelEnum.ERP_K3CLOUD)) {
            // 金蝶使用extend.queryCode
            final ErpFieldExtendEntity idFieldExtend = erpFieldManager.findIdFieldExtendCache(tenantId, dataCenterId, realApiName);
            if (Objects.isNull(idFieldExtend)) {
                return abbreviatedString(result);
            }

            // 尝试获取num字段
            Optional.ofNullable(erpFieldManager.queryNumFieldExtend(tenantId, dataCenterId, realApiName))
                    .map(ErpFieldExtendEntity::getQueryCode)
                    .ifPresent(fields::add);

            fields.add(idFieldExtend.getQueryCode());
        } else {
            String masterSplitObjApiName = erpObjManager.getMasterSplitObjApiName(tenantId, dataCenterId, realApiName);
            final ErpObjectFieldEntity masterIdField = erpFieldManager.findIdField(tenantId, masterSplitObjApiName);
            if (Objects.isNull(masterIdField)) {
                return abbreviatedString(result);
            }
            fields.add(masterIdField.getFieldApiName());
        }

        final ISimpleLogService simpleLogService = simpleLogServiceMap.getOrDefault(channel, defaultSimpleLogService);
        try {
            final String truncationResult = simpleLogService.truncationResult(result, erpConnectInfo, fields);
            // 为了防止精简后数据量还是太大,再执行一次字符串截断
            return abbreviatedString(truncationResult);
        } catch (Exception e) {
            log.warn("truncationJson error, result:{}", result, e);
            return abbreviatedString(result);
        }
    }

    // 为了单元测试,不配置final,否则会在编译时替换
    private static int SUFFIX_LENGTH = 50; // 后缀长度

    public String abbreviatedString(String responseBody) {
        try {
            if (RamUsageEstimateUtil.sizeOfObjectIgnoreException(responseBody) > ConfigCenter.LIST_CONTENT_LOG_LENGTH_LIMIT) {
                // STRING_SIZE + (long) NUM_BYTES_ARRAY_HEADER = 40 , 1个字符按2个字节算
                int maxLength = (int) ((ConfigCenter.LIST_CONTENT_LOG_LENGTH_LIMIT - 40) / 2);
                if (responseBody.length() > maxLength && maxLength > SUFFIX_LENGTH) {
                    int prefixLength = maxLength - SUFFIX_LENGTH;
                    String prefix = responseBody.substring(0, prefixLength);
                    String suffix = responseBody.substring(responseBody.length() - SUFFIX_LENGTH);
                    responseBody = prefix + "..." + suffix;
                }
            }
            return responseBody;
        } catch (Exception e) {
            log.warn("abbreviatedString error", e);
            return responseBody;
        }
    }

    private String truncationJson(String jsonString, final String dataField, Set<String> includeFields, String channelType) throws Exception {
        return truncationJson(jsonString, Lists.newArrayList(dataField), includeFields, channelType);
    }

    private String truncationListJson(String jsonString, final String dataField, Set<String> includeFields, String channelType) throws Exception {
        return truncationJson(jsonString, Lists.newArrayList(dataField, "dataList"), ImmutableMap.of("masterFieldVal", includeFields), channelType);
    }


    private String truncationJson(String jsonString, List<String> dataFields, Set<String> includeFields, String channelType) throws Exception {
        return truncationJson(jsonString, dataFields, Collections.singletonMap("", includeFields), channelType);
    }

    public String truncationJson(String jsonString, List<String> dataFields, Map<String, Set<String>> includeFieldMap, String channelType) throws Exception {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();

        // 将JSON字符串解析为JsonNode
        JsonNode jsonNode = objectMapper.readTree(jsonString);

        final ArrayNode resultArray = getDataNodes(jsonNode, dataFields);

        if (Objects.isNull(resultArray)) {
            log.warn("truncationJson unknown channel:{} result:{} dataFields:{}", channelType, jsonString, dataFields);
            return abbreviatedString(jsonString);
        }

        // 处理result数组
        for (int i = 1; i < resultArray.size(); i++) {
            ObjectNode item = (ObjectNode) resultArray.get(i);
            if (!includeFieldMap.containsKey("")) {
                // 保留关键字段
                item.retain(includeFieldMap.keySet());
            }

            for (Map.Entry<String, Set<String>> entry : includeFieldMap.entrySet()) {
                String key = entry.getKey();
                Set<String> fields = entry.getValue();
                final ObjectNode keyNode = StringUtils.isNotBlank(key) ? (ObjectNode) item.get(key) : item;
                if (keyNode != null) {
                    // 检查 keyNode 是否包含 fields 中的字段
                    boolean containsAllFields = fields.stream().anyMatch(keyNode::has);
                    if (containsAllFields) {
                        // 保留id,name字段，删除其他字段
                        keyNode.retain(fields);
                    }
                }
            }
        }

        // 打印修改后的JSON
        return objectMapper.writeValueAsString(jsonNode);
    }

    private static @Nullable ArrayNode getDataNodes(JsonNode jsonNode, List<String> dataFields) {
        // 获取result字段的ArrayNode
        if (CollectionUtils.isEmpty(dataFields)) {
            return (ArrayNode) jsonNode;
        }

        ArrayNode resultArray = null;
        JsonNode jsonNode1 = jsonNode;
        for (String dataField : dataFields) {
            if (StringUtils.isEmpty(dataField)) {
                continue;
            }
            jsonNode1 = jsonNode1.get(dataField);
            if (Objects.isNull(jsonNode1)) {
                return null;
            }
        }
        if (jsonNode1.isArray()) {
            resultArray = (ArrayNode) jsonNode1;
        }
        return resultArray;
    }

    interface ISimpleLogService {
        String truncationResult(String jsonString, ErpConnectInfoEntity connectInfo, Set<String> includeFields) throws Exception;
    }

    private Map<ErpChannelEnum, ISimpleLogService> simpleLogServiceMap = ImmutableMap.of(
            // K3是单独id查询的
            ErpChannelEnum.ERP_K3CLOUD, (jsonString, connectInfo, includeFields) -> truncationJson(jsonString, "data", includeFields, "ERP_K3CLOUD"),

            ErpChannelEnum.ERP_SAP, (jsonString, connectInfo, includeFields) -> {
                final String dataName = ((SapConnectParam) connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams())).getResultFormat().getDataName();
                return truncationListJson(jsonString, dataName, includeFields, "ERP_SAP");
            },

            ErpChannelEnum.STANDARD_CHANNEL, (jsonString, connectInfo, includeFields) -> {
                final StandardConnectParam connectParam = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams());
                if (ConnectorHandlerType.HUB.equals(connectParam.getConnectorHandlerType())) {
                    return truncationListJson(jsonString, "", includeFields, "STANDARD_HUB");
                } else if (ConnectorHandlerType.REST_API.equals(connectParam.getConnectorHandlerType())) {
                    final String dataName = connectParam.getResultFormatOrDefault().getDataName();
                    return truncationListJson(jsonString, dataName, includeFields, "STANDARD_REST_API");
                } else if (ConnectorHandlerType.APL_CLASS.equals(connectParam.getConnectorHandlerType())) {
                    return truncationListJson(jsonString, "", includeFields, "STANDARD_APL_CLASS");
                } else {
                    return truncationListJson(jsonString, "data", includeFields, "STANDARD_CHANNEL");
                }
            },
            ErpChannelEnum.YXT_MARKETING_ZHIHU, (jsonString, connectInfo, includeFields) -> truncationJson(jsonString, Lists.newArrayList("data"), ImmutableMap.of("phone", includeFields), "YXT_MARKETING_ZHIHU"),
            ErpChannelEnum.ERP_K3CLOUD_ULTIMATE, (jsonString, connectInfo, includeFields) -> truncationJson(jsonString, Lists.newArrayList("data", "rows"), ImmutableMap.of("", includeFields), "ERP_K3CLOUD_ULTIMATE")
    );

    private ISimpleLogService defaultSimpleLogService = (jsonString, connectInfo, includeFields) -> truncationListJson(jsonString, "data", includeFields, connectInfo.getChannel().name());
}
