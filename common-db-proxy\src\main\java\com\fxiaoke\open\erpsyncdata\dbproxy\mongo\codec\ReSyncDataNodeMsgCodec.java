package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReSyncDataNodeMsg;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.mongodb.MongoClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.*;
import org.bson.codecs.Codec;
import org.bson.codecs.CollectibleCodec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;
import org.bson.types.ObjectId;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:24 2022/12/8
 * @Desc:
 */
@Slf4j
public class ReSyncDataNodeMsgCodec implements CollectibleCodec<ReSyncDataNodeMsg> {
    private final Codec<Document> documentCodec;

    /**
     * Default constructor.
     */
    public ReSyncDataNodeMsgCodec() {
        this.documentCodec = MongoClient.getDefaultCodecRegistry().get(Document.class);
    }


    private ReSyncDataNodeMsg convert(Document document) {
        if (document == null) {
            return null;
        }
        ReSyncDataNodeMsg msg = BeanUtil.toBean(document, ReSyncDataNodeMsg.class,
                CopyOptions.create().ignoreError().setIgnoreProperties("syncDataEntity"));
        msg.setSyncDataEntity(convert2SyncDataEntity(document.get("syncDataEntity")));
        msg.setId(document.getObjectId("_id"));
        return msg;
    }

    private SyncDataEntity convert2SyncDataEntity(Object doc) {
        if (doc != null) {
            try {
                SyncDataEntity data = JacksonUtil.fromJson(JacksonUtil.toJson(doc), SyncDataEntity.class);
                return data;
            } catch (Exception e) {
                log.error("decode Object Data failed,", e);
            }
        }
        return null;
    }

    @Override
    public ReSyncDataNodeMsg decode(BsonReader reader, DecoderContext decoderContext) {
        Document document = documentCodec.decode(reader, decoderContext);
        ReSyncDataNodeMsg msg = convert(document);
        return msg;
    }

    @Override
    public void encode(BsonWriter writer, ReSyncDataNodeMsg value, EncoderContext encoderContext) {
        Map<String, Object> map = BeanUtil.beanToMap(value);
        Document document = new Document(map);
        Object msg = document.remove("syncDataEntity");
        document.put("syncDataEntity", JacksonUtil.toJson(msg));
        documentCodec.encode(writer, document, encoderContext);
    }

    @Override
    public Class<ReSyncDataNodeMsg> getEncoderClass() {
        return ReSyncDataNodeMsg.class;
    }

    @Override
    public ReSyncDataNodeMsg generateIdIfAbsentFromDocument(ReSyncDataNodeMsg document) {
        if (!documentHasId(document)) {
            document.setId(new ObjectId());
        }
        return document;
    }

    @Override
    public boolean documentHasId(ReSyncDataNodeMsg document) {
        return document.getId() != null;
    }

    @Override
    public BsonValue getDocumentId(ReSyncDataNodeMsg document) {
        if (!documentHasId(document)) {
            throw new IllegalStateException("The document does not contain an _id");
        }
        return new BsonString(document.getId().toString());
    }
}
