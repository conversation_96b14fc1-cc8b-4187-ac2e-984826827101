package com.fxiaoke.open.erpsyncdata.monitor.handler;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 17:40 2022/8/3
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "syncTimeOutDataHandler")
public class SyncTimeOutDataHandler extends IJobHandler {

    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        return ReturnT.SUCCESS;
    }
}
