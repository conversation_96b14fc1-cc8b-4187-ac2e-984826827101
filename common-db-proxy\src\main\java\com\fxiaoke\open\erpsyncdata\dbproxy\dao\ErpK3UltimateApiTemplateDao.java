package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateApiTemplateEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 云星空旗舰版api template dao
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
@Repository
public interface ErpK3UltimateApiTemplateDao extends ErpBaseDao<ErpK3UltimateApiTemplateEntity>, ITenant<ErpK3UltimateApiTemplateDao> {
    ErpK3UltimateApiTemplateEntity findData(@Param("tenantId") String tenantId,
                                      @Param("dataCenterId") String dataCenterId,
                                      @Param("erpObjApiName") String erpObjApiName);
}