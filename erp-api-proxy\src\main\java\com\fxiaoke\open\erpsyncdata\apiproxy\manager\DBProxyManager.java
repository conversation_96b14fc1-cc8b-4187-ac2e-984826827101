package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.common.http.handler.TimeoutSettings;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.dbproxy.DBProxyV2Handler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpDbProxyConfigDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDBProxyConfigEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DbTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DBProxyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.proxyservice.DbProxyServiceInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_DB_PROXY)
public class DBProxyManager extends BaseErpDataManager {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private ErpDbProxyConfigDao erpDbProxyConfigDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private DBProxyV2Handler dbProxyV2Handler;

    private final static String BY_ID_COND_SURROUND_REG = "(?s)/\\*ByIdCondBegin\\*/.*?/\\*ByIdCondEnd\\*/";
    private final static String BATCH_COND_SURROUND_REG = "(?s)/\\*BatchCondBegin\\*/.*?/\\*BatchCondEnd\\*/";

    private final ConcurrentHashMap<String, DateTimeFormatter> formatterMap = new ConcurrentHashMap<>();


    /**
     * db连接器开始时间占位符
     * 必须配合结束时间占位符使用，当同时存在开始时间占位符和结束时间占位符时，集成平台将不再另外拼接条件
     */
    private final static String PH_START_TIME = "${START_TIME}";
    /**
     * db连接器结束时间占位符
     */
    private final static String PH_END_TIME = "${END_TIME}";



    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        if (StringUtils.isEmpty(erpIdArg.getDataId())) {
            return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s37,erpIdArg.getTenantId()));
        }
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.getErpObjData(erpIdArg, connectInfo);
        }
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(erpIdArg.getTenantId(), erpIdArg.getObjAPIName(), connectInfo.getId());
        DBProxyRequestArg requestArg =
                buildQueryRequestArg(dbProxyConfigEntity,
                        erpIdArg.getDataId()
                );
        String requestBody = JacksonUtil.toJson(requestArg);
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(erpIdArg.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(erpIdArg.getObjAPIName())
                .type(ErpObjInterfaceUrlEnum.queryMasterById.name())
                .remark("").build();
        return postWithMonitor(
                interfaceMonitorData, connectParam, "/queryMasterById", requestBody,
                new TypeReference<Result<StandardData>>() {
                }, ConfigCenter.CONTENT_LENGTH_LIMIT, null);
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if (erpObjDataResult != null) {
            Result<List<StandardData>> dataList = new Result<>();
            if (erpObjDataResult.getData() != null) {
                dataList.setData(Lists.newArrayList(erpObjDataResult.getData()));
                return dataList;
            } else {
                dataList.setData(Lists.newArrayList());
                return dataList;
            }
        }
        return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s38,erpIdArg.getTenantId()));
    }

    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg,
                                                         ErpConnectInfoEntity connectInfo) {
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if(timeFilterArg.getOperationType()!=null&& EventTypeEnum.INVALID.getType()==timeFilterArg.getOperationType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
        }
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.listErpObjDataByTime(timeFilterArg, connectInfo);
        }
        //2.0以下版本不支持作废
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            return Result.newSuccess();
        }
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName(), connectInfo
                        .getId());
        DBProxyRequestArg requestArg;
        if (dbProxyConfigEntity.isAlwaysOffsetZero()) {
            requestArg = buildBatchQueryRequestArgOffsetZero(dbProxyConfigEntity,
                    timeFilterArg.getStartTime(),
                    timeFilterArg.getEndTime(),
                    timeFilterArg.getLastMaxId(),
                    timeFilterArg.getLimit(),
                    connectParam.getDbType());
        } else {
            requestArg = buildBatchQueryRequestArg(dbProxyConfigEntity,
                    timeFilterArg.getStartTime(),
                    timeFilterArg.getEndTime(),
                    timeFilterArg.getOffset(),
                    timeFilterArg.getLimit(),
                    connectParam.getDbType());
        }
        String requestBody = JacksonUtil.toJson(requestArg);
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(timeFilterArg.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(timeFilterArg.getObjAPIName())
                .type(type.name())
                .remark("")
                .timeFilterArg(timeFilterArg)
                .build();
        Result<StandardListData> standardListDataResult = postWithMonitor(interfaceMonitorData, connectParam, "/queryMasterBatch", requestBody
                , new TypeReference<Result<StandardListData>>() {
                },
                ConfigCenter.LIST_CONTENT_LENGTH_LIMIT, null);
        if (dbProxyConfigEntity.isAlwaysOffsetZero()) {
            //动态时间还需要处理结果
            processDynamicStartTimeResult(standardListDataResult,dbProxyConfigEntity);
        }
        return standardListDataResult;
    }

    private void processDynamicStartTimeResult(Result<StandardListData> result, ErpDBProxyConfigEntity dbProxyConfigEntity) {
        if (!result.isSuccess()) {
            return;
        }
        StandardListData data = result.getData();
        List<StandardData> dataList = data.getDataList();
        if (dataList.isEmpty()) {
            return;
        }
        ObjectData lastData = dataList.get(dataList.size() - 1).getMasterFieldVal();
        String dbKey = dbProxyConfigEntity.getDbKey();
        String dateField = dbProxyConfigEntity.getDateTimeConditionField();
        String maxId = lastData.getString(dbKey);
        String maxDate = lastData.getString(dateField);
        long maxTime;
        if (StrUtil.isBlank(maxId)) {
            throw new ErpSyncDataException(I18NStringEnum.s247,
                    dbProxyConfigEntity.getTenantId());
        }
        if (StrUtil.isBlank(maxDate)) {
            throw new ErpSyncDataException(I18NStringEnum.s248,
                    dbProxyConfigEntity.getTenantId());
        }
        try {
            //尝试解析时间
            DateTime maxDateTime = DateUtil.parse(maxDate);
            maxTime = maxDateTime.getTime();
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e, I18NStringEnum.s249);
        }
        data.setMaxId(maxId);
        data.setMaxTime(maxTime);
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.createErpObjData(standardData, connectInfo);
        }
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(), standardData.getObjAPIName(), connectInfo
                        .getId());

        DBProxyRequestArg requestArg = buildUpdateRequestArg(dbProxyConfigEntity, connectInfo, standardData, "add");
        String requestBody = JacksonUtil.toJson(requestArg);

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(standardData.getObjAPIName())
                .type(ErpObjInterfaceUrlEnum.create.name())
                .remark("").build();
        return postWithMonitor(
                interfaceMonitorData, connectParam, "/create", requestBody, new TypeReference<Result<ErpIdResult>>() {
                }, null, null);
    }

    private static boolean isAboveV2(DBProxyConnectParam connectParam) {
        return VersionComparator.INSTANCE.compare(connectParam.getVersion(), "2.0") >= 0;
    }

    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.invalidErpObjData(standardInvalidData, connectInfo);
        }
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(),
                        standardInvalidData.getObjAPIName(), connectInfo.getId());
        DBProxyRequestArg requestArg = buildInvalidRequestArg(dbProxyConfigEntity, standardInvalidData);
        String requestBody = JacksonUtil.toJson(requestArg);
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(standardInvalidData.getObjAPIName())
                .type(ErpObjInterfaceUrlEnum.invalid.name())
                .remark("").build();
        Result<Void> postResult = postWithMonitor(
                interfaceMonitorData, connectParam, "/invalid",
                requestBody,
                new TypeReference<Result<Void>>() {
                },
                ConfigCenter.CONTENT_LENGTH_LIMIT, null);
        if (postResult.isSuccess()) {
            return new Result<>(standardInvalidData.getMasterFieldVal().getId());
        }
        return Result.copy(postResult);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData,
                                                  ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.invalidErpObjDetailData(standardInvalidData, connectInfo);
        }
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.recoverErpObjData(standardRecoverData, connectInfo);
        }
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.updateErpObjData(standardData, connectInfo);
        }
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                loadDBProxyEaiConfigByTenantIdAndObjApiName(connectInfo.getTenantId(), standardData.getObjAPIName(), connectInfo
                        .getId());
        //先缓存id
        Result<ErpIdResult> result = buildReusltIdFormSourceData(standardData, dbProxyConfigEntity);
        if (!result.isSuccess()) {
            return result;
        }
        DBProxyRequestArg requestArg = buildUpdateRequestArg(dbProxyConfigEntity, connectInfo, standardData, "update");
        String requestBody = JacksonUtil.toJson(requestArg);

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName(standardData.getObjAPIName())
                .type(ErpObjInterfaceUrlEnum.update.name())
                .remark("").build();
        Result<ErpIdResult> resultResult =
                postWithMonitor(interfaceMonitorData, connectParam, "/update", requestBody, new TypeReference<Result<ErpIdResult>>() {
                }, null, null);

        if (resultResult.isSuccess() && resultResult.getData().getDetailDataIds() != null) {
            for (Map.Entry<String, List<String>> entry : resultResult.getData().getDetailDataIds().entrySet()) {
                List<String> value = entry.getValue();
                List<String> detailIds = result.getData().getDetailDataIds().get(entry.getKey());
                for (String s : value) {
                    detailIds.set(detailIds.indexOf("beDetailId"), s);
                }
            }
        } else {
            return resultResult;
        }

        return result;
    }

    /**
     * 更新默认取原来的id.
     *
     * @param standardData
     * @return
     */
    private Result<ErpIdResult> buildReusltIdFormSourceData(StandardData standardData, ErpDBProxyConfigEntity dbProxyConfigEntity) {
        ObjectData masterFieldVal = standardData.getMasterFieldVal();
        String dbKey = dbProxyConfigEntity.getDbKey();
        ErpIdResult erpIdResult = new ErpIdResult();
        Object o = masterFieldVal.get(dbKey);
        if (StringUtils.isEmpty(o)) {
            return Result.newError(I18NStringEnum.s20, standardData.getObjAPIName());
        }
        erpIdResult.setMasterDataId(String.valueOf(o));
        if (dbProxyConfigEntity.getDetails() != null) {
            Map<String, List<String>> detailIdMap = new HashMap<>();
            for (Map.Entry<String, ErpDBProxyConfigEntity> entry : dbProxyConfigEntity.getDetails().entrySet()) {
                String detailDbKey = entry.getValue().getDbKey();
                List<String> detailIds = new ArrayList<>();
                List<ObjectData> detailObjectData = standardData.getDetailFieldVals().get(entry.getKey());
                if (!CollectionUtils.isEmpty(detailObjectData)) {
                    for (ObjectData objectData : detailObjectData) {
                        Object detailId = objectData.get(detailDbKey);
                        if (StringUtils.isEmpty(detailId)) {
                            detailIds.add("beDetailId");//新增的明细id.将会被替换的明细id
                        } else {
                            detailIds.add(String.valueOf(detailId));
                        }
                    }
                }
                // 后面会使用for,所以不能给null
                detailIdMap.put(entry.getKey(), detailIds);
            }
            erpIdResult.setDetailDataIds(detailIdMap);
        }
        return Result.newSuccess(erpIdResult);
    }

    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {

        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        if (isAboveV2(connectParam)) {
            //2.0 以上版本，
            return dbProxyV2Handler.updateErpObjDetailData(doWriteMqData,standardDetailData,connectInfo);
        }
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    private ErpDBProxyConfigEntity loadDBProxyEaiConfigByTenantIdAndObjApiName(String tenantId,
                                                                               String objApiName,
                                                                               String dataCenterId) {
        ErpDBProxyConfigEntity dbProxyConfigEntity =
                erpDbProxyConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .getDBProxyConfigByTenantAndObjApiName(tenantId, objApiName, dataCenterId);
        List<ErpDBProxyConfigEntity> dbProxyConfigEntities =
                erpDbProxyConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .queryDBProxyConfigByTenantAndParentObjApiName(tenantId, objApiName, dataCenterId);
        if (!CollectionUtils.isEmpty(dbProxyConfigEntities)) {
            for (ErpDBProxyConfigEntity entity : dbProxyConfigEntities) {
                dbProxyConfigEntity.getDetails().put(entity.getObjApiName(), entity);
            }
        }
        return dbProxyConfigEntity;
    }


    /**
     * 使用IdAfter方式轮询的请求
     * 限制比较多
     * 不允许替换条件，只能拼接在后面
     */
    private DBProxyRequestArg buildBatchQueryRequestArgOffsetZero(ErpDBProxyConfigEntity dbProxyConfigEntity,
                                                                  Long startTime,
                                                                  Long endTime,
                                                                  String lastMaxId,
                                                                  Integer limit,
                                                                  String dbType) {
        log.info("DBProxyManager.buildBatchQueryRequestArgOffsetZero,dbProxyConfigEntity={},dbType={}", dbProxyConfigEntity, dbType);
        String dbKey = dbProxyConfigEntity.getDbKey();
        String querySql = dbProxyConfigEntity.getQuerySql();
        querySql = StrUtil.removeSuffix(querySql.trim(), ";");
        Pair<String, String> timePair = buildTimePair(dbProxyConfigEntity.getDateFormat(), startTime, endTime, dbType);
        String startTimeStr = timePair.getKey();
        String endTimeStr = timePair.getValue();
        //id顺序只允许一个时间字段。这里不做验证，在设置处验证。
        String dateField = dbProxyConfigEntity.getDateTimeConditionField();
        //固定orderBy
        String orderBy = String.format(" order by %s,%s", dateField, dbKey);
        String sql = "";
        if (lastMaxId != null) {
            //增加id范围的sql
            //如果存在上次的最大id，先查询= startTime,> lastMaxId的
            String where1 = String.format(" and %s = %s and %s > '%s'", dateField, startTimeStr, dbKey, lastMaxId) + orderBy;
            //时间+id范围sql
            sql += querySql + where1 + ";";
        }
        //增加时间范围的sql
        String where2 = String.format(" and %s > %s and %s <= %s", dateField, startTimeStr, dateField, endTimeStr) + orderBy;
        //时间范围sql
        sql += querySql + where2 + ";";
        DBProxyRequestArg.AdaptSqlParams adaptSqlParams = new DBProxyRequestArg.AdaptSqlParams();
        adaptSqlParams.setMasterSql(sql);
        Map<String, List<String>> detailSqls = new HashMap<>();
        dbProxyConfigEntity.getDetails().forEach((detailObjApiName, detail) -> {
            //这里感觉有问题，不知道为什么用一个列表存一条数据。。。
            detailSqls.put(detailObjApiName, Arrays.asList(detail.getQuerySql()));
        });
        adaptSqlParams.setDetailSqlList(detailSqls);
        DBProxyRequestArg requestArg = new DBProxyRequestArg();
        requestArg.setNoOffset(true);
        requestArg.setLimit(limit);
        requestArg.setOffset(0);
        requestArg.setEndTime(endTime);
        requestArg.setStartTime(startTime);
        requestArg.setAdaptSqlParams(adaptSqlParams);
        requestArg.setMasterPrimaryKey(dbKey);
        requestArg.setObjApiName(dbProxyConfigEntity.getObjApiName());
        log.info("DBProxyManager.buildBatchQueryRequestArgOffsetZero,requestArg={}", requestArg);
        return requestArg;
    }

    private Pair<String, String> buildTimePair(String dateFormat, Long startTime, Long endTime, String dbType) {
        dateFormat = StrUtil.blankToDefault(dateFormat, "yyyy-MM-dd HH:mm:ss");
        if (!dateFormat.contains("'")) {
            //当包含单引号，需要在日期格式完全写对dateFormat,不再加工。适用于使用函数的场景。
            if (DbTypeEnum.ORACLE.match(dbType)) {
                //为兼容原逻辑 oracle写死转换格式 输出例子： TO_DATE('2023-08-03 10:59:06','yyyy-mm-dd hh24:mi:ss')
                dateFormat = "'TO_DATE('''yyyy-MM-dd HH:mm:ss'','''yyyy-mm-dd hh24:mi:ss'')'";
            } else {
                //默认包围单引号 输出例子：'2023-08-03 10:59:06'
                dateFormat = "''" + dateFormat + "''";
            }
        }
        //DateTimeFormatter线程安全，可以储存下来。
        DateTimeFormatter dtf = formatterMap.computeIfAbsent(dateFormat, DateTimeFormatter::ofPattern);
        ZonedDateTime zonedDateStartTime = Instant.ofEpochMilli(startTime).atZone(ZoneId.systemDefault());
        ZonedDateTime zonedDateEndTime = Instant.ofEpochMilli(endTime).atZone(ZoneId.systemDefault());
        String startTimeStr1 = dtf.format(zonedDateStartTime);
        String endTimeStr1 = dtf.format(zonedDateEndTime);
        Pair<String, String> timePair = new Pair<>(startTimeStr1, endTimeStr1);
        return timePair;
    }


    private DBProxyRequestArg buildBatchQueryRequestArg(ErpDBProxyConfigEntity dbProxyConfigEntity,
                                                        Long startTime,
                                                        Long endTime,
                                                        Integer offset,
                                                        Integer limit,
                                                        String dbType) {

        log.info("DBProxyManager.buildQueryRequestArg,dbProxyConfigEntity={},dbType={}", dbProxyConfigEntity, dbType);
        String conditionFields = dbProxyConfigEntity.getDateTimeConditionField();
        boolean existTimeCondition = StrUtil.isNotBlank(conditionFields);
        //1.1版本后，允许用;分开插入多条，db连接器分别查询
        String sql = dbProxyConfigEntity.getQuerySql();
        if (StrUtil.isBlank(sql)) {
            throw new ErpSyncDataException(I18NStringEnum.s3634, null);
        }
        sql = sql.replaceAll(BY_ID_COND_SURROUND_REG, "");
        if (StrUtil.containsAll(sql,PH_START_TIME,PH_END_TIME)){
            //该方案有漏洞，，，byId的没去除，所以又用特殊符号扩起来能选择
            //当同时存在 startTime和endTime占位符时，开启自由sql模式，填充变量。这个时候conditionFields仅用作判断是否需要走缓存表。
            Pair<String, String> timePair = buildTimePair(dbProxyConfigEntity.getDateFormat(), startTime, endTime, dbType);
            String startTimeStr = timePair.getKey();
            String endTimeStr = timePair.getValue();
            sql = StrUtil.replace(sql,PH_START_TIME,startTimeStr);
            sql = StrUtil.replace(sql,PH_END_TIME,endTimeStr);
        }else if (existTimeCondition) {
            //原来的，平台拼接条件模式
            Pair<String, String> timePair = buildTimePair(dbProxyConfigEntity.getDateFormat(), startTime, endTime, dbType);
            String startTimeStr = timePair.getKey();
            String endTimeStr = timePair.getValue();
            StringBuilder conditons = new StringBuilder();
            String[] dateTimeConditionField;
            if (conditionFields.contains(CommonConstant.DB_QUERY_TIME_FIELD_SPLIT)) {
                dateTimeConditionField = conditionFields.split(CommonConstant.DB_QUERY_TIME_FIELD_SPLIT);
            } else {
                dateTimeConditionField = conditionFields.split(",");
            }
            for (String field : dateTimeConditionField) {
                if (conditons.length() == 0) {
                    conditons.append(field)
                            .append(">")
                            .append(startTimeStr)
                            .append(" and ")
                            .append(field)
                            .append("<=")
                            .append(endTimeStr);
                } else {
                    conditons.append(" or ")
                            .append(field)
                            .append(">")
                            .append(startTimeStr)
                            .append(" and ")
                            .append(field)
                            .append("<=")
                            .append(endTimeStr);
                }
            }
            if (dbProxyConfigEntity.getQuerySql().contains(CommonConstant.DB_QUERY_CRITERIA)) {
                sql = dbProxyConfigEntity.getQuerySql().replaceAll(CommonConstant.DB_QUERY_CRITERIA, conditons.toString());
            } else {
                sql = dbProxyConfigEntity.getQuerySql() + " and (" + conditons + " )";
            }
        }
        DBProxyRequestArg.AdaptSqlParams adaptSqlParams = new DBProxyRequestArg.AdaptSqlParams();
        adaptSqlParams.setMasterSql(sql);
        Map<String, List<String>> detailSqls = new HashMap<>();
        dbProxyConfigEntity.getDetails().forEach((detailObjApiName, detail) -> {
            detailSqls.put(detailObjApiName, Arrays.asList(detail.getQuerySql()));
        });
        adaptSqlParams.setDetailSqlList(detailSqls);
        DBProxyRequestArg requestArg = new DBProxyRequestArg();
        requestArg.setQueryDataFromCache(!existTimeCondition);
        requestArg.setOffset(offset);
        requestArg.setLimit(limit);
        requestArg.setEndTime(endTime);
        requestArg.setStartTime(startTime);
        requestArg.setAdaptSqlParams(adaptSqlParams);
        requestArg.setMasterPrimaryKey(dbProxyConfigEntity.getDbKey());
        requestArg.setObjApiName(dbProxyConfigEntity.getObjApiName());
        log.info("DBProxyManager.buildQueryRequestArg,requestArg={}", requestArg);
        return requestArg;
    }

    /**
     * <p>构建查询参数</p>
     *
     * @dateTime 2022-06-02 20:13
     * <AUTHOR>
     * @version 1.0
     */
    DBProxyRequestArg buildQueryRequestArg(ErpDBProxyConfigEntity dbProxyConfigEntity, String dataId) {
        log.info("DBProxyManager.buildQueryRequestArg,dbProxyConfigEntity={}", dbProxyConfigEntity);
        String sql = dbProxyConfigEntity.getQuerySql();
        sql = sql.replaceAll(BATCH_COND_SURROUND_REG,"");
        StringBuilder conditons = new StringBuilder();
        conditons.append(dbProxyConfigEntity.getDbKey()).append("='").append(dataId).append("'");
        if (dbProxyConfigEntity.getQuerySql().contains(CommonConstant.DB_QUERY_CRITERIA)) {
            sql = sql.replaceAll(CommonConstant.DB_QUERY_CRITERIA, conditons.toString());
        } else {
            sql = sql + " and (" + conditons + " )";
        }
        DBProxyRequestArg.AdaptSqlParams adaptSqlParams = new DBProxyRequestArg.AdaptSqlParams();
        adaptSqlParams.setMasterSql(sql);
        Map<String, List<String>> detailSqls = new HashMap<>();
        dbProxyConfigEntity.getDetails().forEach((detailObjApiName, detail) -> {
            detailSqls.put(detailObjApiName, Arrays.asList(detail.getQuerySql()));
        });
        adaptSqlParams.setDetailSqlList(detailSqls);
        DBProxyRequestArg requestArg = new DBProxyRequestArg();
        requestArg.setAdaptSqlParams(adaptSqlParams);
        requestArg.setMasterPrimaryKey(dbProxyConfigEntity.getDbKey());
        requestArg.setObjApiName(dbProxyConfigEntity.getObjApiName());
        log.info("DBProxyManager.buildQueryRequestArg,requestArg={}", requestArg);
        return requestArg;
    }

    /**
     * <p>构建新增或更新数据参数</p>
     *
     * @param
     * @param connectInfo
     * @param standardData
     * @param operation
     * @return U8EaiRequestArg
     */
    private DBProxyRequestArg buildUpdateRequestArg(ErpDBProxyConfigEntity dbProxyConfigEntity,
                                                    ErpConnectInfoEntity connectInfo,
                                                    StandardData standardData,
                                                    String operation) {
        DBProxyRequestArg requestArg = new DBProxyRequestArg();
        requestArg.setObjApiName(standardData.getObjAPIName());
        DBProxyRequestArg.AdaptSqlParams adaptSqlParams = new DBProxyRequestArg.AdaptSqlParams();
        requestArg.setAdaptSqlParams(adaptSqlParams);
        requestArg.setMasterPrimaryKey(dbProxyConfigEntity.getDbKey());
        Map<String, ErpDBProxyConfigEntity> details = dbProxyConfigEntity.getDetails();


        //主对象sql生成逻辑
        String sql = dbProxyConfigEntity.getInsertSql();
        if ("update".equals(operation)) {
            sql = dbProxyConfigEntity.getUpdateSql();
        }
        if (StrUtil.isBlank(sql)){
            //异常
            throw new ErpSyncDataException(I18NStringEnum.s3635, null);
        }
        ObjectData objectData = standardData.getMasterFieldVal();
        List<String> insertUpdatesqls = fillSqlValues(sql, Arrays.asList(objectData));
        requestArg.getAdaptSqlParams().setMasterSql(insertUpdatesqls.get(0));

        if ("add".equals(operation) && !StringUtils.isEmpty(dbProxyConfigEntity.getQueryIdSql())) {
            String queryIdSql = dbProxyConfigEntity.getQueryIdSql();
            String querySql = fillSqlValues(queryIdSql, objectData);
            requestArg.getAdaptSqlParams().setMasterIdQuerySql(querySql);
        }

        if (CollectionUtils.isEmpty(dbProxyConfigEntity.getDetails())) {
            return requestArg;
        }
        //明细新增/更新sql生成
        Map<String, List<String>> detailObjSqls = new HashMap<>();
        Map<String, List<String>> detailIdSqls = new HashMap<>();

        requestArg.getAdaptSqlParams().setDetailIdQuerySql(detailIdSqls);
        for (Map.Entry<String, ErpDBProxyConfigEntity> entry : details.entrySet()) {
            if (!standardData.getDetailFieldVals().containsKey(entry.getKey())) {
                continue;
            }
            String dbKey = entry.getValue().getDbKey();
            if (StringUtils.isEmpty(dbKey)) {
                throw new ErpSyncDataException(I18NStringEnum.s250,dbProxyConfigEntity.getTenantId());
            }

            List<ObjectData> detailObj = standardData.getDetailFieldVals().get(entry.getKey());

            String detailAddSql = entry.getValue().getInsertSql();
            String detailUpdateSql = entry.getValue().getUpdateSql();
            if ("add".equals(operation)) {
                List<String> detailSqls = fillSqlValues(detailAddSql, detailObj);
                detailObjSqls.put(entry.getKey(), detailSqls);
                if (!StringUtils.isEmpty(entry.getValue().getQueryIdSql())) {
                    String queryIdSql = entry.getValue().getQueryIdSql();
                    List<String> querySqls = fillSqlValues(queryIdSql, detailObj);
                    detailIdSqls.put(entry.getKey(), querySqls);
                }
            } else {
                String masterId = String.valueOf(objectData.get(dbProxyConfigEntity.getDbKey()));
                //组装明细sql可能有新增明细场景
                List<String> detailSqls = new ArrayList<>();
                for (ObjectData data : detailObj) {
                    if (StringUtils.isEmpty(data.get(dbKey))) {
                        String newSql = fillSqlValues(detailAddSql, data);
                        newSql = newSql.replace("'#pid'", "'" + masterId + "'");
                        detailSqls.add(newSql);
                    } else {
                        String newSql = fillSqlValues(detailUpdateSql, data);
                        newSql = newSql.replace("'#pid'", "'" + masterId + "'");
                        detailSqls.add(newSql);
                    }
                }
                detailObjSqls.put(entry.getKey(), detailSqls);
            }

        }
        adaptSqlParams.setDetailSqlList(detailObjSqls);
        return requestArg;
    }


    /**
     * <p>构建新增或更新数据参数</p>
     *
     * @param
     * @param standardInvalidData
     * @return U8EaiRequestArg
     */
    private DBProxyRequestArg buildInvalidRequestArg(ErpDBProxyConfigEntity dbProxyConfigEntity,
                                                     StandardInvalidData standardInvalidData) {
        DBProxyRequestArg requestArg = new DBProxyRequestArg();
        requestArg.setObjApiName(standardInvalidData.getObjAPIName());
        DBProxyRequestArg.AdaptSqlParams adaptSqlParams = new DBProxyRequestArg.AdaptSqlParams();
        requestArg.setAdaptSqlParams(adaptSqlParams);
        requestArg.setMasterPrimaryKey(dbProxyConfigEntity.getDbKey());
        Map<String, ErpDBProxyConfigEntity> details = dbProxyConfigEntity.getDetails();
        //主对象sql生成逻辑
        String sql = dbProxyConfigEntity.getInvalidSql();
        //只有_id和id字段两个值
        ObjectData objectData = standardInvalidData.getMasterFieldVal();
        List<String> invalidSqls = fillSqlValues(sql, Arrays.asList(objectData));
        requestArg.getAdaptSqlParams().setMasterSql(invalidSqls.get(0));
        return requestArg;
    }

    /**
     * 因为允许为null的对象类型传参，容易在修改代码时导致空指针，都换成必传或者基础类型数据。
     */
    private <T> Result<T> postWithMonitor(InterfaceMonitorData interfaceMonitorData,
                                          DBProxyConnectParam connectParam,
                                          String path,
                                          String requestBody,
                                          TypeReference<Result<T>> t,
                                          Long rspReadLimitLenByte,
                                          Integer timeOutSecond) {
        return dbProxyV2Handler.postWithMonitor(interfaceMonitorData, connectParam, path, requestBody, t, rspReadLimitLenByte, timeOutSecond);
    }


    private List<String> fillSqlValues(String sql, List<ObjectData> objectDatas) {
        List<String> sqls = new ArrayList<>();
        if (StringUtils.isEmpty(sql)) {
            return Collections.emptyList();
        }
        for (ObjectData objectData : objectDatas) {
            List<String> fields = regexSqlFields(sql);
            String newSql = sql;
            for (String field : fields) {
                String key = field.replace("#", "");
                Object fieldV = objectData.get(key);
                newSql = fieldV == null ? newSql.replace("'#" + key + "'", "null") :
                        newSql.replace("'#" + key + "'", "'" + fieldV + "'");
            }
            sqls.add(newSql);

        }
        return sqls;
    }

    private String fillSqlValues(String sql, ObjectData objectData) {
        if (StringUtils.isEmpty(sql)) {
            return "";
        }
        List<String> fields = regexSqlFields(sql);
        String newSql = sql;
        for (String field : fields) {
            String key = field.replace("#", "");
            Object fieldV = objectData.get(key);
            newSql = fieldV == null ? newSql.replace("'#" + key + "'", "null") :
                    newSql.replace("'#" + key + "'", "'" + fieldV + "'");
        }
        return newSql;
    }

    private List<String> regexSqlFields(String sql) {
        List<String> fields = new ArrayList<>();
        String rule = "'.*?'";
        Pattern pattern = Pattern.compile(rule);
        Matcher matcher = pattern.matcher(sql);

        while (matcher.find()) {
            String field = matcher.group();
            field = field.substring(1, field.length() - 1);
            fields.add(field);
        }
        fields.remove("#pid");
        fields.remove("#parentId");
        return fields;
    }

    public Result<DbProxyServiceInfo> getProxyServiceInfo(DBProxyConnectParam dbProxyConnectParam) {
        Request request = new Request.Builder().url(dbProxyConnectParam.getBaseUrl() + "/getServiceInfo")
                .get()
                //设置超时时间
                .tag(TimeoutSettings.class, TimeoutSettings.builder().connect(2).read(2).write(5).build())
                .build();
        try {
            HttpRspLimitLenUtil.ResponseBodyModel responseBodyModel = proxyHttpClient.limitExecute(request);
            if (StrUtil.isNotBlank(responseBodyModel.getBody())) {
                Result<DbProxyServiceInfo> proxyServiceInfoResult = JacksonUtil.fromJson(responseBodyModel.getBody(), new TypeReference<Result<DbProxyServiceInfo>>() {
                });
                return proxyServiceInfoResult;
            }
            //失败
            return Result.newError(responseBodyModel.buildUnOkMessage());
        } catch (Exception e) {
            return Result.newError(ExceptionUtil.getRootCauseMessage(e));
        }
    }

    public Result<Boolean> checkExistRecordTable(ErpConnectInfoEntity connectInfo) {
        DBProxyConnectParam connectParam = ErpChannelEnum.ERP_DB_PROXY.getConnectParam(connectInfo.getConnectParams());
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorData.builder()
                .tenantId(connectInfo.getTenantId())
                .dcId(connectInfo.getId())
                .objApiName("")
                .type(ErpObjInterfaceUrlEnum.check.name())
                .remark("").build();
        return postWithMonitor(
                interfaceMonitorData, connectParam, "/checkExistRecordTable", "{}",
                new TypeReference<Result<Boolean>>() {
                }, ConfigCenter.CONTENT_LENGTH_LIMIT, 3);
    }
}
