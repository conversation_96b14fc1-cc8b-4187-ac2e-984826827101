package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 17:44 2023/3/16
 * @Desc: 失败快照数据上报
 */
@Service
@Slf4j
public class SyncDataErrorProcessor extends AbstractMonitorMqProcessor<SyncDataEntity> {
    @Autowired
    private MonitorReportManager monitorReportManager;

    public SyncDataErrorProcessor() {
        super(MonitorType.SYNC_DATA_ERROR, new TypeReference<SyncDataEntity>() {
        });
    }
    @Override
    void process(SyncDataEntity syncDataEntity) {
        monitorReportManager.sendSyncDataError2BizLog(syncDataEntity);
    }
}
