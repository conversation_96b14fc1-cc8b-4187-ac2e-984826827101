package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.PushData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.PushResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringResponse;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 推送相关接口
 * <AUTHOR> (^_−)☆
 */
public interface ConnectorPushHandler {
    /**
     * @deprecated 旧接口，单一钩子
     */
    @Deprecated
    default Result<StringResponse> webhook(StringRequest arg, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    /**
     * webhook的请求参数处理
     */
    default Result<PushData> webhookProcessRequest(StringRequest request, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    /**
     * webhook的返回结果处理
     *
     * @return 返回的结果
     */
    default Result<StringResponse> webhookProcessResponse(PushResult result, ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }
}
