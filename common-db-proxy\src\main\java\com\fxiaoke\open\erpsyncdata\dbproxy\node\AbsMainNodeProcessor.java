package com.fxiaoke.open.erpsyncdata.dbproxy.node;

import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 分发后节点的处理器
 *
 * <AUTHOR> (^_−)☆
 */
public abstract class AbsMainNodeProcessor extends AbsNodeProcessor<SyncDataContextEvent> {

    @Autowired
    protected MonitorReportManager monitorReportManager;

    protected AbsMainNodeProcessor(DataNodeNameEnum dataNodeNameEnum) {
        super(dataNodeNameEnum);
    }

    @Override
    protected void preReport(SyncDataContextEvent ctx) {
        //默认上报节点监控
        ctx.setCurrentDataNodeName(dataNodeNameEnum);
        monitorReportManager.sendProcessNodeMsgByCtx(ctx, dataNodeNameEnum);
        if (dataNodeNameEnum.getTimePointName() != null) {
            if (ctx.getSyncDataId()!=null){
                TimePointRecorderStatic.recordSync(dataNodeNameEnum.getTimePointName(), ctx.getSyncDataId());
            }else {
                TimePointRecorderStatic.record(dataNodeNameEnum.getTimePointName());
            }
        }
    }

    @Override
    public boolean needProcess(SyncDataContextEvent ctx) {
        //默认是上一步停止了就不再执行
        return !ctx.getStop();
    }

}
