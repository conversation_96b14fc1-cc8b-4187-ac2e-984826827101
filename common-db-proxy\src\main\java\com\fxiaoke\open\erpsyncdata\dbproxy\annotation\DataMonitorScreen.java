package com.fxiaoke.open.erpsyncdata.dbproxy.annotation;

import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @create 2023/12/22 10:20
 * @desc
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface DataMonitorScreen {

    /**
     * 企业账号
     */
    String tenantId() default "";
    /**
     * 外部系统
     */
    String dataCenterId() default "";

    /**
     * 集成流
     */
    String ployDetailId() default "";

    /**
     * 外部对象
     */
    String outSideObjApiName() default "";

    /**
     * 外部对象数据id
     */
    String outSideObjId() default "";

    /**
     * crm对象
     */
    String crmObjApiName() default "";

    /**
     * crm对象数据id
     */
    String crmObjId() default "";

    /**
     * 操作类型
     * @see  CommonConstant
     */
    String operationType() default "";

    /**
     * 具体操作类型
     * @see ErpObjInterfaceUrlEnum
     */
    ErpObjInterfaceUrlEnum operationTypeDetail() default ErpObjInterfaceUrlEnum.create;

    /**
     * 区分是否是历史数据
     */
    String historyDataType() default "";


    /**
     * 源系统
     */
    String sourceSystemType() default "2";

    /**
     * 执行状态 1 成功
     *
     * 2 失败
     */
    String operateStatus() default "";

    /**
     * 外部数据量
     * @return
     */
    String outDataCount() default "0";

    /**
     * 执行时间
     */
    long executeTime() default 0L;

    /**
     * 耗时
     */
    long executeCost() default 0L;
    /**
     * 创建时间
     */
    long createTime() default 0L;

    /**
     * 更新时间
     */
    long updateTime() default 0L;
    /**
     * 是否跳过上报
     */
    String skipSend() default "false";
}
