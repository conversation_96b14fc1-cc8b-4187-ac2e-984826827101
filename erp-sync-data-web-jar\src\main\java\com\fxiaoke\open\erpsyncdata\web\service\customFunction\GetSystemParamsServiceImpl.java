package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 */
@Service("getSystemParams")
public class GetSystemParamsServiceImpl implements CustomFunctionCommonService{
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        String tenantId = arg.getTenantId();
        String params = arg.getParams();
        Map<String, String> systemParams = erpConnectInfoManager.getSystemParams(tenantId, params);
        return Result.newSuccess(JacksonUtil.toJson(systemParams));
    }
}
