{"type": "page", "title": "获取Sap CPQ的集成流信息", "toolbar": [], "body": [{"type": "crud", "initFetch": false, "headerToolbar": ["statistics"], "api": {"method": "get", "url": "../getAllSapCpqPloyDetails?tenantIds=${tenantIds}&checkStd=${checkStd}&checkSap=${checkSap}&erp2Crm=${erp2Crm}&crm2Erp=${crm2Erp}&status=${status}"}, "filter": {"type": "group", "columnCount": 2, "mode": "horizontal", "body": [{"label": "tenantIds", "type": "input-text", "placeholder": "企业id以 , 分隔", "name": "tenantIds"}, {"label": "集成流状态", "type": "select", "name": "status", "value": -1, "options": [{"label": "全部", "value": -1}, {"label": "启用", "value": 1}, {"label": "停用", "value": 2}]}, {"option": "通用连接器", "type": "checkbox", "name": "checkStd", "value": true}, {"option": "Sap连接器", "type": "checkbox", "name": "checkSap", "value": false}, {"option": "erp -> crm", "type": "checkbox", "name": "erp2Crm", "value": true}, {"option": "crm -> erp", "type": "checkbox", "name": "crm2Erp", "value": true}]}, "defaultParams": {"perPage": 100}, "columns": [{"name": "tenantId", "label": "企业id", "sortable": true}, {"name": "id", "label": "集成流id"}, {"name": "sourceTenantType", "label": "企业类型 1.纷享 2.外部对接"}, {"name": "integrationStreamName", "label": "集成流名称"}, {"name": "sourceObjectApiName", "label": "源对象"}, {"name": "destObjectApiName", "label": "目标对象"}, {"name": "sourceDataCenterId", "label": "源数据中心"}, {"name": "destDataCenterId", "label": "目标数据中心"}, {"name": "status", "type": "status", "label": "是否启用", "sortable": "${status} == 1 ? true : false"}, {"name": "beforeFuncApiName", "label": "同步前函数", "onEvent": {"click": {"actions": [{"expression": "${beforeFuncApiName != null && beforeFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${beforeFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}, {"name": "duringFuncApiName", "label": "同步中函数", "onEvent": {"click": {"actions": [{"expression": "${duringFuncApiName != null && duringFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${duringFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}, {"name": "afterFuncApiName", "label": "同步后函数", "onEvent": {"click": {"actions": [{"expression": "${afterFuncApiName != null && afterFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${afterFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}], "loadDataOnce": true, "affixHeader": true, "columnToggled": "auto", "placeholder": "暂无数据", "combineNum": 0}]}