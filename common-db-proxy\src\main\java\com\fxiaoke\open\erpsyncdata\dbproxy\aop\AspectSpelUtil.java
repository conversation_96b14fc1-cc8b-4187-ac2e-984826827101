package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023/2/20 10:33:06
 */
public interface AspectSpelUtil {

    ExpressionParser parser = new SpelExpressionParser();

    DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    Map<String, Expression> expressionMap = new ConcurrentHashMap<>();

    static StandardEvaluationContext getStandardEvaluationContext(final ProceedingJoinPoint proceedingJoinPoint, final Object result, final MethodSignature signature) {
        final Method method = signature.getMethod();
        final String[] parameters = getParameters(method);
        final Object[] args = proceedingJoinPoint.getArgs();
        return getStandardEvaluationContext(parameters, args, result);
    }

    static String[] getParameters(final Method method) {
        return nameDiscoverer.getParameterNames(method);
    }

    static StandardEvaluationContext getStandardEvaluationContext(final String[] parameters, final Object[] args, final Object result) {
        final StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext();
        for (int i = 0; i < parameters.length; i++) {
            standardEvaluationContext.setVariable(parameters[i], args[i]);
        }
        if (Objects.nonNull(result)) {
            standardEvaluationContext.setVariable("result", result);
        }
        return standardEvaluationContext;
    }

    static <T, R, V> BiFunction<T, R, List<V>> consumer2Function(final BiConsumer<T, R> normalFunc) {
        return (tenantId, dataList) -> {
            normalFunc.accept(tenantId, dataList);
            return Collections.emptyList();
        };
    }

    static <T> T getSpelValue(final String expressionString, final StandardEvaluationContext context, final Class<T> tClass) {
        if (StringUtils.isBlank(expressionString)) {
            return null;
        }
        final Expression exp = getExpression(expressionString);
        return exp.getValue(context, tClass);
    }

    static Expression getExpression(String expressionString) {
        return expressionMap.computeIfAbsent(expressionString, parser::parseExpression);
    }

    static <T> T getSpelValue(final String expressionString, final StandardEvaluationContext context) {
        if (StringUtils.isBlank(expressionString)) {
            return null;
        }
        final Expression exp = getExpression(expressionString);
        return (T) exp.getValue(context);
    }

    static void setValue(final String expressionString, final Object value, final StandardEvaluationContext context) {
        if (StringUtils.isBlank(expressionString)) {
            return;
        }
        final Expression exp = getExpression(expressionString);
        exp.setValue(context, value);
    }

    static Map<String, Object> getParameterMap(final ProceedingJoinPoint proceedingJoinPoint) {
        final MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        final String[] parameters = getParameters(signature.getMethod());
        final Object[] args = proceedingJoinPoint.getArgs();
        final Map<String, Object> parameterMap = new HashMap<>();
        for (int i = 0; i < parameters.length; i++) {
            parameterMap.put(parameters[i], args[i]);
        }
        return parameterMap;
    }

    static <T, V, K> T getByMap(Map<K, V> map, K key, Function<V, T> function) {
        final V v = map.get(key);
        return Objects.isNull(v) ? null : function.apply(v);
    }

    // static ErpConnectInfoEntity getErpConnectInfoEntity(final ProceedingJoinPoint proceedingJoinPoint, final MethodSignature signature, final String connectInfoName) {
    //     final Object[] args = proceedingJoinPoint.getArgs();
    //     if (StringUtils.isBlank(connectInfoName)) {
    //         return (ErpConnectInfoEntity) Arrays.stream(args).filter(arg -> arg instanceof ErpConnectInfoEntity).findFirst().orElseThrow(() -> new RuntimeException("没有找到ErpConnectInfoEntity, method:" + signature.getMethod().getName()));
    //     }
    //
    //     final String[] parameters = nameDiscoverer.getParameterNames(signature.getMethod());
    //     return IntStream.range(0, parameters.length)
    //             .filter(i -> Objects.equals(parameters[i], connectInfoName))
    //             .mapToObj(i -> (ErpConnectInfoEntity) args[i])
    //             .findFirst()
    //             .orElseThrow(() -> new RuntimeException("没有找到ErpConnectInfoEntity, method:" + signature.getMethod().getName() + " connectInfoName:" + connectInfoName));
    // }

    static <T> T getSpelValueByArg(final String expressionString, final Object arg) {
        if (StringUtils.isBlank(expressionString)) {
            return null;
        }
        if(arg==null){
            return null;
        }
        final StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext();
        standardEvaluationContext.setVariable("arg", arg);

        final Expression exp = getExpression("#arg"+expressionString);
        return (T) exp.getValue(standardEvaluationContext);
    }
}
