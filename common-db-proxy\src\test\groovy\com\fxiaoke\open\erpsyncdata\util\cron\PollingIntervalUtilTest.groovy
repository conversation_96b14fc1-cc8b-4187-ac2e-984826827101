package com.fxiaoke.open.erpsyncdata.util.cron

import cn.hutool.core.date.DateUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.CronException
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.PollingIntervalUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.CronPattern
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IntervalTimeUnitEnum
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import com.google.common.base.Joiner
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2022-11-8
 */
class PollingIntervalUtilTest extends Specification {

    def "test get cron"(IntervalTimeUnitEnum unit, Integer quantity, String cron) {
        expect:
        cron == getCron(unit, quantity)
        CronPattern.of(cron)
        where:
        unit                         | quantity | cron
        //最小6分钟
        IntervalTimeUnitEnum.minutes | 0        | "0/6 * * * *"
        IntervalTimeUnitEnum.minutes | 3        | "0/6 * * * *"
        IntervalTimeUnitEnum.minutes | 6        | "0/6 * * * *"
        IntervalTimeUnitEnum.minutes | 59       | "0/59 * * * *"
        IntervalTimeUnitEnum.minutes | 60       | "0 0/1 * * *"
        IntervalTimeUnitEnum.minutes | 61       | "0 0/1 * * *"


        IntervalTimeUnitEnum.hours   | 2        | "0 0/2 * * *"
    }

    def "test Exception"() {
        String cron
        //间隔为0的，cron异常
        when:
        cron = getCron(IntervalTimeUnitEnum.hours, 0)
        then:
        thrown(CronException)

        //超过1天执行，报ErpSyncDataException，后面会处理调整配置
        when:
        cron = getCron(IntervalTimeUnitEnum.hours, 24)
        then:
        thrown(ErpSyncDataException)

        when:
        cron = getCron(IntervalTimeUnitEnum.days, 0)
        then:
        thrown(ErpSyncDataException)

        ///间隔为0的，cron异常
        when:
        cron = getCron(IntervalTimeUnitEnum.days, 31)
        then:
        1 == 1
        thrown(ErpSyncDataException)

        when:
        cron = getCron(IntervalTimeUnitEnum.hours, 2)
        then:
        cron == "0 0/2 * * *"
    }

    def getCron(IntervalTimeUnitEnum unit, Integer quantity) {
        PollingIntervalApiDto intervalApiDto = new PollingIntervalApiDto()
        intervalApiDto.setTimeUnit(unit)
        intervalApiDto.setIntervalQuantity(quantity)
        return PollingIntervalUtil.getCronFromApiDto(null, intervalApiDto, 0)
    }

    def "test Cron pattern"() {
        def cron = CronPattern.of("0 1-5/1 * * *")
        def next = DateUtil.calendar()
        println(next.format("yyyy-MM-dd HH:mm:ss"))
        for (i in 0..<10) {
            next = cron.nextMatchAfter(next)
            println(next.format("yyyy-MM-dd HH:mm:ss"))
            next.add(Calendar.MINUTE,1)
        }
        expect:
        cron != null
    }

    def "test"() {
        expect:
        println Joiner.on("#").skipNulls().join(Lists.newArrayList("asdas",null))
    }
}
