package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
@ManagedTenantReplace
public interface ErpConnectInfoDao extends BaseTenantDao<ErpConnectInfoEntity, ErpConnectInfoDao> {
    //不能改变connectinfo的顺序，EncryInterceptor依赖params的顺序com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.EncryptInterceptor.intercept
    @Override
    int updateById(@Param("updated") ErpConnectInfoEntity record);

    /**
     * 获取所有企业tenantId字段
     * @return
     */
    List<String> listTenantId();

    /**
     * 获取企业指定数据中心连接信息
     * 非必要，禁止使用该方法，尤其是可能因数据量大频繁调用的
     *
     * @param tenantId
     * @param id
     * @return
     */
    ErpConnectInfoEntity getByIdAndTenantId(@Param("tenantId")String tenantId,
                                            @Param("id")String id);

    /**
     * 获取企业所有数据中心连接信息
     * 按id排序
     *
     * @param tenantId
     * @return
     */
    List<ErpConnectInfoEntity> listByTenantId(@Param("tenantId")String tenantId);

    List<ErpConnectInfoEntity> listByTenantIdAndIdsExcludeCrm(@Param("tenantId")String tenantId, @Param("ids") Collection<String> ids);

    /**
     * 获取企业所有数据中心连接信息
     * 按id排序
     *
     * @param tenantId
     * @param ids
     * @return
     */
    List<ErpConnectInfoEntity> listByIds(@Param("tenantId")String tenantId, @Param("ids") Collection<String> ids);

    List<Integer> existNum(@Param("tenantId")String tenantId,@Param("connectorId")Integer connectorId);

    /**
     * 获取企业所有非CRM数据中心连接信息
     * 按id排序
     *
     * @param tenantId
     * @return
     */
    List<ErpConnectInfoEntity> listErpDcByTenantId(@Param("tenantId")String tenantId);

    /**
     * 获取企业所有非CRM数据中心id
     * 按id排序
     */
    List<String> listErpDcIdByTenantId(@Param("tenantId")String tenantId);

    List<ErpConnectInfoEntity> listErpDcByTenantIdWithDelete(@Param("tenantId") String tenantId);

    /**
     * 获取企业所有非CRM数据中心连接信息
     * 按id排序
     *
     * @param tenantId
     * @return
     */
    List<ErpConnectInfoEntity> listErpDcExcludeOAByTenantId(@Param("tenantId")String tenantId);

    /**
     * 获取最新的
     * @param tenantId
     * @return
     */
    ErpConnectInfoEntity getCurrentConnectInfo(@Param("tenantId")String tenantId);

    /**
     * 获取CRM渠道的数据中心ID
     */
    ErpConnectInfoEntity getCRMConnectInfo(@Param("tenantId")String tenantId,@Param("channel") String channel);

    /**
     * 根据dataCenterName查询
     */
    ErpConnectInfoEntity queryInfoByName(@Param("tenantId")String tenantId, @Param("name") String name);

    /**
     * 根据dataCenterName查询
     */
    List<ErpConnectInfoEntity> queryInfoByNames(@Param("tenantId") String tenantId, @Param("names") List<String> dcNames);

    ErpConnectInfoEntity getByNumber(@Param("tenantId")String tenantId,
                                     @Param("channel")ErpChannelEnum channel,
                                     @Param("number")Integer number);


    ErpConnectInfoEntity getOneDcByTenantId(@Param("tenantId") String tenantId,
                                            @Param("channel")ErpChannelEnum channel);
    List<ErpConnectInfoEntity> getListDcByTenantId(@Param("tenantId") String tenantId,
                                            @Param("channel")ErpChannelEnum channel);

    ErpConnectInfoEntity getFirstNumErpDcByTenantId(@Param("tenantId") String tenantId);

    int deleteByTenantId(@Param("tenantId") String tenantId);

    List<ErpConnectInfoEntity> list100ByIdAfter(@Param("minId")String minId);

    int logicDelete(@Param("tenantId") String tenantId, @Param("id") String id);
    int deleteByTenantIdAndId(@Param("tenantId") String tenantId, @Param("id") String id);

    int updateNumber(@Param("updatedNumber")Integer updatedNumber, @Param("tenantId")String tenantId, @Param("id")String id);

    List<ErpConnectInfoEntity> listConnectInfoByEis( @Param("eis")List<String> eis);
}