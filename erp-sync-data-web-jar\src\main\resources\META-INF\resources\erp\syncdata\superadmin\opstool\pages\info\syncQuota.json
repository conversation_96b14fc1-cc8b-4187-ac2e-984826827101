{"type": "page", "title": "配额", "remark": null, "name": "quotaList", "toolbar": [], "body": [{"type": "crud", "name": "quotaList", "api": "../listCheckQuotaResult", "loadDataOnce": true, "defaultParams": {"perPage": 100}, "primaryField": "tenantId", "headerToolbar": ["export-csv", "reload", {"label": "发起检查配额", "type": "button", "actionType": "dialog", "level": "primary", "className": "m-b-sm", "dialog": {"title": "新增表单", "body": {"type": "form", "api": "post:../checkQuotaTask", "body": [{"label": "tenantIds", "desc": "逗号分隔", "type": "textarea", "name": "tenantIds"}, {"name": "cleanLog", "type": "checkbox", "option": "清理日志"}, {"name": "alert", "type": "checkbox", "option": "发送告警"}, {"name": "vacuum", "type": "checkbox", "option": "执行vacuum"}]}}}], "columns": [{"name": "tenantId", "label": "tenantId", "searchable": true}, {"name": "checkTime", "label": "统计时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"name": "streamQuota", "label": "集成流配额"}, {"name": "streamUsed", "label": "已使用集成流配额"}, {"name": "streamEnable", "label": "已启用集成流数量"}, {"name": "mappingQuota", "label": "中间表配额"}, {"name": "mappingUsed", "label": "已使用中间表"}, {"name": "usedPercent", "label": "中间表使用百分比"}, {"name": "mappingCountVo", "type": "json", "label": "已使用中间表"}, {"name": "stopStreamTime", "label": "停用集成流时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"name": "validModuleCodes", "type": "json", "label": "有效模块code"}, {"name": "validConnectorKey", "type": "json", "label": "有效连接器key"}, {"name": "validPkgMap", "type": "json", "label": "有效资源包，数量"}, {"name": "success", "label": "success", "type": "status"}, {"name": "exceptionMsg", "label": "异常信息"}, {"name": "cleanLogMsg", "type": "json", "label": "清理日志结果"}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "combineNum": 0, "bodyClassName": "panel-default"}]}