package com.fxiaoke.open.erpsyncdata.dbproxy.elastic;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/5/8
 */
public enum InvokeFeature {
    /**
     * 计算结果内存大小,结果的对象需要实现{@link com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable}
     */
    calculateResultSize,
    /**
     * 发送到bizLog，不区分企业全部上报
     */
    sendBizLog,

    ;

    InvokeFeature() {
        mask = (1 << ordinal());
    }

    public final int mask;

    public final int getMask() {
        return mask;
    }


    public static int of(InvokeFeature[] features) {
        if (features == null) {
            return 0;
        }

        int value = 0;

        for (InvokeFeature feature: features) {
            value |= feature.mask;
        }

        return value;
    }

    public static boolean isEnabled(int features, InvokeFeature feature) {
        return (features & feature.mask) != 0;
    }

    public static boolean isEnabled(InvokeFeature[] features, InvokeFeature feature) {
        return isEnabled(of(features), feature);
    }
}
