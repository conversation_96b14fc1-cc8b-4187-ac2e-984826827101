package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.*;
import org.mongodb.morphia.utils.IndexType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13 11:45:07
 * 上游企业添加下游代管任务
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity(value = "manage_group_add_task", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenant_id"), @Field("group_id"), @Field(value = "create_time", type = IndexType.DESC)}, options = @IndexOptions(background = true)),
        @Index(fields = {@Field("create_time")}, options = @IndexOptions(background = true, expireAfterSeconds = 8 * 24 * 3600))
})
public class ManageGroupAddTaskEntity {
    public static final String STATUS_INIT = "start";
    public static final String STATUS_END = "end";

    @Id
    private String id;
    /**
     * 上游企业ID
     */
    @Property("tenant_id")
    private String tenantId;
    @Property("group_id")
    private String groupId;
    @Property("status")
    private String status;
    @Property("downstream_ids")
    private List<String> downstreamIds;
    @Property("create_time")
    private Long createTime;
    @Property("update_time")
    private Long updateTime;
}
