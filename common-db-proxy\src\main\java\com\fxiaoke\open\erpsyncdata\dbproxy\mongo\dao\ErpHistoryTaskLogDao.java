package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.arg.QueryTaskArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpHistoryTaskLog;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 历史任务的快照日志
 */
@Slf4j
@Repository
@DependsOn("erpSyncDataMongoStore")
public class ErpHistoryTaskLogDao {

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    private DatastoreExt store;
    private String DATABASE = "erp_sync_data";
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();
    private final static String ERP_HISTORY_TASK_LOG = "erp_history_task_log";
    private final static String id="_id";


    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<ErpHistoryTaskLog> coll = getColl();
        IndexOptions indexOptions = new IndexOptions().unique(true);
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
        //过期自动清理时间,30天
        Bson idxExpire = Indexes.descending(ErpHistoryTaskLog.Fields.createTime);
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(90L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending(ErpHistoryTaskLog.Fields.tenantId)
                , Indexes.ascending(ErpHistoryTaskLog.Fields.dataCenterId)
                , Indexes.ascending(ErpHistoryTaskLog.Fields.realObjApiName)
                , Indexes.descending(ErpHistoryTaskLog.Fields.updateTime));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("data_filters").background(true)));

        Bson idxTypeByDataName = Indexes.compoundIndex(Indexes.ascending(ErpHistoryTaskLog.Fields.tenantId)
                , Indexes.ascending(ErpHistoryTaskLog.Fields.dataCenterId)
                , Indexes.ascending(ErpHistoryTaskLog.Fields.taskId)
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxTypeByDataName, new IndexOptions().name("task_id_filters").background(true)));

        List<String> created = coll.createIndexes(toBeCreate);
    }


    private MongoCollection<ErpHistoryTaskLog> getColl() {
        MongoCollection<ErpHistoryTaskLog> coll = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(ERP_HISTORY_TASK_LOG, ErpHistoryTaskLog.class);
        if(!collectionCache.contains(ERP_HISTORY_TASK_LOG)){
            return coll;
        }
        createIndex();
        return coll;
    }

    public void batchInsert(String tenantId, List<ErpHistoryTaskLog> erpHistoryTaskLogs) {
        UpdateOneModel<ErpHistoryTaskLog> updateOneModel;
        List<WriteModel<ErpHistoryTaskLog>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (ErpHistoryTaskLog syncLog : erpHistoryTaskLogs) {
            if(syncLog.getId()==null){
                syncLog.setId(ObjectId.get());
            }
            updateOneModel = new UpdateOneModel<>(filterIdDoc(tenantId,syncLog), upsert(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<ErpHistoryTaskLog> collection = getColl();
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }


    public Bson filterIdDoc(String tenantId,ErpHistoryTaskLog erpHistoryTaskLog) {
        Document result = new Document();
        result.put(id, erpHistoryTaskLog.getId());
        result.put(ErpHistoryTaskLog.Fields.tenantId, tenantId);
        return result;
    }

    public List<ErpHistoryTaskLog> pageByFiltersByFilterInStatus(String tenantId,String dataCenterId,String taskId,Integer page ,Integer pageSize) {
        List<ErpHistoryTaskLog> result = new ArrayList<>();
        MongoCollection<ErpHistoryTaskLog> collection = getColl();
        QueryTaskArg queryTaskArg= QueryTaskArg.builder().taskId(taskId).tenantId(tenantId).dataCenterId(dataCenterId).build();
        if(ObjectUtils.isNotEmpty(page)){
            queryTaskArg.setPage(page);
        }
        if(ObjectUtils.isNotEmpty(pageSize)){
            queryTaskArg.setPageSize(pageSize);
        }
        Bson filter = buildFilter(queryTaskArg);
        int offset=(queryTaskArg.getPage()-1)*queryTaskArg.getPageSize();
        collection.find(filter)
                .sort(Sorts.descending("updateTime"))
                .skip(offset)
                .limit(queryTaskArg.getPageSize())
                .into(result);
        return result;
    }

    public Integer countTasklogById(String tenantId,String dataCenterId,String taskId ){
        QueryTaskArg queryTaskArg= QueryTaskArg.builder().taskId(taskId).tenantId(tenantId).dataCenterId(dataCenterId).build();
        MongoCollection<ErpHistoryTaskLog> collection = getColl();
        Bson filer =buildFilter(queryTaskArg);
        long countDocuments = collection.countDocuments(filer);
        return Integer.valueOf(String.valueOf(countDocuments));
    }

    public void  updateLastErpHistoryByTaskId(String tenantId,String dataCenterId,String taskId,ErpHistoryTaskLog erpHistoryTaskLog){
        ErpHistoryTaskLog firstByTaskId = findFirstByTaskId(tenantId, dataCenterId, taskId);
        if(ObjectUtils.isNotEmpty(firstByTaskId)){
            erpHistoryTaskLog.setId(firstByTaskId.getId());
            batchInsert(tenantId,Lists.newArrayList(erpHistoryTaskLog));

        }
    }

    public void  deleteByTaskId(String tenantId,String dataCenterId,String taskId){
        MongoCollection<ErpHistoryTaskLog> coll = getColl();
        if(StringUtils.isAnyEmpty(tenantId,dataCenterId,taskId)){
            return;
        }
        QueryTaskArg queryTaskArg= QueryTaskArg.builder().taskId(taskId).tenantId(tenantId).dataCenterId(dataCenterId).build();
        Bson filer =buildFilter(queryTaskArg);
        coll.deleteMany(filer);
    }

    public void  updateLastStatusByTaskId(String tenantId, String dataCenterId, String taskId, Integer newStatus){
        ErpHistoryTaskLog firstByTaskId = findFirstByTaskId(tenantId, dataCenterId, taskId);
        if(ObjectUtils.isNotEmpty(firstByTaskId)){
            ErpHistoryTaskLog updateErpTaskLog=new ErpHistoryTaskLog();
            updateErpTaskLog.setId(firstByTaskId.getId());
            updateErpTaskLog.setTaskStatus(newStatus);
            batchInsert(tenantId,Lists.newArrayList(updateErpTaskLog));

        }
    }

    public void  stopLastTaskByTaskId(String tenantId,String dataCenterId,String taskId,String remark, boolean needStop ){
        ErpHistoryTaskLog firstByTaskId = findFirstByTaskId(tenantId, dataCenterId, taskId);
        if(ObjectUtils.isNotEmpty(firstByTaskId)){
            firstByTaskId.setRemark(remark);
            firstByTaskId.setNeedStop(needStop);
            batchInsert(tenantId,Lists.newArrayList(firstByTaskId));

        }
    }

    public ErpHistoryTaskLog findFirstByTaskId(String tenantId,String dataCenterId,String taskId){
        MongoCollection<ErpHistoryTaskLog> collection = getColl();
        QueryTaskArg queryTaskArg= QueryTaskArg.builder().taskId(taskId).tenantId(tenantId).dataCenterId(dataCenterId).build();
        Bson filter = buildFilter(queryTaskArg);
        ErpHistoryTaskLog erpHistoryTaskLog = collection.find(filter).sort(Sorts.descending("createTime")).first();
        return erpHistoryTaskLog;
    }

    public List<ErpHistoryTaskLog> limitCountLog(String tenantId,String dataCenterId,List<String> taskIds){
        MongoCollection<ErpHistoryTaskLog> collection = getColl();
        QueryTaskArg queryTaskArg= QueryTaskArg.builder().taskIds(taskIds).tenantId(tenantId).dataCenterId(dataCenterId).build();
        Bson filter = buildFilter(queryTaskArg);
        List<ErpHistoryTaskLog> erpHistoryTaskLogs=Lists.newArrayList();
        erpHistoryTaskLogs = collection.find(filter).sort(Sorts.descending("updateTime")).limit(1000).into(erpHistoryTaskLogs);
        return erpHistoryTaskLogs;
    }

    private Bson buildFilter(QueryTaskArg queryTaskArg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(ErpHistoryTaskLog.Fields.tenantId, queryTaskArg.getTenantId()));
        if (queryTaskArg.getDataCenterId() != null ) {
            filters.add(Filters.eq(ErpHistoryTaskLog.Fields.dataCenterId, queryTaskArg.getDataCenterId()));
        }
        if (queryTaskArg.getTaskId() != null ) {
            filters.add(Filters.eq(ErpHistoryTaskLog.Fields.taskId, queryTaskArg.getTaskId()));
        }
        if (queryTaskArg.getTaskIds() != null ) {
            filters.add(Filters.in(ErpHistoryTaskLog.Fields.taskId, queryTaskArg.getTaskIds()));
        }
        Bson filter = Filters.and(filters);
        return filter;
    }

    public Bson upsert(ErpHistoryTaskLog erpHistoryTaskLog){
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        //更新
        if(erpHistoryTaskLog.getUpdateTime()!=null){
            updateDoc.append(ErpHistoryTaskLog.Fields.updateTime, erpHistoryTaskLog.getUpdateTime());
        }else{
            updateDoc.append(ErpHistoryTaskLog.Fields.updateTime, new Date());
        }

        //不为空,才更新
        if(erpHistoryTaskLog.getTaskId()!=null){
            updateDoc.append(ErpHistoryTaskLog.Fields.taskId, erpHistoryTaskLog.getTaskId());
        }
        if(erpHistoryTaskLog.getTaskNum()!=null){
            updateDoc.append(ErpHistoryTaskLog.Fields.taskNum, erpHistoryTaskLog.getTaskNum());
        }
        if(erpHistoryTaskLog.getTaskName()!=null){
            updateDoc.append(ErpHistoryTaskLog.Fields.taskName, erpHistoryTaskLog.getTaskName());
        }
        if (StringUtils.isNotBlank(erpHistoryTaskLog.getFilerString())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.filerString, erpHistoryTaskLog.getFilerString());
        }
        if ( StringUtils.isNotBlank(erpHistoryTaskLog.getDataIds())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.dataIds, erpHistoryTaskLog.getDataIds());
        }

        if (StringUtils.isNotBlank(erpHistoryTaskLog.getRemark())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.remark, erpHistoryTaskLog.getRemark());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getEndTime())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.endTime, erpHistoryTaskLog.getEndTime());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getExecuteTime())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.executeTime, erpHistoryTaskLog.getExecuteTime());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getLastQueryStartTime())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.lastQueryStartTime, erpHistoryTaskLog.getLastQueryStartTime());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getRelatedPloyDetailId())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.relatedPloyDetailId, erpHistoryTaskLog.getRelatedPloyDetailId());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getLimit())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.limit, erpHistoryTaskLog.getLimit());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getOffset())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.offset, erpHistoryTaskLog.getOffset());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getTotalCostTime())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.totalCostTime, erpHistoryTaskLog.getTotalCostTime());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getTotalDataSize())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.totalDataSize, erpHistoryTaskLog.getTotalDataSize());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getTaskStatus())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.taskStatus, erpHistoryTaskLog.getTaskStatus());
        }
        if (ObjectUtils.isNotEmpty(erpHistoryTaskLog.getNeedStop())) {
            updateDoc.append(ErpHistoryTaskLog.Fields.needStop, erpHistoryTaskLog.getNeedStop());
        }


        //插入
        setOnInsertDoc
                .append(ErpHistoryTaskLog.Fields.tenantId, erpHistoryTaskLog.getTenantId())
                .append(ErpHistoryTaskLog.Fields.dataCenterId, erpHistoryTaskLog.getDataCenterId())
                .append(id,erpHistoryTaskLog.getId())
                .append(ErpHistoryTaskLog.Fields.createTime,new Date());


        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);

        return doc;
    }

}
