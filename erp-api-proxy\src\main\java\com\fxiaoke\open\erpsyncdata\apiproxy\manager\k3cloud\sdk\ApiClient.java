package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.http.client.CookieStore;
import org.json.JSONObject;

@ToString

public class ApiClient {

    private String _serverUrl;
    private CookieStore _cookieStore;
    @Getter
    @Setter
    public String userToken;
    @Getter
    @Setter
    public String kdServiceSessionId;
    public JSONObject loginContext;

    public ApiClient(String serverUrl) {
        if (!serverUrl.endsWith("/")) {
            serverUrl += "/";
        }
        this._serverUrl = serverUrl;
    }

    public <T> ApiRequest<T> createRequest(String servicename, Object[] parameters, Class<T> returnType) {
        return new ApiServiceRequest(this._serverUrl, this._cookieStore, servicename, parameters, returnType);
    }

    public <T> T execute(String servicename, Object[] parameters, Class<T> returnType) throws Exception {
        ApiRequest request = createRequest(servicename, parameters, returnType);
        ApiHttpClient httpClient = new ApiHttpClient();
        request.setListener(httpClient);
        return (T) httpClient.Send(request, returnType);
    }

    public <T> ApiRequest<T> executeAsync(String servicename, Object[] parameters, Class<T> returnType, IAsyncActionCallBack<T> callback) throws Exception {
        ApiRequest request = createRequest(servicename, parameters, returnType);
        ApiHttpClient httpClient = new ApiHttpClient(callback);
        request.setListener(httpClient);
        httpClient.syncSend(request);
        return request;
    }

    /**
     * 登录
     *
     * @param dbId     数据中心id
     * @param userName 用户名
     * @param password 用户密码
     * @param lcid     语言id，中文为2052
     * @return
     */
    public Boolean login(String dbId, String userName, String password, int lcid) {
        try {
            Object[] loginInfo = {dbId, userName, password, lcid};
            ApiRequest request = createRequest("Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser",
                    loginInfo, String.class);
            ApiHttpClient httpClient = new ApiHttpClient();
            request.setListener(httpClient);
            String ret = (String) httpClient.Send(request, String.class);
            JSONObject jsonObject=new JSONObject(ret);
            if(jsonObject.get("Context")!=null&&!"null".equals(jsonObject.get("Context").toString())){
                this.loginContext=jsonObject.getJSONObject("Context");
            }
            int result = jsonObject.getInt("LoginResultType");
            if (result == 1) {
                this._cookieStore = request.getCookieStore();
                this.userToken = this.loginContext.getString("UserToken");
                return Boolean.valueOf(true);
            }
            return Boolean.valueOf(false);
        }catch (Throwable e){
            throw ErpSyncDataException.wrap(e);
        }
    }

}
