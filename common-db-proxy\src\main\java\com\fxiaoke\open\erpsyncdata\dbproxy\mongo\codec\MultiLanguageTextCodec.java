package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import com.fxiaoke.open.erpsyncdata.preprocess.model.MultiLanguageText;
import com.mongodb.MongoClient;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonReader;
import org.bson.BsonType;
import org.bson.BsonWriter;
import org.bson.Document;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
public class MultiLanguageTextCodec implements Codec<MultiLanguageText> {
    private final Codec<Document> documentCodec;
    private final Codec<String> stringCodec;

    public MultiLanguageTextCodec() {
        documentCodec = MongoClient.getDefaultCodecRegistry().get(Document.class);
        stringCodec = MongoClient.getDefaultCodecRegistry().get(String.class);
    }

    @Override
    public MultiLanguageText decode(BsonReader reader, DecoderContext decoderContext) {

        MultiLanguageText multiLanguageText = new MultiLanguageText();

        reader.readStartDocument();
        while (reader.readBsonType() != BsonType.END_OF_DOCUMENT) {
            String fieldName = reader.readName();
            multiLanguageText.set(fieldName, readValue(reader, decoderContext));
        }

        reader.readEndDocument();

        return multiLanguageText;
    }


    private String readValue(final BsonReader reader, final DecoderContext decoderContext) {
        BsonType bsonType = reader.getCurrentBsonType();
        if (bsonType == BsonType.STRING) {
            return stringCodec.decode(reader, decoderContext);
        } else {
            return null;
        }
    }

    @Override
    public void encode(BsonWriter writer, MultiLanguageText value, EncoderContext encoderContext) {
        Document document = new Document();
        if (value != null) {
            document.putAll(value.getAll());
        }
        documentCodec.encode(writer, document, encoderContext);
    }

    @Override
    public Class<MultiLanguageText> getEncoderClass() {
        return MultiLanguageText.class;
    }
}
