package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/9/10 16:39
 * 处理数据的拼接id
 * @desc
 */
@Component
public class ComplexDataConvertUtils {
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpFieldManager erpFieldManager;

    public StandardListData complexId(StandardListData standardListData, TimeFilterArg timeFilterArg,String dataCenterId){
        //支持sap拼接主键
        //根据dbid+erp_realapiname查询对应的拓展
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntityList = erpObjManager.listMasterRelationsByRealApiName(timeFilterArg.getTenantId(),dataCenterId,timeFilterArg.getObjAPIName());

        //理论上应该只有一条数据
        if(CollectionUtils.isNotEmpty(erpObjectRelationshipEntityList)) {
            String erpSplitObjectApiname = erpObjectRelationshipEntityList.get(0).getErpSplitObjectApiname();
            ErpObjectFieldEntity idField = erpFieldManager.findIdField(timeFilterArg.getTenantId(), erpSplitObjectApiname);
            //判断是不是配置了id拼接
            if (idField != null && StringUtils.isNotBlank(idField.getFieldExtendValue())
                    && idField.getFieldExtendValue().contains("{")) {
                JSONObject extend = JSONObject.parseObject(idField.getFieldExtendValue());
                String splicingFormula = extend.getString("splicing_formula");
                String[] splits=null;
                if (StringUtils.isNotBlank(splicingFormula)) {
                    String splicingStr = "\\+";
                    StringBuffer newValue = new StringBuffer();
                    splits= splicingFormula.split(splicingStr);
                }
                if(ObjectUtils.isEmpty(splits)){
                    return standardListData;
                }
                for (StandardData standardData : standardListData.getDataList()) {
                    String regex = "$";
                    StringBuffer newValue=new StringBuffer();
                    for (String split : splits) {
                        if (split != null && split.startsWith(regex) && split.endsWith(regex)) {
                            String splicingFieldKey = split.substring(1, split.length() - 1);
                            Object value = standardData.getMasterFieldVal().get(splicingFieldKey);
                            if (value != null) {
                                newValue.append(value);
                            }
                        } else {
                            newValue.append(split);
                        }
                    }
                    standardData.getMasterFieldVal().put(idField.getFieldApiName(), newValue.toString());
                }
            }
        }
        return standardListData;
    }
}
