---
description: 
globs: 
alwaysApply: false
---
# 单元测试规范

## 1. 覆盖率要求以及生产代码修改约束
- 不允许直接修改生产代码（src/main/java目录下的代码）
- 所有功能改动必须通过修改测试代码（src/test目录下的代码）来实现
- 如果发现生产代码有问题，应该先写测试用例复现问题，然后再修改生产代码
- 发现需要执行的核心用例代码比较复杂的时候，需要将测试用例拆分，避免测试用例比较复杂，注意还是不能修改生成的代码适配测试用例
- **核心包的单元测试必须在 erp-sync-data-all 模块中补充完整的 JUnit 测试用例**


### 1.1 核心包覆盖率目标
- 行覆盖率（Line Coverage）：≥ 80%
- 分支覆盖率（Branch Coverage）：≥ 70%
- 方法覆盖率（Method Coverage）：≥ 85%
- 类覆盖率（Class Coverage）：≥ 90%

### 1.2 重点覆盖包
```
com.fxiaoke.open.erpsyncdata.writer.manager.**
com.fxiaoke.open.erpsyncdata.main.service
com.fxiaoke.open.erpsyncdata.main.dispatcher.**
com.fxiaoke.open.erpsyncdata.admin.manager.**
com.fxiaoke.open.erpsyncdata.admin.service.impl.**
```

## 2. 测试规范

### 2.1 基本规范
1. 测试类命名：被测试类名 + Test
2. 测试方法命名：test + 方法名 + 测试场景
3. 每个测试方法只测试一个功能点
4. 遵循 AAA (Arrange-Act-Assert) 模式

### 2.2 测试框架选择
1. 主要测试框架：JUnit/Spock
2. Mock框架：Mockito
3. 断言框架：AssertJ/Spock内置断言

### 2.3 测试用例设计
1. 正常流程测试（Happy Path）
2. 边界条件测试
3. 异常流程测试
4. 参数校验测试

### 2.4 代码组织
```java
@Test
public void testMethodName_Scenario() {
    // Arrange - 准备测试数据和环境
    
    // Act - 执行被测试方法
    
    // Assert - 验证结果
}
```

## 3. 最佳实践

### 3.1 测试隔离
1. 每个测试用例必须独立
2. 使用@Before/@After进行测试环境准备和清理
3. 避免测试用例之间的依赖

### 3.2 测试数据管理
1. 使用Builder模式构建测试数据
2. 避免使用硬编码的测试数据
3. 使用测试工具类管理公共测试数据

### 3.3 Mock使用规范
1. 只Mock外部依赖
2. Mock返回值要符合实际业务场景
3. 验证Mock对象的调用次数和参数

### 3.4 数据库测试
1. 使用H2等内存数据库
2. 每个测试用例后清理测试数据
3. 使用事务回滚保证数据一致性

## 4. 代码审查要求

### 4.1 测试代码审查清单
1. 测试覆盖率是否达标
2. 测试用例是否完整
3. 是否包含边界条件测试
4. 是否包含异常流程测试
5. Mock使用是否合理
6. 测试代码是否简洁清晰

### 4.2 常见问题
1. 测试用例过于复杂
2. 测试数据准备不充分
3. 断言不够具体
4. 测试用例之间存在依赖
5. 没有测试异常流程

## 5. 持续集成

### 5.1 CI配置
1. 在每次提交时运行单元测试
2. 生成测试覆盖率报告
3. 如果测试失败或覆盖率不达标则阻止合并

### 5.2 覆盖率报告要求
1. 在CI pipeline中生成HTML格式的覆盖率报告
2. 保存历史覆盖率数据，用于趋势分析
3. 设置覆盖率阈值，低于阈值则构建失败

# 代码修改规则

## 1. 生产代码修改约束

### 1.1 核心原则
- 不允许直接修改生产代码（src/main/java目录下的代码）
- 所有功能改动必须通过修改测试代码（src/test目录下的代码）来实现
- 如果发现生产代码有问题，应该先写测试用例复现问题，然后再修改生产代码

### 1.2 重点保护的生产代码文件
以下文件的修改需要特别谨慎，必须经过完整的测试验证：
```
com.fxiaoke.open.erpsyncdata.apiproxy.aop.TruncationInterfaceMonitorAspect
com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3FilterStringBuilder
```

### 1.3 修改流程
1. 编写测试用例
   - 确保测试用例能够覆盖要修改的功能点
   - 测试用例应该包含正常流程和异常流程
   - 遵循单元测试规范中的覆盖率要求
   - 需要检查导入java package是不是真实存在，需要用到的常量，变量需要先在项目里面search,不能随意增加enum,constans

2. 运行测试
   - 确保新增的测试用例能够失败（体现出问题）
   - 记录失败的原因和相关日志

3. 修改测试代码
   - 根据实际的生产代码行为调整测试用例的预期结果
   - 不要为了通过测试而降低测试标准

4. 验证修改
   - 确保修改后的测试用例能够通过
   - 确保没有引入新的问题
   - 确保覆盖率达标

## 2. 特殊场景处理

### 2.1 JSON处理相关
- 在处理JSON数据时，需要保持原有的数据结构
- 字段截断时需要保留必要的字段（如id、tenant_id等）
- 确保JSON格式的正确性

### 2.2 数据截断相关
- 遵循现有的截断逻辑
- 保持原有的数据长度限制
- 确保截断后的数据仍然可用

## 3. 代码审查要求

### 3.1 审查清单
1. 确认是否修改了生产代码
2. 检查测试用例的完整性
3. 验证测试覆盖率
4. 确认修改是否符合业务逻辑

### 3.2 常见问题
1. 直接修改生产代码而不是测试代码
2. 测试用例覆盖不够完整
3. 为了通过测试而降低测试标准
4. 没有考虑到边界情况

## 4. 持续集成

### 4.1 CI配置
1. 在每次提交时运行单元测试
2. 生成测试覆盖率报告
3. 如果测试失败或覆盖率不达标则阻止合并

### 4.2 覆盖率要求
遵循单元测试规范中的覆盖率目标：
- 行覆盖率（Line Coverage）：≥ 80%
- 分支覆盖率（Branch Coverage）：≥ 70%
- 方法覆盖率（Method Coverage）：≥ 85%
- 类覆盖率（Class Coverage）：≥ 90%