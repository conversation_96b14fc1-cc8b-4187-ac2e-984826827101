{"type": "page", "title": "集成流", "remark": null, "name": "streamList_new", "initApi": "../config/initPageData?page=streamList", "toolbar": [], "body": [{"type": "crud", "name": "allStreams", "api": "../allStreamInfoQuery", "loadDataOnce": false, "autoFillHeight": true, "defaultParams": {"perPage": 50}, "primaryField": "tenantId", "headerToolbar": [{"type": "button", "label": "全量导出", "actionType": "ajax", "api": "get:../allStreamInfoExport", "feedback": {"title": "操作执行中，稍后可查看企信通知"}}, {"type": "button", "label": "收集最新数据", "actionType": "ajax", "api": "get:../allStreamInfoQuery?refresh=true", "reload": "allStreams.columns", "feedback": {"title": "操作执行中，稍后可刷新页面查看"}}, "reload", "bulkActions", {"type": "tpl", "tpl": "${reminder}"}], "filter": {"mode": "horizontal", "body": [{"label": "ea", "type": "input-text", "name": "enterpriseAccounts", "placeholder": "搜索企业ea，用英文逗号分隔多个"}, {"label": "crm对象", "type": "input-text", "name": "crmObjApiNames", "placeholder": "搜索crm对象名，用英文逗号分隔多个（请注意拼写正确）"}]}, "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据，请刷新缓存", "combineNum": 0}]}