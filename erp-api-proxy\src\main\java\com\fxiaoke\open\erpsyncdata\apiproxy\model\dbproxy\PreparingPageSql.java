package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;

/**
 * 预备的Sql
 * 在准备后之后调用toNamedSql转换为NamedSql
 *
 * <AUTHOR> (^_−)☆
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PreparingPageSql extends PreparingSql {
    private int limit = 100;
    private int offset = 0;
    private boolean noOffset = false;

    public PreparingPageSql(String sql) {
        super(sql);
    }
}
