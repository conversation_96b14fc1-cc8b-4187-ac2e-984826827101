package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpCustomInterfaceEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * ERP自定义接口dao
 *
 * <AUTHOR>
 * @date 2023.06.16
 */
@Repository
@ManagedTenantReplace
public interface ErpCustomInterfaceDao extends BaseTenantDao<ErpCustomInterfaceEntity, ErpCustomInterfaceDao> {

    ErpCustomInterfaceEntity findByTenantIdAndId(@Param("tenantId") String tenantId, @Param("id") String id);

    ErpCustomInterfaceEntity findData(@Param("tenantId") String tenantId,
                                          @Param("dataCenterId") String dataCenterId,
                                          @Param("objApiName") String objApiName,
                                          @Param("interfaceType") ErpObjInterfaceUrlEnum interfaceType);
}