package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Slf4j
@Service
public class IdGenerator {

    /**
     * id生成器，
     * @deprecated 推荐直接使用 {@link com.fxiaoke.api.IdGenerator#get()}
     * @return
     */
    @LogLevel(value = LogLevelEnum.TRACE)
    @Deprecated
    public String get() {
        String idStr;
        try {
            idStr = com.fxiaoke.api.IdGenerator.get();
        } catch (Exception e) {
            idStr = IdUtil.nanoId();
        }
        return idStr;
    }

    /**
     *
     * @param tenantId
     * @param srcId  分表字段值
     * @return
     */
    @LogLevel(value = LogLevelEnum.TRACE)
    public String get(String tenantId,String srcId) {
        //暂不使用
        return get();
    }

    private String getSplitId(String srcId) {
        int i = Math.abs(srcId.hashCode() % 100);
        String srcId2;
        if (i < 10) {
            srcId2 = "0" + i;
        } else {
            srcId2 = String.valueOf(i);
        }
        return "erpid" + srcId2 + get();
    }
}
