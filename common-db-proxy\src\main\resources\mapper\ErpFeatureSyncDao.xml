<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFeatureSyncDao">
  <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFeatureSyncEntity">
    <!--@mbg.generated-->
    <!--@Table erp_feature_sync-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="sync_ploy_detail_id" jdbcType="VARCHAR" property="syncPloyDetailId" />
    <result column="condition" jdbcType="VARCHAR" property="condition" />
    <result column="calculation" jdbcType="VARCHAR" property="calculation" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, sync_ploy_detail_id, "condition", calculation, create_time, update_time
  </sql>

<!--auto generated by MybatisCodeHelper on 2021-05-20-->
  <select id="queryAllByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_feature_sync
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </select>
<!--auto generated by MybatisCodeHelper on 2021-05-22-->
  <select id="queryDistinctTenantId" resultType="java.lang.String">
        select distinct(tenant_id)
        from erp_feature_sync
    </select>
</mapper>