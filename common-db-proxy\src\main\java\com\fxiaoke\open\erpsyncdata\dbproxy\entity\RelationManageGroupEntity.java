package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/3/04 11:57:00
 * <p>
 * 代管企业组
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "relation_manage_group")
public class RelationManageGroupEntity {
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 上游企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 分组名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 模板企业id
     */
    @Column(name = "template_id")
    private String templateId;

    /**
     * 模板企业数据中心id
     */
    @Column(name = "dc_id")
    private String dcId;

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "update_time")
    private Long updateTime;
}
