package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.standard.StdRestApiHandler;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 实际上为StdRestApiHandler,不应该有调用
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.STANDARD_CHANNEL)
public class StandardDataManager extends StdRestApiHandler {
}