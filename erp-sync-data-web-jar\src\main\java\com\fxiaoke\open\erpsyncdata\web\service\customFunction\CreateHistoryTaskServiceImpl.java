package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CreateErpHistoryDataTaskArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Service("createHistoryTask")
public class CreateHistoryTaskServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;

    @Override
    public Result<?> executeLogic(CustomFunctionCommonArg arg) {
        String tenantId = arg.getTenantId();
        String paramsStr = arg.getParams();
        if (paramsStr == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        ErpHistoryDataTaskResult erpHistoryDataTaskResult = JacksonUtil.fromJson(paramsStr, ErpHistoryDataTaskResult.class);
        if (erpHistoryDataTaskResult == null || erpHistoryDataTaskResult.getDataCenterId() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        erpHistoryDataTaskResult.setId(null);
        CreateErpHistoryDataTaskArg createErpHistoryDataTaskArg = new CreateErpHistoryDataTaskArg();
        createErpHistoryDataTaskArg.setTask(erpHistoryDataTaskResult);
        createErpHistoryDataTaskArg.setIsCheck(false);
        String dataCenterId = erpHistoryDataTaskResult.getDataCenterId();
        Result<String> erpHistoryDataTask = erpHistoryDataTaskService.createErpHistoryDataTask(tenantId, dataCenterId, -10001, createErpHistoryDataTaskArg, I18nUtil.getLocaleFromTrace());
        return erpHistoryDataTask;
    }
}
