package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.arg.GetConfigValueByKeyArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.result.GetConfigValueByKeyResult;
import com.fxiaoke.crmrestapi.service.SkuSpuService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFuncArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFunctionResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.admin.FunctionInfo;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.BaseResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.CrmConfig;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.EnterpriseRelationSupportResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.FindCustomFieldDescribeResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ModifyLog;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ProductCategory;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmConfigKeyConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionResult;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SfaApiManager {
    //SFA新的服务地址
    @ReloadableProperty("rest.sfa.object.url.prefix")
    private String restSfaUri;

    @ReloadableProperty("paas.function.url")
    private String apiBusUrl;

    @ReloadableProperty("rest.object.url.prefix")
    private String restObjectUri;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private SkuSpuService skuSpuService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * CRM系统管理员身份
     **/
    private final int USER_SYSTEM = -10000;

    /**
     * 创建产品分类接口,db创建
     *
     * @param tenantId
     * @param vo
     * @return
     */
    public ProductCategory.SaveResult createProductCategoryAdd2Db(String tenantId,
                                                                  ProductCategory.Vo vo) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        ObjectData objectData = vo.toObjData();
        String url = Joiner.on(StringUtils.EMPTY).join(restSfaUri, "/product_category/service/add_to_db");
        return proxyHttpClient.postUrl(url, objectData, headerMap, new TypeReference<ProductCategory.SaveResult>() {
        });
    }

    /**
     * 产品分类同步描述
     *
     * @param tenantId
     * @return
     */
    public BaseResult<Boolean> syncDbProductCategory2Describe(String tenantId) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        String url = Joiner.on(StringUtils.EMPTY).join(restSfaUri, "/product_category/service/sync_describe");
        return proxyHttpClient.postUrl(url, null, headerMap, new TypeReference<BaseResult<Boolean>>() {
        });
    }

    /**
     * 更新产品分类接口
     *
     * @param tenantId
     * @param vo
     * @return
     */
    public ProductCategory.UpdateResult updateProductCategoryPack(String tenantId,
                                                                  ProductCategory.Vo vo) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        ObjectData objectData = vo.toObjData();
        String url = Joiner.on(StringUtils.EMPTY).join(restSfaUri, "/product_category/service/update");
        return proxyHttpClient.postUrl(url, objectData, headerMap, new TypeReference<ProductCategory.UpdateResult>() {
        });
    }


    public ProductCategory.ListResult listProductCategory(String tenantId) {
        String url = restSfaUri + "/product_category/service/list";
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        return proxyHttpClient.postUrl(url, null, headerMap, new TypeReference<ProductCategory.ListResult>() {
        });
    }


    public ObjectData executeCustomFunction(HeaderObj headerObj, FunctionServiceExecuteArg arg, boolean serializeNull) {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(apiBusUrl, "/v1/function/currencyFunction");
        if (serializeNull) {
            return proxyHttpClient.postUrlSerialNull(url, arg, headerMap, new TypeReference<ObjectData>() {
            });
        } else {
            return proxyHttpClient.postUrl(url, arg, headerMap, new TypeReference<ObjectData>() {
            });
        }
    }

    public ObjectData queryFunctionRunDetail(String tenantId, String functionApiName, String traceId) {
        Map<String, String> headerMap = buildHeader(tenantId);
        String url = Joiner.on(StringUtils.EMPTY).join(ConfigCenter.APIBUS_GLOBAL, "/fs-paas-function-engine/log/user/execute/list");
        Map<String, Object> detailMap = Maps.newHashMap();
        detailMap.put("functionApiName", functionApiName);
        detailMap.put("traceId", traceId);
        detailMap.put("pageSize", 20);
        detailMap.put("nextPage", true);
        return proxyHttpClient.postUrl(url, detailMap, headerMap, new TypeReference<ObjectData>() {
        });
    }

    /**
     * 查找自定义函数，获取自定义函数的详情
     *
     * @param headerObj
     * @param arg
     * @return
     */
    public ObjectData findCustomFunction(HeaderObj headerObj, FunctionServiceFindArg arg) {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(apiBusUrl, "/API/v1/inner/object/function/service/find");
        String json = GsonUtil.toJson(arg);
        return proxyHttpClient.postUrlByJson(url, json, headerMap, new TypeReference<ObjectData>() {
        });
    }


    /**
     * 查找自定义函数，获取自定义函数的详情
     *
     * @param headerObj
     * @param arg
     * @return
     */
    public Result<FunctionServiceFindResult> findFunction(HeaderObj headerObj, FunctionServiceFindArg arg) {
        Map<String, String> headerMap = getHeader(headerObj);
        String url = Joiner.on(StringUtils.EMPTY).join(apiBusUrl, "/API/v1/inner/object/function/service/find");
        Request.Builder builder = new Request.Builder()
                .url(url)
                .headers(Headers.of(headerMap))
                .post(RequestBody.create(GsonUtil.toJson(arg), ProxyHttpClient.JSON_TYPE));
        Result<String> result = proxyHttpClient.requestWrapException(builder, null);
        return processFUnctionResult(result);
    }

    /**
     * 模糊匹配函数function,对齐页面
     * @param
     * @return
     */
    public Result<QueryFunctionResult> queryRegularFunction(HeaderObj headerObj, QueryFuncArg queryFuncArg) {
        Map<String, String> headerMap = getHeader(headerObj);
        String url = Joiner.on(StringUtils.EMPTY).join(apiBusUrl, "/v1/function/query");
        Request.Builder builder = new Request.Builder()
                .url(url)
                .headers(Headers.of(headerMap))
                .post(RequestBody.create(GsonUtil.toJson(queryFuncArg), ProxyHttpClient.JSON_TYPE));
        Result<String> result = proxyHttpClient.requestWrapException(builder, null);

        Result<QueryFunctionResult> functionResultResult = processListFUnctionResult(result, new TypeToken<QueryFunctionResult>() {
        }.getType());
        return functionResultResult;
    }

    private static Result<FunctionServiceFindResult> processFUnctionResult(Result<String> httpResult) {
        if (!httpResult.isSuccess()) {
            return Result.copy(httpResult);
        }
        try {
            //必须使用gson，因为FunctionServiceFindData用的Gson的注解
            FunctionResult<FunctionServiceFindResult> result = GsonUtil.fromJson(httpResult.getData(), new TypeToken<FunctionResult<FunctionServiceFindResult>>() {
            }.getType());
            if (result.isSuccess()) {
                return Result.newSuccess(result.getResult());
            } else {
                //包装异常
                log.info("processFUnctionResult return Error {}",result);
                return Result.newError(ResultCodeEnum.CALL_PAAS_FAILED,
                        ResultCodeEnum.CALL_PAAS_FAILED.getErrMsg() + ":" + result.getErrMessage());
            }
        } catch (Exception e) {
            //反序列化失败
            log.warn("Incorrect format of data returned by API {}",httpResult.getData());
            return Result.newError(ResultCodeEnum.INCORRECT_FORMAT_RETURNED);
        }
    }

    private static <T> Result<T> processListFUnctionResult(Result<String> httpResult, Type type) {
        if (!httpResult.isSuccess()) {
            return Result.copy(httpResult);
        }
        try {
            //必须使用gson，因为FunctionServiceFindData用的Gson的注解
          T result= GsonUtil.fromJson(httpResult.getData(), type);
          return Result.newSuccess(result);
        } catch (Exception e) {
            //反序列化失败
            log.warn("Incorrect format of data returned by API {}",httpResult.getData());
            return Result.newError(ResultCodeEnum.INCORRECT_FORMAT_RETURNED);
        }
    }


    /**
     * 创建函数
     *
     * @param headerObj
     * @param functionInfo
     * @return
     */
    public Result<FunctionServiceFindResult> createFunction(HeaderObj headerObj, FunctionInfo functionInfo) {
        Map<String, String> headerMap = getHeader(headerObj);
        String url = Joiner.on(StringUtils.EMPTY).join(apiBusUrl, "/v1/function/create");
        String bodyJson = GsonUtil.toJson(Collections.singletonMap("function",functionInfo));
        Request.Builder builder = new Request.Builder()
                .url(url)
                .headers(Headers.of(headerMap))
                .post(RequestBody.create(bodyJson, ProxyHttpClient.JSON_TYPE));
        Result<String> result = proxyHttpClient.requestWrapException(builder, null);
        return processFUnctionResult(result);
    }

    /**
     * 编辑函数
     *
     * @param headerObj
     * @param functionInfo
     * @return
     */
    public Result<FunctionServiceFindResult> editFunction(HeaderObj headerObj, FunctionInfo functionInfo) {
        Map<String, String> headerMap = getHeader(headerObj);
        String url = Joiner.on(StringUtils.EMPTY).join(apiBusUrl, "/v1/function/update");
        String bodyJson = GsonUtil.toJson(Collections.singletonMap("function",functionInfo));
        Request.Builder builder = new Request.Builder()
                .url(url)
                .headers(Headers.of(headerMap))
                .post(RequestBody.create(bodyJson, ProxyHttpClient.JSON_TYPE));
        Result<String> result = proxyHttpClient.requestWrapException(builder, null);
        return processFUnctionResult(result);
    }

    /**
     * 判断客户有没有购买企业互联
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @Cached(expire = 300, cacheType = CacheType.LOCAL)
    public boolean isEnterpriseRelationSupport(String tenantId, Integer userId) {
        Map<String, String> headerMap = buildHeader(tenantId, userId);

        String url = restSfaUri + "/enterpriseRelation/service/isSupport";
        EnterpriseRelationSupportResult result = proxyHttpClient.postUrl(url, new Object(), headerMap, new TypeReference<EnterpriseRelationSupportResult>() {
        });
        if (result == null || result.isSuccess() == false) return false;
        return result.getData().isSupport();
    }

    /**
     * 查找字段描述
     *
     * @param tenantId
     * @param userId
     * @return
     */
    public FindCustomFieldDescribeResult findCustomFieldDescribe(String tenantId, Integer userId, String describeAPIName, String fieldApiName) {
        Map<String, String> headerMap = buildHeader(tenantId, userId);

        Map<String, Object> body = new HashMap<>();
        body.put("describeAPIName", describeAPIName);
        body.put("fieldApiName", fieldApiName);
        body.put("includeDescribeExtra", true);

        String url = restSfaUri + "/describe/service/findCustomFieldDescribe";
        try {
            return proxyHttpClient.postUrl(url, body, headerMap, new TypeReference<FindCustomFieldDescribeResult>() {
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 字段是否有外部数据权限，也就是外部联系人权限
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, postCondition = "#result!=null")
    public Boolean haveOutDataPrivilege(String tenantId, Integer userId, String describeAPIName, String fieldApiName) {
        FindCustomFieldDescribeResult result = findCustomFieldDescribe(tenantId, userId, describeAPIName, fieldApiName);
        if (result == null || !result.isSuccess()) return null;
        String relation_outer_data_privilege = MapUtils.getString(result.getData().getField(), "relation_outer_data_privilege");
        return StringUtils.equalsIgnoreCase(relation_outer_data_privilege, "outer_owner");
    }

    /**
     * 查询修改记录
     * 返回结果是时间倒序的
     */
    public BaseResult<ModifyLog.Result> queryModifyLog(String tenantId, ModifyLog.Arg arg) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        String url = restObjectUri + "/modifyLog/service/getModifyLogList";
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(url, arg, headerMap, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        return JacksonUtil.fromJson(result.getBody(), new com.fasterxml.jackson.core.type.TypeReference<BaseResult<ModifyLog.Result>>() {
        });
    }


    /**
     * 查询crm配置
     */
    public BaseResult<CrmConfig.BatchResult> batchGetCrmConfig(String tenantId, CrmConfig.BatchArg arg) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        String url = restObjectUri + "/biz_config/service/get_config_values";
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(url, arg, headerMap, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        return JacksonUtil.fromJson(result.getBody(), new com.fasterxml.jackson.core.type.TypeReference<BaseResult<CrmConfig.BatchResult>>() {
        });
    }

    /**
     * 查询crm配置
     */
    public BaseResult<CrmConfig.Result> getCrmConfig(String tenantId, CrmConfig.Arg arg) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        String url = restObjectUri + "/biz_config/service/get_config_value_by_key";
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(url, arg, headerMap, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        return JacksonUtil.fromJson(result.getBody(), new com.fasterxml.jackson.core.type.TypeReference<BaseResult<CrmConfig.Result>>() {
        });
    }

    /**
     * 查询进销存配置
     */
    public BaseResult<CrmConfig.Result> getStockConfig(String tenantId, CrmConfig.Arg arg) {
        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        String url = restObjectUri + "/stock_config/service/get_config_value_by_key";
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(url, arg, headerMap, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        return JacksonUtil.fromJson(result.getBody(), new com.fasterxml.jackson.core.type.TypeReference<BaseResult<CrmConfig.Result>>() {
        });
    }

    /**
     * @param tenantId
     * @param updateConfiguration 是否更新配置。返回最新的结果
     * @return
     */
    public String querySpuOpenStatus(String tenantId, boolean updateConfiguration) {
        ErpTenantConfigurationEntity entity = tenantConfigurationManager.findOne("all", "0", "all", TenantConfigurationTypeEnum.SPU_OPEN_CLOSE_STATUS.name());
        if (ObjectUtils.isNotEmpty(entity)) {
            Map<String, String> openStatusMap = JSON.parseObject(entity.getConfiguration(), Map.class);
            if (ObjectUtils.isNotEmpty(openStatusMap.get(tenantId)) && !updateConfiguration) {//不更新，直接返回
                return openStatusMap.get(tenantId);
            } else {
                //为空时，一定会更新
                com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
                GetConfigValueByKeyArg arg = new GetConfigValueByKeyArg();
                arg.setKey(CrmConfigKeyConstants.SPU);
                GetConfigValueByKeyResult isOpenSpu = skuSpuService.getConfigValueByKey(headerObj, arg);
                if (isOpenSpu == null || isOpenSpu.getCode() != 0) {
                    log.error("query spuOpen status exception:{}", isOpenSpu);
                    throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s252.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s252.getI18nValue(), isOpenSpu.getMessage()),
                            Lists.newArrayList( isOpenSpu.getMessage())),
                            null,
                            null);
                }
                openStatusMap.put(tenantId, isOpenSpu.getValue());
                entity.setConfiguration(JSONObject.toJSONString(openStatusMap));
                tenantConfigurationManager.updateById(entity.getId(), entity);
                return isOpenSpu.getValue();
            }
        } else {
            com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
            GetConfigValueByKeyArg arg = new GetConfigValueByKeyArg();
            arg.setKey(CrmConfigKeyConstants.SPU);
            GetConfigValueByKeyResult isOpenSpu = skuSpuService.getConfigValueByKey(headerObj, arg);
            ErpTenantConfigurationEntity erpTenantConfigurationEntity = new ErpTenantConfigurationEntity();
            String configId = idGenerator.get();
            erpTenantConfigurationEntity.setId(configId);
            erpTenantConfigurationEntity.setChannel("all");
            erpTenantConfigurationEntity.setDataCenterId("0");
            erpTenantConfigurationEntity.setTenantId("all");
            erpTenantConfigurationEntity.setType(TenantConfigurationTypeEnum.SPU_OPEN_CLOSE_STATUS.name());
            Map<String, String> openStatusMap = Maps.newHashMap();
            openStatusMap.put(tenantId, isOpenSpu.getValue());
            erpTenantConfigurationEntity.setConfiguration(JSONObject.toJSONString(openStatusMap));
            tenantConfigurationManager.insert(tenantId, erpTenantConfigurationEntity);
            return isOpenSpu.getValue();
        }
    }


    private Map<String, String> buildHeader(String tenantId, int operatorId) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userInfo", operatorId + "");
        headerMap.put("client_info", "rest-api");
        headerMap.put(I18NStringManager.X_FS_LOCALE,i18NStringManager.getDefaultLang(tenantId));
        return headerMap;
    }

    private Map<String, String> getHeader(HeaderObj headerObj) {
        Map<String, String> headerMap = new HashMap<>();
        headerObj.forEach((key, value) -> {
            headerMap.put(key, value == null ? "" : value.toString());
        });
        return headerMap;
    }

    private Map<String,String> buildHeader(String tenantId){
        Map<String,String> headerMap=Maps.newHashMap();
        headerMap.put("X-fs-Enterprise-Id",tenantId);
        headerMap.put("X-fs-Employee-Id","-10000");
        headerMap.put("Content-Type","application/json");
        return headerMap;
    }
}
