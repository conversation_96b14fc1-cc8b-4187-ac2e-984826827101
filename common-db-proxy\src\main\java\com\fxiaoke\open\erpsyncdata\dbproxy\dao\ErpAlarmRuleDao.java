package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 告警规则dao
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Repository
@ManagedTenantReplace
public interface ErpAlarmRuleDao extends ErpBaseDao<ErpAlarmRuleEntity>, ITenant<ErpAlarmRuleDao> {
    List<ErpAlarmRuleEntity> findData(@Param("tenantId") String tenantId,
                                      @Param("dataCenterId") String dataCenterId,
                                      @Param("alarmRuleType") AlarmRuleType alarmRuleType,
                                      @Param("alarmType") AlarmType alarmType,
                                      @Param("limit") Integer limit,
                                      @Param("offset") Integer offset);

    int count(@Param("tenantId") String tenantId,
              @Param("dataCenterId") String dataCenterId,
              @Param("alarmRuleType") AlarmRuleType alarmRuleType,
              @Param("alarmType") AlarmType alarmType);

    int deleteByDataCenterId(@Param("tenantId") String tenantId,
                             @Param("dataCenterId") String dataCenterId);
}