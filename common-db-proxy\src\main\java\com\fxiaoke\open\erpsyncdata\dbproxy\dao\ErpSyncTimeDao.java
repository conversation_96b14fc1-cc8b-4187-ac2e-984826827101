package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.MybatisReplaceEnterpriseAspect;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
public interface ErpSyncTimeDao extends BaseTenantDao<ErpSyncTimeEntity, ErpSyncTimeDao> {
    /**
     * 获取有效同步对象扩展数据时间
     * 同步时间以及同步策略详情数据
     *
     * @param tenantId 企业ei
     * @return result
     *
     * 实现方式有变更的话需要同步修改 listSyncExtent
     */
    @LogLevel(LogLevelEnum.DEBUG)
    List<ErpSyncExtentDTO> listSyncExtentByTenantId(@Param("tenantId") String tenantId);

    ErpSyncTimeEntity getByUnique(@Param("tenantId")String tenantId, @Param("objectApiName")String objectApiName, @Param("operationType")Integer operationType);

    List<ErpSyncTimeEntity> listByTenantIdAndObjectApiName(@Param("tenantId")String tenantId,@Param("objectApiName")String objectApiName);

    List<ErpSyncTimeEntity> batchListByTenantIdAndObjectApiName(@Param("tenantId")String tenantId,@Param("objectApiNames")List<String> objectApiNames);


    int updateLastQueryMongoTimeById(@Param("updatedLastQueryMongoTime")Long updatedLastQueryMongoTime,@Param("id")String id);

	int deleteByTenantIdAndId(@Param("tenantId")String tenantId,@Param("id")String id);

    int deleteByTenantId(String tenantId);

    int deleteByTenantIdAndObjectApiName(@Param("tenantId")String tenantId,@Param("objectApiNames")List<String> objectApiNames);

    int updatePollingInterval(@Param("tenantId")String tenantId,
                              @Param("id")String id, @Param("updatedPollingInterval")String updatedPollingInterval);


    void updateType(@Param("tenantId") String tenantId, @Param("id")String id, @Param("type") Integer type);

    /**
     * 因为1+N集成管理需要查看模版企业集成流,添加参数
     * 专门用于1+N集成管理
     * @see MybatisReplaceEnterpriseAspect#replaceListSyncExtent
     */
    List<ErpSyncExtentDTO> listSyncExtent(@Param("tenantId") String tenantId, @Param("ployDetailTenantId") String ployDetailTenantId);

}