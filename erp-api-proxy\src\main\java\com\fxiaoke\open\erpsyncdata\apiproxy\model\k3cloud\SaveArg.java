package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.crmrestapi.common.data.TypeHashMap;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 该类为保存接口传输的data
 * 1.formid：业务对象表单Id，字符串类型（必录）
 * 2.data：JSON格式数据（详情参考JSON格式数据）（必录）
 *      2.1.NeedUpDateFields：需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
 *      2.2.NeedReturnFields：需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
 *      2.3.IsDeleteEntry：是否删除已存在的分录，布尔类型，默认true（非必录）
 *      2.4.SubSystemId：表单所在的子系统内码，字符串类型（非必录）
 *      2.5.IsVerifyBaseDataField：是否验证所有的基础资料有效性，布尔类，默认false（非必录）
 *      2.6.IsEntryBatchFill：是否批量填充分录，默认true（非必录）
 *      2.7.ValidateFlag：是否验证标志，布尔类型，默认true（非必录）
 *      2.8.NumberSearch：是否用编码搜索基础资料，布尔类型，默认true（非必录）
 *      2.9.InterationFlags：交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
 *      2.10.Model：表单数据包，JSON类型（必录）
 * <AUTHOR> (^_−)☆
 * @date 2020/11/10
 */
@Data
public class SaveArg  extends HashMap<String,Object> implements Serializable {
    private static final long serialVersionUID = 5474600421937087900L;

    /**
     * 之前代码的默认值。为了后面容易拓展saveArg.将saveArg支持成Map类型。支持各种版本的金蝶的相关参数。
     */
    public SaveArg(){
        //是否自动提交并审核，默认false
        this.put("IsAutoSubmitAndAudit",false);
        //是否批量填充分录，默认true（非必录）
        this.put("IsEntryBatchFill",true);
        //是否验证所有的基础资料有效性，默认false
        this.put("IsVerifyBaseDataField",false);
    }


    public void setNeedReturnFields(List<String> needReturnFields) {
        this.put("NeedReturnFields",needReturnFields);
    }

    public void setIsAutoSubmitAndAudit(Boolean autoSubmitAndAudit) {
        this.put("IsAutoSubmitAndAudit",autoSubmitAndAudit);
    }

    public void setSubSystemId(String subSystemId) {
        this.put("SubSystemId",subSystemId);
    }

    public void setIsEntryBatchFill(Boolean entryBatchFill) {
        this.put("IsEntryBatchFill",entryBatchFill);
    }

    public void setIsVerifyBaseDataField(Boolean verifyBaseDataField) {
        this.put("IsVerifyBaseDataField",verifyBaseDataField);
    }

    public void setModel(K3Model model) {
        this.put("Model",model);
    }



    public List<String> getNeedReturnFields() {
        return (List<String>) this.get("NeedReturnFields");
    }

    public Boolean getIsAutoSubmitAndAudit() {
        return (Boolean) this.get("IsAutoSubmitAndAudit");
    }

    public String getSubSystemId() {
        return (String) this.get("SubSystemId");
    }

    public Boolean getIsEntryBatchFill() {
        return (Boolean) this.get("IsEntryBatchFill");
    }

    public Boolean getIsVerifyBaseDataField() {
        return (Boolean) this.get("IsVerifyBaseDataField");
    }

    public K3Model getModel() {
        return (K3Model) this.get("Model");
    }

    //    /**
//     * 需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
//     * 可用于返回明细id
//     */
//    @SerializedName("NeedReturnFields")
//    @JsonProperty("NeedReturnFields")
//    private List<String> needReturnFields;
//    /**
//     * 是否自动提交并审核，默认false
//     */
//    @SerializedName("IsAutoSubmitAndAudit")
//    @JsonProperty("IsAutoSubmitAndAudit")
//    private Boolean isAutoSubmitAndAudit = false;
//
//    /**
//     * 表单所在的子系统内码，默认null
//     */
//    @SerializedName("SubSystemId")
//    @JsonProperty("SubSystemId")
//    private String subSystemId = null;
//
//    /**
//     * 表单所在的子系统内码，默认null
//     */
//    @SerializedName("IsEntryBatchFill")
//    @JsonProperty("IsEntryBatchFill")
//    private Boolean isEntryBatchFill = true;
//
//    /**
//     * 是否验证所有的基础资料有效性，默认false
//     */
//    @SerializedName("IsVerifyBaseDataField")
//    @JsonProperty("IsVerifyBaseDataField")
//    private Boolean isVerifyBaseDataField = false;
//
//    /**
//     * 表单数据包，JSON类型（必录）
//     */
//    @SerializedName("Model")
//    @JsonProperty("Model")
//    private K3Model model;
//


}
