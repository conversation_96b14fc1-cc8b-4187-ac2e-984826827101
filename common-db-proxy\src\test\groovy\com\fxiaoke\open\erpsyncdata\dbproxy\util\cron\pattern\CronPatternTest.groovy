package com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import org.apache.commons.lang3.time.FastDateFormat
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/11/9 20:24:05
 */
class CronPatternTest extends Specification {

    @Unroll
    def "测试cron表达式"() {
        when:
        def cronPattern = CronPattern.of(corn)
        def currentTime = FastDateFormat.getInstance("HH:mm").parse(time).getTime();
        def match = cronPattern.match(currentTime, false)


        then:
        result == match

        where:
        time    | corn          || result
        "16:55" | "1/6 * * * ?" || true
        "16:56" | "2/6 * * * ?" || true
        "16:57" | "3/6 * * * ?" || true
        "16:58" | "4/6 * * * ?" || true
        "16:59" | "5/6 * * * ?" || true

        "20:30" | "1/6 * * * ?" || false
        "20:00" | "2/6 * * * ?" || false
        "20:00" | "3/6 * * * ?" || false
    }

    @Unroll
    def "测试打散结果"() {
        when:
        def minute = ConfigCenterConfig.getDefaultBeginMinute(tenantId, objApiName)
        println minute

        then:
        result == minute

        where:
        tenantId | objApiName || result
        "88521"  | "test"     || 2
        "88521"  | "test_12"  || 4

        "84801"  | "test"     || 4
        "84801"  | "test_12"  || 2

        "88754"  | "test"     || 0
        "88754"  | "test_12"  || 1

        "88753"  | "testdfg"  || 1
        "88753"  | "testfgh"  || 2

//        多连接器后缀一致
        "88753"  | "test_12"  || 2
        "88753"  | "testA_12" || 2
        "88753"  | "testB_12" || 1
        "88753"  | "testC_12" || 0
    }

    @Unroll
    def "getCronBeginMinute"() {
        when:
        def configCenterConfig = new ConfigCenterConfig(tenantConfigurationManager: Mock(TenantConfigurationManager) {
            findOne(*_) >> {
                if (Objects.isNull(config)) {
                    return null
                }
                return new ErpTenantConfigurationEntity(configuration: config)
            }
        })
        def minute = configCenterConfig.getCronBeginMinute("123", "api")


        then:
        minute == result

        where:
        config                   || result
        null                     || 4
        ""                       || 4
        '{"1":1}'                || 4
        '{"1_api":2, "1":1}'     || 4

        '{"123_api":2, "123":1}' || 2

        '{"123":1}'              || 1
    }
}
