package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 10:59 2021/3/1
 * @Desc:
 */
@Data
public class MessageNotificationConfiguration implements Serializable {
    private static final long serialVersionUID = 1207000146158698012L;
    /**
     * 状态，1 开启  2 关闭
     */
    private Integer status;
    /**
     * 通知人，1 固定人  2 数据负责人
     */
    private Integer type;
    /**
     * 通知人列表
     */
    private List<Integer> users;

    /**
     * 纷享实施负责人
     */
    private Map<String,List<Integer>> fsAdmin;
}
