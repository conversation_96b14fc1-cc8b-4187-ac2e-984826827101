package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant.U8ObjIdFieldConfig;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant.U8UrlConfig;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ProxyRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MessageNotificationConfiguration;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import javax.xml.bind.ValidationException;
import java.util.*;

/**
 * U8 接口封装
 *
 * @date 2020/8/25
 */
@Slf4j
public abstract class U8DataManagerAbstracta {

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    NotificationService notificationService;

    @Autowired
    protected TenantConfigurationManager tenantConfigurationManager;

    @Autowired
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("dss.u8.middle.url")
    private String u8MiddleServerUrl;

    private static final String TOKEN_URL = "/system/token";

    private static final String TRADEID_URL = "/system/tradeid";

    private static final String U8_TOKEN_PRE = "u8_token:";

    private static long expireTime = 1 * 60 * 90;//90分钟

    protected String getBatchQueryUrl(String apiName) {
        return "/api/" + U8UrlConfig.getApiName(apiName, "query") + "/batch_get";
    }

    protected String getLoadUrl(String apiName) {
        return "/api/" + U8UrlConfig.getApiName(apiName, "load") + "/get";
    }

    protected String getCreateUrl(String apiName) {
        return "/api/" + U8UrlConfig.getApiName(apiName, "create") + "/add";
    }

    protected String getUpdateUrl(String apiName) {
        return "/api/" + U8UrlConfig.getApiName(apiName, "update") + "/edit";
    }

    protected String getVerifyUrl(String apiName) {
        return "/api/" + U8UrlConfig.getApiName(apiName, "verify") + "/verify";
    }

    protected String getLoginUrl() {
        return "/api/user/login_v2";
    }

    protected abstract StandardListData transferU8ToStandardListData(String objApiName, Map<String, Object> map);

    protected abstract StandardData transferU8ToStandardData(String objApiName, Map<String, Object> map);

    protected abstract Map transferStandardToU8(String objApiName, StandardData standardData);

    protected abstract void generatorId(StandardData standardData);

    /**
     * <p>获取U8Token信息</p>
     * redisDataSource.get().set(key, value, "NX", "EX", time);
     * 存储数据到缓存中，并制定过期时间以及当 Key 存在时是否覆盖
     * key   缓存键
     * value 缓存值
     * nxxx  该的值只能取 NX 或者 XX，
     * 如果取 NX，则只有当 key 不存在时才进行 set
     * 如果取 XX，则只有当 key 已经存在时才进行 set
     * expx  该的值只能取 EX 或者 PX，代表数据过期时间的单位，EX 代表秒，PX 代表毫秒
     * time  过期时间，单位是 expx 所代表的单位
     *
     * @dateTime 2020/9/2 11:42
     * <AUTHOR>
     * @version 1.0
     */
    public String getRequestParams(U8ConnectParam connectParam) throws ValidationException {
        String key = U8_TOKEN_PRE + connectParam.getAppKey() + ":" + connectParam.getDs_sequence();
        String requestParams = redisDataSource.get(this.getClass().getSimpleName()).get(key);
        if (requestParams == null) {

            StringBuilder requestBuilder = new StringBuilder();
            requestBuilder.append(connectParam.getBaseUrl()).append(TOKEN_URL)
                    .append("?from_account=").append(connectParam.getFromAccount()).append("&app_key=")
                    .append(connectParam.getAppKey()).append("&app_secret=").append(connectParam.getAppSecret());
            String requestUrl = requestBuilder.toString();

            HttpRspLimitLenUtil.ResponseBodyModel response = null;
            response = proxyHttpClient.getUrl(requestUrl, Collections.emptyMap(), (Long)null);
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            String resultCode = resultObj.getString("errcode");

            if ("0".equals(resultCode)) {
                String token = ((JSONObject) resultObj.get("token")).getString("id");
                log.info("refresh token:【{}】", token);
                StringBuilder requestParamsBuilder = new StringBuilder();
                requestParamsBuilder.append("?from_account=").append(connectParam.getFromAccount()).append("&to_account=")
                        .append(connectParam.getToAccount()).append("&app_key=").append(connectParam.getAppKey())
                        .append("&token=").append(token).append("&ds_sequence=").append(connectParam.getDs_sequence());
                requestParams = requestParamsBuilder.toString();


                String value = requestParams;
                redisDataSource.get(this.getClass().getSimpleName()).set(key, value, "NX", "EX", expireTime);
            } else {
                String resultMsg = resultObj.getString("errmsg");
                throw new ValidationException(resultMsg);
            }

        }
        return requestParams;
    }

    /**
     * <p>获取U8交易号信息</p>
     *
     * @dateTime 2020/9/2 11:42
     * <AUTHOR>
     * @version 1.0
     */
    private String getTradeid(U8ConnectParam connectParam) throws ValidationException {
        String requestParams = getRequestParams(connectParam);
        String[] tradeIdRequestParams = requestParams.split("[=&]");
        String tradeIdRequestParam = new StringBuilder().append(tradeIdRequestParams[0]).append("=").append(tradeIdRequestParams[1])
                .append("&").append(tradeIdRequestParams[4]).append("=").append(tradeIdRequestParams[5])
                .append("&").append(tradeIdRequestParams[6]).append("=").append(tradeIdRequestParams[7]).toString();
        String requestUrl = connectParam.getBaseUrl() + TRADEID_URL + tradeIdRequestParam;
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        response = proxyHttpClient.getUrl(requestUrl, Collections.emptyMap(), (Long)null);
        JSONObject resultObj = JSONObject.parseObject(response.getBody());
        String resultCode = resultObj.getString("errcode");

        if ("0".equals(resultCode)) {
            String tradeid = ((JSONObject) resultObj.get("trade")).getString("id");
            return tradeid;
        } else {
            String resultMsg = resultObj.getString("errmsg");
            throw new ValidationException(resultMsg);
        }
    }

    /**
     * 构建参数，发送请求，并保存日志，解析返回结果转换为标准格式
     *
     * @param connectParam 连接参数
     * @param servicePath  服务地址
     * @param params       参数
     * @param interfaceMonitorData    接口日志
     * @return 标准结果
     */
    protected Result get(U8ConnectParam connectParam,
                         String servicePath,
                         Map<String, String> params,
                         InterfaceMonitorData interfaceMonitorData) {
        String url = connectParam.getBaseUrl() + servicePath;
        String objApiName = interfaceMonitorData.getObjApiName();
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        Integer status = 1;
        String rspStr = "";
        try {
            String urlParams = getRequestParams(connectParam);
            url += urlParams;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (StringUtils.isNotEmpty(entry.getValue())){
                    url += "&" + entry.getKey() + "=" + entry.getValue();
                }
            }
            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "UTF-8");
            response = proxyHttpClient.getUrl(url, header, (Long)null);
            if (StringUtils.isEmpty(response.getBody())){
                ThreadUtil.sleep(5*1000);
                response = proxyHttpClient.getUrl(url, header, (Long)null);
                if (StringUtils.isEmpty(response.getBody())){
                    return Result.newSystemError(I18NStringEnum.s647);
                }
            }
            rspStr = response.getBody();
            return handleGetResponse(objApiName, url, response.getBody());
        } catch (Exception e) {
            status=2;
            rspStr = e + " " + rspStr;
            log.debug("get error,connectParam:{},url:{},headerMap:{},params:{},response:{}", connectParam, url, params,response, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            stopWatch.stop();
            BaseErpDataManager.saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(), interfaceMonitorData.getObjApiName(), interfaceMonitorData.getType(), new ProxyRequest(url, Collections.emptyMap(), params),
                    rspStr, status, callTime, System.currentTimeMillis(),
                    "", TraceUtil.get(), stopWatch.getTotalTimeMillis(), null);
        }
    }

    protected Result handleGetResponse(String objApiName, String url, String response) {
        JSONObject resultObj = JSONObject.parseObject(response);
        String resultCode = resultObj.getString("errcode");
        if ("0".equals(resultCode)) {
            Object object = resultObj.get(objApiName);
            List<Object> dataList = new ArrayList<>();
            if (object instanceof JSONArray) {
                JSONArray jsonArray = resultObj.getJSONArray(objApiName);
                for (Object o : jsonArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    StandardData standardData = transferU8ToStandardData(objApiName, jsonObject);
                    dataList.add(standardData);
                }
            } else {
                StandardData standardData = transferU8ToStandardData(objApiName, resultObj.getJSONObject(objApiName));
                return new Result(standardData);
            }
            return new Result(dataList);
        }
        if ("20002".equals(resultCode)) {
            StandardData standardData = new StandardData();
            standardData.setObjAPIName(objApiName);
            return new Result(standardData);
        } else {
            String resultMsg = resultObj.getString("errmsg");
            //请求失败
            log.warn("handleGetResponse failed,url:{},response:{}", url, response);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
        }
    }

    /**
     * 构建参数，发送请求，并保存日志，解析返回结果转换为标准格式
     *
     * @param connectParam 连接参数
     * @param params       参数
     * @param interfaceMonitorData    接口日志
     * @return 标准结果
     */
    private Result batchGet(U8ConnectParam connectParam,
                            Map<String, Object> params,
                            InterfaceMonitorData interfaceMonitorData) {
        String objApiName = interfaceMonitorData.getObjApiName();
        String tenantId = interfaceMonitorData.getTenantId();
        String requestUrl = u8MiddleServerUrl + "/yongyou/ds/query/data";
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        Integer status = 1;
        String rspStr = "";
        try {
            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "UTF-8");
            response = proxyHttpClient.postUrl(requestUrl, params, header, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
            if(response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}",tenantId);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            rspStr = response.getBody();
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            String resultCode = resultObj.getString("status");
            if ("0".equals(resultCode)) {
                Map<String, Object> datas = resultObj;
                datas.putAll((Map) datas.get("datas"));
                datas.remove("datas");
                StandardListData standardListData = transferU8ToStandardListData(objApiName, datas);
                return new Result(standardListData);
            } else {
                status=2;
                //请求失败
                String resultMsg = resultObj.getString("errmsg");
                log.warn("batchGet failed,url:{},param:{},header:{},response:{}", requestUrl, params, Collections.emptyMap(), response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
            }
        } catch (Exception e) {
            status=2;
            rspStr = e + " " + rspStr;
            log.debug("batchGet error,connectParam:{},url:{},headerMap:{},params:{}", connectParam, requestUrl, Collections.emptyMap(), params, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            stopWatch.stop();
            BaseErpDataManager.saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(),
                    interfaceMonitorData.getDcId(), interfaceMonitorData.getObjApiName(),
                    interfaceMonitorData.getType(), new ProxyRequest(requestUrl, Collections.emptyMap(), params),
                    rspStr, status, callTime, System.currentTimeMillis(),
                    "", TraceUtil.get(), stopWatch.getTotalTimeMillis(), interfaceMonitorData.getTimeFilterArg());
        }
    }


    /**
     * 构建参数，发送请求，并保存日志，解析返回结果转换为标准格式
     *
     * @param connectParam 连接参数
     * @param servicePath  服务地址
     * @param params       参数
     * @param interfaceMonitorData    接口日志
     * @return 标准结果
     */
    private Result post(U8ConnectParam connectParam,
                        String servicePath,
                        StandardData params,
                        InterfaceMonitorData interfaceMonitorData,
                        Long rspReadLimitLenByte) {
        String url = connectParam.getBaseUrl() + servicePath;
        String apiName = params.getObjAPIName();
        String tenantId = interfaceMonitorData.getTenantId();
        String dcId = interfaceMonitorData.getDcId();
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        Map map = transferStandardToU8(interfaceMonitorData.getObjApiName(), params);
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        Integer status = 1;
        String rspStr = "";
        try {
            String urlParams = getRequestParams(connectParam);
            url += urlParams;
            if (url.contains("/add")) {
                url += "&sync=1&tradeid=" + getTradeid(connectParam);
            }
            response = proxyHttpClient.postUrl(url, map, Collections.emptyMap(),rspReadLimitLenByte);
            if(response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}",tenantId);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            rspStr = response.getBody();
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            String resultCode = resultObj.getString("errcode");

            if ("0".equals(resultCode)) {
                //调用接口成功
                if (url.contains("/add")) {
                    String id = resultObj.getString("id");
                    return getObjDataIds(tenantId, dcId, apiName, params, id, connectParam);
                }

                ErpIdResult erpIdResult = new ErpIdResult();
                erpIdResult.setMasterDataId(params.getMasterFieldVal().getId());
                return new Result(erpIdResult);
            } else {
                status=2;
                //请求失败
                String resultMsg = resultObj.getString("errmsg");
                log.warn("post failed,url:{},param:{},header:{},response:{}", url, map, Collections.emptyMap(), response);
                if (resultMsg.contains(i18NStringManager.getByEi(I18NStringEnum.s3764, tenantId))){
                    Result<Object> objectResult = new Result<>(ResultCodeEnum.SOCKETTIMEOUT);
                    objectResult.setErrMsg(resultCode + "," + resultMsg);
                    return objectResult;
                }
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
            }
        } catch (Exception e) {
            status=2;
            rspStr = e + " " + rspStr;
            log.debug("post error,connectParam:{},url:{},headerMap:{},params:{},response:{},error:{}", connectParam, url, Collections.emptyMap(), map, response, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            stopWatch.stop();
            BaseErpDataManager.saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), dcId,
                    interfaceMonitorData.getObjApiName(), interfaceMonitorData.getType(),
                    new ProxyRequest(url, Collections.emptyMap(), map), rspStr, status, callTime, System.currentTimeMillis(),
                    "", TraceUtil.get(), stopWatch.getTotalTimeMillis(), null);
        }
    }

    protected Result getObjDataIds(String tenantId,String dcId, String apiName, StandardData standardData, String id, U8ConnectParam connectParam) {

        ErpIdResult erpIdResult = new ErpIdResult();

        Map<String, String> params = new HashMap<>();
        params.put("id",id);
        String masterCodes = U8ObjIdFieldConfig.getApiName(apiName);
        String masterId = null;
        String[] masterIdCodes = masterCodes.split(",");
        for (String idCode : masterIdCodes) {
            if (masterId == null) {
                masterId = id;
                params.put("id",id);
            } else {
                masterId = masterId + "#" + standardData.getMasterFieldVal().get(idCode);
                if (standardData.getMasterFieldVal().get(idCode)!=null){
                    params.put(idCode,standardData.getMasterFieldVal().get(idCode)+"");
                }
            }
        }

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                dcId,
                apiName,
                ErpObjInterfaceUrlEnum.queryMasterById.name());

        String queryPath = getLoadUrl(apiName);

        Result<StandardData> result = get(connectParam, queryPath, params, interfaceMonitorData);
        if (!result.isSuccess()){
            return result;
        }
        if(result.getData()==null||result.getData().getMasterFieldVal()==null) {
            throw new SyncDataException(com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum.THIRD_APPLICATION_ERROR.getErrCode(),
                    i18NStringManager.getByEi(I18NStringEnum.s3722, tenantId));
        }
        String mainId = result.getData().getMasterFieldVal().getString("masterId");
        erpIdResult.setMasterDataId(mainId);

        for (Map.Entry<String, List<ObjectData>> entry : result.getData().getDetailFieldVals().entrySet()) {
            List<String> detailId = new ArrayList<>();
            for (ObjectData objectData : entry.getValue()) {
                detailId.add(objectData.getString("detailId"));
            }
            erpIdResult.getDetailDataIds().put(entry.getKey(), detailId);
        }
        return new Result(erpIdResult);
    }


    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        String connectParam = connectInfo.getConnectParams();
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            type=ErpObjInterfaceUrlEnum.queryInvalid;
            return Result.newSuccess(new StandardListData());
        }
        U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(timeFilterArg.getTenantId(),
                connectInfo.getId(),
                timeFilterArg.getObjAPIName(),
                type.name());
        interfaceMonitorData.setTimeFilterArg(timeFilterArg);
        //发送请求
        Map requestMap = buildRequestParams(timeFilterArg);
        requestMap.put("dsSequence", U8ConnectParam.getDs_sequence());
        Result<StandardListData> result = batchGet(U8ConnectParam, requestMap, interfaceMonitorData);
        listErpObjDataByTimeAfter(result,timeFilterArg.getTenantId(),connectInfo.getId(),timeFilterArg.getSnapshotId());
        return result;

    }


    /**
     * 构建请求参数格式
     *
     * @param timeFilterArg
     * @return
     */
    private Map buildRequestParams(TimeFilterArg timeFilterArg) {
        Map<String, Object> requestMap = new HashMap<>();
        Map<String, Object> queryTimeFilter = new HashMap<>();
        requestMap.put("tenantId", timeFilterArg.getTenantId());
        requestMap.put("objName", timeFilterArg.getObjAPIName());
        requestMap.put("params", queryTimeFilter);
        if (timeFilterArg.getOffset() != null && timeFilterArg.getLimit() != null && timeFilterArg.getLimit() > 0) {
            requestMap.put("page_index", ((timeFilterArg.getOffset() + timeFilterArg.getLimit()) / timeFilterArg.getLimit()));
            requestMap.put("rows_per_page", timeFilterArg.getLimit());
        }
        if (timeFilterArg != null) {
            requestMap.put("startTime", timeFilterArg.getStartTime());
            requestMap.put("endTime", timeFilterArg.getEndTime());
        }
        ListUtils.emptyIfNull(timeFilterArg.getFilters()).stream()
                .flatMap(Collection::stream)
                .distinct()
                .filter(filter -> "EQ".equals(filter.getOperate()))
                .forEach(filter -> queryTimeFilter.put(filter.getFieldApiName(), filter.getFieldValue().get(0)));
        return requestMap;
    }

    /**
     * 新建接口
     */
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        String connectParam = connectInfo.getConnectParams();
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getMasterFieldVal().getApiName();
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        //创建时取出Id字段
        standardData.removeId();
        U8ConnectParam u8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);
        String savePath = getCreateUrl(objApiName);
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.create.name());

        //发送请求
        Result<ErpIdResult> result = post(u8ConnectParam, savePath, standardData, interfaceMonitorData, (Long)null);

        //需要审核的单据在新增成功之后直接审核，没有审核人时，不执行审核操作
        if (result.isSuccess() && (null != standardData.getMasterFieldVal().get("virtual_person_code"))) {
            //需要审核的单据apiName
            if (ConfigCenter.U8_VERIFY_APINAME_SET.contains(objApiName)) {
                StandardData standardData1 = new StandardData();
                standardData1.setObjAPIName(objApiName);
                ObjectData masterFieldVal = new ObjectData();
                masterFieldVal.put("masterId", result.getData().getMasterDataId());
                masterFieldVal.put("object_describe_api_name", standardData.getMasterFieldVal().getApiName());
                masterFieldVal.put("_id", standardData.getMasterFieldVal().getId());
                masterFieldVal.put("name", standardData.getMasterFieldVal().getName());
                String person_code = (String) standardData.getMasterFieldVal().get("virtual_person_code");
                masterFieldVal.put("person_code", person_code);
                masterFieldVal.put("user_id", standardData.getMasterFieldVal().get("virtual_person_code"));
                masterFieldVal.put("password", standardData.getMasterFieldVal().getString("virtual_password"));
                //付款单、应收单需要传入单据类型voucher_type
                String voucher_code = result.getData().getMasterDataId();
                if (result.getData().getMasterDataId() != null) {
                    if ("pay".equals(objApiName) || "accept".equals(objApiName)) {
                        //付款单、应收单的masterId为voucher_code#voucher_type
                        String[] ids = result.getData().getMasterDataId().split("#");
                        voucher_code = ids[0];
                        masterFieldVal.put("voucher_code", ids[0]);
                        masterFieldVal.put("voucher_type", ids[1]);
                    } else {
                        masterFieldVal.put("voucher_code", result.getData().getMasterDataId());
                    }
                }
                standardData1.setMasterFieldVal(masterFieldVal);

                try {
                    Result<String> verifyResult = verifyErpObjData(standardData1, connectInfo);
                    if (!verifyResult.isSuccess()) {
                        //审核失败企信通知
                        ErpTenantConfigurationEntity messageNotification = tenantConfigurationManager.findOne(tenantId, connectInfo.getId(), connectInfo.getChannel().name(), TenantConfigurationTypeEnum.messageNotification.name());
                        if (messageNotification != null && messageNotification.getConfiguration() != null) {
                            MessageNotificationConfiguration erpConfiguration = JsonUtil.fromJson(messageNotification.getConfiguration(), MessageNotificationConfiguration.class);
                            SendTextNoticeArg arg = new SendTextNoticeArg();
                            arg.setTenantId(tenantId);
                            arg.setDataCenterId(connectInfo.getId());
                            arg.setMsgTitle(i18NStringManager.getByEi(I18NStringEnum.s648,tenantId));
                            arg.setReceivers(erpConfiguration.getUsers());
                            arg.setMsg(objApiName + i18NStringManager.getByEi2(I18NStringEnum.s649.getI18nKey(),
                                    tenantId,
                                    String.format(I18NStringEnum.s649.getI18nValue(), voucher_code,person_code,verifyResult.getErrMsg()),
                                    Lists.newArrayList(voucher_code,person_code,verifyResult.getErrMsg())));
                            Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(arg, AlarmRuleType.GENERAL,
                                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                                    AlarmType.SYNC_EXCEPTION,
                                    AlarmLevel.IMPORTANT);
                            log.info("notificationService.sendErpSyncDataAppNotice result={}", voidResult);
                        }
                    }
                } catch (Exception e) {
                    log.info("verify info, objApiName:{},exception:", objApiName, e);
                }
            }
        }
        return result;
    }

    /**
     * 更新接口
     */
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        String objApiName = standardData.getMasterFieldVal().getApiName();
        //单据的修改直接返回成功
        if (ConfigCenter.U8_VERIFY_APINAME_SET.contains(objApiName)) {
            ErpIdResult erpIdResult = new ErpIdResult();
            erpIdResult.setMasterDataId(standardData.getMasterFieldVal().getId());
            Map<String, List<ObjectData>> detailFieldVals = standardData.getDetailFieldVals();
            if (detailFieldVals != null && !detailFieldVals.isEmpty()) {
                Map<String, List<String>> detailDataIds = new HashMap<>();
                for (Map.Entry entry : detailFieldVals.entrySet()) {
                    List<String> detailIds = new ArrayList<>();
                    for (ObjectData detailData : (List<ObjectData>) entry.getValue()) {
                        detailIds.add(detailData.getId());
                    }
                    detailDataIds.put((String) entry.getKey(), detailIds);
                }
                erpIdResult.setDetailDataIds(detailDataIds);
            }
            return new Result<>(erpIdResult);
        }
        String connectParam = connectInfo.getConnectParams();
        String tenantId = connectInfo.getTenantId();
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        //创建时取出Id字段
        if (standardData.getMasterFieldVal().get("masterId") != null) {
            String masterCodes = U8ObjIdFieldConfig.getApiName(objApiName);
            String[] masterIdCodes = masterCodes.split(",");
            String[] ids = standardData.getMasterFieldVal().getString("masterId").split("#");
            for (int i = 0; i < masterIdCodes.length; i++) {
                standardData.getMasterFieldVal().put(masterIdCodes[i], ids[i]);
            }
        }
        U8ConnectParam u8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);
        String savePath = getUpdateUrl(objApiName);
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.update.name());

        //发送请求
        Result<ErpIdResult> result = post(u8ConnectParam, savePath, standardData, interfaceMonitorData, (Long)null);

        return result;

    }

    /**
     * 获取接口
     */
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        String connectParam = connectInfo.getConnectParams();
        U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);
        String queryPath = getLoadUrl(erpIdArg.getObjAPIName());
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectInfo.getId(),
                erpIdArg.getObjAPIName(),
                ErpObjInterfaceUrlEnum.queryMasterById.name());

        //构建请求参数
        Map<String, String> params = new HashMap<>();
        String apiName = erpIdArg.getObjAPIName();
        String masterCodes = U8ObjIdFieldConfig.getApiName(apiName);

        String[] masterIdCodes = masterCodes.split(",");
        String[] ids = erpIdArg.getDataId().split("#");
        for (int i = 0; i < masterIdCodes.length; i++) {
            if (i == 0) {
                params.put("id", ids[0]);
            } else {
                params.put(masterIdCodes[i], ids[i]);
            }
        }

        //发送请求
        Result result = get(U8ConnectParam, queryPath, params, interfaceMonitorData);

        //处理返回数据
        StandardData returnData = null;
        if (result.getData() instanceof List) {
            List<StandardData> dataList = (List<StandardData>) result.getData();
            for (StandardData standardData : dataList) {
                Boolean rightData = false;
                for (int i = 0; i < ids.length; i++) {
                    if (ids[i].equals(standardData.getMasterFieldVal().getString(masterIdCodes[i]))) {
                        rightData = true;
                    } else {
                        rightData = false;
                    }
                }
                if (rightData) {
                    returnData = standardData;
                    break;
                }
            }
        } else {
            returnData = (StandardData) result.getData();
        }

        getErpObjDataAfter(returnData);
        return new Result<>(returnData);

    }

    /**
     * 审核接口
     */
    public Result<String> verifyErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {

        String connectParam = connectInfo.getConnectParams();
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getMasterFieldVal().getApiName();
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        String savePath = getVerifyUrl(objApiName);
        //U8链接参数
        U8ConnectParam u8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);

        //密码不为空时，先登录审核人账号再审核
        if (StringUtils.isNotEmpty(standardData.getMasterFieldVal().getString("password"))) {
            Result<String> loginResult = login(standardData, connectInfo);
            if (!loginResult.isSuccess()) {
                //登录失败时直接返回
                log.info("verify info, objApiName:{},exception:", objApiName, i18NStringManager.getByEi(I18NStringEnum.s3718, tenantId));
                return loginResult;
            }
        }

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                ErpObjInterfaceUrlEnum.update.name());

        //发送请求
        Result<String> result = post(u8ConnectParam, savePath, standardData, interfaceMonitorData, (Long)null);

        return result;
    }

    public Result<String> login(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        standardData.setObjAPIName("user");
        String connectParam = connectInfo.getConnectParams();
        String tenantId = connectInfo.getTenantId();
        String objApiName = "user";
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();
        String loginPath = getLoginUrl();

        //U8连接参数
        U8ConnectParam u8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);

        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId,
                connectInfo.getId(),
                objApiName,
                "login");

        //发送请求
        Result<String> result = post(u8ConnectParam, loginPath, standardData, interfaceMonitorData, (Long)null);

        return result;
    }

    protected void getErpObjDataAfter(StandardData data) {
        return;
    }

    protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                             String tenantId,
                                             String dataCenterId,
                                             String snapshotId) {
        return;
    }

}