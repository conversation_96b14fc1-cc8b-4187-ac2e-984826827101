package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/6
 */
@Slf4j
@Service
public class TestProcessor extends AbstractMonitorMqProcessor<String> {

    protected TestProcessor() {
        super(MonitorType.TEST);
    }

    @Override
    void process(String obj) {
        log.info("monitor test processor,{}", obj);
    }
}
