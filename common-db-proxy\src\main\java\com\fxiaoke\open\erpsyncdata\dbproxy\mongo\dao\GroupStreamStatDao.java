package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseLogMongoStore;
import com.fxiaoke.open.erpsyncdata.preprocess.model.GroupStreamStat;
import com.fxiaoke.open.erpsyncdata.preprocess.model.GroupStreamStat.Fields;
import com.fxiaoke.open.erpsyncdata.preprocess.model.OrderBy;
import com.fxiaoke.open.erpsyncdata.preprocess.model.UpGroupStreamStat;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */
@Slf4j
@Repository
public class GroupStreamStatDao extends BaseLogMongoStore<GroupStreamStat> {


    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(GroupStreamStat.class)
                        .automatic(true).build()));
    }

    protected GroupStreamStatDao() {
        super(SingleCodecHolder.codecRegistry);
    }

    @Override
    public String getCollName(String tenantId) {
        return "group_stream_stat";
    }

    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> indexModels = new ArrayList<>();
        //过期时间
        indexModels.add(new IndexModel(Indexes.ascending(StreamStat.Fields.createTime),
                new IndexOptions().expireAfter(10L, TimeUnit.DAYS).background(true)));
        return indexModels;
    }

    public void insert(String tenantId, GroupStreamStat groupStreamStat) {
        MongoCollection<GroupStreamStat> collection = getOrCreateCollection(tenantId);
        collection.insertOne(groupStreamStat);
    }

    public GroupStreamStat getLatestTraceIdAndTime(String tenantId, String dcId, Date date7DaysAge) {
        Bson filters = Filters.and(
                Filters.eq(Fields.upTenantId, tenantId),
                Filters.eq(Fields.dcId, dcId),
                Filters.gt(Fields.createTime, date7DaysAge)
        );
        Bson sorts = Sorts.descending(StreamStat.Fields.createTime);
        GroupStreamStat first = getOrCreateCollection(tenantId).find(filters).limit(1).sort(sorts).first();
        return first;
    }

    public Page<GroupStreamStat> calDownstreamStat(String tenantId, String dcId, String traceId, Date date7DaysAge, List<OrderBy> orderBys, Integer limit, Integer offset, @Nullable Integer alertStatus, @Nullable Integer syncDataStatus, Set<String> filterTenantIds) {
        //这里排序没有索引，判断不会有很多数据，就直接算吧。
        List<Bson> filtersList = Lists.newArrayList(
                Filters.eq(Fields.upTenantId, tenantId),
                Filters.eq(Fields.dcId, dcId),
                Filters.eq(Fields.traceId, traceId),
                Filters.gt(Fields.createTime, date7DaysAge)
        );
        if (Objects.equals(alertStatus, 1)) {
            //仅正常数据
            filtersList.add(filtersNullOrZero(Fields.alertingStreamCount));
        } else if (Objects.equals(alertStatus, 2)) {
            //仅告警数据
            filtersList.add(Filters.gt(Fields.alertingStreamCount, 0));
        }
        if (Objects.equals(syncDataStatus, 1)) {
            //仅正常数据
            filtersList.add(filtersNullOrZero(Fields.mappingFailedStreamCount));
        } else if (Objects.equals(syncDataStatus, 2)) {
            //仅异常数据
            filtersList.add(Filters.gt(Fields.mappingFailedStreamCount, 0));
        }
        if (CollUtil.isNotEmpty(filterTenantIds)) {
            filtersList.add(Filters.in(Fields.downstreamId, filterTenantIds));
        }
        Bson filters = Filters.and(filtersList);
        List<Bson> sorts = orderBys.stream().map(v -> v.isAsc() ? Sorts.ascending(v.getField()) : Sorts.descending(v.getField())).collect(Collectors.toList());
        //增加时间排序
        sorts.add(Sorts.descending(StreamStat.Fields.createTime));
        Bson sort = Sorts.orderBy(sorts);
        FindIterable<GroupStreamStat> findIterable = getOrCreateCollection(tenantId)
                .find(filters)
                .sort(sort)
                .skip(offset)
                .limit(limit);
        ArrayList<GroupStreamStat> dataList = Lists.newArrayList(findIterable);
        Page<GroupStreamStat> pageResult = new Page<>();
        pageResult.setData(dataList);
        //查询总数
        long count = getOrCreateCollection(tenantId).countDocuments(filters);
        pageResult.setTotalNum(count);
        return pageResult;
    }

    @NotNull
    private static Bson filtersNullOrZero(String field) {
        return Filters.or(Filters.not(Filters.exists(field)), Filters.eq(field, null), Filters.eq(field, 0));
    }

    @NotNull
    public UpGroupStreamStat calStatGroupByDcLatest(String tenantId, String dcId) {
        Date date7DaysAge = DateUtil.date().offset(DateField.DAY_OF_YEAR, -7).toJdkDate();
        GroupStreamStat latestTraceIdAndTime = getLatestTraceIdAndTime(tenantId, dcId, date7DaysAge);
        if (latestTraceIdAndTime == null) {
            return new UpGroupStreamStat();
        }
        return calStatGroupByTrace(tenantId, dcId, latestTraceIdAndTime.getTraceId(), date7DaysAge);
    }


    public UpGroupStreamStat calStatGroupByTrace(String tenantId, String dcId, String traceId, Date date7DaysAge) {
        //这里排序没有索引，判断不会有很多数据，就直接算吧。
        Bson filters = Filters.and(
                Filters.eq(Fields.upTenantId, tenantId),
                Filters.eq(Fields.dcId, dcId),
                Filters.eq(Fields.traceId, traceId),
                Filters.gt(Fields.createTime, date7DaysAge)
        );
        List<GroupStreamStat> downStats = new ArrayList<>();
        getOrCreateCollection(tenantId)
                .find(filters)
                .limit(10000)
                .into(downStats);
        int downstreamCount = 0;
        int alertingDownstreamCount = 0;
        int failedDownstreamCount = 0;
        long lastStatTime = 0L;

        for (GroupStreamStat stat : downStats) {
            downstreamCount++;
            if (stat.getAlertingStreamCount() > 0) {
                alertingDownstreamCount++;
            }
            if (stat.getMappingFailedStreamCount() > 0) {
                failedDownstreamCount++;
            }
            if (stat.getCreateTime().getTime() > lastStatTime) {
                lastStatTime = stat.getCreateTime().getTime();
            }
        }

        UpGroupStreamStat upStat = UpGroupStreamStat.builder()
                .lastStatTime(lastStatTime)
                .downstreamCount(downstreamCount)
                .alertingDownstreamCount(alertingDownstreamCount)
                .failedDownstreamCount(failedDownstreamCount)
                .build();
        return upStat;
    }


    public int countDownstreamStat(String tenantId, String dcId, String traceId, Date date7DaysAge) {

        //这里排序没有索引，判断不会有很多数据，就直接算吧。
        Bson filters = Filters.and(
                Filters.eq(Fields.upTenantId, tenantId),
                Filters.eq(Fields.dcId, dcId),
                Filters.eq(Fields.traceId, traceId),
                Filters.gt(Fields.createTime, date7DaysAge)
        );
        long count = getOrCreateCollection(tenantId)
                .countDocuments(filters);
        return (int) count;
    }


    public int countAlertingDownstream(String tenantId, String dcId, String traceId, Date date7DaysAge) {
        //这里排序没有索引，判断不会有很多数据，就直接算吧。
        Bson filters = Filters.and(
                Filters.eq(Fields.upTenantId, tenantId),
                Filters.eq(Fields.dcId, dcId),
                Filters.eq(Fields.traceId, traceId),
                Filters.gt(Fields.createTime, date7DaysAge),
                Filters.gt(Fields.alertingStreamCount, 0)
        );
        long count = getOrCreateCollection(tenantId)
                .countDocuments(filters);
        return (int) count;
    }


    public int countFailedDownstream(String tenantId, String dcId, String traceId, Date date7DaysAge) {
        //这里排序没有索引，判断不会有很多数据，就直接算吧。
        Bson filters = Filters.and(
                Filters.eq(Fields.upTenantId, tenantId),
                Filters.eq(Fields.dcId, dcId),
                Filters.eq(Fields.traceId, traceId),
                Filters.gt(Fields.createTime, date7DaysAge),
                Filters.gt(Fields.mappingFailedStreamCount, 0)
        );
        long count = getOrCreateCollection(tenantId)
                .countDocuments(filters);
        return (int) count;
    }
}
