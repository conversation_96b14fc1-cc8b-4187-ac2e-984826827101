package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import lombok.Data;

import java.io.Serializable;

@Data
public class DeleteWithLogicBySourceData implements Serializable {
    private static final long serialVersionUID = -6000140863951516012L;
    private String sourceTenantId;
    private String sourceObjectApiName;
    private String sourceDataId;
    private String destTenantId;
    private String destObjectApiName;

    public DeleteWithLogicBySourceData(String sourceTenantId, String sourceObjectApiName, String sourceDataId, String destTenantId, String destObjectApiName) {
        this.sourceTenantId = sourceTenantId;
        this.sourceObjectApiName = sourceObjectApiName;
        this.sourceDataId = sourceDataId;
        this.destTenantId = destTenantId;
        this.destObjectApiName = destObjectApiName;
    }
}
