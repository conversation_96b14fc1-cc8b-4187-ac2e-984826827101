package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * erp对象字段信息
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("erpObjectApiName")
@Table(name = "erp_object_field")
public class ErpObjectFieldEntity {

    @Id
    private String id;

    /**
     * 企业id
     */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 渠道
     */
    private ErpChannelEnum channel;

    /**
    * 对象apiName
    */
    @Column(name = "erp_object_apiname")
    private String erpObjectApiName;

    /**
    * 字段apiName
    */
    @Column(name = "field_apiname")
    private String fieldApiName;

    /**
    * 字段Label
    */
    private String fieldLabel;

    /**
    * 是否必填
    */
    private Boolean required;

    /**
    * 字段类型
    */
    private ErpFieldTypeEnum fieldDefineType;

    /**
     * 字段扩展值
     */
    private String fieldExtendValue;

    /**
    * 修改时间
    */
    private Long createTime;

    /**
    * 字段排序
    */
    private Long updateTime;

    public Boolean getRequired() {
        return required != null && required;
    }
}