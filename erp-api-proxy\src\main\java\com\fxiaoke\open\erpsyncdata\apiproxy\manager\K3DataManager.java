package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.unit.DataSizeUtil;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CSaveAction;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3SystemParameterUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3DocumentStatusEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3SOChangeTypeEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.factory.SpecialBusinessFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.XOrderUtils;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.Operate;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.custom.ErpObjIdNumberMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AfterSystemProcessModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpIdNumberNameKeyMapping;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3CreateConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum.REAL_OBJECT;
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum.SPLIT_OBJECT;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/9
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_K3CLOUD)
public class K3DataManager extends BaseErpDataManager {
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private SpecialBusinessFactory specialBusinessFactory;
    @Autowired
    private CommonBusinessManager commonBusinessManager;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ErpObjIdNumberMappingManager erpObjIdNumberMappingManager;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    private static String ERP_FIELD_CONVERT_SUFFIX="%s__r";
    /**
     * 缓存分页轮询时上一页最后一条数据的信息
     */
    private ThreadLocal<Map<String,LastErpDataModel>> lastErpDataCache = ThreadLocal.withInitial(()-> new HashMap<>());

    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        log.debug("K3DataManager.getErpObjData,erpIdArg={}", erpIdArg);
        String masterApiName = erpIdArg.getObjAPIName();
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(erpIdArg.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(masterApiName);
        if (specialBusiness.needSpecialGetAndQuery(erpIdArg.getDataId())) {
            //单独接口获取单条数据
            return specialBusiness.specialGetErpObjData(erpIdArg, apiClient);
        }
        ErpIdNumberNameKeyMapping keyMapping = tenantConfigurationManager.getK3cObjIdNumberNameKeyMapping(erpIdArg.getTenantId(),masterApiName);
        log.debug("K3DataManager.getErpObjData,keyMapping={}", keyMapping);
        //如果开启了ERP对象编码更新
        if(keyMapping!=null) {
            List<ErpObjIdNumberMappingEntity> entityList = erpObjIdNumberMappingManager.queryByDataNumber(erpIdArg.getTenantId(),
                    connectInfo.getId(),
                    masterApiName,
                    erpIdArg.getDataId());
            log.debug("K3DataManager.getErpObjData,entityList={}", entityList);
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(entityList)) {
                String dataNumber = commonBusinessManager.getNumberById(erpIdArg.getObjAPIName(),
                        entityList.get(0).getDataId(),
                        keyMapping.getDataIdKey(),
                        keyMapping.getDataNumberKey(),
                        apiClient);
                log.debug("K3DataManager.getErpObjData,new dataNumber={},old dataNumber={}", dataNumber,erpIdArg.getDataId());
                if(StringUtils.isNotEmpty(dataNumber) && !StringUtils.equalsIgnoreCase(dataNumber,erpIdArg.getDataId())) {
                    //用新的ERP对象编码覆盖旧的ERP对象编码
                    erpIdArg.setDataId(dataNumber);
                }
            }
        }
        log.debug("K3DataManager.getErpObjData,erpIdArg2={}", erpIdArg);
        return commonGet(erpIdArg, specialBusiness, apiClient);
    }

    @InvokeMonitor(tenantId = "#erpIdArg.tenantId", dcId = "#apiClient.dataCenterId", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#erpIdArg.objAPIName", dataId = "#erpIdArg.dataId", sourceEventType = "#erpIdArg.sourceEventType", snapshotId = "#erpIdArg.syncPloyDetailSnapshotId", action = ActionEnum.GET, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<StandardData> commonGet(ErpIdArg erpIdArg, SpecialBusiness specialBusiness, K3CloudApiClient apiClient) {
        log.debug("K3DataManager.commonGet,erpIdArg={}", erpIdArg);
        String tenantId = erpIdArg.getTenantId();
        String masterApiName = erpIdArg.getObjAPIName();
        K3DataConverter converter = k3DataManager.buildConverter(tenantId,apiClient.getDataCenterId(), masterApiName);
        //默认会使用id查询数据
        ViewArg viewArg = new ViewArg();
        //根据id字段的viewCode判断是否使用id查看
        boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
        log.debug("K3DataManager.commonGet,useId={}", useId);
        if (useId) {
            if (erpIdArg.isDataIdIsNumber()) {
                String dataId = commonBusinessManager.getIdByNumber(masterApiName,
                        erpIdArg.getDataId(),
                        apiClient);
                erpIdArg.setDataId(dataId);
            }
            viewArg.setId(erpIdArg.getDataId());
        } else {
            viewArg.setNumber(erpIdArg.getDataId());
        }
        // 查看对象前的特殊逻辑
        specialBusiness.beforeRunView(viewArg, erpIdArg, apiClient);
        Boolean useViewInterface = tenantConfigurationManager.getUseViewInterFace(tenantId, apiClient.getDataCenterId(), masterApiName);
        Result<ViewResult> viewResult = this.getAndLog(tenantId, erpIdArg,masterApiName, apiClient, viewArg,converter,useViewInterface);
        if (!viewResult.isSuccess()) {
            return Result.copy(viewResult);
        }
        ErpIdNumberNameKeyMapping keyMapping = tenantConfigurationManager.getK3cObjIdNumberNameKeyMapping(erpIdArg.getTenantId(),masterApiName);
        log.debug("K3DataManager.commonGet,keyMapping={}", keyMapping);
        if(keyMapping!=null) {
            //1.先更新集成平台的物料中间表
            checkAndUpdateMappingData(tenantId,masterApiName,erpIdArg,apiClient);
            //2.再更新物料表
            checkAndInsertMaterialData(tenantId,apiClient.getDataCenterId(),masterApiName,viewResult);
        }
        //对比数据 去掉比对。后期需要切换接口直接使用接口调用比对
//        superAdminReplaceViewService.compareAndSendMessage(tenantId,masterApiName,apiClient,viewArg,converter);
        //获取编码字段
        ErpFieldExtendEntity erpFieldExtendEntity = erpFieldManager.queryNumFieldExtend(erpIdArg.getTenantId(),apiClient.getDataCenterId(), erpIdArg.getObjAPIName());
        Result<StandardData> result;
        if(!useViewInterface){
            result = converter.convertQueryResult(tenantId,converter.getIdFieldExtend(),erpFieldExtendEntity, viewResult.getData());
            if (!result.isSuccess()) {
                return result;
            }
        }else{//走view接口获取的数据
            result = converter.convertViewResult(tenantId,erpFieldExtendEntity, viewResult.getData());
            if (!result.isSuccess()) {
                return result;
            }
        }
        //convertErpFieldValue2Crm 前置处理K3业务员逻辑，避免后续预处理出现erp调用的时候网络问题
        convertK3DataErpOrDept(result.getData(),tenantId,apiClient.getDataCenterId(),erpIdArg);
        specialBusiness.afterRunView(erpIdArg, result.getData(), viewResult.getData().getResult().getResult(), apiClient);
        result.getData().setSyncLogId(LogIdUtil.get());
        return result;
    }

    /**
     * @param objAPINameMap <真实对象apiname, 拆分对象apiname>
     * */
    public void convertK3DataErpOrDept(StandardData standardData,String tenantId,String dataCenterId,ErpIdArg erpIdArg){
        //遍历主对象，从对象的相关的erpObjField的数据
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        Map<String, List<ObjectData>> erpObjVals=Maps.newHashMap();
        erpObjVals.put(erpIdArg.getObjAPIName(),Lists.newArrayList(standardData.getMasterFieldVal()));
        erpObjVals.putAll(standardData.getDetailFieldVals());

        Map<String,String> objAPINameMap = erpObjManager.getK3ObjRealApiNameMap(tenantId,dataCenterId,erpIdArg.getObjAPIName());

        for (String erpObjKey : erpObjVals.keySet()) {
            if(ObjectUtils.isNotEmpty(erpObjVals.get(erpObjKey))){
                String splitApiName=objAPINameMap.get(erpObjKey);
                for (ObjectData objectData : erpObjVals.get(erpObjKey)) {
                    Map<String, ErpObjectFieldEntity> erpObjFieldMap = getErpObjFieldMap(tenantId,dataCenterId, splitApiName);
                    for (String erpFieldKey : erpObjFieldMap.keySet()) {
                        convertErpFieldValue2crm(erpConnectInfoEntity,erpObjFieldMap.get(erpFieldKey),objectData,erpFieldKey,standardData.getMasterFieldVal().get(erpFieldKey));
                    }
                }
            }
        }

    }

    private void convertErpFieldValue2crm(ErpConnectInfoEntity connectInfo,ErpObjectFieldEntity fieldEntity,ObjectData objectData,String fieldKey,Object fieldValue){
        //根据字段类型，employee进行数据转换
        ErpFieldTypeEnum erpFieldTypeEnum=fieldEntity.getFieldDefineType();
        switch (erpFieldTypeEnum){
            case department:
            case employee:
                convertFieldValueEmpOrDepart(connectInfo, fieldValue, erpFieldTypeEnum, fieldKey, objectData,
                        fieldEntity.getFieldExtendValue());
                break;
            case employee_many:
                convertFieldValueEmployeeMany(connectInfo, fieldValue, fieldKey, objectData,fieldEntity.getFieldExtendValue());
                break;
            default:
                break;
        }
    }

    /**
     * K3C业务员转换
     * @param connectInfo
     * @param oldValue
     * @param fieldType
     * @param fieldKey
     * @param objectData
     * @param extendValue
     */
    public void convertFieldValueEmpOrDepart(ErpConnectInfoEntity connectInfo, Object oldValue, ErpFieldTypeEnum fieldType,
                                            String fieldKey,
                                            ObjectData objectData, String extendValue) {
        log.debug("SpecialFieldPreprocessManager.covertFieldValueEmpOrDepart,oldValue={},fieldKey={},fieldType={},crm2erp={},extendValue={},objectData={}",
                oldValue,fieldKey,fieldType,true,extendValue,objectData);
        if (oldValue == null||StringUtils.isBlank(oldValue.toString())) return;
        String convertKey= String.format(ERP_FIELD_CONVERT_SUFFIX,fieldKey);
            if (oldValue instanceof List) {
                List<Object>list= (List) oldValue;
                if(CollectionUtils.isNotEmpty(list)){//不为空才进行处理
                    List<String> oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
                    List<String> newList = Lists.newArrayList();
                    if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                        for (String oldsStr : oldList) {
                            oldsStr = dealK3CloudOperatorErp2Crm(oldsStr, connectInfo);
                            String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr);
                            log.info("trace SpecialFieldPreprocessManager.covertFieldValueEmpOrDepart,connectInfo:{}, fieldType:{}, oldsStr:{},newStr={}",
                                    connectInfo, fieldType, oldsStr, newStr);
                            newList.add(newStr);
                        }
                    } else {
                        for (String oldsStr : oldList) {
                            String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr);
                            newList.add(newStr);
                        }
                    }
                    objectData.put(convertKey, newList);
                }
            } else {//string->list
                String old = oldValue.toString();
                if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                    old = dealK3CloudOperatorErp2Crm(old, connectInfo);
                }
                String newStr = getConvertedFieldValue(connectInfo, fieldType, old);
                if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                    log.info("trace SpecialFieldPreprocessManager.covertFieldValueEmpOrDepart,connectInfo:{}, fieldType:{}, oldsStr:{},newStr={}",
                            connectInfo, fieldType, old, newStr);
                }
                objectData.put(convertKey, Lists.newArrayList(newStr));
            }
    }

    private void convertFieldValueEmployeeMany(ErpConnectInfoEntity connectInfo,
                                               Object oldValue,
                                               String fieldKey,
                                               ObjectData objectData,

                                               String extendValue) {
        log.info("SpecialFieldPreprocessManager.convertFieldValueEmployeeMany,oldValue={},fieldKey={},objectData={},extendValue={}",
                oldValue,fieldKey,objectData,extendValue);
        if (oldValue == null||StringUtils.isBlank(oldValue.toString())) return;
            List<Object> list= null;
            List<String> oldList = null;
            if (oldValue instanceof List) {
                list= (List) oldValue;
                if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                    oldList = new ArrayList<>();
                    for(Object item : list) {
                        Map<String,Object> map = (Map<String,Object>)item;
                        Map<String,Object> childMap = (Map<String,Object>)map.get(fieldKey);
                        oldList.add(MapUtils.getString(childMap,"Number"));
                    }
                } else {
                    oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
                }
            } else {//string->list
                list = Lists.newArrayList((oldValue.toString()).split(","));
                oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
            }
        convertFieldValueEmpOrDepart(connectInfo,oldList, ErpFieldTypeEnum.employee,fieldKey,objectData,extendValue);

        log.info("SpecialFieldPreprocessManager.convertFieldValueEmployeeMany,objectData={}", objectData);
    }
    /**
     * 获取转换后的字段值，如果找到多个，取第一个(最新的一个)
     *
     * @param connectInfo
     * @param fieldType
     * @param fieldValue
     * @return
     */
    public String getConvertedFieldValue(ErpConnectInfoEntity connectInfo, ErpFieldTypeEnum fieldType, String fieldValue) {
        if (fieldType==null){
            //按道理都进不来
            return null;
        }
        if (connectInfo == null || connectInfo.getTenantId() == null || fieldValue == null) {
            return fieldType.defaultValue(null, false, null,true);
        }
        //erp转crm
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                        connectInfo.getId(), fieldType, null, fieldValue);
        if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getFsDataId())) {
            return erpFieldDataMappingEntities.get(0).getFsDataId();
        }
        return fieldType.defaultValue(fieldValue, false, connectInfo.getChannel(),true);
    }


    private String dealK3CloudOperatorErp2Crm(String value, ErpConnectInfoEntity connectInfo) {
        FilterData filterData2 = FilterData.builder().fieldApiName("FNumber").fieldValue(Arrays.asList(value)).operate("IN").build();
        List<FilterData> filterDataList = Lists.newArrayList();
        filterDataList.add(filterData2);
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FStaffId.FNumber,FNumber");
        queryArg.setFormId(ObjectApiNameEnum.K3CLOUD_OPERATOR.getObjApiName());
        queryArg.addAndFilters(filterDataList);
        Result<List<K3Model>> result = k3DataManager.queryK3ObjData(connectInfo.getTenantId(), connectInfo.getId(), queryArg);
        if (result.getData() != null && result.getData().size() > 0) {
            return String.valueOf(result.getData().get(0).get("FStaffId.FNumber"));
        }
        return value;
    }
    /**
     * @param tenantId
     * @param objApiName
     * @return
     */
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public Map<String, ErpObjectFieldEntity> getErpObjFieldMap(String tenantId,String dataCenterId, String objApiName) {
              List<ErpObjectFieldEntity> objFields = erpFieldManager.queryAllField(tenantId,dataCenterId, objApiName);
        Map<String, ErpObjectFieldEntity> map = objFields.stream().collect(Collectors
                .toMap(ErpObjectFieldEntity::getFieldApiName, v -> v, (u, v) -> u));
        return map;
    }

    // 一定是ERP往CRM方向，一定是使用编码做主键
    private void checkAndUpdateMappingData(String tenantId,String masterApiName,ErpIdArg erpIdArg,K3CloudApiClient apiClient) {
        ErpIdNumberNameKeyMapping keyMapping = tenantConfigurationManager.getK3cObjIdNumberNameKeyMapping(tenantId,masterApiName);
        if(keyMapping==null) return;

        SyncPloyDetailSnapshotEntity ployDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId,
                erpIdArg.getSyncPloyDetailSnapshotId());
        if(ployDetailSnapshotEntity==null) return;

        String dataNumber = erpIdArg.getDataId();
        String dataId = commonBusinessManager.getIdByNumber(masterApiName,
                dataNumber,
                keyMapping.getDataIdKey(),
                keyMapping.getDataNumberKey(),
                apiClient);
        ErpObjIdNumberMappingEntity erpObjIdNumberMappingEntity = erpObjIdNumberMappingManager.queryByDataId(tenantId,
                apiClient.getDataCenterId(),
                masterApiName,
                dataId);
        if(erpObjIdNumberMappingEntity ==null) return;

        Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = syncDataMappingManager.getMapping2Way(tenantId,
                tenantId,
                ployDetailSnapshotEntity.getSourceObjectApiName(),
                erpObjIdNumberMappingEntity.getDataNumber(),
                tenantId,
                ployDetailSnapshotEntity.getDestObjectApiName());
        if(mapping2Way==null) return;

        if (mapping2Way.getLeft() != null) {
            int count = syncDataMappingManager.updateSourceDataIdById(tenantId,
                    mapping2Way.getLeft().getId(),
                    dataNumber);
            if (count != 1) {
                log.warn(
                        "K3DataManager.checkAndUpdateMappingData,updateSourceDataIdById,count={},ei={},sourceDataId={},dataNumber={}",
                        count, tenantId, mapping2Way.getLeft().getId(), dataNumber);
            }
        }
        if(mapping2Way.getRight()!=null) {
            int count = syncDataMappingManager.updateDestDataIdById(tenantId,
                    mapping2Way.getRight().getId(),
                    dataNumber);
            if (count != 1) {
                log.warn(
                        "K3DataManager.checkAndUpdateMappingData,updateDestDataIdById,count={},ei={},sourceDataId={},dataNumber={}",
                        count, tenantId, mapping2Way.getRight().getId(), dataNumber);
            }
        }
    }


    @InvokeMonitor(tenantId = "#tenantId", dcId = "#apiClient.dataCenterId", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#masterApiName", action = ActionEnum.GET, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<ViewResult> getAndLog(String tenantId,
                                        Object arg,
                                        String masterApiName,
                                        K3CloudApiClient apiClient,
                                        ViewArg viewArg,
                                        K3DataConverter converter,
                                        Boolean useViewInterface) {
        Result<ViewResult> viewResult;
        if(useViewInterface){//走view接口
            viewResult = apiClient.view(ErpObjInterfaceUrlEnum.queryMasterById, masterApiName, viewArg);
            if(!ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrCode().equals(viewResult.getErrCode())){//大小超过限制不重试
                if (viewResult.getData() == null||viewResult.getData().getResult()==null) {//重试一遍
                    viewResult = apiClient.view(ErpObjInterfaceUrlEnum.queryMasterById, masterApiName, viewArg);
                }
            }
        }else {//默认都走分明细对象查询
            viewResult=getDataSplitDetailByBillQuery(tenantId,viewArg ,masterApiName, converter, apiClient);
        }
        return viewResult;
    }

    private void checkAndInsertMaterialData(String tenantId,String dcId,String masterApiName,Result<ViewResult> viewResult) {
        if(!viewResult.isSuccess()
                || viewResult.getData()==null
                || viewResult.getData().getResult()==null
                || viewResult.getData().getResult().getResult()==null) return;

        ErpIdNumberNameKeyMapping keyMapping = tenantConfigurationManager.getK3cObjIdNumberNameKeyMapping(tenantId,masterApiName);
        log.debug("K3DataManager.checkAndInsertMaterialData,keyMapping={}", keyMapping);

        if(keyMapping==null) return;

        String dataId = null;
        String dataNumber = null;
        String dataName = null;
        for(String key : viewResult.getData().getResult().getResult().keySet()) {
            if(StringUtils.equalsIgnoreCase(key,keyMapping.getDataIdKey())) {
                dataId = viewResult.getData().getResult().getResult().getString(key);
            }
            if(StringUtils.equalsIgnoreCase(key,keyMapping.getDataNumberKey())) {
                dataNumber = viewResult.getData().getResult().getResult().getString(key);
            }
            if(StringUtils.equalsIgnoreCase(key,keyMapping.getDataNameKey())) {
                dataName = viewResult.getData().getResult().getResult().getString(key);
            }
        }
        if(StringUtils.isNotEmpty(dataId)) {
            ErpObjIdNumberMappingEntity erpObjIdNumberMappingEntity = erpObjIdNumberMappingManager.queryByDataId(tenantId,
                    dcId,
                    masterApiName,
                    dataId);
            if(erpObjIdNumberMappingEntity ==null) {
                int insert = erpObjIdNumberMappingManager.insert(tenantId,dcId,masterApiName, dataId, dataNumber, dataName);
                if(insert!=1) {
                    log.debug("K3DataManager.checkAndInsertMaterialData,insert data to material table failed,dataId={},dataNumber={},dataName={}",
                            dataId,dataNumber,dataName);
                }
            } else {
                //比较ID编码映射表里面的ERP对象编码和K3C查询过来的ERP对象编码是否相同，如果不同，说明ERP对象编码被更新过
                if(!StringUtils.equalsIgnoreCase(dataNumber, erpObjIdNumberMappingEntity.getDataNumber())) {
                    int updateNumber = erpObjIdNumberMappingManager.updateDataNumber(tenantId,dcId,masterApiName, dataId, dataNumber);
                    if(updateNumber!=1) {
                        log.debug("K3DataManager.checkAndInsertMaterialData,update material data failed,dataId={},dataNumber={}",
                                dataId,dataNumber);
                    }
                }
            }
        }
    }


//    private Result<ViewResult> getDataByBillQuery(String tenantId, ViewArg viewArg, String fromId, K3DataConverter converter, K3CloudApiClient apiClient) {
//        QueryArg queryArg=new QueryArg();
//        queryArg.setFormId(fromId);
//        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(fromId);
//        String erpId=null;
//        if(StringUtils.isNotBlank(viewArg.getNumber())){
//            erpId = viewArg.getNumber();
//            boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
//            if(useId){//为了适配debugData,设置的id字段是id，又使用编码查询
//                ErpFieldExtendEntity erpFieldExtendEntity = erpFieldManager.queryNumFieldExtend(tenantId,apiClient.getDataCenterId(), fromId);//获取编码
//                if(erpFieldExtendEntity!=null&&StringUtils.isNotBlank(erpFieldExtendEntity.getQueryCode())){
//                    queryArg.appendEqualFilter(erpFieldExtendEntity.getQueryCode(),erpId);
//                }else{
//                    throw new RuntimeException("没找到编码字段apiName,通过编码获取不到数据");
//                }
//            }else{
//                if(StringUtils.isBlank(converter.getIdFieldExtend().getQueryCode())){
//                    throw new RuntimeException("id字段["+converter.getIdFieldExtend().getFieldApiName()+"]没有查询接口编码，请加上");
//                }
//                queryArg.appendEqualFilter(converter.getIdFieldExtend().getQueryCode(),erpId);
//            }
//        }else{
//            erpId=viewArg.getId();
//            if(StringUtils.isBlank(converter.getIdFieldExtend().getQueryCode())){
//                throw new RuntimeException("id字段["+converter.getIdFieldExtend().getFieldApiName()+"]没有查询接口编码，请加上");
//            }
//            queryArg.appendEqualFilter(converter.getIdFieldExtend().getQueryCode(),erpId);
//        }
//        List<String> fieldList = getFieldList(converter);
//        if(viewArg.getCreateOrgId()!=null){
//            queryArg.appendEqualFilter("FCreateOrgId",viewArg.getCreateOrgId().toString());
//        }
//        queryArg.setFieldKeysByList(fieldList);
//        //获取数据前逻辑
//        specialBusiness.beforeGetDataByBillQuery(tenantId,queryArg,apiClient);
//        int limit=1000;//1000一次
//        int offset=0;
//        List<K3Model> allDataList=Lists.newArrayList();
//        String message="";
//        // 处理queryArg
//        String viewExtend = converter.getIdFieldExtend().getViewExtend();
//        handleArgWithViewExtend(queryArg, viewExtend);
//        while(offset<ConfigCenter.BILL_QUERY_SIZE_LIMIT){//默认查询BILL_QUERY_SIZE_LIMIT，为了防止一直循环以及数据过大，offset大于BILL_QUERY_SIZE_LIMIT的数据不查了，
//            queryArg.setStartRow(offset);
//            queryArg.setLimit(limit);
//            Result<List<K3Model>> listResult = apiClient.queryReturnMap(queryArg);
//            if(!listResult.isSuccess()||listResult.getData()==null){
//                listResult.setData(allDataList);
//                message=listResult.getErrMsg();
//                break;
//            }else{
//                allDataList.addAll(listResult.getData());
//                if(listResult.getData().size()<limit){
//                    break;
//                }
//            }
//            offset+=limit;
//        }
//        if(offset>ConfigCenter.BILL_QUERY_SIZE_LIMIT){
//            log.warn("k3批量查询接口ExecuteBillQuery数据量大于{}，viewArg={},",ConfigCenter.BILL_QUERY_SIZE_LIMIT,viewArg);
//        }
//        if(CollectionUtils.isEmpty(allDataList)){
//            ViewResult.Result result=new ViewResult.Result();
//            ResponseStatus responseStatus=new ResponseStatus();
//            responseStatus.setErrorCode("500");
//            responseStatus.setIsSuccess(false);
//            ResponseStatus.Messages error=new ResponseStatus.Messages();
//            error.setMessage("没找到筛选条件为["+queryArg.getFilterString()+"]的数据,原因："+message);
//            responseStatus.setErrors(Lists.newArrayList(error));
//            result.setResponseStatus(responseStatus);
//            ViewResult viewResult=new ViewResult();
//            viewResult.setResult(result);
//            return Result.newSuccess(viewResult);
//        }
//        return buildViewResult(allDataList,converter);
//    }

    /**
     * 按明细对象多次获取数据
     * @param tenantId
     * @param viewArg
     * @param fromId
     * @param converter
     * @param apiClient
     * @return
     */
    private Result<ViewResult> getDataSplitDetailByBillQuery(String tenantId, ViewArg viewArg, String fromId, K3DataConverter converter, K3CloudApiClient apiClient) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(fromId);
        if(StringUtils.isNotBlank(converter.getIdFieldViewExtendRealObjApiName())){
            queryArg.setFormId(converter.getIdFieldViewExtendRealObjApiName());
        }
        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(fromId);
        String erpId = null;
        if (StringUtils.isNotBlank(viewArg.getNumber())) {
            erpId = viewArg.getNumber();
            boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
            if(useId){//为了适配debugData,设置的id字段是id，又使用编码查询
                ErpFieldExtendEntity erpFieldExtendEntity = erpFieldManager.queryNumFieldExtend(tenantId,apiClient.getDataCenterId(), fromId);//获取编码
                if(erpFieldExtendEntity!=null&&StringUtils.isNotBlank(erpFieldExtendEntity.getQueryCode())){
                    queryArg.appendEqualFilter(erpFieldExtendEntity.getQueryCode(),erpId);
                }else{
                    throw new RuntimeException(i18NStringManager.getByEi(I18NStringEnum.s1,tenantId));
                }
            } else {
                if (StringUtils.isBlank(converter.getIdFieldExtend().getQueryCode())) {
                    throw new RuntimeException(i18NStringManager.getByEi2(I18NStringEnum.s2.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s2.getI18nValue(), converter.getIdFieldExtend().getFieldApiName()),
                            Lists.newArrayList(converter.getIdFieldExtend().getFieldApiName())));
                }
                queryArg.appendEqualFilter(converter.getIdFieldExtend().getQueryCode(), erpId);
            }
        } else {
            erpId = viewArg.getId();
            if (StringUtils.isBlank(converter.getIdFieldExtend().getQueryCode())) {
                throw new RuntimeException(i18NStringManager.getByEi2(I18NStringEnum.s2.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s2.getI18nValue(), converter.getIdFieldExtend().getFieldApiName()),
                        Lists.newArrayList(converter.getIdFieldExtend().getFieldApiName())));
            }
            queryArg.appendEqualFilter(converter.getIdFieldExtend().getQueryCode(), erpId);
        }
        if (viewArg.getCreateOrgId() != null) {
            queryArg.appendEqualFilter("FCreateOrgId", viewArg.getCreateOrgId().toString());
        }
        //主对象字段
        List<String> mainFieldList = getFieldList(converter.getMasterFieldExtends());
        //key apiName,value 数据
        Map<String, List<K3Model>> splitDataMap = new HashMap<>();
        //只有主对象
        Result<List<K3Model>> listResult;
        //只执行一次
        specialBusiness.beforeGetDataByBillQuery(tenantId, queryArg, apiClient);
        if (MapUtils.isEmpty(converter.getDetailFieldExtendMap())) {
            //只有主对象时，查询一次主对象数据
            queryArg.setFieldKeysByList(mainFieldList);
            listResult = excuteK3Request(fromId,queryArg, converter, apiClient);
            if (!listResult.isSuccess()) {
                return buildErrorViewResult(tenantId,queryArg.getFilterString(), listResult.getErrMsg());
            }
            splitDataMap.put("main", listResult.getData());
        } else {
            //带主从，查从对象数据顺带查主数据
            for (Map.Entry<String, List<ErpFieldExtendEntity>> entry : converter.getDetailFieldExtendMap().entrySet()) {
                List<String> allField = new ArrayList<>();
                allField.addAll(mainFieldList);
                allField.addAll(getFieldList(entry.getValue()));
                queryArg.setFieldKeysByList(allField);
                listResult = excuteK3Request(fromId,queryArg, converter, apiClient);
                if (!listResult.isSuccess()) {
                    //任一次错误，都返回失败结果
                    return buildErrorViewResult(tenantId,queryArg.getFilterString(), listResult.getErrMsg());
                }
                splitDataMap.put(entry.getKey(), listResult.getData());
            }
        }
        return buildSuccessViewResult(tenantId,queryArg, splitDataMap, converter);
    }

    private Result<ViewResult> buildErrorViewResult(String tenantId,String filterString, String errorMessage) {
        ViewResult.Result result=new ViewResult.Result();
        ResponseStatus responseStatus=new ResponseStatus();
        responseStatus.setErrorCode("500");
        responseStatus.setIsSuccess(false);
        ResponseStatus.Messages error=new ResponseStatus.Messages();
        error.setMessage(i18NStringManager.getByEi2(I18NStringEnum.s3.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s3.getI18nValue(), filterString,errorMessage),
                Lists.newArrayList(filterString,errorMessage)));
        responseStatus.setErrors(Lists.newArrayList(error));
        result.setResponseStatus(responseStatus);
        ViewResult viewResult=new ViewResult();
        viewResult.setResult(result);
        return Result.newSuccess(viewResult);
    }

    private Result<List<K3Model>> excuteK3Request(String logFormId,QueryArg queryArg,K3DataConverter converter, K3CloudApiClient apiClient){
        int limit=1000;//1000一次
        int offset=0;
        List<K3Model> allDataList=Lists.newArrayList();
        // 处理queryArg
        String viewExtend = converter.getIdFieldExtend().getViewExtend();
       commonBusinessManager.handleArgWithViewExtend(queryArg, viewExtend);

        long totalObjectByteSize = 0;
        while(offset<ConfigCenter.BILL_QUERY_SIZE_LIMIT){
            //默认查询BILL_QUERY_SIZE_LIMIT，为了防止一直循环以及数据过大，offset大于BILL_QUERY_SIZE_LIMIT的数据不查了，
            queryArg.setStartRow(offset);
            queryArg.setLimit(limit);
            Result<List<K3Model>> listResult = apiClient.queryReturnMap(logFormId,queryArg);
            if(!listResult.isSuccess()||listResult.getData()==null){
                listResult.setData(allDataList);
                return listResult;
            }else{
                allDataList.addAll(listResult.getData());
                if(listResult.getData().size()<limit){
                    break;
                }
            }
            offset+=limit;

            //检查k3c是不是billquery查询的明细总大小按照单条数据累加计算，超过了单条数据长度限制
            totalObjectByteSize += RamUsageEstimateUtil.sizeOfObjectIgnoreException(listResult);
            if (totalObjectByteSize > ConfigCenter.CONTENT_LENGTH_LIMIT * 30) {
                log.error("!!!明细数据累加起来太大, 丢弃此数据,ei:{}, formid:{}, offset:{},size:{}", apiClient.getTenantId(), queryArg.getFormId(), offset, DataSizeUtil.format(totalObjectByteSize));
                return Result.newError(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
        }

        if(offset>ConfigCenter.BILL_QUERY_SIZE_LIMIT){
            log.warn("k3批量查询接口ExecuteBillQuery数据量大于{}，queryArg={},",ConfigCenter.BILL_QUERY_SIZE_LIMIT,queryArg);
        }
        return Result.newSuccess(allDataList);
    }

    private Result<ViewResult> buildSuccessViewResult(String tenantId,QueryArg queryArg, Map<String, List<K3Model>> splitDataMap, K3DataConverter converter) {
        //从任一非空数据中取主对象数据
        Optional<List<K3Model>> optMain = splitDataMap.values().stream().filter(v -> CollUtil.isNotEmpty(v)).findAny();
        if (!optMain.isPresent()) {
            //不存在非空数据，返回错误
            return buildErrorViewResult(tenantId,queryArg.getFilterString(),i18NStringManager.getByEi(I18NStringEnum.s4,tenantId));
        }
        K3Model mainK3Model=new K3Model();
        for(ErpFieldExtendEntity field:converter.getMasterFieldExtends()){//主对象
            if(StringUtils.isBlank(field.getQueryCode())){
                continue;
            }
            Object value=optMain.get().get(0).get(field.getQueryCode());
            mainK3Model.put(field.getQueryCode(),value);
        }
        //明细
        for(String detailObjApiName:converter.getDetailIdFieldExtend().keySet()){
            List<K3Model> detailList=Lists.newArrayList();
            ErpFieldExtendEntity fieldIdExtendEntity = converter.getDetailIdFieldExtend().get(detailObjApiName);
            String detailObjIdField=fieldIdExtendEntity.getQueryCode();
            //选出对应明细的列表
            List<K3Model> k3Models = splitDataMap.get(detailObjApiName);
            if (CollUtil.isEmpty(k3Models)){
                continue;
            }
            List<String> idKey=k3Models.stream().filter(v->StringUtils.isNotBlank(v.getString(detailObjIdField))
                            &&!"0".equals(v.getString(detailObjIdField)))
                    .map(v->v.getString(detailObjIdField)).distinct().collect(Collectors.toList());//顺序一样,明细id为0的去掉
            Map<String, List<K3Model>> detail = k3Models.stream().filter(v->StringUtils.isNotBlank(v.getString(detailObjIdField))).collect(Collectors.groupingBy(v -> v.getString(detailObjIdField)));
            for(String detailDataId:idKey){
                if(!CollectionUtils.isEmpty(detail.get(detailDataId))){
                    K3Model detailK3Model=new K3Model();
                    for(ErpFieldExtendEntity field:converter.getDetailFieldExtendMap().get(detailObjApiName)){//从对象
                        if(StringUtils.isBlank(field.getQueryCode())){
                            continue;
                        }
                        Object value=detail.get(detailDataId).get(0).get(field.getQueryCode());
                        detailK3Model.put(field.getQueryCode(),value);
                    }
                    if(!CollectionUtils.isEmpty(detailK3Model.keySet())){
                        detailList.add(detailK3Model);
                    }
                }
            }
            mainK3Model.put(converter.getMasterDetailFieldMap().get(detailObjApiName).getViewCode(),detailList);
        }
        ViewResult.Result result=new ViewResult.Result();
        result.setResult(mainK3Model);
        ViewResult viewResult=new ViewResult();
        viewResult.setResult(result);
        return Result.newSuccess(viewResult);
    }

//    private Result<ViewResult> buildViewResult(List<K3Model> k3ModelList, K3DataConverter converter) {
//        K3Model mainK3Model=new K3Model();
//        for(ErpFieldExtendEntity field:converter.getMasterFieldExtends()){//主对象
//            if(StringUtils.isBlank(field.getQueryCode())){
//                continue;
//            }
//            Object value=k3ModelList.get(0).get(field.getQueryCode());
//            mainK3Model.put(field.getQueryCode(),value);
//        }
//        //明细
//        for(String detailObjApiName:converter.getDetailIdFieldExtend().keySet()){
//            List<K3Model> detailList=Lists.newArrayList();
//            ErpFieldExtendEntity fieldIdExtendEntity = converter.getDetailIdFieldExtend().get(detailObjApiName);
//            String detailObjIdField=fieldIdExtendEntity.getQueryCode();
//            List<String> idKey=k3ModelList.stream().filter(v->StringUtils.isNotBlank(v.getString(detailObjIdField))
//                    &&!"0".equals(v.getString(detailObjIdField)))
//                    .map(v->v.getString(detailObjIdField)).distinct().collect(Collectors.toList());//顺序一样,明细id为0的去掉
//            Map<String, List<K3Model>> detail = k3ModelList.stream().filter(v->StringUtils.isNotBlank(v.getString(detailObjIdField))).collect(Collectors.groupingBy(v -> v.getString(detailObjIdField)));
//            for(String detailDataId:idKey){
//                if(!CollectionUtils.isEmpty(detail.get(detailDataId))){
//                    K3Model detailK3Model=new K3Model();
//                    for(ErpFieldExtendEntity field:converter.getDetailFieldExtendMap().get(detailObjApiName)){//从对象
//                        if(StringUtils.isBlank(field.getQueryCode())){
//                            continue;
//                        }
//                        Object value=detail.get(detailDataId).get(0).get(field.getQueryCode());
//                        detailK3Model.put(field.getQueryCode(),value);
//                    }
//                    if(!CollectionUtils.isEmpty(detailK3Model.keySet())){
//                        detailList.add(detailK3Model);
//                    }
//                }
//            }
//            mainK3Model.put(converter.getMasterDetailFieldMap().get(detailObjApiName).getViewCode(),detailList);
//        }
//        ViewResult.Result result=new ViewResult.Result();
//        result.setResult(mainK3Model);
//        ViewResult viewResult=new ViewResult();
//        viewResult.setResult(result);
//        return Result.newSuccess(viewResult);
//    }

//    public List<String> getFieldList(K3DataConverter converter){
//        List<ErpFieldExtendEntity> masterFields=converter.getMasterFieldExtends();
//        Set<String> fieldList=masterFields.stream().filter(field ->StringUtils.isNotBlank(field.getQueryCode()))
//                .filter(field->!ErpFieldTypeEnum.detail.equals(field.getFieldDefineType()))
//                .map(ErpFieldExtendEntity::getQueryCode).collect(Collectors.toSet());
//        for(List<ErpFieldExtendEntity> detail:converter.getDetailFieldExtendMap().values()){
//            Set<String> detailFieldList=detail.stream().filter(field ->StringUtils.isNotBlank(field.getQueryCode()))
//                    .filter(field->!ErpFieldTypeEnum.detail.equals(field.getFieldDefineType()))
//                    .map(ErpFieldExtendEntity::getQueryCode).collect(Collectors.toSet());
//            fieldList.addAll(detailFieldList);
//        }
//        return Lists.newArrayList(fieldList);
//    }

    public List<String> getFieldList(List<ErpFieldExtendEntity> fieldExtendEntities){

        Set<String> fieldList=fieldExtendEntities.stream().filter(field ->StringUtils.isNotBlank(field.getQueryCode()))
                .filter(field->!ErpFieldTypeEnum.detail.equals(field.getFieldDefineType()))
                .map(ErpFieldExtendEntity::getQueryCode).collect(Collectors.toSet());

        return Lists.newArrayList(fieldList);
    }
    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if (erpObjDataResult != null) {
            String masterApiName = erpIdArg.getObjAPIName();
            SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(masterApiName);
            List<StandardData> allData = Lists.newArrayList();
            Result<List<StandardData>> dataListResult = new Result<>();
            if (erpObjDataResult.getData() != null) {
                allData.add(erpObjDataResult.getData());
                specialBusiness.afterRunGetReSyncObjDataById(erpIdArg, allData);
            }
            dataListResult.setErrCode(erpObjDataResult.getErrCode());
            dataListResult.setErrMsg(erpObjDataResult.getErrMsg());
            dataListResult.setData(allData);
            return dataListResult;
        }
        return null;
    }

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        try {
            K3CloudApiClient k3CloudApiClient = apiClientHolder.getK3ApiClient(timeFilterArg.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
            k3CloudApiClient.setDataCenterId(connectInfo.getId());
            String objApiName = timeFilterArg.getObjAPIName();
            //特殊逻辑服务
            SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(objApiName);
            if (specialBusiness.needSpecialGetAndQuery(null)) {
                //单独接口获取列表数据
                return specialBusiness.specialListErpObjData(timeFilterArg, k3CloudApiClient);
            }
            //一般情况都走这个逻辑获取id列表
            K3DataConverter converter = k3DataManager.buildConverter(timeFilterArg.getTenantId(),k3CloudApiClient.getDataCenterId(), objApiName);
            ErpFieldExtendEntity idFieldExtend = converter.getIdFieldExtend();
            QueryArg queryArg = this.buildQueryParams(timeFilterArg, idFieldExtend,k3CloudApiClient.getDataCenterId());
            if(StringUtils.isNotBlank(converter.getIdFieldViewExtendRealObjApiName())){
                queryArg.setFormId(converter.getIdFieldViewExtendRealObjApiName());
            }//查询前特殊逻辑
            specialBusiness.beforeRunBillQuery(queryArg, timeFilterArg, k3CloudApiClient);
            Result<List<List<Object>>> listResult = this.listAndLog(connectInfo.getTenantId(), objApiName, k3CloudApiClient, queryArg, timeFilterArg);
            if (!listResult.isSuccess()) {
                manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),new ArrayList<>());
                return Result.copy(listResult);
            }
            List<List<Object>> idListList = listResult.getData();
            // 去重处理
            specialBusiness.distinctByNumber(idListList);
            List<String> idList = idListList.stream().map(l -> String.valueOf(l.get(0))).collect(Collectors.toList());
            log.debug("K3DataManager.listErpObjDataByTime,idList={}", idList);
            //获取id和失败数据，不再入库
            ErpIdArg erpIdArg = new ErpIdArg();
            erpIdArg.setObjAPIName(objApiName);
            erpIdArg.setTenantId(timeFilterArg.getTenantId());
            erpIdArg.setSyncPloyDetailSnapshotId(timeFilterArg.getSnapshotId());
            List<StandardData> standardDataList = Lists.newArrayList();
            Result<StandardData> erpObjDataRes = Result.newError(i18NStringManager.getByEi(I18NStringEnum.s7,timeFilterArg.getTenantId()));
            String preLogId=LogIdUtil.get();//注：K3的批量 查询数据时，读接口为了精确匹配到getById。在读接口的时候，也进行了二段式id拼接

            long curPageDataTotalBytes = 0;
            for (int i = 0; i < idList.size(); i++) {
                String id=idList.get(i);
                String newLogId = LogIdUtil.buildChildLogIdRestId(preLogId, i);
                TraceUtil.initTrace(newLogId);
                erpIdArg.setDataId(id);
                log.debug("listErpObjDataByTime commonget logId:{}", LogIdUtil.get());
                erpObjDataRes = this.commonGet(erpIdArg, specialBusiness, k3CloudApiClient);
                if (erpObjDataRes.isSuccess()) {
                    standardDataList.add(erpObjDataRes.getData());
                    curPageDataTotalBytes += RamUsageEstimateUtil.sizeOfObjectIgnoreException(erpObjDataRes.getData());
                    if (curPageDataTotalBytes > ConfigCenter.LIST_CONTENT_LENGTH_LIMIT * 10) {
                        //本次批量获取数据，超过大小限制了。
                        log.error("!!!discard k3c data, trace listErpObjDataByTime exceed LIST_CONTENT_LENGTH_LIMIT, curindex:{}, cur listDataLength:{}",
                                i, DataSizeUtil.format(curPageDataTotalBytes));
                        return Result.newError(ResultCodeEnum.ERP_LIST_DATA_TOO_LONG);
                    }
                }
            }
            LogIdUtil.reset(preLogId);
            TraceUtil.initTrace(preLogId);
            if (!idList.isEmpty()&&standardDataList.isEmpty()){
                manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),new ArrayList<>());
                //全部失败
                return Result.newError(ResultCodeEnum.NOT_ALLOW_SKIP_TIME_ERROR,i18NStringManager.getByEi(I18NStringEnum.s8,timeFilterArg.getTenantId())+erpObjDataRes.getErrMsg());
            }
            //获取列表后特殊逻辑
            specialBusiness.afterRunListData(timeFilterArg, standardDataList, k3CloudApiClient);
            StandardListData standardListData = new StandardListData();
            standardListData.setDataList(standardDataList);

            manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),standardDataList);
            return Result.newSuccess(standardListData);
        } catch (Exception e) {
            manageLastErpDataCache(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName(),new ArrayList<>());
            throw e;
        }
    }

    /**
     * 管理分页轮询最后一条数据的缓存
     * @param tenantId
     * @param erpObjApiName
     * @param standardDataList
     */
    private void manageLastErpDataCache(String tenantId,String erpObjApiName,List<StandardData> standardDataList) {
        String key = tenantId + "_" + erpObjApiName;

        String operation = "put";
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(standardDataList)) {
            StandardData lastStandardData = standardDataList.get(standardDataList.size()-1);

            LastErpDataModel lastErpDataModel = new LastErpDataModel();
            lastErpDataModel.setErpId(lastStandardData.getMasterFieldVal().getString("erp_id"));
            lastErpDataModel.setErpNum(lastStandardData.getMasterFieldVal().getString("erp_num"));

            Map<String,LastErpDataModel> lastErpDataMap = lastErpDataCache.get();
            lastErpDataMap.put(key,lastErpDataModel);
            lastErpDataCache.set(lastErpDataMap);
        } else {
            lastErpDataCache.get().remove(key);
            operation = "remove";
        }
        log.debug("K3DataManager.manageLastErpDataMap,operation={},lastErpDataCache={}", operation ,JSONObject.toJSONString(lastErpDataCache.get()));
    }

    @InvokeMonitor(tenantId = "#tenantId", dcId = "#k3CloudApiClient.dataCenterId", invokeType = InvokeTypeEnum.ERP, count = "T(com.fxiaoke.open.erpsyncdata.apiproxy.aop.ErpInvokeMonitorAspect).size(#result?.getData())", objAPIName = "#objApiName", action = ActionEnum.LIST, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<List<List<Object>>> listAndLog(String tenantId, String objApiName, K3CloudApiClient k3CloudApiClient, QueryArg queryArg,TimeFilterArg timeFilterArg) {
        return k3CloudApiClient.executeBillQuery(objApiName,queryArg, timeFilterArg);
    }

    public QueryArg buildQueryParams(TimeFilterArg timeFilterArg, ErpFieldExtendEntity idFieldExtend,String dataCenterId) {
        String queryArgFilterString = getFilterString(timeFilterArg);

        String erpIdFieldApiName = idFieldExtend.getSaveCode();
        if(StringUtils.isEmpty(erpIdFieldApiName)) {
            erpIdFieldApiName = idFieldExtend.getQueryCode();
        }
        if(StringUtils.isEmpty(erpIdFieldApiName)) {
            throw new ErpSyncDataException(ResultCodeEnum.K3CLOUD_ID_FIELD_EXTEND_CONFIG_ERROR,timeFilterArg.getTenantId());
        }

        QueryArg queryArg = new QueryArg();
        queryArg.setFilterString(queryArgFilterString);
        queryArg.setFormId(timeFilterArg.getObjAPIName());
        queryArg.setFieldKeys(idFieldExtend.getSaveCode());
        if (timeFilterArg.getLimit() != null) {
            queryArg.setLimit(timeFilterArg.getLimit());
        }

        String lastErpId = null;
        String lastErpNum = null;
        LastErpDataModel lastErpDataModel = lastErpDataCache.get().get(timeFilterArg.getTenantId()+"_"+timeFilterArg.getObjAPIName());
        log.debug("K3DataManager.buildQueryParams,lastErpDataModel={}", lastErpDataModel);
        if(lastErpDataModel!=null) {
            lastErpId = lastErpDataModel.getErpId();
            lastErpNum = lastErpDataModel.getErpNum();
        }

        //如果lastErpId和lastErpNum同时为空，走老逻辑
        if(StringUtils.isEmpty(lastErpId) && StringUtils.isEmpty(lastErpNum)) {
            if (timeFilterArg.getOffset() != null) {
                queryArg.setStartRow(timeFilterArg.getOffset());
            }
        } else {
            queryArg.setStartRow(0);
        }

        commonBusinessManager.handleArgWithViewExtend(queryArg,idFieldExtend.getViewExtend());
        if(StringUtils.isNotEmpty(lastErpId)) {
            K3DataConverter converter = k3DataManager.buildConverter(timeFilterArg.getTenantId(),dataCenterId, timeFilterArg.getObjAPIName());
            //根据id字段的viewCode判断是否使用id查看
            boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
            String idFieldValue = useId ? lastErpId : lastErpNum;
            String filterString = erpIdFieldApiName + " > " + "\'" + idFieldValue + "\'";
            if(StringUtils.isNotEmpty(queryArg.getFilterString())) {
                filterString += " and (" + queryArg.getFilterString() + ")";
            }
            queryArg.setFilterString(filterString);
        }
        queryArg.setOrderString(erpIdFieldApiName+" asc");
        log.debug("K3DataManager.buildQueryParams,queryArg={}", JSONObject.toJSONString(queryArg));
        return queryArg;
    }

    public String getFilterString(final TimeFilterArg timeFilterArg) {
//        如果是自定义历史任务,以filter为准
        if (StringUtils.isNotEmpty(timeFilterArg.getCustomFilterString())) {
            return timeFilterArg.getCustomFilterString();
        }

        // 添加默认条件
        List<FilterData> defaultFilterList = Lists.newArrayList();

        // 获取默认条件
        if (ConfigCenter.K3_EXIST_CANCEL_OBJ.contains(timeFilterArg.getObjAPIName())) {
            final String operate = Objects.equals(timeFilterArg.getOperationType(), EventTypeEnum.INVALID.getType()) ?
                    Operate.IS : Operate.IS_NOT;
            FilterData status = FilterData.builder().fieldApiName("FCancelStatus").operate(operate).fieldValue(Lists.newArrayList("B")).build();
            defaultFilterList.add(status);
        }


        if (CollectionUtils.isNotEmpty(timeFilterArg.getFilters())) {
            if (timeFilterArg.getFilters().stream().flatMap(Collection::stream).anyMatch(f -> (f.getIsVariableBetween() != null && f.getIsVariableBetween()))) {
                //     有时间字段的,以filter为准
                return getFilterString(timeFilterArg.getFilters(), defaultFilterList, timeFilterArg.getStartTime(), timeFilterArg.getEndTime());
            }

            // 没有时间字段的,都是and条件
            defaultFilterList.addAll(timeFilterArg.getFilters().get(0));
        }

        if (configCenterConfig.isNoTimeFilterObj(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName())) {
            //物料分组默认不使用时间筛选条件，每次全量查。
            final ArrayList<List<FilterData>> filters = Lists.newArrayList();
            filters.add(Lists.newArrayList());
            return getFilterString(filters, defaultFilterList, timeFilterArg.getStartTime(), timeFilterArg.getEndTime());
        }

        // 获取默认的or条件
        final List<List<FilterData>> defaultOrFilters = getDefaultOrFilters(timeFilterArg);
        return getFilterString(defaultOrFilters, defaultFilterList, timeFilterArg.getStartTime(), timeFilterArg.getEndTime());
    }

    private List<List<FilterData>> getDefaultOrFilters(final TimeFilterArg timeFilterArg) {
        List<List<FilterData>> defaultOrFilterList = Lists.newArrayList();
        if (Objects.equals(timeFilterArg.getOperationType(), EventTypeEnum.INVALID.getType())) {
            //FieldType.DATE,FieldType.DATE_TIME会加上{ts'yyyy-mm-dd hh:mm:ss'}
            FilterData cancelDate = FilterData.builder().fieldApiName("FCancelDate").fieldType(FieldType.DATE_TIME).
                    operate(Operate.BETWEEN).isVariableBetween(true).build();
            defaultOrFilterList.add(Lists.newArrayList(cancelDate));
            return defaultOrFilterList;
        }

        //最后修改时间
        FilterData modifyDate = FilterData.builder().fieldApiName("FModifyDate").fieldType(FieldType.DATE_TIME).
                operate(Operate.BETWEEN).isVariableBetween(true).build();
        defaultOrFilterList.add(Lists.newArrayList(modifyDate));
        if (!configCenterConfig.getNO_APPROVE_FORM_SET().contains(timeFilterArg.getObjAPIName())) {
            //增加审核时间筛选
            FilterData approveDate = FilterData.builder().fieldApiName("FApproveDate").fieldType(FieldType.DATE_TIME).
                    operate(Operate.BETWEEN).isVariableBetween(true).build();
            defaultOrFilterList.add(Lists.newArrayList(approveDate));
        }
        if (configCenterConfig.getK3_ALLOW_QUERY_FORBIDDATA().contains(timeFilterArg.getTenantId() + ":" + timeFilterArg.getObjAPIName())) {
            //增加禁用时间筛选
            FilterData forbidDate = FilterData.builder().fieldApiName("FForbidDate").fieldType(FieldType.DATE_TIME).
                    operate(Operate.BETWEEN).isVariableBetween(true).build();
            defaultOrFilterList.add(Lists.newArrayList(forbidDate));
        }
        return defaultOrFilterList;
    }

    private String getFilterString(final List<List<FilterData>> filters, final List<FilterData> defaultFilterList, final Long startTime, final Long endTime) {
        filters.stream()
                .flatMap(Collection::stream)
                .filter(f -> (f.getIsVariableBetween() != null && f.getIsVariableBetween()))
                .forEach(f -> f.setOperate(Operate.BETWEEN));
        // 防止污染原来的list
        List<List<FilterData>> filterList = filters.stream()
                .map(Lists::newArrayList)
                // 将defaultFilterList加入到filters中
                .peek(list -> list.addAll(defaultFilterList))
                .collect(Collectors.toList());
        K3FilterStringBuilder k3FilterStringBuilder = new K3FilterStringBuilder(filterList, startTime, endTime);
        return k3FilterStringBuilder.build();
    }




    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getMasterFieldVal().getApiName();
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(tenantId, connectInfo.getConnectParams(), connectInfo.getId());
        SaveArg saveArg = new SaveArg();
        K3DataConverter saveConverter = buildSaveConverter(tenantId, apiClient.getDataCenterId(), standardData);
        //转换参数
        saveConverter.fillSaveArg(standardData, saveArg,connectInfo.getTenantId());
        // 创建对象前的特殊逻辑
        IdSaveExtend saveExtend = IdSaveExtend.of(objApiName, saveConverter.getIdFieldExtend().getSaveExtend());
        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(standardData.getObjAPIName());
        Result<String> specResult = specialBusiness.beforeRunCreateErpObjData(standardData, apiClient, saveArg, saveExtend);
        if (!specResult.isSuccess()) {
            return Result.copy(specResult);
        }

        //standardData.removeId();

        Result<SaveResult> saveResultRes;
        if (!StringUtils.isEmpty(saveExtend.getSubSystemId())) {
            saveArg.setSubSystemId(saveExtend.getSubSystemId());
        } else {
            if(StringUtils.equalsIgnoreCase(objApiName,K3CloudForm.CRM_Contract)) {
                //销售合同同步，默认预置子系统ID
                saveArg.setSubSystemId("23");
            }
        }
        if (saveExtend.getIsEntryBatchFill() != null) {
            saveArg.setIsEntryBatchFill(saveExtend.getIsEntryBatchFill());
        }
        if (saveExtend.getIsVerifyBaseDataField() != null && (saveArg.getIsVerifyBaseDataField() == null||!saveArg.getIsVerifyBaseDataField())) {//如果saveArg的IsVerifyBaseDataField为空或者为默认值false，才以saveExtend的IsVerifyBaseDataField为准
            saveArg.setIsVerifyBaseDataField(saveExtend.getIsVerifyBaseDataField());
        }
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        if (saveExtend.getUseDraft()) {
            saveArg.setIsAutoSubmitAndAudit(false);
            saveExtend.setIsAutoSubmitAndAudit(false);
            //暂存
            saveResultRes = apiClient.draft(objApiName, saveArg);
        } else {
            //保存
            saveResultRes = apiClient.save(ErpObjInterfaceUrlEnum.create, objApiName, saveArg);
        }
        //保存接口日志,1成功，2失败
        if (!saveResultRes.isSuccess()) {
            return Result.copy(saveResultRes);
        }
        Result<ErpIdResult> idResult = saveConverter.convertSaveResult(tenantId,saveResultRes.getData());
        if(!idResult.isSuccess()){
            return idResult;
        }

        //只有销售订单才支持对比源订单和目标订单明细的数目，如果明细数量不一致，需要删除ERP上的目标订单
        if (StringUtils.equalsIgnoreCase(objApiName, K3CloudForm.SAL_SaleOrder)) {
            //源订单明细列表
            List<K3Model> entryList = saveArg.getModel().getDetails("FSaleOrderEntry");
            log.debug("K3DataManager.createErpObjData,SAL_SaleOrder,entryList={}", entryList);

            QueryArg queryArg = new QueryArg();
            queryArg.setFieldKeys("FBillNo,FSaleOrderEntry_FEntryID,FRowType,FMaterialId.FNumber");
            queryArg.setFormId(objApiName);
            queryArg.appendEqualFilter("FBillNo", saveResultRes.getData().getResult().getNumber());
            queryArg.setLimit(2000);
            commonBusinessManager.fillQueryArgByViewExtend(tenantId, apiClient.getDataCenterId(), queryArg);

            //查询目标订单明细数据
            Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
            log.debug("K3DataManager.createErpObjData,SAL_SaleOrder,query result={}", result);
            //如果源订单明细数量和目标订单明细数量不一致
            if (result.isSuccess() && result.getData().size() != entryList.size()) {
                List<String> notMatchNumber = printNotMatchEntryList(entryList, result.getData());
                queryArg.appendFilterString("FRowType!='Son'");
                //如果源订单明细数量和目标订单明细数量不一致，排除ERP订单明细 产品类型=套件子项，重新查询
                result = apiClient.queryReturnMap(queryArg);
                log.debug("K3DataManager.createErpObjData,SAL_SaleOrder,query result2={}", result);
                log.debug("K3DataManager.createErpObjData,SAL_SaleOrder,crm entry size={},erp entry2 size={}", entryList.size(),
                        result.getData().size());
                //如果排除 产品类型=套件子项 后源订单明细数量和目标订单明细数量还不一致，那就需要删除ERP上新建的销售订单
                if (result.isSuccess() && result.getData().size() != entryList.size()) {
                    List<String> notMatchMaterialNumberList = printNotMatchEntryList(entryList,result.getData());
                    DeleteArg deleteArg = new DeleteArg();
                    deleteArg.setIds(saveResultRes.getData().getResult().getId());
                    log.debug("K3DataManager.createErpObjData,SAL_SaleOrder,deleteArg={}", deleteArg);
                    //删除ERP上新建的销售订单
                    Result<DeleteResult> deleteResult = apiClient.delete(objApiName, deleteArg);
                    log.debug("K3DataManager.createErpObjData,SAL_SaleOrder,deleteResult={}", deleteResult);
                    if (deleteResult.isSuccess()) {
                        String afterDeleteMsg=String.format(I18NStringEnum.s1153.getI18nValue(),i18NStringManager.getByEi(I18NStringEnum.s9,tenantId),
                                JSONObject.toJSONString(notMatchNumber),
                                i18NStringManager.getByEi(I18NStringEnum.s6,tenantId));
                        AfterSystemProcessModel afterSystemProcessModel= AfterSystemProcessModel.builder()
                                .afterProcessType(AfterSystemProcessEnum.CANCEL_WRITE.name())
                                .processReason(afterDeleteMsg)
                                .build();
                        syncLogManager.saveLog(tenantId,SyncLogTypeEnum.AFTER_SYSTEM_PROCESS, SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),afterSystemProcessModel);
                        return Result.newError(ResultCodeEnum.SALES_ORDER_ENTRY_SIZE_NOT_MATCH_AND_DELETE_SUCCESS);
                    } else {
                        String afterDeleteMsg=String.format(I18NStringEnum.s1153.getI18nValue(),i18NStringManager.getByEi(I18NStringEnum.s9,tenantId),
                                JSONObject.toJSONString(notMatchNumber),
                                i18NStringManager.getByEi(I18NStringEnum.s7,tenantId));
                        AfterSystemProcessModel afterSystemProcessModel= AfterSystemProcessModel.builder()
                                .afterProcessType(AfterSystemProcessEnum.CANCEL_WRITE.name())
                                .processReason(afterDeleteMsg)
                                .build();
                        syncLogManager.saveLog(tenantId,SyncLogTypeEnum.AFTER_SYSTEM_PROCESS,SyncLogStatusEnum.SYNC_FAIL.getStatus(),afterSystemProcessModel);
                        return Result.newError(ResultCodeEnum.SALES_ORDER_ENTRY_SIZE_NOT_MATCH_AND_DELETE_FAILED);
                    }
                }
            }
        }

        //提交不审核
        if (saveExtend.getIsAutoSubmitWithoutAudit()) {
            K3CreateConfig createConfig = configCenterConfig.getK3CreateConfig(tenantId, connectInfo.getId(), objApiName);
            idResult = submitWithoutAudit(apiClient, objApiName, saveResultRes.getData(), idResult, createConfig);
            saveExtend.setIsAutoSubmitAndAudit(false);
        }
        //提交并审核
        if (saveExtend.getIsAutoSubmitAndAudit()) {
            K3CreateConfig createConfig = configCenterConfig.getK3CreateConfig(tenantId, connectInfo.getId(), objApiName);
            idResult = submitAndAudit(apiClient, objApiName, saveResultRes.getData(), idResult, createConfig);
        }

        detailNotMatchSpecialProcess(tenantId,connectInfo.getId(),connectInfo.getChannel().name(),apiClient,idResult,standardData);
        specialBusiness.afterRunCreateErpObjData(idResult.getData(),standardData,saveArg,apiClient);
        return idResult;
    }

    /**
     * 对CRM没开CPQ，ERP开了CPQ，报明细ID不一致的问题，通用处理方案
     * @param tenantId
     * @param dataCenterId
     * @param channel
     * @param apiClient
     * @param idResult
     */
    private void detailNotMatchSpecialProcess(String tenantId, String dataCenterId, String channel, K3CloudApiClient apiClient,
                                              Result<ErpIdResult> idResult,StandardData standardData) {
        if(standardData.getDetailFieldVals()==null || standardData.getDetailFieldVals().isEmpty()) return;

        String specialQuery = configCenterConfig.getK3CloudDetailNotMatchSpecialQuery(tenantId,dataCenterId,channel,standardData.getObjAPIName());
        if(StringUtils.isNotEmpty(specialQuery)) {
            QueryArg2 queryArg = JSONObject.parseObject(specialQuery,QueryArg2.class);
            if(queryArg!=null
                    && StringUtils.isNotEmpty(queryArg.getFilterString())
                    && StringUtils.isNotEmpty(queryArg.getFieldKeys())
                    && StringUtils.isNotEmpty(queryArg.getFormId())
                    && StringUtils.isNotEmpty(queryArg.getDetailApiName())) {

                List<ObjectData> srcDetailList = standardData.getDetailFieldVals().get(queryArg.getDetailApiName());
                List<String> dstDetailIdList = idResult.getData().getDetailDataIds().get(queryArg.getDetailApiName());
                //如果源对象明细的数量和erp返回的对象明细数量一样，直接返回
                if(dstDetailIdList.size()== srcDetailList.size()) return;

                queryArg.setFilterString(queryArg.getFilterString().replace("{masterDataId}",idResult.getData().getMasterDataId()));

                Result<List<List<Object>>> queryResult = apiClient.executeBillQuery(queryArg);
                log.debug("K3DataManager.detailNotMatchSpecialProcess,queryResult={}",queryResult);
                if(queryResult.isSuccess() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryResult.getData())) {
                    List<String> idList = new ArrayList<>();
                    for(List<Object> list : queryResult.getData()) {
                        idList.add(list.get(0).toString());//get(0)=明细ID
                    }
                    log.debug("K3DataManager.detailNotMatchSpecialProcess,idList={}",idList);
                    if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(idList)) {
                        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(dstDetailIdList)) {
                            dstDetailIdList.clear();
                            dstDetailIdList.addAll(idList);
                        }
                    }
                    log.debug("K3DataManager.detailNotMatchSpecialProcess,idResult={}",idResult);
                }
            }
        }
    }

    private String getMaterialNumber(K3Model item) {
        LinkedHashMap materialId = (LinkedHashMap)item.get("FMaterialId","FMaterialID","FMATERIALID");
        if(materialId==null) {
            log.debug("K3DataManager.isItemExist,material number not exist,item={}",item);
            return null;
        }
        String srcMaterialNumber = materialId.getOrDefault("FNumber","").toString();
        if(StringUtils.isEmpty(srcMaterialNumber)) {
            srcMaterialNumber = materialId.getOrDefault("FNUMBER","").toString();
        }
        if(StringUtils.isEmpty(srcMaterialNumber)) {
            log.debug("K3DataManager.isItemExist,srcMaterialNumber is empty,item={}",item);
            return null;
        }
        return srcMaterialNumber;
    }

    private boolean isItemExist(K3Model item,List<K3Model> list) {
        String srcMaterialNumber = getMaterialNumber(item);

        log.debug("K3DataManager.isItemExist,srcMaterialNumber={}",srcMaterialNumber);
        for(K3Model model : list) {
            String materialNumber = model.getString("FMaterialId.FNumber");
            log.debug("K3DataManager.isItemExist,materialNumber={}",materialNumber);
            if(StringUtils.equalsIgnoreCase(materialNumber,srcMaterialNumber)) return true;
        }
        return false;
    }

    /**
     * 输出源订单明细和目标订单明细不匹配的明细项
     * @param srcList
     * @param destList
     */
    private List<String> printNotMatchEntryList(List<K3Model> srcList,List<K3Model> destList) {
        List<K3Model> notMatchEntryList = new ArrayList<>();
        List<String> notMatchMaterialNumber = new ArrayList<>();
        for(K3Model item : srcList) {
            if(!isItemExist(item,destList)) {
                notMatchEntryList.add(item);
                String materialNumber = getMaterialNumber(item);
                if(StringUtils.isNotEmpty(materialNumber)) {
                    notMatchMaterialNumber.add(materialNumber);
                } else {
                    log.debug("K3DataManager.printNotMatchEntryList,materialNumber={},item={}",materialNumber,item);
                }
            }
        }
        log.debug("K3DataManager.printNotMatchEntryList,notMatchEntryList={}",JSONObject.toJSONString(notMatchEntryList));
        log.debug("K3DataManager.printNotMatchEntryList,notMatchMaterialNumber={}",JSONObject.toJSONString(notMatchMaterialNumber));
        return notMatchMaterialNumber;
    }

    private Result<ErpIdResult> submitAndAudit(K3CloudApiClient apiClient,
                                               String objApiName,
                                               SaveResult saveResult,
                                               Result<ErpIdResult> idResult,
                                               K3CreateConfig createConfig) {

        final Result<ErpIdResult> submit = submit(apiClient, objApiName, saveResult, idResult, createConfig.isDeleteBySubmitFail());

        final Result<ErpIdResult> audit = audit(apiClient, objApiName, saveResult, submit, createConfig.isDeleteByAuditFail());

        if (audit.isSuccess()) {
            return audit;
        }

        if (Objects.equals(createConfig.getReturnStepStatus(), K3DocumentStatusEnum.ADD.getStatus())) {
            idResult.setErrMsg(audit.getErrMsg());
            return idResult;
        } else if (Objects.equals(createConfig.getReturnStepStatus(), K3DocumentStatusEnum.AUDITING.getStatus())) {
            submit.setErrMsg(audit.getErrMsg());
            return submit;
        }
        return audit;
    }

    private Result<ErpIdResult> audit(K3CloudApiClient apiClient, String objApiName, SaveResult saveResult, Result<ErpIdResult> idResult, boolean delFailBill) {
        if (!idResult.isSuccess()) {
            return idResult;
        }
        Submit.BaseArg arg = new Submit.BaseArg();
        arg.setIds(saveResult.getResult().getId());
        setInterfaceArg(arg,apiClient,objApiName,K3CSaveAction.AUDIT);
        //审核
        Result<Submit.Result> audit = apiClient.audit(objApiName, arg);

        String auditErrMsg = Submit.checkResult(audit);
        if (StringUtils.isNotEmpty(auditErrMsg)) {
            audit.setErrCode(ResultCodeEnum.K3C_BILL_AUDIT_FAILED.getErrCode());
            audit.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s10,apiClient.getTenantId()) + auditErrMsg);
            if(delFailBill) {
//                审核失败需要先撤销后再删除
                arg = new Submit.BaseArg();
                arg.setIds(saveResult.getResult().getId());
                setInterfaceArg(arg,apiClient,objApiName,K3CSaveAction.CANCEL_ASSIGN);
                apiClient.cancelAssign(objApiName, arg);
                deleteFailedBill(apiClient, objApiName, saveResult.getResult().getId());
                audit.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s11,apiClient.getTenantId()) + auditErrMsg);
            }
        }

        Result<ErpIdResult> result = Result.copy(audit);
        result.setData(idResult.getData());

        return result;
    }

    private Result<ErpIdResult> submitWithoutAudit(K3CloudApiClient apiClient,
                                                   String objApiName,
                                                   SaveResult saveResult,
                                                   Result<ErpIdResult> idResult,
                                                   K3CreateConfig createConfig) {
        final Result<ErpIdResult> submit = submit(apiClient, objApiName, saveResult, idResult, createConfig.isDeleteBySubmitFail());
        if (submit.isSuccess()) {
            return idResult;
        }

        if (Objects.equals(createConfig.getReturnStepStatus(), K3DocumentStatusEnum.ADD.getStatus())) {
            idResult.setErrMsg(submit.getErrMsg());
            return idResult;
        }
        return submit;
    }

    private Result<ErpIdResult> submit(K3CloudApiClient apiClient,
                          String objApiName,
                          SaveResult saveResult,
                          Result<ErpIdResult> idResult,
                          boolean delFailBill) {
        if (!idResult.isSuccess()) {
            return idResult;
        }

        Submit.BaseArg arg = new Submit.BaseArg();
        arg.setIds(saveResult.getResult().getId());
        setInterfaceArg(arg,apiClient,objApiName,K3CSaveAction.SUBMIT);
        Result<Submit.Result> submit = apiClient.submit(objApiName, arg);

        String submitErrMsg = Submit.checkResult(submit);
        if (StringUtils.isNotEmpty(submitErrMsg)) {
            submit.setErrCode(ResultCodeEnum.K3C_BILL_SUBMIT_FAILED.getErrCode());
            if (delFailBill) {
                deleteFailedBill(apiClient, objApiName, saveResult.getResult().getId());
                submit.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s13,apiClient.getTenantId()) + submitErrMsg);
            } else {
                submit.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s12,apiClient.getTenantId()) + submitErrMsg);
            }
        }

        Result<ErpIdResult> result = Result.copy(submit);
        result.setData(idResult.getData());

        return result;
    }

    private void setInterfaceArg(Submit.BaseArg arg, K3CloudApiClient apiClient, String objApiName,K3CSaveAction action) {
        Map<String,Map<String,Map<String,Object>>> interfaceArgSettings=tenantConfigurationManager.getK3InterfaceArg(apiClient.getTenantId(),apiClient.getDataCenterId());
        if(interfaceArgSettings.containsKey(objApiName)
                &&interfaceArgSettings.get(objApiName).containsKey(action.getType())
                &&interfaceArgSettings.get(objApiName).get(action.getType())!=null){
            arg.putAll(interfaceArgSettings.get(objApiName).get(action.getType()));
        }
    }

//    public Result<Void> deleteErpObjData(SyncDataContextEvent doWriteMqData, ErpConnectInfoEntity connectInfo, String objAPIName, String dataId) {
//        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(connectInfo.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
//        deleteFailedBill(apiClient, objAPIName, dataId);
//        return Result.newSuccess();
//    }

    private void deleteFailedBill(K3CloudApiClient apiClient, String objApiName, String formId) {
        log.debug("K3DataManager.deleteFailedBill,objApiName={},formId={}",objApiName,formId);
        DeleteArg arg = new DeleteArg();
        arg.setIds(formId);
        Result<DeleteResult> deleteResult = apiClient.delete(objApiName, arg);
        log.info("K3DataManager.deleteFailedBill,objApiName={},formId={},deleteResult={}",objApiName,formId,deleteResult);
    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        log.debug("K3DataManager.createErpObjDetailData,doWriteMqData={}", doWriteMqData);
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(connectInfo.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
        String dataId = standardInvalidData.getMasterFieldVal().getId();
        String formId = standardInvalidData.getObjAPIName();
        K3DataConverter converter = k3DataManager.buildConverter(connectInfo.getTenantId(),apiClient.getDataCenterId(), formId);
        Submit.BaseArg arg = new Submit.BaseArg();
        ExcuteOperationArg excuteOperationArg = new ExcuteOperationArg();
        //根据id字段的viewCode判断是否使用id查看
        boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
        if (useId) {
            arg.setIds(dataId);
            excuteOperationArg.setIds(dataId);
        } else {
            List<String> numberList = Lists.newArrayList();
            numberList.add(dataId);
            arg.setNumbers(numberList);
            excuteOperationArg.setNumbers(numberList);
        }
        // 反审核
        setInterfaceArg(arg,apiClient,formId,K3CSaveAction.UN_AUDIT);
        Result<Submit.Result> unAudit = apiClient.unAudit(formId, arg);
        String unAuditErrMsg = Submit.checkResult(unAudit);
        // 作废
        /**
         * crm作废 ->k3c作废。
         * 但是k3c的客户和物料，联系人这种基础资料没有作废接口，只有禁用接口，
         * 所以如果对象是 客户和物料，则需要调用禁用接口。
         * */
        String operation = "Cancel";
        if (configCenterConfig.getK3ForbidOperationApiName().contains(formId)) {
            operation = "Forbid";
        }
        Object[] data = new Object[]{formId, operation, excuteOperationArg};
        Result<ExcuteOperationArg.Result> result = apiClient.excuteOperation(ErpObjInterfaceUrlEnum.invalid, formId, data);
        //保存接口日志
        String invalidErrMsg = ExcuteOperationArg.checkResult(result);
        if (invalidErrMsg != null) {
            if (unAuditErrMsg != null) {
                invalidErrMsg += unAuditErrMsg;
            }
            return Result.newError("s306240072", i18NStringManager.getByEi(I18NStringEnum.s14,apiClient.getTenantId()) + invalidErrMsg);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(connectInfo.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
        String dataId = standardDeleteData.getMasterFieldVal().getId();
        String formId = standardDeleteData.getObjAPIName();
        K3DataConverter converter = k3DataManager.buildConverter(connectInfo.getTenantId(),apiClient.getDataCenterId(), formId);
        Submit.BaseArg arg = new Submit.BaseArg();
        DeleteArg deleteArg = new DeleteArg();
        ExcuteOperationArg excuteOperationArg = new ExcuteOperationArg();
        //根据id字段的viewCode判断是否使用id查看
        boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
        if (useId) {
            arg.setIds(dataId);
            deleteArg.setIds(dataId);
            excuteOperationArg.setIds(dataId);
        } else {
            List<String> numberList = Lists.newArrayList();
            numberList.add(dataId);
            arg.setNumbers(numberList);
            deleteArg.setNumbers(numberList);
            excuteOperationArg.setNumbers(numberList);
        }
        // 反审核
        setInterfaceArg(arg,apiClient,formId,K3CSaveAction.UN_AUDIT);
        Result<Submit.Result> unAudit = apiClient.unAudit(formId, arg);
        String unAuditErrMsg = Submit.checkResult(unAudit);

        Result<DeleteResult> result = apiClient.delete(formId, deleteArg);
        //保存接口日志
        String deleteErrMsg = DeleteResult.checkDeleteResult(result);
        if (deleteErrMsg != null) {
            if (unAuditErrMsg != null) {
                deleteErrMsg += unAuditErrMsg;
            }
            return Result.newError("s306240072", i18NStringManager.getByEi(I18NStringEnum.s352,apiClient.getTenantId()) + ":"+deleteErrMsg);
        }
        return Result.newSuccess();
    }
    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return new Result<>();
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(connectInfo.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
        String dataId = standardRecoverData.getMasterFieldVal().getId();
        String formId = standardRecoverData.getObjAPIName();
        K3DataConverter converter = k3DataManager.buildConverter(connectInfo.getTenantId(),apiClient.getDataCenterId(), formId);

        ExcuteOperationArg excuteOperationArg = new ExcuteOperationArg();
        //根据id字段的viewCode判断是否使用id查看
        boolean useId = StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");
        if (useId) {
            excuteOperationArg.setIds(dataId);
        } else {
            List<String> numberList = Lists.newArrayList();
            numberList.add(dataId);
            excuteOperationArg.setNumbers(numberList);
        }
        // 反禁用,目前只有基础资料有反禁用接口
        String operation = "Enable";
        Object[] data = new Object[]{formId, operation, excuteOperationArg};
        Result<ExcuteOperationArg.Result> result = apiClient.excuteOperation(ErpObjInterfaceUrlEnum.recover, formId, data);
        //保存接口日志
        String recoverErrMsg = ExcuteOperationArg.checkResult(result);
        if (recoverErrMsg != null) {
            return new Result(ResultCodeEnum.ENABLE_FAIL, recoverErrMsg);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        log.debug("K3DataManager.updateErpObjData,standardData={}",standardData);
        log.debug("K3DataManager.updateErpObjData,connectInfo={}",JacksonUtil.toJson(connectInfo));
        String tenantId = connectInfo.getTenantId();

        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(tenantId, connectInfo.getConnectParams(), connectInfo.getId());

        if (StringUtils.equalsIgnoreCase(standardData.getMasterFieldVal().getApiName(), K3CloudForm.SAL_SaleOrder)) {
            boolean useDirectOrderChange = configCenterConfig.getUseDirectOrderChange(tenantId);
            log.debug("K3DataManager.updateErpObjData,useDirectOrderChange={}", useDirectOrderChange);
            if(!useDirectOrderChange) {
                String saleOrgNumber = standardData.getMasterFieldVal().getString("FSaleOrgId.FNumber");
                K3SOChangeTypeEnum k3SOChangeTypeEnum = K3SystemParameterUtil.getK3SoChangeType(apiClient, saleOrgNumber);
                log.debug("K3DataManager.updateErpObjData,k3SOChangeTypeEnum={}", k3SOChangeTypeEnum);
                switch (k3SOChangeTypeEnum) {
                    case ENABLE_SO_NEW_CHANGE_BILL:
                        return updateSalesOrder(tenantId, standardData, apiClient);
                    case ENABLE_SO_CHANGE_BILL:
                        throw new RuntimeException(i18NStringManager.getByEi(I18NStringEnum.s15,apiClient.getTenantId()));
                }
            }
        }

        String objApiName = standardData.getMasterFieldVal().getApiName();
        String dataId = standardData.getMasterFieldVal().getId();
        SaveArg saveArg = new SaveArg();
        //更新数据不能提交和审核
        saveArg.setIsAutoSubmitAndAudit(false);
        K3DataConverter saveConverter = buildSaveConverter(tenantId, apiClient.getDataCenterId(), standardData);
        //转换参数
        saveConverter.fillSaveArg(standardData, saveArg,tenantId);
        // 查看对象前的特殊逻辑
        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(objApiName);
        //保留这一句，如果有保存参数可以在这里面存
        IdSaveExtend saveExtend = IdSaveExtend.of(objApiName, saveConverter.getIdFieldExtend().getSaveExtend());
        specialBusiness.beforeRunUpdate(saveArg, standardData, saveConverter, apiClient,saveExtend);
        if (!StringUtils.isEmpty(saveExtend.getSubSystemId())) {
            saveArg.setSubSystemId(saveExtend.getSubSystemId());
        } else if(StringUtils.equalsIgnoreCase(objApiName,K3CloudForm.CRM_Contract)) {
                //销售合同同步，默认预置子系统ID
                saveArg.setSubSystemId("23");
        }
        if (saveExtend.getIsEntryBatchFill() != null) {
            saveArg.setIsEntryBatchFill(saveExtend.getIsEntryBatchFill());
        }
        if (saveExtend.getIsVerifyBaseDataField() != null && (saveArg.getIsVerifyBaseDataField() == null||!saveArg.getIsVerifyBaseDataField())) {//如果saveArg的IsVerifyBaseDataField为空或者为默认值false，才以saveExtend的IsVerifyBaseDataField为准
            saveArg.setIsVerifyBaseDataField(saveExtend.getIsVerifyBaseDataField());
        }
        log.debug("K3DataManager.updateErpObjData,saveArg={}", saveArg);
        Result<SaveResult> saveResult;

        boolean unAuditBeforeSave = tenantConfigurationManager.inGroupWhiteList(tenantId,objApiName,TenantConfigurationTypeEnum.unAuditSaveEiObjectList);
        log.debug("K3DataManager.updateErpObjData,unAuditBeforeSave={}", unAuditBeforeSave);

        boolean needSubmitAndAudit = false;
        boolean useId = StringUtils.equalsIgnoreCase(saveConverter.getIdFieldExtend().getViewCode(), "Id");
        if(unAuditBeforeSave) {
            String documentStatus = getDocumentStatus(apiClient, objApiName, dataId, useId);
            log.debug("K3DataManager.updateErpObjData,documentStatus={}", documentStatus);
            if(StringUtils.equalsIgnoreCase(documentStatus,"C")) {
                Submit.BaseArg baseArg = new Submit.BaseArg();
                if (useId) {
                    baseArg.setIds(dataId);
                } else {
                    baseArg.setNumbers(Lists.newArrayList(dataId));
                }
                // 反审核
                setInterfaceArg(baseArg,apiClient,objApiName,K3CSaveAction.UN_AUDIT);
                Result<Submit.Result> unAudit = apiClient.unAudit(objApiName, baseArg);
                String unAuditErrMsg = Submit.checkResult(unAudit);
                if(unAudit.isSuccess() && StringUtils.isEmpty(unAuditErrMsg)) {
                    //更新时不再调用暂存接口，统一调用保存接口
                    needSubmitAndAudit = true;
                }
            }
        } else if (StringUtils.isNotBlank(saveExtend.getModifyStatus()) && !StringUtils.equalsIgnoreCase(saveExtend.getModifyStatus(), K3DocumentStatusEnum.AUDITED.getStatus())) {
            // 需要先反审核/反撤销 在修改数据
            String documentStatus = getDocumentStatus(apiClient, objApiName, dataId, useId);
            Map<String,Map<String,Map<String,Object>>> interfaceArgSettings=tenantConfigurationManager.getK3InterfaceArg(apiClient.getTenantId(),apiClient.getDataCenterId());
            final Result<Void> result = K3DocumentStatusEnum.changeStatusBeforeSave(documentStatus, saveExtend.getModifyStatus(), apiClient, objApiName, dataId, useId,interfaceArgSettings);
            log.debug("K3DocumentStatusEnum.changeStatusBeforeSave,id={},documentStatus={},modifyStatus={},idResult={}", dataId,documentStatus, saveExtend.getModifyStatus(), result.getErrMsg());
            if (!result.isSuccess()) {
                return Result.copy(result);
            }
        }

        //更新时不再调用暂存接口，统一调用保存接口
        saveResult = apiClient.save(ErpObjInterfaceUrlEnum.update, objApiName, saveArg);
        //保存接口日志
        if (!saveResult.isSuccess()) {
            return Result.copy(saveResult);
        }
        Result<ErpIdResult> idResult = saveConverter.convertSaveResult(tenantId,saveResult.getData());

        // 修改单据状态
        if (StringUtils.isNotBlank(saveExtend.getModifyStatus())) {
            final String id = saveResult.getData().getResult().getId();
            final String documentStatus = getDocumentStatus(apiClient, objApiName, id, true);
            Map<String,Map<String,Map<String,Object>>> interfaceArgSettings=tenantConfigurationManager.getK3InterfaceArg(apiClient.getTenantId(),apiClient.getDataCenterId());
            K3DocumentStatusEnum.changeStatus(documentStatus, saveExtend.getModifyStatus(), apiClient, objApiName, id, idResult,interfaceArgSettings);
            log.debug("K3DocumentStatusEnum.changeStatus,id={},documentStatus={},modifyStatus={},idResult={}", id,documentStatus, saveExtend.getModifyStatus(), idResult.getErrMsg());
        } else if (needSubmitAndAudit) {
            //兼容老的k3c，提交和审核单独调用
            idResult = submitAndAudit(apiClient, objApiName, saveResult.getData(), idResult, K3CreateConfig.DefaultUpdateConfig);
        }

        return idResult;
    }

    private static String getDocumentStatus(final K3CloudApiClient apiClient, final String objApiName, final String dataId, final boolean useId) {
        //根据id字段的viewCode判断是否使用id查看
        ViewArg viewArg = new ViewArg();
        if (useId) {
            viewArg.setId(dataId);
        } else {
            viewArg.setNumber(dataId);
        }
        String documentStatus = "";
        try {
            //查询ERP单据状态，并根据状态来判断保存时是否需要自动提交审核
            Result<ViewResult> viewResult = apiClient.view(objApiName, viewArg);
            if(viewResult.isSuccess()
                    && viewResult.getData()!=null
                    && viewResult.getData().getResult()!=null
                    && viewResult.getData().getResult().getResult()!=null) {
                documentStatus = viewResult.getData().getResult().getResult().entrySet().stream()
                        .filter(entry -> StringUtils.equalsIgnoreCase(entry.getKey(), "DocumentStatus") || StringUtils.equalsIgnoreCase(entry.getKey(), "FDocumentStatus"))
                        .findFirst()
                        .map(Map.Entry::getValue)
                        .map(String::valueOf)
                        .orElse("");
            }
        } catch (Exception e) {
            log.warn("K3DataManager.updateErpObjData,get documentStatus error, objApiName:{}, viewArg:{}", objApiName, viewArg, e);
        }
        return documentStatus;
    }

    private Result<ErpIdResult> updateSalesOrder(final String tenantId, StandardData standardData, K3CloudApiClient apiClient) {
        log.debug("K3DataManager.updateSalesOrder,standardData={}", standardData);
        String objApiName = K3CloudForm.SAL_XORDER;
        String dataId = standardData.getMasterFieldVal().getId();
        String dataName = standardData.getMasterFieldVal().getName();

        log.debug("K3DataManager.updateSalesOrder,objApiName={},dataId={},dataName={}", objApiName, dataId, dataName);

        SaveArg saveArg = new SaveArg();
        K3DataConverter saveConverter = buildSaveConverter(tenantId, apiClient.getDataCenterId(), standardData);
        //转换参数
        saveConverter.fillSaveArg(standardData, saveArg,tenantId);
        //更新数据不能提交和审核
        saveArg.setIsAutoSubmitAndAudit(true);

        // 查看对象前的特殊逻辑
        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(objApiName);
        log.debug("K3DataManager.updateSalesOrder.specialBusiness={}", specialBusiness);
        IdSaveExtend saveExtend = IdSaveExtend.of(objApiName, saveConverter.getIdFieldExtend().getSaveExtend());
        specialBusiness.beforeRunUpdate(saveArg, standardData, saveConverter, apiClient, saveExtend);

        //更新的订单明细列表
        // List<K3Model> updateEntryList = new ArrayList<>();
        // saveArg.getModel().getDetails("FSaleOrderEntry").forEach(k3Model -> {
        //     log.debug("K3DataManager.updateSalesOrder.k3Model={}", JSONObject.toJSONString(k3Model));
        //     String FChangeType = k3Model.getString("FChangeType");
        //     if(StringUtils.equalsIgnoreCase(FChangeType,"B")) {
        //         updateEntryList.add(k3Model);
        //     }
        // });

        Result<SaveResult> saveResult;

        log.debug("K3DataManager.updateSalesOrder,saveArg={}", saveArg);
        SaveArg saveArg2 = saveArg;
        List<String> saleOrderPlanRemoveFEntryIdEiList = tenantConfigurationManager.getSaleOrderPlanRemoveFEntryIdEiList();

        boolean removeSaleOrderFEntryId = false;
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(saleOrderPlanRemoveFEntryIdEiList)
                && saleOrderPlanRemoveFEntryIdEiList.contains(tenantId)
                && saveArg.getModel().containsKey("FSaleOrderPlan")) {
            // 支持销售订单整单赠送的场景
            removeSaleOrderFEntryId = true;
            saveArg2 = new SaveArg();
            BeanUtils.copyProperties(saveArg,saveArg2);

            List<K3Model> saleOrderPlanModelList = (List<K3Model>)saveArg2.getModel().get("FSaleOrderPlan");

            saleOrderPlanModelList.forEach((k3Model -> k3Model.remove("FEntryID")));
        }
        log.debug("K3DataManager.updateSalesOrder,saveArg2={}", saveArg2);
        String saveArgJson = JSONObject.toJSONString(saveArg2);
        log.debug("K3DataManager.updateSalesOrder.saveArgJson={}", saveArgJson);

        if (saveExtend.getUseDraft()) {
            //暂存
            saveResult = apiClient.draft(objApiName, saveArg2);
        } else {
            //保存
            saveResult = apiClient.save(ErpObjInterfaceUrlEnum.update, objApiName, saveArg2);
        }
        log.debug("K3DataManager.updateSalesOrder,saveResult={}", saveResult);
        //保存接口日志
        //固定为订单，但是传参为订单新变更单
        Result<ErpIdResult> idResult = saveConverter.convertSaveResult(tenantId,saveResult.getData());
        if (!idResult.isSuccess()) {
            String fid = null;
            for(String key : saveArg2.getModel().keySet()) {
                if(StringUtils.equalsIgnoreCase(key,"FID")) {
                    fid = saveArg2.getModel().getString(key);
                    break;
                }
            }
            if(StringUtils.isNotEmpty(fid)) {
                deleteFailedBill(apiClient,objApiName,fid);
            }
            return Result.copy(idResult);
        }
        log.debug("K3DataManager.updateSalesOrder,idResult={}", idResult);
        List<String> entryIdList = new ArrayList<>();
        Object salesOrderId2XOrderIdObj = standardData.getMasterFieldVal().get("salesOrderId2XOrderId");

        //客户的k3c系统低于7.6版本
        if(idResult.isSuccess() && salesOrderId2XOrderIdObj==null) {
            ViewArg viewArg=new ViewArg();
            viewArg.setId(saveConverter.getDataId(saveResult.getData(),tenantId));
            Result<ViewResult> xOrderViewResult = apiClient.view(K3CloudForm.SAL_XORDER, viewArg);
            Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = XOrderUtils.getSalesOrderId2XOrderId(xOrderViewResult.getData());
            salesOrderId2XOrderIdObj = salesOrderId2XOrderId;
        }

        if (idResult.isSuccess() && salesOrderId2XOrderIdObj!=null) {
            List<K3Model> totalDataList = new ArrayList<>();
            int limit = 2000;
            //分页查询订单明细数据
            for (int i = 0; ; i += limit) {
                QueryArg queryArg = new QueryArg();
//                订单明细和订单计划明细 不支持复合id,只支持默认的id字段
                final List<String> fieldKeys = Lists.newArrayList("FID", "FBillNo", "FSaleOrderEntry_FEntryID", "FSaleOrderPlan_FEntryID", "FMaterialId.FNumber");
//                BillQuery和save的code一样,所以直接拿saveCode查询和取值来计算复合id
                fieldKeys.addAll(saveConverter.getSaveCompositeFields().getSaveCodes());
                queryArg.setFieldKeys(fieldKeys.stream().distinct().collect(Collectors.joining(",")));
                queryArg.setFormId(K3CloudForm.SAL_SaleOrder);
                queryArg.setFilterString("FBillNo='" + standardData.getMasterFieldVal().getId().split("#")[1] + "'");
                queryArg.setStartRow(i);
                queryArg.setLimit(limit);
                commonBusinessManager.fillQueryArgByViewExtend(tenantId, apiClient.getDataCenterId(), queryArg);
                Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
                if (result.isSuccess()) {
                    totalDataList.addAll(result.getData());
                    if (result.getData().size() < limit) {
                        break;
                    }
                } else {
                    break;
                }
            }

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(totalDataList)) {
                idResult.getData().setMasterDataId(saveConverter.getMasterDataId(totalDataList.get(0)));
                idResult.getData().setMasterDataName(saveConverter.getMasterDataName(totalDataList.get(0)));
                Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = (Map<String, Map<Integer, Integer>>)salesOrderId2XOrderIdObj;

                //获取销售订单明细id
                List<String> entryList = idResult.getData().getDetailDataIds().get("SAL_SaleOrder.SaleOrderEntry");
                for(K3Model entry : totalDataList) {
                    Integer salesOrderEntryId = entry.getInt("FSaleOrderEntry_FEntryID");
                    Integer xOrderEntryId = salesOrderId2XOrderId.get("detail").get(salesOrderEntryId);
                    if(xOrderEntryId==null || entryList.contains(xOrderEntryId+"")) {
                        if (!entryIdList.contains(salesOrderEntryId+"")){
                            entryIdList.add(salesOrderEntryId+"");
                        }
                    }
                }
                entryList.clear();
                entryList.addAll(entryIdList);
                log.debug("K3DataManager.updateSalesOrder,entryList={}", entryList);

                //获取销售订单计划明细id
                List<String> orderPlanEntryList = idResult.getData().getDetailDataIds().get("SAL_SaleOrder.SaleOrderPlan");
                log.debug("K3DataManager.updateSalesOrder.orderPlanEntryList={}", orderPlanEntryList);
                if(!removeSaleOrderFEntryId) {
                    List<String> newOrderPlanEntryIdList = new ArrayList<>();
                    if (orderPlanEntryList==null){
                        orderPlanEntryList=new ArrayList<>();
                    }
                    for(K3Model entry : totalDataList) {
                        Integer orderPlanEntryId=  entry.getInt("FSaleOrderPlan_FEntryID");
                        Integer xOrderPlanEntryId = salesOrderId2XOrderId.get("SaleOrderPlan").get(orderPlanEntryId);
                        log.debug("K3DataManager.updateSalesOrder.xOrderPlanEntryId={},orderPlanEntryId={}", xOrderPlanEntryId,orderPlanEntryId);
                        if(xOrderPlanEntryId==null || orderPlanEntryList.contains(xOrderPlanEntryId+"")) {
                            if (!newOrderPlanEntryIdList.contains(orderPlanEntryId+"")){
                                newOrderPlanEntryIdList.add(orderPlanEntryId+"");
                            }
                        }
                    }
                    log.debug("K3DataManager.updateSalesOrder.newOrderPlanEntryIdList={}", newOrderPlanEntryIdList);
                    orderPlanEntryList.clear();
                    orderPlanEntryList.addAll(newOrderPlanEntryIdList);
                }
            }
        }
        if(!idResult.isSuccess() && salesOrderId2XOrderIdObj==null) {
            throw new SyncDataException(com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum.K3CLOUD_NOT_SUPPORT_XORDER);
        }
        log.debug("K3DataManager.updateSalesOrder,idResult2={}", idResult);
        return idResult;
    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        log.debug("K3DataManager.updateErpObjDetailData,doWriteMqData={}", doWriteMqData);
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**
     * 构造保存接口转换器
     *
     * @param tenantId
     * @param standardData
     * @return
     */
    public K3DataConverter buildSaveConverter(String tenantId, String dataCenterId, StandardData standardData) {
        final String realApiName = standardData.getObjAPIName();
        List<ErpFieldExtendEntity> masterFieldExtends = erpFieldManager.queryAllFieldExtend(tenantId, dataCenterId, realApiName);
        ErpFieldExtendEntity idFieldExtend = K3DataConverter.getIdFieldExtend(tenantId,masterFieldExtends);
        List<ErpFieldExtendEntity> numFieldExtend = masterFieldExtends.stream().filter(extend -> "e12".equals(extend.getErpFieldType())).collect(Collectors.toList());
        //key:明细的apiName，value:明细的信息
        Map<String, ErpFieldExtendEntity> masterDetailFieldMap = masterFieldExtends.stream()
                .filter(v -> v.getFieldDefineType().equals(ErpFieldTypeEnum.detail))
                .collect(Collectors.toMap(ErpFieldExtendEntity::getFieldApiName, v -> v, (u, v) -> u));
        Map<String, List<ErpFieldExtendEntity>> detailFieldExtendMap = new HashMap<>();
        for (String detailApiName : standardData.getDetailFieldVals().keySet()) {
            ErpFieldExtendEntity masterDetailField = masterDetailFieldMap.get(detailApiName);
            if (masterDetailField == null || StringUtils.isBlank(masterDetailField.getSaveCode())) {
                throw new ErpSyncDataException(ResultCodeEnum.FIELD_CONVERT_EXCEPTION, i18NStringManager.getByEi(I18NStringEnum.s16,tenantId) + detailApiName);
            }
            List<ErpFieldExtendEntity> detailFields = erpFieldManager.queryAllFieldExtend(tenantId, dataCenterId, detailApiName);
            detailFieldExtendMap.put(detailApiName, detailFields);
        }

        K3DataConverter k3DataConverter = new K3DataConverter();
        Map<String,Object> saveArgSettings = getSaveArgSettings(tenantId,dataCenterId,realApiName);
        if(ObjectUtils.isNotEmpty(saveArgSettings)){
            k3DataConverter.setSaveArgSettings(saveArgSettings);
        }
        if(!CollectionUtils.isEmpty(numFieldExtend)){
            k3DataConverter.setNumFieldExtend(numFieldExtend.get(0));
        }

//        设置各对象的主键
//      主对象id字段
        final String splitObjApiName = erpObjManager.getMasterSplitObjApiName(tenantId, dataCenterId, realApiName);
        final K3DataConverter.SaveCompositeFields saveCompositeFields = getSaveCompositeFields(tenantId, masterFieldExtends, splitObjApiName);
//        从对象id字段
        Map<String, K3DataConverter.SaveCompositeFields> detailSaveCompositeFields = new HashMap<>();
        if (MapUtils.isNotEmpty(standardData.getDetailFieldVals())) {
            final List<ErpObjectEntity> erpObjectEntities = erpObjectDao.queryByRealObjApiName(tenantId, dataCenterId, realApiName);
            final Set<String> detailApiNames = standardData.getDetailFieldVals().keySet();
            detailSaveCompositeFields = erpObjectEntities.stream()
                    .filter(v -> detailApiNames.contains(v.getErpObjectExtendValue()))
                    .collect(Collectors.toMap(
                            ErpObjectEntity::getErpObjectExtendValue,
                            v -> getSaveCompositeFields(tenantId, detailFieldExtendMap.get(v.getErpObjectExtendValue()), v.getErpObjectApiName())));
        }

        k3DataConverter.setSaveCompositeFields(saveCompositeFields);
        k3DataConverter.setDetailSaveCompositeFields(detailSaveCompositeFields);
        k3DataConverter.setMasterFieldExtends(masterFieldExtends);
        k3DataConverter.setMasterDetailFieldMap(masterDetailFieldMap);
        k3DataConverter.setDetailFieldExtendMap(detailFieldExtendMap);
        k3DataConverter.setIdFieldExtend(idFieldExtend);
        k3DataConverter.setI18NStringManager(i18NStringManager);
        k3DataConverter.setTenantConfigurationManager(tenantConfigurationManager);
        return k3DataConverter;
    }

    private Map<String, Object> getSaveArgSettings(String tenantId, String dataCenterId, String realApiName) {
        ErpTenantConfigurationEntity erpTenantConfigurationEntity = tenantConfigurationManager.findOne(tenantId, dataCenterId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_SAVE_ARG_SETTING.name());
        if (ObjectUtils.isNotEmpty(erpTenantConfigurationEntity)) {//全企业的
            String configuration = erpTenantConfigurationEntity.getConfiguration();
            return JSONObject.parseObject(configuration, Map.class);
        }
        erpTenantConfigurationEntity = tenantConfigurationManager.findOne(tenantId, dataCenterId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_SAVE_ARG_SETTING.name());
        if (ObjectUtils.isNotEmpty(erpTenantConfigurationEntity)) {//按企业对象的
            String configuration = erpTenantConfigurationEntity.getConfiguration();
            Map<String, Map<String, Object>> result = JSONObject.parseObject(configuration, Map.class);
            if (MapUtils.isNotEmpty(result)) {
                return result.get(realApiName);
            }
        }
        return null;
    }

    @NotNull
    private K3DataConverter.SaveCompositeFields getSaveCompositeFields(String tenantId, List<ErpFieldExtendEntity> fieldExtends, String splitObjApiName) {
        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, splitObjApiName);
        CompositeIdExtend idExtent = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
        final List<String> idFieldApiNames = idExtent.isComposite() ? idExtent.getCompositeFields() : Lists.newArrayList(idField.getFieldApiName());
//        转为saveCode
        final List<String> saveCodes = idFieldApiNames.stream()
                .map(v -> fieldExtends.stream()
                        .filter(field -> field.getFieldApiName().equals(v))
                        .findFirst()
                        .map(ErpFieldExtendEntity::getSaveCode)
                        .orElseThrow(() -> new ErpSyncDataException(ResultCodeEnum.NOT_SET_FIELD, i18NStringManager.getByEi2(I18NStringEnum.s17.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s17.getI18nValue(), splitObjApiName),
                                Lists.newArrayList(splitObjApiName)) + v)))
                .collect(Collectors.toList());
        return new K3DataConverter.SaveCompositeFields(saveCodes, idExtent.getSeparator());
    }

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public K3DataConverter buildConverter(String tenantId,String dataCenterId, String objApiName) {
        List<ErpFieldExtendEntity> masterFieldExtends = erpFieldManager.getAllNeedQueryFieldExtend(tenantId, dataCenterId, objApiName);
        //key:明细的apiName，value:明细的信息
        Map<String, ErpFieldExtendEntity> masterDetailFieldMap = masterFieldExtends.stream()
                .filter(v -> v.getFieldDefineType().equals(ErpFieldTypeEnum.detail))
                .collect(Collectors.toMap(ErpFieldExtendEntity::getFieldApiName, v -> v, (u, v) -> u));
        Map<String, List<ErpFieldExtendEntity>> detailFieldExtendMap = new HashMap<>();
        for (String detailApiName : masterDetailFieldMap.keySet()) {
            List<ErpFieldExtendEntity> detailFields = erpFieldManager.getAllNeedQueryFieldExtend(tenantId,dataCenterId, detailApiName);
            detailFieldExtendMap.put(detailApiName, detailFields);
        }
        K3DataConverter k3DataConverter = new K3DataConverter();
        k3DataConverter.setMasterFieldExtends(masterFieldExtends);
        k3DataConverter.setMasterDetailFieldMap(masterDetailFieldMap);
        k3DataConverter.setDetailFieldExtendMap(detailFieldExtendMap);
        ErpFieldExtendEntity idFieldExtend = K3DataConverter.getIdFieldExtend(tenantId,masterFieldExtends);
        k3DataConverter.setIdFieldExtend(idFieldExtend);
        Map<String, ErpFieldExtendEntity> detailIdFieldExtendMap= Maps.newHashMap();
        for(String detailObjApiName:detailFieldExtendMap.keySet()){
            if(!CollectionUtils.isEmpty(detailFieldExtendMap.get(detailObjApiName))){
                try{
                    ErpFieldExtendEntity detailIdFieldExtend = K3DataConverter.getIdFieldExtend(tenantId,detailFieldExtendMap.get(detailObjApiName));
                    detailIdFieldExtendMap.put(detailObjApiName,detailIdFieldExtend);
                }catch (Exception e){
                    log.warn("tenantId={},objApiName={},找不到id字段,或者id字段保存查看编码为空",tenantId,detailObjApiName);
                }

            }
        }
        k3DataConverter.setDetailIdFieldExtend(detailIdFieldExtendMap);
        return k3DataConverter;
    }

    /**
     * 直接查询K3对象，
     * 注：返回值的key为filterKey
     *
     * @param tenantId
     * @param queryArg
     * @return
     */
    @InvokeMonitor(tenantId = "#tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.ERP, count = "#result?.getData()?.size()?:0", objAPIName = "#queryArg.formId", action = ActionEnum.QUERY, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<List<K3Model>> queryK3ObjData(String tenantId, String dataCenterId, QueryArg queryArg) {
        commonBusinessManager.fillQueryArgByViewExtend(tenantId, dataCenterId, queryArg);

        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(tenantId, connectInfo.getConnectParams(), connectInfo.getId());
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        return result;
    }

    /**
     * 直接查询K3对象，所有数据
     * 注：返回值的key为filterKey
     *
     * @param tenantId
     * @param queryArg
     * @return
     */
    @InvokeMonitor(tenantId = "#tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.ERP, count = "#result?.getData()?.size()?:0", objAPIName = "#queryArg.formId", action = ActionEnum.QUERY, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<List<K3Model>> queryK3ObjDataALl(String tenantId, String dataCenterId, QueryArg queryArg) {
        commonBusinessManager.fillQueryArgByViewExtend(tenantId, dataCenterId, queryArg);

        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(tenantId, connectInfo.getConnectParams(), connectInfo.getId());
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        return result;
    }

    /**
     * 用户的处理逻辑
     * @param connectInfo
     * @param oldValue
     * @param fieldKey
     * @param objectData
     * @param crm2erp
     */
    public void covertFieldValueUser(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp) {
        log.info("SpecialFieldPreprocessManager.covertFieldValueUser,oldValue={},fieldKey={},crm2erp={},objectData={}",
                oldValue,fieldKey,crm2erp,objectData);
        if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
            String oldStr = oldValue.toString();
            if (oldValue instanceof List) {//list->string
                List<Object>list= (List) oldValue;
                List<String> oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(oldList)) {
                    oldStr = oldList.get(0);
                }
            }
            if (crm2erp) {
                List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                        erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(), connectInfo.getId(),
                                ErpFieldTypeEnum.employee, oldStr, null);
                //todo 这个后续需要去掉
                //兼容老逻辑，暂时保留这段代码，因为涉及到刷库
                boolean fieldKeyUpdated = false;
                if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities)
                        && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getErpDataId())) {
                    String json = erpFieldDataMappingEntities.get(0).getFieldDataExtendValue();

                    if (StringUtils.isNotEmpty(json)) {
                        Map<String, Object> result = JacksonUtil.fromJson(json, Map.class);
                        String newValue = String.valueOf(result.get("erpUserId"));
                        log.info("SpecialFieldPreprocessManager.covertFieldValueUser,crm->erp,old,oldStr={},newValue={}",
                                oldStr,newValue);
                        if(StringUtils.isNotEmpty(newValue)) {
                            objectData.put(fieldKey, newValue);
                            fieldKeyUpdated = true;
                            //return;
                        }
                    }
                }
                erpFieldDataMappingEntities =
                        erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(), connectInfo.getId(),
                                ErpFieldTypeEnum.user, oldStr, null);
                log.info("SpecialFieldPreprocessManager.covertFieldValueUser,crm->erp,oldStr={},erpFieldDataMappingEntities={}",
                        oldStr,erpFieldDataMappingEntities);
                if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities)
                        && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getErpDataId())) {
                    String newValue = erpFieldDataMappingEntities.get(0).getErpDataId();
                    log.info("SpecialFieldPreprocessManager.covertFieldValueUser,crm->erp,new,oldStr={},newValue={}",
                            oldStr,newValue);
                    if(StringUtils.isNotEmpty(newValue)) {
                        objectData.put(fieldKey, newValue);
                        return;
                    }
                }
                if(!fieldKeyUpdated) {
                    objectData.put(fieldKey, null);
                }
            } else {
                List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                        erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(), connectInfo.getId(),
                                ErpFieldTypeEnum.employee, null, null);
                //todo 这个后续需要去掉
                //兼容老逻辑，暂时保留这段代码，因为涉及到刷库
                boolean fieldKeyUpdated = false;
                for (ErpFieldDataMappingEntity erpFieldDataMappingEntity : erpFieldDataMappingEntities) {
                    String json = erpFieldDataMappingEntity.getFieldDataExtendValue();
                    Map<String, Object> jsonMap = JacksonUtil.fromJson(json, Map.class);
                    if (oldStr.equals(String.valueOf(jsonMap.get("erpUserId")))) {
                        String newValue = erpFieldDataMappingEntity.getFsDataId();
                        log.info("SpecialFieldPreprocessManager.covertFieldValueUser,erp->crm,old,oldStr={},newValue={}",
                                oldStr,newValue);
                        if(StringUtils.isNotEmpty(newValue)) {
                            List<String> newValueList = Lists.newArrayList();
                            newValueList.add(newValue);
                            objectData.put(fieldKey, newValueList);
                            fieldKeyUpdated = true;
                            //return;
                        }
                    }
                }

                erpFieldDataMappingEntities =
                        erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(), connectInfo.getId(),
                                ErpFieldTypeEnum.user, null, oldStr);
                log.info("SpecialFieldPreprocessManager.covertFieldValueUser,erp->crm,oldStr={},erpFieldDataMappingEntities={}",
                        oldStr,erpFieldDataMappingEntities);
                if(CollectionUtils.isNotEmpty(erpFieldDataMappingEntities)) {
                    String newValue = erpFieldDataMappingEntities.get(0).getFsDataId();
                    log.info("SpecialFieldPreprocessManager.covertFieldValueUser,erp->crm,new,oldStr={},newValue={}",
                            oldStr,newValue);
                    if(StringUtils.isNotEmpty(newValue)) {
                        List<String> newValueList = Lists.newArrayList();
                        newValueList.add(newValue);
                        objectData.put(fieldKey, newValueList);
                        return;
                    }
                }

                if(!fieldKeyUpdated) {
                    objectData.put(fieldKey, new ArrayList<>());
                }
            }
        }
        objectData.put(fieldKey, Lists.newArrayList("-10000"));
    }
    /**
     * 根据字段类型获取业务员编号
     */
    @InvokeMonitor(tenantId = "#tenantId", dcId = "#dataCenterId", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "'BD_OPERATOR'", action = ActionEnum.GET, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public String getK3OperatorNo(String tenantId, String dataCenterId, String empNo, String fieldExtendValue, String orgNo, String deptName) {
        log.debug("K3DataManager.getK3OperatorNo,tenantId={},empNo={},fieldExtendValue={},orgNo={},deptName={}", tenantId,
                empNo, fieldExtendValue, orgNo, deptName);
        K3FieldExtend.EmpInfo empInfo;
        if (StringUtils.isNotBlank(fieldExtendValue)) {
            try {
                empInfo = JacksonUtil.fromJson(fieldExtendValue, K3FieldExtend.EmpInfo.class);
            } catch (Exception e) {
                log.error("field extend error", e);
                return empNo;
            }
            if (K3FieldExtend.OperatorTypeEnum.EMP.equals(empInfo.getOperatorType())) {
                //员工类型直接返回
                return empNo;
            }
        }
        //没储存业务员信息会取第一个
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FEmpNumber,FNumber,FOperatorType");
        queryArg.setFormId(ObjectApiNameEnum.K3CLOUD_OPERATOR.getObjApiName());
        queryArg.appendEqualFilter("FForbiddenStatus", "0");
        queryArg.appendEqualFilter("FEmpNumber", empNo);
        if (StringUtils.isNotBlank(orgNo)) {
            queryArg.appendEqualFilter("FBizOrgId.FNumber", orgNo);
        }
        if (StringUtils.isNotBlank(deptName)) {
            queryArg.appendEqualFilter("Fdept", deptName);
        }
        Result<List<K3Model>> result = queryK3ObjData(tenantId, dataCenterId, queryArg);
        log.debug("K3DataManager.getK3OperatorNo,result={}", result);
        if (result.getData() != null && result.getData().size() > 0) {
            //默认先取销售员
            List<K3Model> xsyList = result.getData().stream().filter(k3Model ->
                    ("XSY".equalsIgnoreCase(String.valueOf(k3Model.get("FOperatorType").toString())))).collect(Collectors.toList());
            log.debug("K3DataManager.getK3OperatorNo,xsyList={}", xsyList);
            if(xsyList.size() > 0){
                String number = String.valueOf(xsyList.get(0).get("FNumber"));
                log.debug("K3DataManager.getK3OperatorNo,number={}", number);
                return number;
            }
            String number = String.valueOf(result.getData().get(0).get("FNumber"));
            log.debug("K3DataManager.getK3OperatorNo,number={}", number);
            return number;
        }
        return empNo;
    }

    /**
     * 分页轮询场景，存储的是上一页最后一条数据的ID和编码信息
     */
    @Data
    public static class LastErpDataModel implements Serializable {
        /**
         * ERP数据ID
         */
        private String erpId;
        /**
         * ERP数据编码
         */
        private String erpNum;
    }
}
