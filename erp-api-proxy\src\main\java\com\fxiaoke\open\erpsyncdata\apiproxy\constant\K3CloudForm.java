package com.fxiaoke.open.erpsyncdata.apiproxy.constant;


/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/24 14:35
 * @<NAME_EMAIL>
 * @version 1.0
 */
public interface K3CloudForm {

    /** 客户 BD_Customer */
    String BD_Customer = "BD_Customer";

    /** 联系人 CONTACT */
    String BD_CommonContact = "BD_CommonContact";

    /** 应收单 AR_receivable */
    String AR_receivable = "AR_receivable";

    /** 员工 BD_Empinfo */
    String BD_Empinfo = "BD_Empinfo";

    /** 员工任岗 BD_NEWSTAFF */
    String BD_NEWSTAFF = "BD_NEWSTAFF";

    /** 业务员 BD_OPERATOR */
    String BD_OPERATOR = "BD_OPERATOR";

    /** 产品 BD_MATERIAL */
    String BD_MATERIAL = "BD_MATERIAL";

    /**
     * 产品分组（物料分组）
     */
    String SAL_MATERIALGROUP="SAL_MATERIALGROUP";

    /** 组织 ORG_Organizations */
    String ORG_Organizations = "ORG_Organizations";

    /** 订单 SAL_SaleOrder */
    String SAL_SaleOrder = "SAL_SaleOrder";

    /** 销售订单变更单 SAL_SaleOrderChange */
    String SAL_SALE_ORDER_CHANGE = "SAL_SaleOrderChange";

    /** 销售订单新变更单 */
    String SAL_XORDER = "SAL_XORDER";

    /** 回款-收款单 AR_RECEIVEBILL */
    String AR_RECEIVEBILL = "AR_RECEIVEBILL";

    /** 收款结算单 */
    String SC_RECEIVESETTLEBILL = "SC_RECEIVESETTLEBILL";

    /** 库存-即时库存 STK_Inventory */
    String STK_Inventory = "STK_Inventory";

    /** 预留关系*/
    String PLN_RESERVELINK = "PLN_RESERVELINK";

    /**普通发票**/
    String IV_SALESOC = "IV_SALESOC";

    /**专用发票**/
    String IV_SALESIC = "IV_SALESIC";

    /** 发货通知单 */
    String SAL_DELIVERYNOTICE = "SAL_DELIVERYNOTICE";

    /** 序列号 */
    String BD_SerialMainFile = "BD_SerialMainFile";

    /** 销售出库单 */
    String SAL_OUTSTOCK = "SAL_OUTSTOCK";

    /** 销售退货单 */
    String SAL_RETURNSTOCK = "SAL_RETURNSTOCK";

    /** 收款退款单 */
    String AR_REFUNDBILL = "AR_REFUNDBILL";

    /**币别**/
    String BD_Currency="BD_Currency";
    /**供应商**/
    String BD_Supplier="BD_Supplier";
    /**税率**/
    String BD_TaxRate="BD_TaxRate";
    /**收款条件**/
    String BD_RecCondition="BD_RecCondition";
    /**结算方式**/
    String BD_SETTLETYPE="BD_SETTLETYPE";
    /**销售部门**/
    String BD_Department="BD_Department";
    /**销售组**/
    String BD_OPERATORGROUPBILL="BD_OPERATORGROUPBILL";
    /**价目表**/
    String BD_SAL_PriceList="BD_SAL_PriceList";
    /**折扣表**/
    String BD_SAL_DiscountList="BD_SAL_DiscountList";
    /**计量单位**/
    String BD_UNIT="BD_UNIT";
    /**辅助资料类别**/
    String BOS_ASSISTANTDATA = "BOS_ASSISTANTDATA";
    /** 辅助资料 BOS_ASSISTANTDATA_DETAIL */
    String BOS_ASSISTANTDATA_DETAIL = "BOS_ASSISTANTDATA_DETAIL";
    /** 系统用户 */
    String SEC_User = "SEC_User";
    /** 仓位值集 */
    String BD_FLEXVALUES = "BD_FLEXVALUES";
    /** 仓库 */
    String BD_STOCK = "BD_STOCK";
    /** 物料清单 */
    String ENG_BOM = "ENG_BOM";

    /**
     * 批号
     */
    String BATCH_OBJ="BD_BatchMainFile";


    /**
     * 仓位值组合
     */
    String FLEX_VALUES_DETAIL = "BD_FLEXVALUESDETAIL";

    /**
     * 直接调拨单
     */
    String STK_TransferDirect = "STK_TransferDirect";

    /**
     * 系统参数
     */
    String BOS_SystemParameter = "BOS_SystemParameter";

    /**
     * 辅助属性
     */
    String BD_FLEXAUXPROPERTY = "BD_FLEXAUXPROPERTY";
    /**
     * 可销控制-客户物料
     */
    String SAL_SC_CUSTMAT = "SAL_SC_CustMat";
    /**
     *
     */
    String BD_MATERIALUNITCONVERT = "BD_MATERIALUNITCONVERT";
    /**
     * 销售合同
     */
    String CRM_Contract="CRM_Contract";
}
