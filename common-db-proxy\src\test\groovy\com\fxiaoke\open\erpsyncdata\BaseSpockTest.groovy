package com.fxiaoke.open.erpsyncdata


import groovy.util.logging.Slf4j
import spock.lang.Specification

/**
 * 设置变量
 * <AUTHOR> (^_−)☆
 * @date 2023/5/10
 */
@Slf4j
abstract class BaseSpockTest extends Specification {
    static {
        setFsTestEnv()
        System.setProperty("process.name", "fs-erp-sync-data-test")
        System.setProperty("datacenter.datasource.config", "erp-sync-data-all")
    }

    protected static setFsTestEnv() {
        log.info("set env")
        System.setProperty("process.profile", "fstest")
    }
}
