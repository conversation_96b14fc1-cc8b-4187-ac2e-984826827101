package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import cn.hutool.core.thread.ThreadUtil
import com.facishare.converter.EIEAConverter
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum
import groovy.util.logging.Slf4j
import spock.lang.Specification

import java.util.concurrent.atomic.AtomicLong

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2022-12-13
 */
@Slf4j
class SyncLogManagerSpec extends Specification {
    SyncLogManager syncLogManager
    CHSyncLogManager syncLogDao
    String tenantId = "83952"
    AtomicLong count = new AtomicLong(0)
    boolean daoHealthy = true;

    void setup() {
        syncLogDao = Stub()
        //模拟插入耗时
        syncLogDao.insert(*_) >> {
            String tenantId, SyncLog syncLog ->
//                if (daoHealthy) {
//                    count.addAndGet(syncLogs.size())
//                    log.info("insert {}", syncLogs.size())
//                } else {
//                    throw new ErpSyncDataException("test exception")
//                }
                count.incrementAndGet()
        }
        EIEAConverter eieaConverter = Stub()
        eieaConverter.enterpriseIdToAccount(_ as int) >> { return "test" }
        syncLogManager = new SyncLogManager(
                chSyncLogManager: syncLogDao,
                eieaConverter: eieaConverter,
        )
    }

    def "正常情况多线程插入和消费"(Long testNum, Boolean success) {
        given:
        count.set(0)
        for (i in 0..<testNum) {
            ThreadUtil.execute({ ->
                syncLogManager.saveLog(tenantId, SyncLogTypeEnum.COMMON, 0, "test save " + i)
            })
        }
        ThreadUtil.sleep(4000)
        //当测试数据大于
        Long successCount = count.get()
        log.info("testResult {},{}", testNum, successCount )
        expect:
        //成功数量需要5000
        success == (successCount == testNum || (successCount <= testNum && successCount > 5000))
        where:
        testNum | success
        10      | true
        5000    | true
        6000    | true
        10000   | true
        20000   | true
    }

//    def "当接口异常后，能继续插入"() {
//        given:
//        //模拟插入异常
//        daoHealthy = false
//        when:
//        //跑满队列
//        for (i in 0..<6000) {
//            ThreadUtil.execute({ ->
//                syncLogManager.saveLog(tenantId, SyncLogTypeEnum.COMMON, 0, "test save exception " + i)
//            })
//        }
//        // 这里不要设置等待时间太久了，可能最终结果会是4900，推测是有100个任务在队列中过期了
//        ThreadUtil.sleep(5000)
//        def size1 = syncLogManager.getQueueSize()
//        //模拟插入恢复正常
//        daoHealthy = true
//        //继续消费
//        for (i in 0..<6000) {
//            ThreadUtil.execute({ ->
//                syncLogManager.saveLog(tenantId, SyncLogTypeEnum.COMMON, 0, "test save " + i)
//            })
//        }
//        ThreadUtil.sleep(5000)
//        // TODO: 这里有问题
//        def size2 = syncLogManager.getQueueSize()
//        then:
//        true == (size1 <= 5000 && size1 >= 4900)
//        size2 == 0
//    }
}
