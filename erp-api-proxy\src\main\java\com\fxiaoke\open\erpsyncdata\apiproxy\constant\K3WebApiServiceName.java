package com.fxiaoke.open.erpsyncdata.apiproxy.constant;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/4
 */
public interface K3WebApiServiceName {
    String VALIDATE_USER = "Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser";
    String SAVE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save";
    String BATCH_SAVE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.BatchSave";
    String AUDIT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit";
    String DELETE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete";
    String UN_AUDIT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit";
    String SUBMIT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit";
    String VIEW = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View";
    String BILL_QUERY = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery";
    String DRAFT = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Draft";
    String GET_DATA_CENTER_LIST = "Kingdee.BOS.ServiceFacade.ServicesStub.Account.AccountService.GetDataCenterList";
    String EXCUTE_OPERATION = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExcuteOperation";
    public static final String BUSINESS_INFO = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryBusinessInfo";
    public static final String CREATEXORDER = "Kingdee.K3.SCM.WebApi.ServicesStub.SaveXSaleOrderWebApi.SaveXSaleOrder";
    public static final String DISTRIBUTE = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Allocate";


}
