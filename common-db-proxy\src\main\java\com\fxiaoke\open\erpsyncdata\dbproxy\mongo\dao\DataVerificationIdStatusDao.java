package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdCheckStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationIdStatus;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.Sorts;
import com.mongodb.client.result.UpdateResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @Date: 18:00 2022/11/9
 * @Desc:
 */

@Slf4j
@Repository
@DependsOn("erpSyncDataMongoStore")
public class DataVerificationIdStatusDao {
    private static final String _id = "_id";
    private static final String f_status = "status";
    private static final String f_checkStatus = "checkStatus";
    private static final String f_tenantId = "tenantId";
    private static final String f_dataId = "dataId";
    private static final String f_dataVerificationTaskId = "dataVerificationTaskId";
    private static final String f_updateTime = "updateTime";
    private DatastoreExt store;

    private String DATABASE;
    private final static String collectionName_Prefix = "data_verification_id_status_";
    private final static int collectionSize = 20;

    @Getter
    private final Set<String> dataVerificationIdStatusCollectionCache = Sets.newConcurrentHashSet();

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(DataVerificationIdStatus.class)
                        .automatic(true).build()));
    }

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    public void setStore(DatastoreExt store) {
        this.store = store;
        store.getMongo().getDatabase(DATABASE)
                .listCollectionNames().iterator().forEachRemaining(v -> {
            if (v.startsWith(collectionName_Prefix)) {
                dataVerificationIdStatusCollectionCache.add(v);
            }
        });
    }

    DataVerificationIdStatusDao() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    public void deleteByDataVerificationTaskId(String tenantId, String dataVerificationTaskId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(dataVerificationTaskId)) {
            return;
        }
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_dataVerificationTaskId, dataVerificationTaskId));
        Bson filter = and(filters);
        MongoCollection<DataVerificationIdStatus> collectionList = this.getOrCreateDataVerificationIdStatusCollection(tenantId);
        collectionList.deleteMany(filter);
    }

    public List<DataVerificationIdStatus> queryDataVerificationIdStatus(String tenantId,
                                                                        String dataVerificationTaskId,
                                                                        List<IdSyncStatus> syncStatus,
                                                                        List<IdSyncStatus> ignoreStatus,
                                                                        IdCheckStatus checkStatus,
                                                                        String searchDataId,
                                                                        String lastId,
                                                                        Integer limit) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(dataVerificationTaskId)) {
            return null;
        }
        Bson filter = buildFilter(tenantId, dataVerificationTaskId, syncStatus, ignoreStatus, checkStatus, searchDataId, lastId);
        MongoCollection<DataVerificationIdStatus> collectionList = this.getOrCreateDataVerificationIdStatusCollection(tenantId);
        FindIterable<DataVerificationIdStatus> dataVerificationIdStatuses = collectionList.find(filter).sort(Sorts.ascending(_id)).limit(limit);
        return Lists.newArrayList(dataVerificationIdStatuses);
    }

    public Long countDataVerificationIdStatus(String tenantId,
                                              String dataVerificationTaskId,
                                              List<IdSyncStatus> syncStatus,
                                              List<IdSyncStatus> ignoreStatus,
                                              IdCheckStatus checkStatus,
                                              String searchDataId,
                                              String lastId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(dataVerificationTaskId)) {
            return 0L;
        }
        Bson filter = buildFilter(tenantId, dataVerificationTaskId, syncStatus, ignoreStatus, checkStatus, searchDataId, lastId);
        MongoCollection<DataVerificationIdStatus> collectionList = this.getOrCreateDataVerificationIdStatusCollection(tenantId);
        Long documents = collectionList.countDocuments(filter);
        return documents;
    }

    private Bson buildFilter(String tenantId, String dataVerificationTaskId, List<IdSyncStatus> syncStatus, List<IdSyncStatus> ignoreStatus, IdCheckStatus checkStatus, String searchDataId, String lastId) {
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_dataVerificationTaskId, dataVerificationTaskId));
        if (CollectionUtils.isNotEmpty(syncStatus)) {
            List<String> newSyncStatus = syncStatus.stream().map(syncStatus1 -> syncStatus1.name()).collect(Collectors.toList());
            filters.add(Filters.in(f_status, newSyncStatus));
        } else if (CollectionUtils.isNotEmpty(ignoreStatus)) {
            List<String> newSyncStatus = ignoreStatus.stream().map(syncStatus1 -> syncStatus1.name()).collect(Collectors.toList());
            filters.add(Filters.nin(f_status, newSyncStatus));
        }
        if (checkStatus != null) {
            filters.add(Filters.eq(f_checkStatus, checkStatus.name()));
        }
        if (StringUtils.isNotBlank(searchDataId)) {
            filters.add(Filters.eq(f_dataId, searchDataId));
        }
        if (StringUtils.isNotBlank(lastId)) {
            filters.add(Filters.gt(_id, new ObjectId(lastId)));
        }
        Bson filter = and(filters);
        return filter;
    }

    public Integer updateCheckStatus(String tenantId,
                                     String dataVerificationTaskId,
                                     List<String> dataIds,
                                     IdCheckStatus checkStatus) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(dataVerificationTaskId) || checkStatus == null) {
            return 0;
        }
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq(f_tenantId, tenantId));
        filters.add(Filters.eq(f_dataVerificationTaskId, dataVerificationTaskId));
        if (CollectionUtils.isNotEmpty(dataIds)) {
            filters.add(Filters.in(f_dataId, dataIds));
        }
        List<Bson> updates = new ArrayList<>();
        if (checkStatus != null) {
            updates.add(set(f_checkStatus, checkStatus.name()));
        }
        Bson filter = and(filters);
        MongoCollection<DataVerificationIdStatus> collectionList = this.getOrCreateDataVerificationIdStatusCollection(tenantId);
        Bson update = combine(updates);
        UpdateResult updateResult = collectionList.updateMany(filter, update);
        return (int) updateResult.getMatchedCount();
    }

    public void batchInsert(String tenantId, List<DataVerificationIdStatus> dataList) {
        MongoCollection<DataVerificationIdStatus> collectionList = this.getOrCreateDataVerificationIdStatusCollection(tenantId);
        collectionList.insertMany(dataList);
    }

    public DataVerificationIdStatus buildDataVerificationTask(ObjectId id, String tenantId, String dataVerificationTaskId, String dataId, String statusReason,
                                                              IdSyncStatus status, Date createTime, Date updateTime) {
        return DataVerificationIdStatus.builder()
                .id(id)
                .tenantId(tenantId)
                .dataVerificationTaskId(dataVerificationTaskId)
                .dataId(dataId)
                .status(status)
                .statusReason(statusReason)
                .createTime(createTime)
                .updateTime(updateTime)
                .build();
    }

    public MongoCollection<DataVerificationIdStatus> getOrCreateDataVerificationIdStatusCollection(String tenantId) {
        String collectionName = getCollectionName(tenantId);
        MongoCollection<DataVerificationIdStatus> collectionList = store.getMongo().getDatabase(DATABASE).withCodecRegistry(SingleCodecHolder.codecRegistry).getCollection(collectionName, DataVerificationIdStatus.class);
        if (!dataVerificationIdStatusCollectionCache.add(collectionName)) {
            return collectionList;
        }
        List<String> exists = Lists.newArrayList();
        collectionList.listIndexes().iterator().forEachRemaining(doc -> {
            String name = doc.getString("name");
            if (!"_id_".equals(name)) {
                exists.add(name);
            }
        });
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending(f_updateTime);
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(3L, TimeUnit.DAYS).background(true)));
        }
        key = "index_id_status";
        if (!exists.remove(key)) {
            Bson index_ei_obj_data_id = Indexes.compoundIndex(Indexes.ascending(f_dataId), Indexes.ascending(f_dataVerificationTaskId), Indexes.ascending(f_status), Indexes.ascending(f_checkStatus));
            toBeCreate.add(new IndexModel(index_ei_obj_data_id, new IndexOptions().name(key).background(true)));
        }
        key = "index_task_id";
        if (!exists.remove(key)) {
            Bson index_task_id = Indexes.compoundIndex(Indexes.ascending(f_dataVerificationTaskId), Indexes.ascending(f_tenantId), Indexes.ascending(_id));
            toBeCreate.add(new IndexModel(index_task_id, new IndexOptions().name(key).background(true)));
        }
        key = "index_task_status";
        if (!exists.remove(key)) {
            Bson index_task_status = Indexes.compoundIndex(Indexes.ascending(f_status), Indexes.ascending(f_dataVerificationTaskId), Indexes.ascending(f_tenantId), Indexes.ascending(_id));
            toBeCreate.add(new IndexModel(index_task_status, new IndexOptions().name(key).background(true)));
        }

        if (!toBeCreate.isEmpty()) {
            List<String> created = collectionList.createIndexes(toBeCreate);
            log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        }
        return collectionList;
    }

    private String getCollectionName(String tenantId) {
        try {
            Integer ei = Integer.valueOf(tenantId);
            Integer index = ei % collectionSize;
            return collectionName_Prefix + index;
        } catch (Exception e) {
            return collectionName_Prefix + "common";
        }

    }
}
