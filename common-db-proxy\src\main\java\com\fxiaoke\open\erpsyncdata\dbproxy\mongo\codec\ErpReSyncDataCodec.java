package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.mongodb.MongoClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonReader;
import org.bson.BsonString;
import org.bson.BsonValue;
import org.bson.BsonWriter;
import org.bson.Document;
import org.bson.codecs.Codec;
import org.bson.codecs.CollectibleCodec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;
import org.bson.types.ObjectId;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:24 2022/12/8
 * @Desc:
 */
@Slf4j
public class ErpReSyncDataCodec implements CollectibleCodec<ErpReSyncData> {
    private final Codec<Document> documentCodec;

    /**
     * Default constructor.
     */
    public ErpReSyncDataCodec() {
        this.documentCodec = MongoClient.getDefaultCodecRegistry().get(Document.class);
    }


    private ErpReSyncData convert(Document document) {
        if (document == null) {
            return null;
        }
        ErpReSyncData erpReSyncData = BeanUtil.toBean(document, ErpReSyncData.class,
                CopyOptions.create().ignoreError().setIgnoreProperties("_id", "checkMessage"));
        erpReSyncData.setId(document.getObjectId("_id"));
        erpReSyncData.setCheckMessage(convert2CheckMessageListData(document.get("checkMessage")));
        return erpReSyncData;
    }

    private CheckMessageListData convert2CheckMessageListData(Object doc) {
        if (doc instanceof String) {
            String str = (String) doc;
            if (StringUtils.isBlank(str)){
                return null;
            }
            try {
                CheckMessageListData checkMessageListData = JacksonUtil.fromJson(str,CheckMessageListData.class);
                return checkMessageListData;
            }catch (Exception e){
                log.error("decode Object Data failed,",e);
            }
        }
        return null;
    }


    @Override
    public ErpReSyncData decode(BsonReader reader, DecoderContext decoderContext) {
        Document document = documentCodec.decode(reader, decoderContext);
        ErpReSyncData erpReSyncData = convert(document);
        return erpReSyncData;
    }

    @Override
    public void encode(BsonWriter writer, ErpReSyncData value, EncoderContext encoderContext) {
        Map<String, Object> map = BeanUtil.beanToMap(value);
        map.remove("id");
        map.put("_id", value.getId());
        Document document = new Document(map);
        Object checkMessage = document.remove("checkMessage");
        document.put("checkMessage", JacksonUtil.toJson(checkMessage));

        documentCodec.encode(writer, document, encoderContext);
    }

    @Override
    public Class<ErpReSyncData> getEncoderClass() {
        return ErpReSyncData.class;
    }

    @Override
    public ErpReSyncData generateIdIfAbsentFromDocument(ErpReSyncData document) {
        if (!documentHasId(document)) {
            document.setId(new ObjectId());
        }
        return document;
    }

    @Override
    public boolean documentHasId(ErpReSyncData document) {
        return document.getId() != null;
    }

    @Override
    public BsonValue getDocumentId(ErpReSyncData document) {
        if (!documentHasId(document)) {
            throw new IllegalStateException("The document does not contain an _id");
        }
        return new BsonString(document.getId().toString());
    }
}
