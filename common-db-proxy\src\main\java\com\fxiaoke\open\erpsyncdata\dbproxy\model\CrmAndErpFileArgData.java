package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.model.OutFile;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrmAndErpFileArgData implements Serializable {
    private CrmFileModel crmFileModel;
    private OutFile outFile;
}
