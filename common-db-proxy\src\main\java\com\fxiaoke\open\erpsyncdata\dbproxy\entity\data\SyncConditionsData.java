package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncForceConstant;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import lombok.Data;

import java.util.List;

@Data
public class SyncConditionsData {
    private List<List<FilterData>> filters;
    private String apiName;
    /**
     * 强制同步
     */
    private Boolean isSyncForce = SyncForceConstant.CLOSE;
}
