package com.fxiaoke.open.erpsyncdata.web.transfer.overwrite;

import com.facishare.transfer.model.EnterpriseData;
import com.facishare.transfer.service.EnterpriseService;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:11:13
 */
public class EnterpriseServiceImpl implements EnterpriseService {

    @Override
    public List<EnterpriseData> getEnterpriseFilterEnv(final Collection<String> collection, final boolean b) {
        return collection.stream()
                .distinct()
                .filter(StringUtils::isNumeric)
                .map(s -> new EnterpriseData(Integer.valueOf(s), s))
                .collect(Collectors.toList());
    }

    /**
     * 按环境/企业id迁移,不支持所有迁移
     */
    @Override
    public List<EnterpriseData> queryAllEnterprise(final boolean b) {
        return new ArrayList<>();
    }
}
