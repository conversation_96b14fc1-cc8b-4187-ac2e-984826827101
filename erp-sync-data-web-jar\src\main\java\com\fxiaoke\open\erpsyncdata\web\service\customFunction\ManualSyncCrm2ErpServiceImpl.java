package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncByCrmDataIdArg;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("manualSyncCrm2Erp")
public class ManualSyncCrm2ErpServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;

    /**
     * @param arg
     * @return
     */
    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        if (arg.getParams() == null || arg.getParams().isEmpty()) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String tenantId=arg.getTenantId();
        SyncByCrmDataIdArg syncByCrmDataIdArg = JsonUtil.fromJson(arg.getParams(), SyncByCrmDataIdArg.class);
        if (StringUtils.isAnyBlank(syncByCrmDataIdArg.getCrmObjectApiName(),syncByCrmDataIdArg.getCrmDataId()) ) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setObjAPIName(syncByCrmDataIdArg.getCrmObjectApiName());
        erpIdArg.setDataId(syncByCrmDataIdArg.getCrmDataId());
        Result<Void> result = adminSyncDataMappingService.syncSingletonData(erpIdArg, syncByCrmDataIdArg.getErpObjectApiName(), DataReceiveTypeEnum.FUNCTION_TRIGGER);
        return Result.copy(result);
    }


}
