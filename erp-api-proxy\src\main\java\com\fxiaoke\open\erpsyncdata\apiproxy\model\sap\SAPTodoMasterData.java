package com.fxiaoke.open.erpsyncdata.apiproxy.model.sap;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2020/12/30
 * @Desc:
 */
@Data
public class SAPTodoMasterData implements Serializable {
    @SerializedName("todoType")
    private String todoType;

    @SerializedName("workflowName")
    private String workflowName;

    @SerializedName("requestName")
    private String requestName;

    @SerializedName("toUserCode")
    private String toUserCode;

    @SerializedName("objAPIName")
    private String objAPIName;

    @SerializedName("dataId")
    private String dataId;

    @SerializedName("workflowId")
    private String workflowId;


}
