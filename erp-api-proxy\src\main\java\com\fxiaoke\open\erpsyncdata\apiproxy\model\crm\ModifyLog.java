package com.fxiaoke.open.erpsyncdata.apiproxy.model.crm;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/18
 */
public class ModifyLog {
    @Data
    public static class Arg{
        private Condition condition;
        /**
         * 每页的大小 目前最大199，切记不要传200，可放到配置文件
         */
        private int pageSize;
    }

    @Data
    @Builder
    public static class Condition{
        //当前页的最后一个logID，用于下一页的起始条件,
        private List<Object> searchAfter;
        //当前页的最后一个logID，用于下一页的起始条件。旧条件，880后使用searchAfter
        @Deprecated
        private String logId;
        //操作模块,对象的apiName
        private String module;
        //数据id
        private String objectId;
        private long operationTimeFrom;
        private long operationTimeTo;
    }

    @Data
    public static class Result{
        private List<Info> modifyLogInfos;
        private int totalCount;
        private int pageSize;
        private Boolean hasMore;
    }


    @Data
    public static class Info{
        private List<DiffObjectData> objectData;
        private String dataId;
        private String objectApiName;
        private String logId;
        private List<Object> searchAfter;
        /**
         * 操作行为，新建（1），编辑（2），作废（3），恢复（4）等
         */
        private String bizOperationName;
    }

    @Data
    public static class DiffObjectData{
        private String fieldApiName;
        private String renderType;
        private Map<String, Object> value;
        private Map<String, Object> oldValue;
    }
}
