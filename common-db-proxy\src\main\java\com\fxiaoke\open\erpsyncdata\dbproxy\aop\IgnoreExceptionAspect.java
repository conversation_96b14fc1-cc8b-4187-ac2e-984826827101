package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/21 15:03:52
 */
@Slf4j
@Aspect
@Component
public class IgnoreExceptionAspect {

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.monitor.CheckProductErpDataMonitor.*(..))")
    public Object ignoreException(ProceedingJoinPoint jp) {
        try {
            return jp.proceed();
        } catch (Throwable t) {
            final MethodSignature signature = (MethodSignature) jp.getSignature();
            final String methodName = signature.getName();
            final Object[] args = jp.getArgs();
            final List<String> argList = Arrays.stream(args).map(JSON::toJSONString).collect(Collectors.toList());
            final String className = jp.getTarget().getClass().getSimpleName();
            log.error("{} {} error, args:{}", className, methodName, argList, t);
            return null;
        }
    }
}
