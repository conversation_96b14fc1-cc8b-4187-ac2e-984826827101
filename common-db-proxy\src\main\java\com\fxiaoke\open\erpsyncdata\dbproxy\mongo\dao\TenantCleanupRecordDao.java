package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TenantCleanupRecord;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 企业账号资源清理记录DAO
 * 用于管理需要统计清理的企业账号资源记录
 */
@Slf4j
@Repository
@DependsOn("erpSyncDataMongoStore")
//IgnoreI18nFile
public class TenantCleanupRecordDao {

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    private DatastoreExt store;
    
    private String DATABASE = "erp_sync_data";
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();
    private final static String TENANT_CLEANUP_RECORD = "tenant_cleanup_record";
    private final static String ID = "_id";

    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    private static class SingleCodecHolder {
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(TenantCleanupRecord.class)
                        .automatic(true).build()));
    }

    private void createIndex() {
        // 创建索引，如果已存在mongo不会执行操作
        MongoCollection<TenantCleanupRecord> coll = getColl();
        List<IndexModel> toBeCreate = Lists.newArrayList();

        // 过期自动清理时间，90天
        org.bson.conversions.Bson idxExpire = Indexes.descending(TenantCleanupRecord.Fields.createTime);
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(90L, TimeUnit.DAYS).background(true)));

        // tenantId索引
        org.bson.conversions.Bson idxTenantId = Indexes.ascending(TenantCleanupRecord.Fields.tenantId);
        toBeCreate.add(new IndexModel(idxTenantId, new IndexOptions().name("index_tenant_id")
                .unique(true).background(true)));

        // 删除状态索引
        org.bson.conversions.Bson idxDeleted = Indexes.ascending(TenantCleanupRecord.Fields.isDeleted);
        toBeCreate.add(new IndexModel(idxDeleted, new IndexOptions().name("index_is_deleted").background(true)));

        // 过期时间索引
        org.bson.conversions.Bson idxExpireTime = Indexes.ascending(TenantCleanupRecord.Fields.expireTime);
        toBeCreate.add(new IndexModel(idxExpireTime, new IndexOptions().name("index_expire_time_field").background(true)));

        // 企业级别索引
        org.bson.conversions.Bson idxTenantLevel = Indexes.ascending(TenantCleanupRecord.Fields.tenantLevel);
        toBeCreate.add(new IndexModel(idxTenantLevel, new IndexOptions().name("index_tenant_level").background(true)));

        // 清理状态索引
        org.bson.conversions.Bson idxCleanStatus = Indexes.ascending(TenantCleanupRecord.Fields.cleanStatus);
        toBeCreate.add(new IndexModel(idxCleanStatus, new IndexOptions().name("index_clean_status").background(true)));

        // 许可证过期时间索引
        org.bson.conversions.Bson idxLicenseExpireTime = Indexes.ascending(TenantCleanupRecord.Fields.licenseExpireTime);
        toBeCreate.add(new IndexModel(idxLicenseExpireTime, new IndexOptions().name("index_license_expire_time").background(true)));

        // 复合索引：企业级别+清理状态
        org.bson.conversions.Bson idxComposite = Indexes.compoundIndex(
                Indexes.ascending(TenantCleanupRecord.Fields.tenantLevel),
                Indexes.ascending(TenantCleanupRecord.Fields.cleanStatus)
        );
        toBeCreate.add(new IndexModel(idxComposite, new IndexOptions().name("index_level_clean_status").background(true)));

        List<String> created = coll.createIndexes(toBeCreate);
        log.info("Created indexes: {}", created);
    }

    private MongoCollection<TenantCleanupRecord> getColl() {
        MongoCollection<TenantCleanupRecord> coll = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(TENANT_CLEANUP_RECORD, TenantCleanupRecord.class);
        
        if (!collectionCache.contains(TENANT_CLEANUP_RECORD)) {
            createIndex();
            collectionCache.add(TENANT_CLEANUP_RECORD);
        }
        return coll;
    }

    /**
     * 批量插入或更新企业清理记录
     */
    public void batchUpsert(List<TenantCleanupRecord> records) {
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        List<WriteModel<TenantCleanupRecord>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        
        for (TenantCleanupRecord record : records) {
            if (record.getId() == null) {
                record.setId(ObjectId.get());
            }
            
            UpdateOneModel<TenantCleanupRecord> updateOneModel = new UpdateOneModel<>(
                    filterByTenantId(record.getTenantId()), 
                    upsertDocument(record), 
                    updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        MongoCollection<TenantCleanupRecord> collection = getColl();
        
        try {
            BulkWriteResult bulkWriteResult = collection.bulkWrite(requests, bulkOption);
            if (!bulkWriteResult.wasAcknowledged()) {
                throw new RuntimeException("批量upsert失败. result:" + bulkWriteResult);
            }
            log.info("批量upsert企业清理记录成功，处理记录数: {}", records.size());
        } catch (Exception e) {
            log.error("批量upsert企业清理记录失败", e);
            throw e;
        }
    }

    /**
     * 根据tenantId构建过滤条件
     */
    public org.bson.conversions.Bson filterByTenantId(String tenantId) {
        Document result = new Document();
        result.put(TenantCleanupRecord.Fields.tenantId, tenantId);
        return result;
    }

    /**
     * 根据tenantId查询企业清理记录
     */
    public TenantCleanupRecord queryByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        
        MongoCollection<TenantCleanupRecord> collection = getColl();
        org.bson.conversions.Bson filter = filterByTenantId(tenantId);
        
        return collection.find(filter)
                .sort(Sorts.descending(TenantCleanupRecord.Fields.updateTime))
                .first();
    }

    /**
     * 查询未删除的企业清理记录
     */
    public List<TenantCleanupRecord> queryUndeleted(int limit) {
        MongoCollection<TenantCleanupRecord> collection = getColl();
        Document filter = new Document(TenantCleanupRecord.Fields.isDeleted, false);

        return collection.find(filter)
                .sort(Sorts.ascending(TenantCleanupRecord.Fields.expireTime))
                .limit(limit)
                .into(new ArrayList<>());
    }

    /**
     * 查询已过期且未删除的企业清理记录
     */
    public List<TenantCleanupRecord> queryExpiredUndeleted(int limit) {
        MongoCollection<TenantCleanupRecord> collection = getColl();
        Document filter = new Document()
                .append(TenantCleanupRecord.Fields.isDeleted, false)
                .append(TenantCleanupRecord.Fields.expireTime, new Document("$lt", new Date()));

        return collection.find(filter)
                .sort(Sorts.ascending(TenantCleanupRecord.Fields.expireTime))
                .limit(limit)
                .into(new ArrayList<>());
    }

    /**
     * 根据企业级别查询企业清理记录
     */
    public List<TenantCleanupRecord> queryByTenantLevel(String tenantLevel, int limit) {
        if (StringUtils.isBlank(tenantLevel)) {
            return new ArrayList<>();
        }

        MongoCollection<TenantCleanupRecord> collection = getColl();
        Document filter = new Document(TenantCleanupRecord.Fields.tenantLevel, tenantLevel);

        return collection.find(filter)
                .sort(Sorts.descending(TenantCleanupRecord.Fields.updateTime))
                .limit(limit)
                .into(new ArrayList<>());
    }

    /**
     * 根据清理状态查询企业清理记录
     */
    public List<TenantCleanupRecord> queryByCleanStatus(String cleanStatus, int limit) {
        if (StringUtils.isBlank(cleanStatus)) {
            return new ArrayList<>();
        }

        MongoCollection<TenantCleanupRecord> collection = getColl();
        Document filter = new Document(TenantCleanupRecord.Fields.cleanStatus, cleanStatus);

        return collection.find(filter)
                .sort(Sorts.descending(TenantCleanupRecord.Fields.updateTime))
                .limit(limit)
                .into(new ArrayList<>());
    }

    /**
     * 查询许可证已过期的企业清理记录
     */
    public List<TenantCleanupRecord> queryLicenseExpired(int limit) {
        MongoCollection<TenantCleanupRecord> collection = getColl();
        Document filter = new Document(TenantCleanupRecord.Fields.licenseExpireTime,
                new Document("$lt", new Date()));

        return collection.find(filter)
                .sort(Sorts.ascending(TenantCleanupRecord.Fields.licenseExpireTime))
                .limit(limit)
                .into(new ArrayList<>());
    }

    /**
     * 根据多个条件查询企业清理记录
     */
    public List<TenantCleanupRecord> queryByMultipleConditions(String tenantLevel, String cleanStatus,
                                                               Boolean isDeleted, int limit) {
        MongoCollection<TenantCleanupRecord> collection = getColl();
        Document filter = new Document();

        if (StringUtils.isNotBlank(tenantLevel)) {
            filter.append(TenantCleanupRecord.Fields.tenantLevel, tenantLevel);
        }
        if (StringUtils.isNotBlank(cleanStatus)) {
            filter.append(TenantCleanupRecord.Fields.cleanStatus, cleanStatus);
        }
        if (isDeleted != null) {
            filter.append(TenantCleanupRecord.Fields.isDeleted, isDeleted);
        }

        return collection.find(filter)
                .sort(Sorts.descending(TenantCleanupRecord.Fields.updateTime))
                .limit(limit)
                .into(new ArrayList<>());
    }

    /**
     * 标记企业为已删除
     */
    public void markAsDeleted(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return;
        }

        MongoCollection<TenantCleanupRecord> collection = getColl();
        org.bson.conversions.Bson filter = filterByTenantId(tenantId);
        Document update = new Document("$set", new Document()
                .append(TenantCleanupRecord.Fields.isDeleted, true)
                .append(TenantCleanupRecord.Fields.updateTime, new Date()));

        collection.updateOne(filter, update);
        log.info("delete tenanid{}", tenantId);
    }

    /**
     * 构建upsert文档
     */
    public org.bson.conversions.Bson upsertDocument(TenantCleanupRecord record) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();

        // 更新字段
        if (record.getUpdateTime() != null) {
            updateDoc.append(TenantCleanupRecord.Fields.updateTime, record.getUpdateTime());
        } else {
            updateDoc.append(TenantCleanupRecord.Fields.updateTime, new Date());
        }

        if (StringUtils.isNotBlank(record.getTenantName())) {
            updateDoc.append(TenantCleanupRecord.Fields.tenantName, record.getTenantName());
        }

        if (StringUtils.isNotBlank(record.getTenantOwner())) {
            updateDoc.append(TenantCleanupRecord.Fields.tenantOwner, record.getTenantOwner());
        }

        if (StringUtils.isNotBlank(record.getTenantLevel())) {
            updateDoc.append(TenantCleanupRecord.Fields.tenantLevel, record.getTenantLevel());
        }

        if (StringUtils.isNotBlank(record.getCleanStatus())) {
            updateDoc.append(TenantCleanupRecord.Fields.cleanStatus, record.getCleanStatus());
        }

        if (record.getExpireTime() != null) {
            updateDoc.append(TenantCleanupRecord.Fields.expireTime, record.getExpireTime());
        }

        if (record.getLicenseExpireTime() != null) {
            updateDoc.append(TenantCleanupRecord.Fields.licenseExpireTime, record.getLicenseExpireTime());
        }

        if (record.getIsDeleted() != null) {
            updateDoc.append(TenantCleanupRecord.Fields.isDeleted, record.getIsDeleted());
        }

        if (StringUtils.isNotBlank(record.getRemark())) {
            updateDoc.append(TenantCleanupRecord.Fields.remark, record.getRemark());
        }

        // 插入时设置的字段
        setOnInsertDoc
                .append(TenantCleanupRecord.Fields.tenantId, record.getTenantId())
                .append(ID, record.getId())
                .append(TenantCleanupRecord.Fields.createTime, new Date())
                .append(TenantCleanupRecord.Fields.isDeleted, false);

        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);

        return doc;
    }
}
