package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;

import java.util.Map;
import java.util.Set;

/**
 * 使用指标
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/13
 */

@Data
@FieldNameConstants
public class UsageMetricDoc {
    /**
     * id
     */
    @Id
    private ObjectId id;

    /**
     * 企业id，为空为全局
     */
    private String tenantId;
    /**
     * 唯一键，企业内唯一
     */
    private String uniKey;
    /**
     * 指标
     */
    private Map<String, Double> metrics;

    /**
     * 关联企业id,仅全局计数使用
     */
    private Set<String> linkTenantIds;
    /**
     * 关联集成流Id
     */
    private Set<String> linkStreamIds;
}
