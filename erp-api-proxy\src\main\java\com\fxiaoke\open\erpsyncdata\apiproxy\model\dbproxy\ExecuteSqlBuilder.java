package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Setter
public class ExecuteSqlBuilder {
    List<String> sqlList;
    String sql;

    public List<String> sqlList() {
        if (sqlList != null && !sqlList.isEmpty()) {
            return sqlList;
        }
        if (sql!=null){
            return Lists.newArrayList(sql);
        }
        return Lists.newArrayList();
    }
}
