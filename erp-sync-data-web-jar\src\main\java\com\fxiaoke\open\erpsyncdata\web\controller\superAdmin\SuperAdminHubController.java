package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alicp.jetcache.anno.support.GlobalCacheConfig;
import com.fxiaoke.i18n.SupportLanguage;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.i18n.util.LangIndex;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.AmisResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.OuterConnectorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.OuterConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/hub")
@Slf4j
// IgnoreI18nFile
public class SuperAdminHubController extends SuperAdminBaseController {
    @Autowired
    private OuterConnectorManager outerConnectorManager;
    @Autowired
    private ConfigCenterManager configCenterManager;
    @Autowired
    private GlobalCacheConfig config;


    /**
     * 获取hub列表
     */
    @GetMapping("getHubInfoList")
    public Result<Amis.Crud<HubInfo>> getHubInfoList() {
        List<HubInfo> hubInfoList = outerConnectorManager.getHubInfoList();
        Amis.ColHelper<HubInfo> colHelper = Amis.Crud.parseHelper(HubInfo.class, hubInfoList);
        colHelper.remove("outerConnectors");
        colHelper.get("name").fixed("left");
        return Result.newSuccess(colHelper.getCrud());
    }


    /**
     * 获取连接器列表
     */
    @GetMapping("getConnectorList")
    public Result<Amis.Crud<Dict>> getConnectorList(@RequestParam(required = false) String hubName) {
        String finalHubName = StrUtil.blankToDefault(hubName, "default");
        List<HubInfo> hubInfoList = outerConnectorManager.getHubInfoList();
        List<OuterConnector> outerConnectors = hubInfoList.stream().filter(v -> v.getName().equals(finalHubName)).findFirst().map(v -> v.getOuterConnectors()).orElse(null);
        if (CollUtil.isEmpty(outerConnectors)) {
            return Result.newError("hub not found:" + finalHubName);
        }
        I18nClient i18nClient = I18nClient.getInstance();
        Map<Byte, SupportLanguage> languageMapMapping = LangIndex.getInstance().getLanguageMapMapping();
        List<Dict> dictList = outerConnectors.stream()
                .map(v -> {
                    Dict dict = Dict.parse(v);
                    String iconUrl = v.getIconUrl();
                    if (StrUtil.isNotBlank(iconUrl)) {
                        if (!iconUrl.startsWith("https://")) {
                            iconUrl = ConfigCenter.ERP_DOMAIN_URL + "/FSC/EM/File/GetByPath?path=" + iconUrl;
                        }
                    }
                    dict.put("iconUrl", iconUrl);
                    String i18nKey = v.getI18nKey();
                    dict.put("i18nKey", i18nKey);
                    Localization localization = i18nClient.get(i18nKey, 0);
                    if (localization == null) {
                        dict.put("notSetI18n", true);
                    } else {
                        dict.put("notSetI18n", false);
                        dict.put("name_en", localization.getEn());
                    }
                    return dict;
                })
                .collect(Collectors.toList());
        Amis.ColHelper<Dict> colHelper = Amis.Crud.parseHelper(OuterConnector.class, dictList);
        colHelper.get("apiName").fixed("left");
        //加工列
        colHelper.get("iconUrl").label("icon").type("image");
        colHelper.add(Amis.Col.of("i18nKey"),
                Amis.Col.of("notSetI18n"),
                Amis.Col.of("name_en")
        );
        return Result.newSuccess(colHelper.getCrud());
    }

    /**
     * 获取当前配置中心Hub信息
     */
    @GetMapping("getHubInfoStr")
    public Result<Dict> getHubInfoStr() {
        //这里需要读取原始的
        String hubInfo = configCenterManager.readHubInfo();
        return AmisResult.of("hubInfoStr", hubInfo);
    }

    /**
     * 更新Hub信息
     */
    @PostMapping("updateHubInfoStr")
    public Result<Void> updateHubInfoStr(@RequestBody Dict arg) {
        String hubInfoStr = arg.getStr("hubInfoStr");
        //校验
        List<HubInfo> hubInfos = JSON.parseArray(hubInfoStr, HubInfo.class);
        if (CollUtil.isEmpty(hubInfos)) {
            return Result.newError("hubInfoStr is invalid");
        }
        //转换成紧凑字符串
        hubInfoStr = JSON.parseArray(hubInfoStr).toString();
        String oldHubInfoStr = configCenterManager.readHubInfo();
        boolean b = configCenterManager.updateHubInfo(hubInfoStr, getName());
        log.info("update HubInfoStr ,old:{},new:{}", oldHubInfoStr, hubInfoStr);
        return Result.newSuccess();
    }
}
