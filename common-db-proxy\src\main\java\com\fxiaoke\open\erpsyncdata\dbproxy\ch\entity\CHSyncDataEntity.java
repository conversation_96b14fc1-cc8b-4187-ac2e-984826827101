package com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CHSyncDataEntity {
    private String appName; // 服务名称
    private String traceId; // 分布式跟踪id
    private String serverIp; // 发出日志的ip
    private String tenantId; // 租户ei信息
    private Date createTime; // 日志上报时间
    private Date updateTime; // 日志上报时间
    private Date expireTime;//过期时间


    private String logType;//日志类型：sync_data\sync_log\interface_monitor
    private String id;//mongoId
    private Integer sourceTenantType;//企业类型 目前为CRM 1
    private Integer destTenantType;//企业类型 目前为CRM 1
    private Integer sourceEventType;//源数据的数据事件类型 1、新增 2、修改 3、作废
    private String sourceObjectApiName;//源企业主对象apiName
    private String sourceDataId;//源数据主键id
    private String erpTempDataDataNumber;//源数据主属性
    private String sourceData;//源主对象数据
    private String sourceDetailSyncDataIds;//源从对象id封装
    private Integer destEventType;//目标企业事件类型 1、新增 2、修改 3、作废
    private String destObjectApiName;//目标企业主对象apiName
    private String destDataId;//目标企数据id
    private String destData;//目标主对象数据
    private Integer syncDataStatus;//数据状态
    private String syncPloyDetailSnapshotId;//快照id
    private String operatorId;//操作人主键id
    private String remark;//备注
    private String errorCode;//错误编码
    private Boolean isDeleted;//逻辑删除
    private String needReturnDestObjectData;//需要返回的目标数据
    private String data;//节点信息
    private String logId;//日志id syncLogId
    private Integer dataReceiveType;//数据接收方式
}
