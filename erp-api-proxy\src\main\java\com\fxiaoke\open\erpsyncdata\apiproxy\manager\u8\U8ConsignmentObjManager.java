package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("consignment")
public class U8ConsignmentObjManager extends U8DefaultMananger {

    @Override
    protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                             String tenantId,
                                             String dataCenterId,
                                             String snapshotId) {
        StandardListData standardListData = standardListDataResult.getData();
        if (standardListData == null) {
            return;
        }
        for (StandardData standardData : standardListData.getDataList()) {
            getRealData(standardData);
        }
    }

    /**
     * 获取接口
     */
    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectParam) {
        U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectParam.getConnectParams(), U8ConnectParam.class);
        String queryPath = getLoadUrl(erpIdArg.getObjAPIName());
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectParam.getId(),
                erpIdArg.getObjAPIName(),
                ErpObjInterfaceUrlEnum.queryMasterById.name());

        Map<String, String> params = new HashMap<>();
        params.put("id",erpIdArg.getDataId());
        //发送请求
        Result<StandardData> result = get(U8ConnectParam, queryPath, params, interfaceMonitorData);
        result.setData(getRealData(result.getData()));
        return result;
    }

    private StandardData getRealData(StandardData standardData) {
        if (standardData == null) {
            return null;
        }

        ObjectData masterObj = standardData.getMasterFieldVal();
        String consignmentId=masterObj.getString("code");
        masterObj.put("consignmentId",consignmentId);
        List<ObjectData> detailObjectDataList = standardData.getDetailFieldVals().get("entry");
        for (ObjectData objectData : detailObjectDataList) {
            objectData.put("consignmentId", consignmentId);
            String rowno = objectData.getString("rowno");
            String iorderseq = objectData.getString("iorderseq");
            if (StringUtils.isEmpty(iorderseq)) {
                iorderseq = rowno;
            }
            objectData.put("entryId",consignmentId + "#" +objectData.get("socode") + "#"+ objectData.get("inventory_code") + "#" + rowno);//虚拟主键
            objectData.put("orderInvertoryCode",
              objectData.get("socode") + "#" + objectData.get("inventory_code") + "#" + iorderseq);//订单产品编码
            Object batch = objectData.get("serial");
            if (batch != null && StringUtils.isNotEmpty((String) batch)) {
                objectData.put("currentStockId",
                  objectData.get("inventory_code") + "#" + masterObj.get("warehouse_code") + "#" + batch);//库存id
            } else {
                objectData.put("currentStockId",
                  objectData.get("inventory_code") + "#" + masterObj.get("warehouse_code"));//库存id
            }
        }
        return standardData;
    }


}
