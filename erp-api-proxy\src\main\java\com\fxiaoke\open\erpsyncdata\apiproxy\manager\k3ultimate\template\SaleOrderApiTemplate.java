package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import org.springframework.stereotype.Component;

@Component
public class SaleOrderApiTemplate extends K3UltimateBaseApiTemplate {
    @Override
    public String getObjApiName() {
        return K3UltimateObjApiName.sm_salorder;
    }

    @Override
    public String getBatchQueryApi() {
        return "/kapi/v2/sm/sm_salorder/query";
    }

    @Override
    public String getBatchAddApi() {
        return "/kapi/v2/sm/sm_salorder/batchAdd";
    }

    @Override
    public String getBatchUpdateApi() {
        return "/kapi/v2/sm/sm_salorder/batchUpdate";
    }

    @Override
    public String getBizChangeApi(ErpObjInterfaceUrlEnum interfaceUrl) {
        //通过订单变更，生成一个新的订单变更单，之后再更新这个新生成的订单变更单
        if(interfaceUrl==ErpObjInterfaceUrlEnum.createXOrder) {
            return "/kapi/v2/sm/sm_salorder/bizchange";
        }
        return null;
    }

    @Override
    public String getBatchSubmitApi() {
        return "/kapi/v2/sm/sm_salorder/batchSubmit";
    }

    @Override
    public String getBatchUnSubmitApi() {
        return "/kapi/v2/sm/sm_salorder/batchUnsubmit";
    }

    @Override
    public String getBatchAuditApi() {
        return "/kapi/v2/sm/sm_salorder/batchAudit";
    }

    @Override
    public String getBatchUnAuditApi() {
        return "/kapi/v2/sm/sm_salorder/batchUnaudit";
    }

    @Override
    public String getBatchEnableApi() {
        return null;
    }

    @Override
    public String getBatchDisableApi() {
        return null;
    }

    @Override
    public String getBatchDeleteApi() {
        return "/kapi/v2/sm/sm_salorder/batchDelete";
    }
}
