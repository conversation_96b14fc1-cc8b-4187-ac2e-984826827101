package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.HistoryTaskDuplicateDataEntity;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import com.mongodb.WriteConcern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/9/27 18:00:08
 */
@Slf4j
@Component
public class HistoryTaskDuplicateDataDao  extends BaseDao<HistoryTaskDuplicateDataEntity> {
    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }

    public Map<String, Integer> getByTaskNum(String tenantId, String taskNum) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        query.retrievedFields(true, "dataId", "duplicateTime");
        Map<String, Integer> map = new HashMap<>();
        for (HistoryTaskDuplicateDataEntity historyTaskDuplicateDataEntity : query) {
            map.put(historyTaskDuplicateDataEntity.getDataId(), historyTaskDuplicateDataEntity.getDuplicateTime());
        }
        return map;
    }

    public void deleteByTaskNum(String tenantId, String taskNum) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        datastore.delete(query);
    }

    public List<Pair<String, Integer>> getByTaskNum(String tenantId, String taskNum, Integer limit) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        query.order("-duplicateTime");
        query.limit(limit);
        query.retrievedFields(true, "dataId", "duplicateTime");
        List<Pair<String, Integer>> list= new ArrayList<>();
        for (HistoryTaskDuplicateDataEntity entity : query) {
            list.add(Pair.of(entity.getDataId(), entity.getDuplicateTime()));
        }

        return list;
    }

    public Map<String, Integer> getByDataIds(String tenantId, String taskNum, List<String> dataIds) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        query.field("dataId").in(dataIds);
        query.retrievedFields(true, "dataId", "duplicateTime");
        Map<String, Integer> map = new HashMap<>();
        for (HistoryTaskDuplicateDataEntity historyTaskDuplicateDataEntity : query) {
            map.put(historyTaskDuplicateDataEntity.getDataId(), historyTaskDuplicateDataEntity.getDuplicateTime());
        }
        return map;
    }

    public Integer getByDataId(String tenantId, String taskNum, String dataId) {
        final HistoryTaskDuplicateDataEntity entity = getQuery(tenantId, taskNum, dataId).get();
        return Optional.ofNullable(entity).map(HistoryTaskDuplicateDataEntity::getDuplicateTime).orElse(null);
    }

    public void incDuplicateTime(String tenantId, String taskNum, String dataId) {
        incDuplicateTime(tenantId, taskNum, dataId, 1);
    }

    public void incDuplicateTime(String tenantId, String taskNum, String dataId, Integer duplicateTime) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum, dataId);

        // 已存在的数据 inc 次数
        final UpdateOperations<HistoryTaskDuplicateDataEntity> update = createUpdate();
        update.inc("duplicateTime", duplicateTime);

        update.setOnInsert("createTime", System.currentTimeMillis());

        datastore.findAndModify(query, update, true, true);
    }

    public void incDuplicateTime(String tenantId, String taskNum, List<String> dataIds, Integer time) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        query.field("dataId").in(dataIds);
        final UpdateOperations<HistoryTaskDuplicateDataEntity> update = createUpdate();
        update.inc("duplicateTime", time);
        datastore.update(query, update);
    }

    public void cleanSingleTime(String tenantId, String taskNum) {
        final Query<HistoryTaskDuplicateDataEntity> query = createQuery(ImmutableMap.of("tenantId", tenantId, "taskNum", taskNum, "duplicateTime", 1));
        try {
            datastore.delete(query, new WriteConcern(0, 600_000));
        } catch (Exception e) {
            log.warn("cleanSingleTime error, tenantId:{} taskNum:{}", tenantId, taskNum, e);
        }
    }

    public void clean(String tenantId, String taskNum) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        try {
            datastore.delete(query, new WriteConcern(0, 600_000));
        } catch (Exception e) {
            log.warn("clean error, tenantId:{} taskNum:{}", tenantId, taskNum, e);
        }
    }

    public Set<String> findExists(String tenantId, String taskNum, List<String> dataIds) {
        final Query<HistoryTaskDuplicateDataEntity> query = getQuery(tenantId, taskNum);
        query.field("dataId").in(dataIds);
        query.retrievedFields(true, "dataId");

        Set<String> list = new HashSet<>();
        query.forEach(e -> list.add(e.getDataId()));

        return list;
    }

    private Query<HistoryTaskDuplicateDataEntity> getQuery(String tenantId, String taskNum) {
        return createQuery(ImmutableMap.of("tenantId", tenantId, "taskNum", taskNum));
    }

    private Query<HistoryTaskDuplicateDataEntity> getQuery(String tenantId, String taskNum, String dataId) {
        return createQuery(ImmutableMap.of("tenantId", tenantId, "taskNum", taskNum, "dataId", dataId));
    }
}
