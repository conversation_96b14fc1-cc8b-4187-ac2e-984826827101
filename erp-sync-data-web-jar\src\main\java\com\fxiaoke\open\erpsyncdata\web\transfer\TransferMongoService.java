package com.fxiaoke.open.erpsyncdata.web.transfer;

import com.facishare.transfer.handler.TransferHandler;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/23 15:35:09
 */
@Slf4j
public class TransferMongoService {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    @Autowired
    private ErpDataTransferMgr dataTransferMgr;

    @Autowired
    private List<TransferHandler> transferHandlerList;

    public Map<String, List<String>> transfer(List<String> eis, Boolean vip, Boolean normal, Boolean gray, List<String> transferHandlerNames, Boolean forceTransfer, String endTime) throws ParseException {
        final Set<String> tenantIds = getTenantIds(vip, normal, gray);
        tenantIds.addAll(eis);
        final List<TransferHandler> transferObjectHandlers = getTransferObjectHandlers(transferHandlerNames);
        if (CollectionUtils.isEmpty(transferObjectHandlers)) {
            throw new RuntimeException(I18NStringEnum.s1166.getI18nValue());
        }

        dataTransferMgr.setStopTime(getStopTime(endTime));

        try {
            final Map<String, List<String>> errorEnterpriseId = transferObjectHandlers.stream().parallel()
                    .map(handler -> {
                        List<String> errTenantIds = dataTransferMgr.batchTransfer(tenantIds, forceTransfer, handler, true);

                        if (CollectionUtils.isEmpty(errTenantIds)) {
                            return null;
                        }

                        return Pair.of(handler.getDataType(), errTenantIds);
                    }).filter(Objects::nonNull).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

            log.info("end transferToPaas tenantIds:{}, errorEnterpriseId:{}", tenantIds, errorEnterpriseId);
            return errorEnterpriseId;
        } finally {
            dataTransferMgr.setStopTime(null);
        }
    }

    private static Long getStopTime(final String endTime) throws ParseException {
        final Long stopTime;
        if (Objects.isNull(endTime)) {
            // 没给时间默认10天
            stopTime = null;
        } else {
            if (endTime.contains(" ")) {
                stopTime = DateUtils.parseDate(endTime, "yyyy-MM-dd hh:mm").getTime();
            } else {
                String format = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd ") + endTime;
                long time = DateUtils.parseDate(format, "yyyy-MM-dd hh:mm").getTime();
                if (System.currentTimeMillis() > time) {
                    time += 1000L * 3600 * 24;
                }
                stopTime = time;
            }
        }
        return stopTime;
    }

    private Set<String> getTenantIds(Boolean vip, Boolean normal, Boolean gray) {
        if (BooleanUtils.isTrue(normal)) {
            final Set<String> tenantIds = Sets.newHashSet(erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId());
            if (BooleanUtils.isTrue(vip)) {
                return tenantIds;
            }
            final Set<String> allTenantIds = Sets.newHashSet(tenantIds);
            final Set<String> vipTenantIds = tenantConfigurationManager.getVipTenantIds();
            allTenantIds.removeAll(vipTenantIds);
            return allTenantIds;
        }

        Set<String> ei = new HashSet<>();
        if (BooleanUtils.isTrue(vip)) {
            ei.addAll(tenantConfigurationManager.getVipTenantIds());
        }
        if (BooleanUtils.isTrue(gray)) {
            ei.addAll(tenantConfigurationManager.getGrayTenantIds());
        }
        return ei;
    }

    private List<TransferHandler> getTransferObjectHandlers(List<String> transferHandlerNames) {
        return transferHandlerList.stream().filter(t -> transferHandlerNames.contains(t.getClass().getSimpleName())).collect(Collectors.toList());
    }
}
