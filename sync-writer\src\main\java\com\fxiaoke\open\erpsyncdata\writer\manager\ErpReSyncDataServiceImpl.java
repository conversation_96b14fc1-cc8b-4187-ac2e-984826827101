package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.converter.manager.ReSyncDataNodeManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpReSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpReSyncDataStatus;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpReSyncDataType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpReSyncDataService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 17:29 2021/11/11
 * @Desc:
 */
@Service("erpReSyncDataService")
@Slf4j
public class ErpReSyncDataServiceImpl implements ErpReSyncDataService {

    @Autowired
    private ErpReSyncDataMongoDao erpReSyncDataMongoDao;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private ProbeErpDataService probeErpDataService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private DoWrite2ErpManager doWrite2ErpHandler;
    @Autowired
    private NodeCompleteDataWriteManager nodeCompleteDataWriteManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private NodeReverseWrite2CrmManager nodeReverseWrite2CrmManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ReSyncDataNodeManager reSyncDataNodeManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private NodeSyncWriteMainManager nodeSyncWriteMainManager;

    private static final String lockResyncPrefix = "redis_lockproberesync_";

    @Override
    public Result<Void> reSyncDataExecuteSingleTenantTasks(String tenantId) {
        log.info("trace reSyncDataExecuteSingleTenantTasks begin get lock,tenantId:{} ", tenantId);
        boolean getEiLock = false;
        String lockName = lockResyncPrefix + tenantId;
        try {
            getEiLock = redissonClient.getLock(lockName).tryLock(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.info("trace reSyncDataExecuteSingleTenantTasks redission lock for ei:{} get exception", e);
        }
        log.info("trace resync redission lock for ei:{},  get  lock result:{}", tenantId, getEiLock);
        if (!getEiLock) {
            return Result.newError(ResultCodeEnum.GET_LOCK_FAILED);
        }

        try {
            log.info("process reSyncDataExecuteSingleTenantTasks ByTenantId execute begin,tenantId:{}", tenantId);
            //重试节点重试
            doReSyncDataNodeTask(tenantId);
            //依赖数据重试
            if (!tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NO_NEED_DO_RE_SYNC_DATA_TENANT)) {
                doReSyncDataTask(tenantId);
            }
            //socket time out重试
            if (tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NEED_DO_SOCKET_TIME_OUT_TENANT)) {
                doReSyncSocketTimeOutTask(tenantId);
            }
            log.info("process reSyncDataExecuteSingleTenantTasks ByTenantId execute finished,tenantId:{}", tenantId);
        } catch (Throwable t) {
            log.error("process reSyncDataExecuteSingleTenantTasks ByTenantId execute Throwable", t);
        } finally {
            log.info("trace resync redission lock for ei:{},  release lock ", tenantId);
            redissonClient.getLock(lockName).unlock();
        }
        return Result.newSuccess();
    }

    private void doReSyncDataNodeTask(String tenantId) {
        reSyncDataNodeManager.executeReSync(tenantId);
    }

    private void doReSyncSocketTimeOutTask(String tenantId) {
        Long now = System.currentTimeMillis();
        change2Waiting(tenantId, now, ErpReSyncDataType.SOCKETTIMEOUTREDO);
        List<ErpReSyncData> allExecutingTryDataList = erpReSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, ErpReSyncDataType.SOCKETTIMEOUTREDO, ErpReSyncDataStatus.EXECUTING, now, 0, 200);
        if (!CollectionUtils.isEmpty(allExecutingTryDataList)) {
            for (ErpReSyncData entity : allExecutingTryDataList) {
                if (System.currentTimeMillis() - entity.getUpdateTime() > 1000 * 60 * 5) {//执行中的数据，5分钟没有被更新，改回等待
                    erpReSyncDataMongoDao.updateStatusByIdAndOldStatus(tenantId, entity.getId(), ErpReSyncDataStatus.WAIT, entity.getStatus(), now);
                }
            }
        }
        while (true) {//筛选时间小于now的，每一条数据要么删除，要么更新更新时间，否则死循环
            List<ErpReSyncData> allWaitingTryDataList = erpReSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, ErpReSyncDataType.SOCKETTIMEOUTREDO, ErpReSyncDataStatus.WAIT, now, 0, 200);
            if (!CollectionUtils.isEmpty(allWaitingTryDataList)) {
                for (ErpReSyncData erpReSyncDataEntity : allWaitingTryDataList) {
                    if (now - erpReSyncDataEntity.getCreateTime() > 1000 * 60 * 60 * 24 * 10) {//10天前的删除掉
                        //删除
                        erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncDataEntity.getId());
                        continue;
                    }
                    Long update = erpReSyncDataMongoDao.updateStatusByIdAndOldStatus(tenantId, erpReSyncDataEntity.getId(), ErpReSyncDataStatus.EXECUTING, erpReSyncDataEntity.getStatus(), now);
                    if (update != 1) {
                        continue;
                    }
                    if (!CollectionUtils.isEmpty(erpReSyncDataEntity.getCheckMessage())) {
                        CheckMessageData checkMessageData = erpReSyncDataEntity.getCheckMessage().get(0);
                        if (!StringUtils.isEmpty(checkMessageData.getDoWriteDataJson())) {
                            SyncDataContextEvent syncDataContextEvent = JsonUtil.fromJson(checkMessageData.getDoWriteDataJson(), SyncDataContextEvent.class);

                            // 设置locale
                            final String oldLocale = TraceUtil.getLocale();
                            try {
                                if (!StringUtils.isEmpty(syncDataContextEvent.getLocale())) {
                                    TraceUtil.setLocale(syncDataContextEvent.getLocale());
                                }
                                syncDataContextEvent = doWrite2ErpHandler.handleDoWrite2Erp(syncDataContextEvent);
                                nodeSyncWriteMainManager.completeWriteUpdateStatus(syncDataContextEvent);


                                if (nodeReverseWrite2CrmManager.needProcess(syncDataContextEvent)) {
                                    //反写crm
                                    nodeReverseWrite2CrmManager.processMessage(syncDataContextEvent);
                                }

                                if (nodeCompleteDataWriteManager.needProcess(syncDataContextEvent)) {
                                    syncDataContextEvent.setTenantId(tenantId);
                                    //同步后函数
                                    nodeCompleteDataWriteManager.processMessage(syncDataContextEvent);
                                }
                                erpReSyncDataEntity.setStatus(ErpReSyncDataStatus.WAIT);
                                erpReSyncDataEntity.setUpdateTime(System.currentTimeMillis());
                                erpReSyncDataMongoDao.updateErpReSyncData(tenantId, erpReSyncDataEntity);
                            } finally {
                                TraceUtil.setLocale(oldLocale);
                            }
                        } else {
                            //删除
                            erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncDataEntity.getId());
                        }
                    } else {
                        //删除
                        erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncDataEntity.getId());
                    }
                }
            } else {
                break;
            }
        }
    }

    private void change2Waiting(String tenantId, Long now, Integer type) {
        List<ErpReSyncData> allExecutingTryDataList = erpReSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, type, ErpReSyncDataStatus.EXECUTING, now, 0, 200);
        if (!CollectionUtils.isEmpty(allExecutingTryDataList)) {
            for (ErpReSyncData entity : allExecutingTryDataList) {
                if (System.currentTimeMillis() - entity.getUpdateTime() > 1000 * 60 * 5) {//执行中的数据，5分钟没有被更新，改回等待
                    erpReSyncDataMongoDao.updateStatusByIdAndOldStatus(tenantId, entity.getId(), ErpReSyncDataStatus.WAIT, entity.getStatus(), now);
                }
            }
        }
    }


    public void doReSyncDataTask(String tenantId) {
        Long now = System.currentTimeMillis();
        change2Waiting(tenantId, now, ErpReSyncDataType.DEPENDMAPPING);

        while (true) {//筛选时间小于now的，每一条数据要么删除，要么更新更新时间，否则可能死循环
            List<ErpReSyncData> allWaitingTryDataList = erpReSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, ErpReSyncDataType.DEPENDMAPPING, ErpReSyncDataStatus.WAIT, now, 0, 200);
            if (!CollectionUtils.isEmpty(allWaitingTryDataList)) {
                log.info("doReSyncDataTask allWaitingTryDataList size={}", allWaitingTryDataList.size());
                List<ObjectId> objectIds = Lists.newArrayList();
                TimedCache<String, Boolean> mappingIsCreated = CacheUtil.newTimedCache(1000 * 60 * 5);
                for (ErpReSyncData erpReSyncData : allWaitingTryDataList) {
                    if (now - erpReSyncData.getCreateTime() > 1000L * 60 * 60 * 24 * ConfigCenter.DO_RE_SYNC_DATA_DAY) {//DO_RE_SYNC_DATA_DAY天前的删除掉
                        //删除
                        erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncData.getId());
                        continue;
                    }
                    Integer oldSize = erpReSyncData.getCheckMessage().size();
                    if (checkDependData(erpReSyncData, mappingIsCreated)) {
                        StringBuilder remark = new StringBuilder();
                        Long update = erpReSyncDataMongoDao.updateStatusByIdAndOldStatus(tenantId, erpReSyncData.getId(), ErpReSyncDataStatus.EXECUTING, erpReSyncData.getStatus(), now);
                        if (update != 1) {
                            continue;
                        }
                        SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, erpReSyncData.getSyncDataId());
                        if (syncDataEntity == null || (syncDataEntity.getIsDeleted() != null && syncDataEntity.getIsDeleted())) {//没找到这个同步记录
                            //删除
                            erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncData.getId());
                            continue;
                        }
                        SyncDataMappingsEntity mappingBySyncDataId = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getDestObjectApiName(), syncDataEntity.getSourceDataId());
                        if (mappingBySyncDataId != null && mappingBySyncDataId.getIsCreated() && mappingBySyncDataId.getLastSyncStatus() == SyncDataStatusEnum.WRITE_SUCCESS.getStatus()) {//已创建/最后同步状态为成功
                            //删除
                            erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncData.getId());
                            continue;
                        }
                        Result<Void> result = this.redoSyncData(tenantId, syncDataEntity, erpReSyncData.getLocale());
                        if (result.isSuccess()
                                || org.apache.commons.lang3.StringUtils.equalsIgnoreCase(result.getErrCode(), ResultCodeEnum.GET_BY_ID_BREAK.getErrCode())) {
                            //删除
                            erpReSyncDataMongoDao.deleteById(tenantId, erpReSyncData.getId());
                            continue;
                        }
                        remark.append(result.getErrMsg());
                        erpReSyncData.setStatus(ErpReSyncDataStatus.WAIT);
                        erpReSyncData.getCheckMessage().clear();
                        erpReSyncData.setRemark(remark.toString());
                        erpReSyncData.setUpdateTime(System.currentTimeMillis());
                        erpReSyncDataMongoDao.updateErpReSyncData(tenantId, erpReSyncData);
                    } else {//校验不通过
                        if (oldSize == erpReSyncData.getCheckMessage().size()) {
                            objectIds.add(erpReSyncData.getId());
                        } else {
                            erpReSyncData.setUpdateTime(System.currentTimeMillis());
                            erpReSyncDataMongoDao.updateErpReSyncData(tenantId, erpReSyncData);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(objectIds)) {//更新最后修改时间
                    erpReSyncDataMongoDao.updateTime(tenantId, objectIds, System.currentTimeMillis());
                }
            } else {
                break;
            }

        }
    }

    public Result<Void> redoSyncData(String tenantId, SyncDataEntity syncDataEntity, String locale) {
        boolean succeed = false;
        try {
            if (EventTypeEnum.INVALID.getType() == syncDataEntity.getSourceEventType()) {//作废
                succeed = false;//不成功，后面会把mq重新发一遍
            } else {
                if (TenantType.ERP.equals(syncDataEntity.getSourceTenantType())) {//如果源企业是erp，从新拉取erp最新数据发送mq,eventTriggerService会找到最新策略id
                    String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, syncDataEntity.getSourceObjectApiName());
                    syncLogManager.initLogId(tenantId, realApiName);
                    ErpIdArg erpIdArg = new ErpIdArg();
                    erpIdArg.setTenantId(syncDataEntity.getSourceTenantId());
                    erpIdArg.setObjAPIName(syncDataEntity.getSourceObjectApiName());
                    erpIdArg.setIncludeDetail(true);
                    erpIdArg.setDataId(syncDataEntity.getSourceDataId());
                    erpIdArg.setSyncPloyDetailSnapshotId(syncDataEntity.getSyncPloyDetailSnapshotId());
                    erpIdArg.setSourceEventType(syncDataEntity.getSourceEventType());
                    log.info("AdminSyncDataMappingServiceImpl.redoSyncData,erpIdArg={},erp->crm", erpIdArg);
                    Result<List<SyncDataContextEvent>> erpObjDataResult = erpDataPreprocessService.getReSyncObjDataById(erpIdArg);
                    if (log.isInfoEnabled()) {
                        log. info("AdminSyncDataMappingServiceImpl.redoSyncData,erpObjDataResult={},erp->crm", JSONObject.toJSONString(erpObjDataResult));
                    }
                    if (!erpObjDataResult.isSuccess()) {
                        return Result.copy(erpObjDataResult);
                    }
                    if (erpObjDataResult.getData() != null) {
                        erpObjDataResult.getData().forEach(contextEvent -> contextEvent.setLocale(locale));
                        boolean needSendDetailEvent = true;
                        if (configCenterConfig.getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET().contains(syncDataEntity.getDestObjectApiName())) {
                            //不需要发送明细数据
                            needSendDetailEvent = false;
                        }
                        probeErpDataService.batchSendErpDataMqByContext(erpObjDataResult.getData(), needSendDetailEvent);
                        succeed = true;
                    }
                } else {//如果源企业是crm，从新拉取crm最新数据发送mq,eventTriggerService会找到最新策略id
                    syncLogManager.initLogId(tenantId, syncDataEntity.getSourceObjectApiName());
                    HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
                    com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> objectDataResult = objectDataService.getById(headerObj, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getSourceData().getId(), false);
                    if (objectDataResult != null && objectDataResult.isSuccess() && objectDataResult.getData() != null && objectDataResult.getData().getObjectData() != null) {
                        com.fxiaoke.crmrestapi.common.data.ObjectData objectData = objectDataResult.getData().getObjectData();
                        ObjectData sourceData = new ObjectData();
                        sourceData.putAll(objectData);
                        BatchSendEventDataArg.EventData eventData = new BatchSendEventDataArg.EventData();
                        eventData.setSourceData(sourceData);
                        eventData.setSourceEventType(syncDataEntity.getSourceEventType());
                        eventData.setSourceTenantType(TenantType.CRM);
                        eventData.setSyncLogId(LogIdUtil.get());
                        BatchSendEventDataArg arg = new BatchSendEventDataArg(Lists.newArrayList(eventData));
                        List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
                        syncDataContextEvents.forEach(contextEvent -> contextEvent.setLocale(locale));
                        eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
                        succeed = true;
                    }
                }
            }
            if (!succeed) {//如果查询最新数据发送mq不成功，把上一次的sourceData重新发送一遍
                BatchSendEventDataArg.EventData eventData = new BatchSendEventDataArg.EventData();
                eventData.setSourceData(syncDataEntity.getSourceData());
                eventData.setSourceEventType(syncDataEntity.getSourceEventType());
                if (TenantType.ERP.equals(syncDataEntity.getSourceTenantType())) {
                    eventData.setSourceTenantType(TenantType.ERP);
                } else {
                    eventData.setSourceTenantType(TenantType.CRM);
                }
                eventData.setSyncLogId(LogIdUtil.get());
                BatchSendEventDataArg arg = new BatchSendEventDataArg(Lists.newArrayList(eventData));
                List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
                eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
            }
        } catch (Exception ex) {
            Result result = Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            result.setErrMsg(ex.getClass().getName() + ":" + ex.getMessage());
            log.warn("resync batchSendEventDataWithPloyDetailSnapshotId syncDataEntity : " + syncDataEntity, ex);
            return result;
        }
        LogIdUtil.clear();
        return Result.newSuccess();
    }

    private boolean checkDependData(ErpReSyncData erpReSyncData, TimedCache<String, Boolean> mappingIsCreated) {
        Boolean checked;
        if (CollectionUtils.isEmpty(erpReSyncData.getCheckMessage())) {
            checked = true;
        } else {
            List<CheckMessageData> notPassList = Lists.newArrayList();
            for (CheckMessageData checkMessageData : erpReSyncData.getCheckMessage()) {
                StringBuffer key = new StringBuffer();
                key.append(erpReSyncData.getTenantId()).append("_").append(checkMessageData.getDependSourceApiName()).append("_")
                        .append(checkMessageData.getDependDestApiName()).append("_").append(checkMessageData.getDependDataId());
                boolean exitMappingAndCreated;
                if (mappingIsCreated.containsKey(key.toString())) {
                    exitMappingAndCreated = mappingIsCreated.get(key.toString());
                } else {
                    exitMappingAndCreated = syncDataMappingsManager.exitMappingAndCreated(erpReSyncData.getTenantId(),
                            checkMessageData.getDependSourceApiName(), checkMessageData.getDependDataId(), checkMessageData.getDependDestApiName());
                    mappingIsCreated.put(key.toString(), exitMappingAndCreated);
                }
                if (!exitMappingAndCreated) {
                    notPassList.add(checkMessageData);
                }
            }
            if (CollectionUtils.isEmpty(notPassList)) {
                checked = true;
            } else {
                checked = false;
                erpReSyncData.getCheckMessage().clear();
                erpReSyncData.getCheckMessage().addAll(notPassList);
            }
        }
        return checked;
    }
}
