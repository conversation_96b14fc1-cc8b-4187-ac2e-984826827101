package com.fxiaoke.open.erpsyncdata.web.interceptor;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.json.GsonHttpMessageConverter;
import springfox.documentation.spring.web.json.Json;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.nio.charset.Charset;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/11
 */
@Slf4j
public class CepGsonConverter extends GsonHttpMessageConverter {

    private static class SpringfoxJsonToGsonAdapter implements JsonSerializer<Json> {
        @Override
        public JsonElement serialize(Json json, Type type, JsonSerializationContext context) {
            return JsonParser.parseString(json.value());
        }
    }

    public CepGsonConverter() {
        super();
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Json.class, new SpringfoxJsonToGsonAdapter())
                .create();
        setGson(gson);
    }

    private Charset getCharset(HttpHeaders headers) {
        if (headers == null || headers.getContentType() == null || headers.getContentType().getCharset() == null) {
            return DEFAULT_CHARSET;
        }
        return headers.getContentType().getCharset();
    }

    @Override
    public Object read(Type type, Class<?> contextClass, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        Reader json = new InputStreamReader(inputMessage.getBody(), getCharset(inputMessage.getHeaders()));
        JsonElement jsonElement = JsonParser.parseReader(json);
        UserVo userVo = UserContextHolder.getUserVo();
        if (userVo != null) {
            if (jsonElement.isJsonObject()) {
                try {

                    Opt.ofNullable(jsonElement.getAsJsonObject().get("currentDcId"))
                            .map(v -> v.getAsString())
                            .ifPresent(currentDcId -> userVo.setDataCenterId(currentDcId));

                    //全局任务管理需要。有时候当前的数据中心id与数据操作需要dcid不一样
                    //此操作，覆盖了上面一句的复制
                    Opt.ofNullable(jsonElement.getAsJsonObject().get("dataCenterId"))
                            .map(v -> StrUtil.trimToNull(v.getAsString()))
                            .ifPresent(currentDcId -> userVo.setDataCenterId(currentDcId));

                    //查询代管下游的数据，这里只负责从参数取值，实际上的校验和转换，在BaseController的getUserVo方法
                    Integer downStreamId = Opt.ofNullable(jsonElement.getAsJsonObject().get("downstreamId"))
                            .map(v -> v.getAsInt())
                            .get();
                    String templateDcId = Opt.ofNullable(jsonElement.getAsJsonObject().get("templateDcId"))
                            .map(v -> v.getAsString())
                            .get();
                    if (downStreamId != null && StrUtil.isNotBlank(templateDcId)) {
                        UserVo hostedUserVo = new UserVo();
                        hostedUserVo.setEnterpriseId(downStreamId);
                        hostedUserVo.setDataCenterId(templateDcId);
                        UserContextHolder.setHostedUserVo(hostedUserVo);
                    }
                } catch (Exception e) {
                    log.info("can not parse currentDcId from json request body");
                }
            }
        }
        //此部分操作corefilter已经做了，可以不需要，看com.github.filter.CoreFilter.doFilter
//        String traceId = inputMessage.getHeaders().getFirst("X-fs-Trace-Id");
//        String userId = inputMessage.getHeaders().getFirst("X-fs-User-Info");
//        String clientInfo = inputMessage.getHeaders().getFirst("X-fs-Client-Info");
//        if (StrUtil.isNotBlank(traceId)){
//            MDC.put("traceId", traceId);
//        }
//        if (StrUtil.isNotBlank(userId)){
//            MDC.put("userId", userId);
//        }
//        if (StrUtil.isNotBlank(clientInfo)){
//            MDC.put("clientInfo", clientInfo);
//        }
        return getGson().fromJson(jsonElement, type);
    }
}
