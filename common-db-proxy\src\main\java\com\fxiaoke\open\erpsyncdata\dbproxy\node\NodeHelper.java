package com.fxiaoke.open.erpsyncdata.dbproxy.node;

import cn.hutool.cache.GlobalPruneTimer;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.log.dto.DataScreenSyncLogDTO;
import com.fxiaoke.open.erpsyncdata.common.constant.NodeDataStatus;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeStatRecord;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeStatusRecord;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
public class NodeHelper {
    private static final TransmittableThreadLocal<NodeContext<?>> contextLocal = new TransmittableThreadLocal<>();

    public static NodeContext<?> setContext(NodeContext<?> context) {
        NodeContext<?> oldCtx = contextLocal.get();
        contextLocal.set(context);
        return oldCtx;
    }


    //统一至少一小时才上报一次
    private static final TimedCache<String, NodeStatRecord> nodeCostRecordMap = new TimedCache<>(TimeUnit.HOURS.toMillis(1L));


    //存储10个key，每分钟都可以上报10个key的数据
    private static final HashSet<String> limitedCostRecordKeySet = new HashSet<>(10);

    static {
        nodeCostRecordMap.schedulePrune(TimeUnit.MINUTES.toMillis(10L));
        nodeCostRecordMap.setListener((k, v) -> {
            //上报耗时数据
            DataScreenSyncLogDTO dto = DataScreenSyncLogDTO.builder()
                    .name(IdGenerator.get())
                    .tenantId(v.getTenantId())
                    .dataCenterId(v.getDcId())
                    .ployDetailId(v.getStreamId())
                    .sourceSystemType(TenantTypeEnum.getNameByType(v.getSourceTenantType()))
                    .operationType("STAT_COST")
                    .operationTypeDetail(v.getDataNodeName().getName())
                    .outDataCount(v.getTotalCount().get())
                    .executeCost(v.getTotalCost().get())
                    .createTime(System.currentTimeMillis())
                    .build();
            MonitorBizLogUtil.sendDataScreen(dto);
        });
        //每分钟都上报10条最早的数据。让数据量少的时候，能及时上报。
        GlobalPruneTimer.INSTANCE.schedule(() -> {
            ArrayList<String> keys = CollUtil.newArrayList(limitedCostRecordKeySet);
            limitedCostRecordKeySet.clear();
            for (String key : keys) {
                nodeCostRecordMap.remove(key);
            }
        }, TimeUnit.MINUTES.toMillis(1L));

    }


    /**
     * 无类型检查，自己编码注意。。。
     */
    public static <T extends NodeContext<T>> T getContext() {
        //noinspection unchecked
        return (T) contextLocal.get();
    }

    public static void removeContext() {
        //先发送数据，再移除
        try {
            sendStatusBizLog();
        } catch (Throwable e) {
            log.error("NodeHelper removeContext sendStatusBizLog error", e);
        }
        contextLocal.remove();
    }

    /**
     * 这是为了 无法直接拿到 context，所以从线程变量取。
     */
    public static void batchAddStatusRecord(NodeDataStatus status, Map<String, Integer> idCountMap) {
        if (MapUtil.isEmpty(idCountMap)) {
            return;
        }
        NodeContext<?> context = getContext();
        if (context != null) {
            idCountMap.forEach((id, count) -> context._addStatusRecord(status, id, count,null));
            checkNeedClearAndSendBizLog(context);
        }
    }


    public static void addStatusRecord(NodeDataStatus status, String id, Integer count) {
        addStatusRecord(status, id, count, null);
    }


    public static void addStatusRecord(NodeDataStatus status, String id, Integer count, Long tpm) {
        NodeContext<?> context = getContext();
        if (context != null) {
            context._addStatusRecord(status, id, count, tpm);
            checkNeedClearAndSendBizLog(context);
        }
    }

    private static void checkNeedClearAndSendBizLog(NodeContext<?> context) {
        if (context.getRecords().size() >= 2000) {
            //达到2000条就清理一次
            log.info("records size reach 2000,tenantId:{},dcId:{},streamIds:{}", context.getTenantId(), context.getDcId(), context.getStreamIds());
            sendStatusBizLog();
        }
    }

    /**
     * 增加耗时数据
     */
    public static void addCostRecord(DataNodeNameEnum dataNodeNameEnum, long cost) {
        NodeContext<?> context = getContext();
        if (context != null) {
            for (String streamId : context.getStreamIds()) {
                String key = context.getTenantId() + ":" + context.getDcId() + ":" + streamId + ":" + dataNodeNameEnum;
                NodeStatRecord nodeStatRecord = nodeCostRecordMap.get(
                        key,
                        false,
                        () -> {
                            //没达到10个时，增加到keySet
                            if (limitedCostRecordKeySet.size() < 10) limitedCostRecordKeySet.add(key);
                            NodeStatRecord newRecord = new NodeStatRecord().setStartTime(System.currentTimeMillis())
                                    .setTenantId(context.getTenantId())
                                    .setDcId(context.getDcId())
                                    .setStreamId(streamId)
                                    .setSourceTenantType(context.getSourceTenantType())
                                    .setDataNodeName(dataNodeNameEnum);
                            return newRecord;
                        });
                nodeStatRecord.add(cost);
            }
        }
    }

    /**
     * 发送状态数据
     */
    public static void sendStatusBizLog() {
        NodeContext<?> context = getContext();
        if (context == null) {
            return;
        }
        for (String streamId : context.getStreamIds()) {
            List<NodeStatusRecord> records = context.getRecords();
            if (CollUtil.isNotEmpty(records)) {
                for (NodeStatusRecord record : records) {
                    //单条的数据上报
                    NodeDataStatus status = record.getStatus();
                    DataScreenSyncLogDTO dto = DataScreenSyncLogDTO.builder()
                            .name(IdGenerator.get())
                            .tenantId(context.getTenantId())
                            .dataCenterId(context.getDcId())
                            .ployDetailId(streamId)
                            .sourceSystemType(TenantTypeEnum.getNameByType(context.getSourceTenantType()))
                            .operationType(status.getNodeType().name())
                            .operateStatus(status.name())
                            //数据id，不一定是外部的
                            .outSideObjId(record.getMainDataId())
                            .outDataCount(record.getCount())
                            .executeCost(record.getLimitTpm())
                            .createTime(System.currentTimeMillis())
                            .build();
                    MonitorBizLogUtil.sendDataScreen(dto);
                }
                //清空已上报的数据
                records.clear();
            }
        }
    }
}
