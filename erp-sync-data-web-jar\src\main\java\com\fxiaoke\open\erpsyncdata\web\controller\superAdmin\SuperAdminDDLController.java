package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.AmisResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.sharding.ShardPolicy;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据库DDL操作
 * 请谨慎使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/8/23
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/ddl")
//IgnoreI18nFile
@Slf4j
public class SuperAdminDDLController extends SuperAdminBaseController {
    private final String mappingPreFix = "sync_data_mappings_";
    @Autowired
    private ErpTableDao erpTableDao;
    @Resource(name = "erpSyncDataMongoStore")
    private DatastoreExt tempStore;
    @Resource(name = "erpSyncDataLogMongoStore")
    private DatastoreExt logStore;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    @Qualifier("shardingSyncTempMongo")
    private DatastoreExt shardingTempstore;
    @Autowired
    @Qualifier("store")
    private DatastoreExt store;
    @Autowired
    private UserCenterService userCenterService;

    private String tempDbName = "erp_sync_data2";
    private String logDbName;
    @Autowired
    private CHSyncLogDao chSyncLogDao;

    @PostConstruct
    public void init() throws Exception {
        this.logDbName = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    @PostMapping("pg/listIndexes")
    public Result<Dict> listIndexes(@RequestBody IndexArg param) {
        String query = String.format("select indexname, indexdef from pg_indexes where tablename = '%s'", param.getTable());
        List<Dict> res = erpTableDao.setTenantId(param.getTenantId()).superQuerySql2(query, param.getTable());
        List<String> indexdefs = res.stream().map(v -> v.getStr("indexdef")).collect(Collectors.toList());
        return AmisResult.list(indexdefs);
    }

    @PostMapping("pg/createIndexes")
    public DeferredResult<Result<Dict>> createPgIndexes(@RequestBody IndexArg indexArg) {
        return asyncExecute(() -> createPgIndexesSync(indexArg), 5, true, "创建索引" + indexArg.getIndexPrefix(), null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> createPgIndexesSync(IndexArg indexArg) {
        if (StrUtil.isBlank(indexArg.getIndexPrefix())) {
            return Result.newError("索引前缀不允许为空");   // ignoreI18n   实施和开发自用
        }
        List<String> failedTenants = new ArrayList<>();
        Map<String, String> tenantTableMap = new LinkedHashMap<>();
        String tablePrefix = mappingPreFix;
        if (StrUtil.isNotBlank(indexArg.getTable())) {
            if (!indexArg.getTable().startsWith(mappingPreFix)) {
                return Result.newError("仅允许修改mapping表");   // ignoreI18n   实施和开发自用
            }
            tablePrefix = indexArg.getTable();
        }
        //所有mapping表
        List<String> allMappings = erpTableDao.setTenantId(indexArg.tenantId).listAllTableLeftMatching(tablePrefix);
        for (String table : allMappings) {
            String tenantId = StrUtil.removePrefix(table, mappingPreFix);
            if (NumberUtil.isInteger(tenantId)) {
                tenantTableMap.put(tenantId, table);
            }
        }

        if (StrUtil.isNotBlank(indexArg.getTenantIdStr())) {
            //筛选企业
            HashSet<String> tenantIds = Sets.newHashSet(Splitter.on(",").split(indexArg.getTenantIdStr()));
            tenantTableMap.keySet().retainAll(tenantIds);
        }
        String indexSqlPrefix;
        if (indexArg.uniqueIndex != null && indexArg.uniqueIndex) {
            indexSqlPrefix = "create unique index concurrently if not exists %s on %s %s";
        } else {
            indexSqlPrefix = "create index concurrently if not exists %s on %s %s";
        }
        for (Map.Entry<String, String> entry : tenantTableMap.entrySet()) {
            String tenantId = entry.getKey();
            String table = entry.getValue();
            String sql = String.format(indexSqlPrefix, indexArg.getIndexPrefix() + tenantId, table, indexArg.getIndex());
            try {
                int i = erpTableDao.setTenantId(indexArg.tenantId).superUpdateSql(sql);
                log.info("{},{}", sql, i);
            } catch (Exception e) {
                failedTenants.add(tenantId);
                log.info("failed,{}", sql, e);
                if (!indexArg.isSkipFailed()) {
                    return Result.newError("失败" + tenantId + ":" + e);   // ignoreI18n   实施和开发自用
                }
            }
        }
        String res = "失败企业：" + failedTenants;   // ignoreI18n   实施和开发自用
        return AmisResult.of(res);

    }

    @PostMapping("pg/dropIndexes")
    public DeferredResult<Result<Dict>> dropPgIndexes(@RequestBody IndexArg indexArg) {
        return asyncExecute(() -> dropPgIndexesSync(indexArg), 5, true, "删除索引" + indexArg.getIndexPrefix(), null);   // ignoreI18n   实施和开发自用
    }

    @PostMapping("pg/execute")
    public DeferredResult<Result<Integer>> pgExecute(@RequestBody Dict arg) {
        String sql = arg.getStr("sql");
        String tenantId = arg.getStr("tenantId");
        return asyncExecute(() -> {
            int i = erpTableDao.setTenantId(tenantId).superUpdateSql(sql);
            return Result.newSuccess(i);
        }, 5, true, "执行sql" + sql, null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> dropPgIndexesSync(IndexArg indexArg) {
        if (StrUtil.isBlank(indexArg.getIndexPrefix())) {
            return Result.newError("索引前缀不允许为空");   // ignoreI18n   实施和开发自用
        }
        List<String> failedTenants = new ArrayList<>();
        Map<String, String> tenantTableMap = new LinkedHashMap<>();
        String tablePrefix = mappingPreFix;
        if (StrUtil.isNotBlank(indexArg.getTable())) {
            if (!indexArg.getTable().startsWith(mappingPreFix)) {
                return Result.newError("仅允许修改mapping表");   // ignoreI18n   实施和开发自用
            }
            tablePrefix = indexArg.getTable();
        }
        //所有mapping表
        List<String> allMappings = erpTableDao.setTenantId(indexArg.tenantId).listAllTableLeftMatching(tablePrefix);
        for (String table : allMappings) {
            String tenantId = StrUtil.removePrefix(table, mappingPreFix);
            if (NumberUtil.isInteger(tenantId)) {
                tenantTableMap.put(tenantId, table);
            }
        }

        if (StrUtil.isNotBlank(indexArg.getTenantIdStr())) {
            //筛选企业
            HashSet<String> tenantIds = Sets.newHashSet(Splitter.on(",").split(indexArg.getTenantIdStr()));
            tenantTableMap.keySet().retainAll(tenantIds);
        }
        for (Map.Entry<String, String> entry : tenantTableMap.entrySet()) {
            String tenantId = entry.getKey();
            String sql = String.format("drop index if exists %s", indexArg.getIndexPrefix() + tenantId);
            try {
                int i = erpTableDao.setTenantId(indexArg.tenantId).superUpdateSql(sql);
                log.info("{},{}", sql, i);
            } catch (Exception e) {
                failedTenants.add(tenantId);
                log.info("failed,{}", sql, e);
                if (!indexArg.isSkipFailed()) {
                    return Result.newError("失败" + tenantId + ":" + e);   // ignoreI18n   实施和开发自用
                }
            }
        }
        String res = "失败企业：" + failedTenants;   // ignoreI18n   实施和开发自用
        return AmisResult.of(res);

    }

    /**
     * @param indexArg db.erp_temp_84307.createIndex({'tenant_id':1,'dc_id':1,"obj_api_name":1,"task_num":1,"_id":-1},{"background":true,"name":"idx_task"});
     * @return
     */
    @PostMapping("mongo/createIndexes")
    public DeferredResult<Result<Dict>> createMongoIndex(@RequestBody CreateMongoIndexesArg indexArg) {
        return asyncExecute(() -> createMongoIndexesSync(indexArg), 5, true, "创建mongo索引(或删除)" + indexArg.getIndexName(), null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> createMongoIndexesSync(CreateMongoIndexesArg indexArg) {
        MongoDatabase db;
        Bson index;
        String indexName = indexArg.getIndexName();
        List<Bson> indexes = new ArrayList<>();
        LinkedHashMap<String, Integer> indexMap = JacksonUtil.fromJson(indexArg.getIndex(), new TypeReference<LinkedHashMap<String, Integer>>() {
        });
        Bson partialFilterExpression = null;
        if (StringUtils.isNotBlank(indexArg.getPartialFilterExpression())) {
            LinkedHashMap<String, Object> filterMap = JacksonUtil.fromJson(indexArg.getPartialFilterExpression(), new TypeReference<LinkedHashMap<String, Object>>() {
            });
            List<Bson> filters = new ArrayList<>();
            filterMap.forEach((k, v) -> {
                filters.add(Filters.eq(k, v));
            });
            if (!filters.isEmpty()) {
                partialFilterExpression = Filters.and(filters);
            }
        }

        indexMap.forEach((field, v) -> {
            if (v == 1) {
                indexes.add(Indexes.ascending(field));
            } else if (v == -1) {
                indexes.add(Indexes.descending(field));
            } else {
                throw new ErpSyncDataException("indexes value only can be 1 or -1", null, null);
            }
        });
        if (indexes.size() == 0) {
            throw new ErpSyncDataException("indexes can not be empty", null, null);
        } else if (indexes.size() == 1) {
            index = indexes.get(0);
        } else {
            index = Indexes.compoundIndex(indexes);
        }
        if (indexArg.getType().equals("log")) {
            db = logStore.getMongo().getDatabase(logDbName);
        } else if (indexArg.getType().equals("temp")) {
            db = tempStore.getMongo().getDatabase(tempDbName);
        } else {
            return Result.newError("type can only be log or temp");
        }
        List<String> tenantIds;
        if (StringUtils.isNotBlank(indexArg.getTenantIdStr())) {
            tenantIds = Splitter.on(",").splitToList(indexArg.getTenantIdStr());
        } else {
            //处理 所有企业
            tenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0")).listTenantId();
        }
        Set<String> collectionNames = new HashSet<>();
        Set<String> collectionNameLimitSet = tenantIds.stream().map(v -> indexArg.getCollection() + "_" + v).collect(Collectors.toSet());
        MongoIterable<String> colNameIter = db.listCollectionNames();
        for (String colName : colNameIter) {
            if (collectionNameLimitSet.contains(colName)) {
                collectionNames.add(colName);
            }
        }
        List<String> successCols = new ArrayList<>();
        List<String> failMsg = new ArrayList<>();
        for (String collectionName : collectionNames) {
            //逐个集合处理
            try {
                MongoCollection<Document> collection = db.getCollection(collectionName);
                if (indexArg.isDropIndexes()) {
                    //删除索引
                    collection.dropIndex(index);
                } else {
                    IndexOptions indexOptions = new IndexOptions().name(indexName).partialFilterExpression(partialFilterExpression).background(true);
                    String index1 = collection.createIndex(index, indexOptions);
                    log.info("create index:{},{}", collectionName, index1);
                }
                successCols.add(collectionName);
            } catch (Exception e) {
                failMsg.add(collectionName + " " + e.getMessage());
                log.info("create mongo index exception,col:{}", collectionName, e);
                if (indexArg.isSkipFailed()) {
                    //跳过异常
                    continue;
                }
                break;
            }
        }
        String msg = String.format("成功：%s,失败：%s", successCols, failMsg);   // ignoreI18n   实施和开发自用
        AmisResult result = AmisResult.of(msg);
        result.setErrMsg(msg);
        return result;
    }


    /**
     *
     */
    @Data
    public static class CreateMongoIndexesArg {
        /**
         * 集合前缀
         */
        private String collection;

        /**
         * store,log或者temp
         */
        private String type;

        /**
         * 索引前缀
         */
        private String indexName;

        /**
         * 范围，只支持eq，如：{"operation_type": null}
         */
        private String partialFilterExpression;

        /**
         * 索引
         */
        private String index;

        /**
         * 限定企业,逗号分隔
         */
        private String tenantIdStr;

        /**
         * 跳过失败的
         */
        private boolean skipFailed;

        /**
         * 删除索引
         */
        private boolean dropIndexes = false;
    }


    /**
     *
     */
    @Data
    public static class IndexArg {
        private String tenantId;
        /**
         * 表名前缀
         */
        private String table;

        /**
         * 索引前缀
         */
        private String indexPrefix;

        /**
         * 索引
         */
        private String index;

        /**
         * 限定企业
         */
        private String tenantIdStr;

        /**
         * 跳过失败的
         */
        private boolean skipFailed;
        /**
         * 是否唯一索引，默认是false
         */
        private Boolean uniqueIndex = false;
    }

    @PostMapping("db/allTableCount")
    public DeferredResult<Result<AllTableCount.Result>> allTableCount(@RequestBody AllTableCount.Arg arg) {
        return asyncExecute(() -> {
            if (Objects.equals(arg.getType(), 1)) {
                return getMappingCount(arg);
            } else if (Objects.equals(arg.getType(), 2)) {
                return getTempCount(arg);
            } else {
                return getDispatchCount(arg);
            }
        }, 60, false, "check the number of database tables", "zh_CN");
    }

    private Result<AllTableCount.Result> getDispatchCount(AllTableCount.Arg arg) {
//        if (Objects.equals(System.getProperty("process.profile"), "foneshare") || System.getProperty("process.profile.candidates").contains("foneshare")) {
        // <bean id="store" name="store" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        //        <property name="configName" value="erp-sync-data-doDispatcher"/>
        //        <property name="sectionNames" value="mongo"/>
        //    </bean>
        final Set<String> collectionNames = store.getDB().getCollectionNames();
        final Set<String> allTables;
        if (arg.isCountAll()) {
            allTables = collectionNames;
        } else {
            allTables = collectionNames.stream()
                    .filter(name -> name.startsWith("buf_"))
                    .collect(Collectors.toSet());
        }

        final String servers = ConfigFactory.getConfig("erp-sync-data-doDispatcher").get("mongo.servers");

        final AllTableCount.TableCount count = new AllTableCount.TableCount();
        count.setId(System.getProperty("process.profile") + ":\n" + servers);
        count.setCount(collectionNames.size());
        count.setAllTables(allTables);

        final Map<String, List<String>> collect = collectionNames.stream()
                .filter(name -> name.startsWith("buf_"))
                .map(name -> {
                    String ei = name.substring("buf_".length());
                    ei = ei.contains("_") ? StringUtils.substringBefore(ei, "_") : ei;
                    return Pair.of(ei, name);
                })
                .filter(pair -> StringUtils.isNumeric(pair.getKey()) && !pair.getKey().startsWith("0"))
                .collect(Collectors.groupingBy(Pair::getKey, Collectors.mapping(Pair::getValue, Collectors.toList())));

        final Set<String> invalidEis = checkTenant(collect.keySet(), arg.isCheckSyncLog());
        count.setInvalidEis(invalidEis);

        List<String> invalidTables = invalidEis.stream()
                .map(collect::get)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        count.setInvalidTables(invalidTables);
        count.setDeleteTableSql(getMongoDeleteTableSql(servers, invalidTables));

        return Result.newSuccess(new AllTableCount.Result(Lists.newArrayList(count)));
    }

    private Result<AllTableCount.Result> getTempCount(AllTableCount.Arg arg) throws Exception {
        ShardPolicy tempShardPolicy = new ShardPolicy();
        tempShardPolicy.setConfigName("erp-sync-temp-mongo");
        tempShardPolicy.afterPropertiesSet();

        final boolean countAll = arg.isCountAll();
        final Set<String> allInstancesKey = tempShardPolicy.getAllInstancesKey();
        final List<AllTableCount.TableCount> tableCounts = allInstancesKey.stream()
                .map(key -> {
                    final Set<String> collectionNames = shardingTempstore.setTenantId(key).getDB().getCollectionNames();
                    final AllTableCount.TableCount count = new AllTableCount.TableCount();
                    final String uri = tempShardPolicy.getUri(key);
                    count.setId(uri);
                    count.setCount(collectionNames.size());

                    if (!arg.isForceCheckInvalid() && count.getCount() < arg.getLimit()) {
                        return count;
                    }

                    final Set<String> allTables;
                    if (countAll) {
                        allTables = collectionNames;
                    } else {
                        allTables = collectionNames.stream()
                                .filter(name -> name.startsWith("erp_temp_"))
                                .collect(Collectors.toSet());
                    }
                    count.setAllTables(allTables);

                    final Set<String> needCheckEis = collectionNames.stream()
                            .filter(name -> name.startsWith("erp_temp_"))
                            .map(name -> name.substring("erp_temp_".length()))
                            .filter(name -> StringUtils.isNumeric(name) && !name.startsWith("0"))
                            .collect(Collectors.toSet());

                    final Set<String> invalidEis = checkTenant(needCheckEis, arg.isCheckSyncLog());
                    count.setInvalidEis(invalidEis);

                    List<String> invalidTables = invalidEis.stream()
                            .map(ei -> "erp_temp_" + ei)
                            .collect(Collectors.toList());
                    count.setInvalidTables(invalidTables);
                    count.setDeleteTableSql(getMongoDeleteTableSql(uri, invalidTables));

                    return count;
                }).collect(Collectors.toList());
        return Result.newSuccess(new AllTableCount.Result(tableCounts));
    }

    private Result<AllTableCount.Result> getMappingCount(AllTableCount.Arg arg) {
        final boolean countAll = arg.isCountAll();
        final String tableNameCondition = countAll ? "" : " AND table_name like 'sync_data_mappings_%'";
        String queryCount = "SELECT COUNT(table_name) AS table_count FROM information_schema.tables WHERE table_type = 'BASE TABLE'  AND table_schema = 'public'" + tableNameCondition;
        String queryTableFormat = "SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE'  AND table_schema = 'public'" + tableNameCondition + " AND table_name > '%s' order by table_name limit 1000";

        final Map<String, List<String>> configRouteTenant = tenantConfigurationManager.getConfigRouteTenant();

        List<AllTableCount.TableCount> counts = new ArrayList<>();

        configRouteTenant.forEach((id, eis) -> {
            final String ei = eis.stream().findFirst().orElse(null);
            if (StringUtils.isEmpty(ei)) {
                return;
            }

            final List<Dict> countDicts = erpTableDao.setTenantId(ei).superQuerySql2(queryCount, ei);
            final Integer tableCount = countDicts.get(0).getInt("table_count");
            final AllTableCount.TableCount count = new AllTableCount.TableCount();
            count.setId(id);
            count.setCount(tableCount);
            counts.add(count);

            if (!arg.isForceCheckInvalid() && tableCount < arg.getLimit()) {
                return;
            }

            final Map<String, List<String>> needCheckEiMap = new HashMap<>();
            final Set<String> allTables = new HashSet<>();
            count.setAllTables(allTables);
            fillNeedCheckEisByMappingTable(queryTableFormat, ei, needCheckEiMap, countAll, allTables);

            final Set<String> invalidEis = checkTenant(needCheckEiMap.keySet(), arg.isCheckSyncLog());
            count.setInvalidEis(invalidEis);

            final List<String> invalidTables = invalidEis.stream()
                    .map(needCheckEiMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            count.setInvalidTables(invalidTables);
            count.setDeleteTableSql(getPgRenameTableSql(count.getId(), invalidTables));
        });
        return Result.newSuccess(new AllTableCount.Result(counts));
    }

    private void fillNeedCheckEisByMappingTable(String queryTableFormat, String ei, Map<String, List<String>> needCheckEis, boolean countAll, Set<String> allTables) {
        String maxTableName = "";
        while (true) {
            final String sql = queryTableFormat.replace("%s", maxTableName);
            final List<Dict> dicts = erpTableDao.setTenantId(ei).superQuerySql2(sql, ei);
            if (CollectionUtils.isEmpty(dicts)) {
                break;
            }
            maxTableName = dicts.get(dicts.size() - 1).getStr("table_name");
            dicts.forEach(dict -> {
                final String tableName = dict.getStr("table_name");
                allTables.add(tableName);

                if (countAll && !tableName.startsWith("sync_data_mappings_")) {
                    return;
                }

                final String tenantId = StringUtils.substringAfterLast(tableName, '_');
                if (StringUtils.isNotBlank(tenantId) && StringUtils.isNumeric(tenantId) && !tenantId.startsWith("0")) {
                    needCheckEis.computeIfAbsent(tenantId, k -> new ArrayList<>()).add(tableName);
                }
            });
        }
    }

    private Set<String> checkTenant(Set<String> needCheckEi, boolean checkSyncLog) {
        List<String> syncLogEis = null;
        if (checkSyncLog) {
            syncLogEis = chSyncLogDao.getAllTenantIds();
        }

        // 企业状态正常,且有连接器
        Set<String> invalidEis = new HashSet<>();
        List<List<String>> split = CollUtil.split(needCheckEi, 200);
        for (List<String> eis : split) {
            final Map<String, EnterpriseData> enterpriseMap = userCenterService.batchGetEnterprise(eis);
            // 获取正常的企业
            Set<String> normalEis = enterpriseMap.entrySet().stream()
                    .filter(entry -> {
                        final EnterpriseData data = entry.getValue();
                        return !checkTenantInvalid(data);
                    }).map(Map.Entry::getKey)
                    .collect(Collectors.toSet());

            // 检查是否有连接器
            final List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.listConnectInfoByEis(Lists.newArrayList(normalEis));
            normalEis = erpConnectInfoEntities.stream()
                    .map(ErpConnectInfoEntity::getTenantId)
                    .distinct()
                    .collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(syncLogEis)) {
                normalEis = normalEis.stream().filter(syncLogEis::contains).collect(Collectors.toSet());
            }

            Set<String> invalid = new HashSet<>(eis);

            // 删除正常的企业
            invalid.removeAll(Sets.newHashSet(normalEis));
            invalidEis.addAll(invalid);
        }
        return invalidEis;
    }

    private boolean checkTenantInvalid(EnterpriseData data) {
        if (data.getRunStatus() == RunStatus.RUN_STATUS_NORMAL || data.getRunStatus() == RunStatus.RUN_STATUS_INVALIDATE) {
            return false;
        }

        // runStatus=5
        if (data.getRunStatus() == RunStatus.RUN_STATUS_DELETE) {
            return true;
        }

        final long stopInterval = System.currentTimeMillis() - data.getModifyTime().getTime();
        if (data.getRunStatus() == RunStatus.RUN_STATUS_STOP) {
            // 沙盒企业停用7天 或者 停用了30天
            if (stopInterval > 30 * 24 * 3600 * 1000L) {
                return true;
            }

            if (stopInterval < 7 * 24 * 3600 * 1000L) {
                return false;
            }

            return data.getEnterpriseAccount().endsWith("_sandbox");
        }

        // 10天企业还没有开通完成的,按企业开通失败算
        return stopInterval > 10 * 24 * 3600 * 1000L;
    }

    private String getMongoDeleteTableSql(String servers, List<String> invalidTables) {
        String format = "var collectionsToDrop = %s\n" +
                "collectionsToDrop.forEach(function(collection) {\n" +
                "    db[collection].drop()\n" +
                "    print(\"Dropped collection: \" + collection)\n" +
                "})";
        final String tableNames = invalidTables.stream()
                .map(s -> "\"" + s + "\"")
                .collect(Collectors.joining(", ", "[", "]"));
        return String.format(format, tableNames);
    }

    private String getPgRenameTableSql(String servers, List<String> invalidTables) {
    final String tableNames = invalidTables.stream()
            .map(s -> "'" + s + "'")
            .collect(Collectors.joining(", ", "[", "]"));
    return  "DO $$\n" +
            "DECLARE\n" +
            "    tables_to_rename TEXT[] := ARRAY" +
            tableNames +
            ";\n" +
            "    table_name TEXT;\n" +
            "BEGIN\n" +
            "    FOREACH table_name IN ARRAY tables_to_rename LOOP\n" +
            "        EXECUTE format('ALTER TABLE IF EXISTS %I RENAME TO delete_%I;', table_name, table_name);\n" +
            "        RAISE NOTICE 'Renamed table: % to delete_%', table_name, table_name;\n" +
            "    END LOOP;\n" +
            "END $$;";
}

    interface AllTableCount {
        @Data
        class Arg {
            // 1-pg/mapping 2-mongo/temp 3-mongo/dispatch
            Integer type;
            Integer limit = 700;
            boolean forceCheckInvalid = false;
            boolean countAll = false;
            boolean checkSyncLog = false;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        class Result {
            private List<TableCount> items;
        }

        @Data
        class TableCount {
            private String id;
            private int count;
            private Set<String> allTables;
            private Set<String> invalidEis;
            private List<String> invalidTables;
            private String deleteTableSql;
        }
    }
}
