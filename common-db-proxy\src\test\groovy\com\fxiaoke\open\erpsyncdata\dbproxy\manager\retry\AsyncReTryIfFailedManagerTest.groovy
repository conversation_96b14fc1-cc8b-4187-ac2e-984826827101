package com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RetrySendMqDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService
import com.google.common.collect.Lists
import lombok.extern.slf4j.Slf4j
import org.bson.types.ObjectId
import spock.lang.Specification
import spock.lang.Unroll

@Slf4j
//@Ignore
@Unroll
class AsyncReTryIfFailedManagerTest extends Specification {
    def "ReTryBatchSendEventData2DispatcherMqByContext - #name"() {
        Integer count = 0;
        SyncDataContextEvent event = new SyncDataContextEvent();
        event.setSourceData(new ObjectData())
        AsyncReTryIfFailedManager monitor = new AsyncReTryIfFailedManager(
                allModelDubboService: Mock(AllModelDubboService) {
                    batchSendEventData2DispatcherMqByContext(*_) >> {
                        count++
                        println name + "，在时间：" + new Date() + ",重试第几次:" + count
                        if (success) {
                            return Result2.newSuccess()
                        } else {
                            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR)
                        }
                    }
                },
                retrySendMqDao: Mock(RetrySendMqDao) {
                    createReTrySendMq(*_) >> { args ->
                        return new ObjectId().toString()
                    }
                    getById(*_) >> { args ->
                        return new ReTrySendMq()
                    }
                    getById(*_) >> { args ->

                    }
                    updateStatusById(*_) >> { args ->

                    }
                }
        )
        when:
        def res = monitor.reTryBatchSendEventData2DispatcherMqByContext(Lists.newArrayList(event), "100)");
//        sleep(11000)
        then:
//        count == tryCount
        res.isSuccess()
        // 原本的逻辑被删除了，现在allModelDubboService没在用了，count只会是0
        count == 0
        where:
        name           | tryCount | success
        "场景1：发送失败" | 2        | false
        "场景2：发送成功" | 1        | true
    }

    def "test retryOriginMQ"() {
        given:
        def manager = new AsyncReTryIfFailedManager(retrySendMqDao: Mock(RetrySendMqDao))
        expect:
        manager.retryOriginMQ(["test", "originMQ"]).isSuccess()
    }
}
