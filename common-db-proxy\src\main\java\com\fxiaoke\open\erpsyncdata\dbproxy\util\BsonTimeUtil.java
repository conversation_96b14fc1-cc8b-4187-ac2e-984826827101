package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import org.bson.BsonTimestamp;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 15:32 2022/9/6
 * @Desc:
 */
public class BsonTimeUtil {
    public static BsonTimestamp long2BsonTimestamp(Long time){
        if(time==null||time==0){
            return new BsonTimestamp();
        }
        return new BsonTimestamp((int)(time/1000),(int)(time%1000));
    }

    public static Date long2BsonDate(Long time){
        if(time==null||time==0){
            return new Date();
        }
        return new Date(time);
    }

    public static Long bsonTimestamp2Long(BsonTimestamp timestamp){
        if(timestamp==null||timestamp.getTime()==0){
            return null;
        }
        //只到秒级别
        Long time = timestamp.getTime() * 1000L;
        return time;
    }
    public static Long bsonDate2Long(Date date){
        if(date==null){
            return null;
        }
        Long time = date.getTime();
        return time;
    }

    public static Long bsonTime2Long(Object times){
        if(times==null){
            return null;
        }
        if(times instanceof BsonTimestamp){
            return bsonTimestamp2Long((BsonTimestamp)times);
        }
        if(times instanceof Long){
            return (Long)times;
        }
        if(times instanceof Date){
            return bsonDate2Long((Date)times);
        }
        return null;
    }
}
