package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.alibaba.fastjson.JSON
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult
import com.facishare.uc.api.model.fscore.EnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.PloyBreakNoticeConfig
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.github.jedis.support.MergeJedisCmd
import spock.lang.Specification


/**
 * <AUTHOR> 
 * @date 2024/10/24 10:04:51
 */
class GetByIdBreakManagerSpec extends Specification {
    RedisDataSource redisDataSource = Mock()
    ConfigCenterConfig configCenterConfig = Mock()
    TenantEnvManager tenantEnvManager = Mock()
    NotificationService notificationService = Mock()
    EnterpriseEditionService enterpriseEditionService = Mock()
    ErpConnectInfoManager erpConnectInfoManager = Mock()
    ErpObjectRelationshipDao erpObjectRelationshipDao = Mock()
    ErpObjManager erpObjManager = Mock()
    TenantConfigurationManager tenantConfigurationManager = Mock()
    I18NStringManager i18NStringManager = Mock()
    DataIntegrationNotificationManager dataIntegrationNotificationManager = Mock()
    MergeJedisCmd jedisCmd = Mock()

    GetByIdBreakManager getByIdBreakManager = new GetByIdBreakManager(
            redisDataSource: redisDataSource
            ,
            configCenterConfig: configCenterConfig
            ,
            tenantEnvManager: tenantEnvManager
            ,
            notificationService: notificationService
            ,
            enterpriseEditionService: enterpriseEditionService
            ,
            erpConnectInfoManager: erpConnectInfoManager
            ,
            erpObjectRelationshipDao: erpObjectRelationshipDao
            ,
            erpObjManager: erpObjManager
            ,
            tenantConfigurationManager: tenantConfigurationManager
            ,
            i18NStringManager: i18NStringManager
            ,
            dataIntegrationNotificationManager: dataIntegrationNotificationManager
    )

    void setup() {
        erpObjectRelationshipDao.setTenantId(_) >> erpObjectRelationshipDao
        redisDataSource.get(_) >> jedisCmd
    }

    def "test increase Failed Count"() {
        given:
        configCenterConfig.getGetByIdBreakExpireTime(_, _, _) >> 2l
        configCenterConfig.getGetByIdBreakExpireTime() >> 1l
        configCenterConfig.getGetByIdBreakCount(_, _, _) >> 1l
        configCenterConfig.getGetByIdBreakCount() >> 1l
        tenantEnvManager.getTenantAllModelEnv(*_) >> ErpSyncDataBackStageEnvironmentEnum.VIP
        notificationService.sendTenantAdminNotice(*_) >> new Result<Void>("errCode", "errMsg", null)
        enterpriseEditionService.getEnterpriseData(*_) >> new GetEnterpriseDataResult(enterpriseData: new EnterpriseData(domain: "test"))
        erpConnectInfoManager.listByTenantId(*_) >> [new ErpConnectInfoEntity("id", "tenantId", ErpChannelEnum.ERP_K3CLOUD, "dataCenterName", "enterpriseName", "connectParams", 1l, 1l, 0, 0)]
        erpObjectRelationshipDao.findByRealObjectApiName(*_) >> [new ErpObjectRelationshipEntity("id", "tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)]
        erpObjManager.getErpObjName(*_) >> "getErpObjNameResponse"
        tenantConfigurationManager.getPloyBreakNoticeConfig(*_) >> new PloyBreakNoticeConfig()
        i18NStringManager.getByEi2(*_) >> "getByEi2Response"
        i18NStringManager.getByEi(*_) >> "getByEiResponse"

        when:
        getByIdBreakManager.increaseFailedCount("88521", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, "objectApiName", "errMsg")

        then:
        1* jedisCmd.get("key_get_by_id_break_88521_dataCenterId_objectApiName") >> JSON.toJSONString(new GetByIdBreakManager.FailedData(failedCount: 2l, timestamp: System.currentTimeMillis() - 100))
        1 * notificationService.sendSuperAdminNoticeToManageTool(*_) >> new Result<Void>("errCode", "errMsg", null)
    }

    def "test get Failed Data"() {
        def data = new GetByIdBreakManager.FailedData(failedCount: 1l)
        given:
        jedisCmd.get(*_) >> JSON.toJSONString(data)

        when:
        GetByIdBreakManager.FailedData result = getByIdBreakManager.getFailedData("tenantId", "dataCenterId", "objectApiName")

        then:
        result == data
    }

    def "test is Break"() {
        given:
        def data = new GetByIdBreakManager.FailedData(failedCount: 2l)
        jedisCmd.get(*_) >> JSON.toJSONString(data)
        configCenterConfig.getGetByIdBreakCount(_, _, _) >> 0
        configCenterConfig.getGetByIdBreakCount() >> 1l
        tenantConfigurationManager.getPloyBreakNoticeConfig(*_) >> new PloyBreakNoticeConfig()

        when:
        boolean result = getByIdBreakManager.isBreak("tenantId", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, "objectApiName")

        then:
        result
    }

    def "test is Tenant Config Break"() {
        given:

        when:
        boolean result = getByIdBreakManager.isTenantConfigBreak("tenantId", "dataCenterId", "objectApiName")

        then:
        1 * tenantConfigurationManager.getNotOfferGetbyidTenantAndObjAPISet("tenantId", "dataCenterId") >> {
            return ["objectApiName"] as Set<String>
        }
        result
    }

    def "test remove Break"() {
        given:


        when:
        getByIdBreakManager.removeBreak("tenantId", "dataCenterId", "ployDetailId", "objectApiName")

        then:
        1 * dataIntegrationNotificationManager.updateAlarmStatus(*_) >> 0l
    }
}
