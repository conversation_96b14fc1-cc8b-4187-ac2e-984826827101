package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.TenantLimitableMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.SyncDataMongoStore;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.mongodb.client.model.Filters.lt;

/**
 * 全继承原来的syncDataDao和AdminSyncDataDao的方法，
 * 补充逻辑时需要注意原来同名的sql实现逻辑是否一致
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Slf4j
@Repository
public class SyncDataMongoDao implements TenantLimitableMongoDao {
    @Autowired
    private SyncDataMongoStore store;
    @Autowired
    private CHSyncDataManager chSyncDataManager;


    @Override
    public String getCollPrefix() {
        return "sync_data";
    }

    @Override
    public CollStat getCollStat(String tenantId) {
        CollStat collStat = new CollStat();
        long count = chSyncDataManager.countByTenantId(tenantId);
        collStat.setCount(count);
        return collStat;
    }

    @Override
    public DateTime findMinDate(String tenantId) {
        Long first = chSyncDataManager.findMinDate(tenantId);
        if (first != null) {
            return DateUtil.date(first);
        }
        return DateUtil.date();
    }

    @Override
    public Long deleteBetween(String tenantId, Date beginDate, Date endDate) {
        return chSyncDataManager.deleteBetween(tenantId, beginDate, endDate);
    }

    public List<SyncDataEntity> listSyncDataLimit1000(String tenantId, String lastId) {
        List<SyncDataEntity> result = new ArrayList<>();
        MongoCollection<SyncDataEntity> collection = store.getOrCreateCollection(tenantId);
        if (StringUtils.isNotBlank(lastId)) {
            collection.find()
                    .filter(lt("_id", new ObjectId(lastId)))
                    .skip(0)
                    .limit(1000)
                    .sort(Sorts.orderBy(Sorts.descending("_id")))
                    .into(result);
        } else {
            collection.find()
                    .skip(0)
                    .limit(1000)
                    .sort(Sorts.orderBy(Sorts.descending("_id")))
                    .into(result);
        }
        return result;
    }
}
