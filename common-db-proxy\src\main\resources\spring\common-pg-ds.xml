<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <!-- 数据库路由配置 -->
    <bean id="myBatisRoutePolicy" class="com.facishare.paas.pod.mybatis.MyBatisRoutePolicy">
        <property name="biz" value="ERPSD"/>
        <property name="application" value="fs-erp-sync-data"/>
        <property name="dialect" value="postgresql"/>
        <!-- 控制pgbouncer和从库，不指定默认是paas,如果需要请自行指定，一般一个业务一个就可以，如paas,bi,不同应用可共用 -->
        <property name="grayConfig" value="erpSyncData"/>
    </bean>

    <!-- 数据库配置 默认走的druid数据源，可能版本原因，建部分范围唯一索引会失败-->
    <bean id="myBatisDataSource" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="tenantPolicy" ref="myBatisRoutePolicy"/>
        <property name="configName" value="erp-sync-data-all"/>
    </bean>

    <!-- 数据库配置 hikari连接池，用于执行建表索引语句-->
    <bean id="hikariDataSource" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="tenantPolicy" ref="myBatisRoutePolicy"/>
        <property name="configName" value="erp-sync-data-all"/>
        <property name="connectionPoolDriver" value="hikari"/>
    </bean>
</beans>