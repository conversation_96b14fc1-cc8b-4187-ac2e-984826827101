package com.fxiaoke.open.erpsyncdata.monitor.model;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/30
 */
@Data
public class CompareObjectMapping {
    private String tenantId;
    /**
     * 比对策略名称
     */
    private String compareName;
    private String sourceObjApiName;
    /**
     * 用于获取数据时过滤
     */
    private String sourceFilterString;
    private DataSourceType sourceType;
    private String destObjApiName;
    private DataSourceType destType;
    /**
     * key:ea,value:userId列表
     */
    private LinkedHashMap<String, List<Integer>> alertUserMap = new LinkedHashMap<>();
    /**
     * 对比策略
     */
    private CompareStrategy compareStrategy;

    private Dict sourceDataSourceInfo;

    private Dict destDataSourceInfo;

    /**
     * 分页大小
     */
    private Integer limit;
}
