package com.fxiaoke.open.erpsyncdata.dbproxy.config

import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil
import groovy.util.logging.Slf4j
import spock.lang.Shared
import spock.lang.Specification

import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executor
import java.util.concurrent.TimeUnit

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
class ExecutorConfigTest extends Specification {
    @Shared
    def executorConfig = new ExecutorConfig()

    def "测试 syncDataExecutor 方法的线程执行"() {
        given: "创建一个 CountDownLatch 用于等待任务完成"
        def latch = new CountDownLatch(1)
        def executedThreadName = ""

        when: "提交一个任务到 executor"
        def traceId = TraceUtil.initTraceWithFormat("83952")
        def traceIdInThread = ""
        log.info("traceId1:{}", traceId)
        Executor executor = executorConfig.syncDataExecutor()
        executor.execute({
            executedThreadName = Thread.currentThread().name
            traceIdInThread = TraceUtil.get()
            log.info("traceId2:{}", traceIdInThread)
            latch.countDown() // 任务完成时释放 latch
        })

        then: "等待任务完成"
        latch.await(1, TimeUnit.SECONDS) // 等待最多 1 秒

        and: "验证任务在正确的线程中执行"
        executedThreadName.startsWith("SyncDataAsync") // 验证线程名称
        traceId == traceIdInThread
    }
}
