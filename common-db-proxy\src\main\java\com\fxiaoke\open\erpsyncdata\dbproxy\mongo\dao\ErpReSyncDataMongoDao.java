package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.MongoStore;
import com.google.common.collect.Lists;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/15
 * @Desc:
 */
@Slf4j
@Component
public class ErpReSyncDataMongoDao {
    @Autowired
    private MongoStore mongoStore;

    public ErpReSyncData getByDataId(String tenantId, String objApiName, String dataId, String ployDetailId) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenantId", tenantId));
        filters.add(Filters.eq("objApiName", objApiName));
        filters.add(Filters.eq("dataId", dataId));
        if(StringUtils.isNotBlank(ployDetailId)){
            filters.add(Filters.eq("ployDetailId", ployDetailId));
        }
        FindIterable<ErpReSyncData> limit = this.getMongoCollection(tenantId).find(Filters.and(filters)).limit(1);
        if (limit.iterator().hasNext()) {
            return limit.iterator().next();
        } else {
            return null;
        }
    }

    public List<ErpReSyncData> listByTenantIdAndTypeAndStatusAndUpdateTime(String tenantId, Integer type,
                                                                           Integer status, Long updateTime,
                                                                           Integer offset, Integer limit) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenantId", tenantId));
        filters.add(Filters.eq("type", type));
        filters.add(Filters.eq("status", status));
        filters.add(Filters.lt("updateTime", updateTime));
        FindIterable<ErpReSyncData> list = this.getMongoCollection(tenantId).find(Filters.and(filters)).skip(offset).limit(limit);
        return Lists.newArrayList(list);
    }

    /**
     * 覆盖旧的
     *
     * @param tenantId
     * @param updated
     */
    public void updateErpReSyncData(String tenantId, ErpReSyncData updated) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("_id", updated.getId()));
        this.getMongoCollection(tenantId).findOneAndReplace(Filters.and(filters), updated);
    }

    public Long updateStatusByIdAndOldStatus(String tenantId, ObjectId id, Integer status, Integer oldStatus, Long updateTime) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("_id", id));
        filters.add(Filters.eq("status", oldStatus));

        List<Bson> updateList = Lists.newArrayList();
        updateList.add(Updates.set("updateTime", updateTime));
        updateList.add(Updates.set("status", status));
        Bson updates = Updates.combine(updateList);

        UpdateResult updateResult = this.getMongoCollection(tenantId).updateOne(Filters.and(filters), updates);
        return updateResult.getMatchedCount();
    }

    public Long deleteByTypeAndApiName(String tenantId, Integer type, String objApiName) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("tenantId", tenantId));
        filters.add(Filters.eq("type", type));
        filters.add(Filters.eq("objApiName", objApiName));
        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteMany(Filters.and(filters));
        return deleteResult.getDeletedCount();
    }

    public MongoCollection<ErpReSyncData> getMongoCollection(String tenantId) {
        return mongoStore.getOrCreateErpReSyncDataCollectionAndIndex(tenantId);
    }

    public Integer insert(String tenantId, ErpReSyncData entity) {
        if (entity == null) {
            return 0;
        }
        this.getMongoCollection(tenantId).insertOne(entity);
        return 1;
    }

    public Integer batchInsert(String tenantId, List<ErpReSyncData> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }
        this.getMongoCollection(tenantId).insertMany(entities);
        return entities.size();
    }

    public Long deleteById(String tenantId, ObjectId id) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.eq("_id", id));
        DeleteResult deleteResult = this.getMongoCollection(tenantId).deleteOne(Filters.and(filters));
        return deleteResult.getDeletedCount();
    }

    public Long updateTime(String tenantId, List<ObjectId> objectIds, Long updateTime) {
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in("_id", objectIds));
        List<Bson> updateList = Lists.newArrayList();
        updateList.add(Updates.set("updateTime", updateTime));
        Bson updates = Updates.combine(updateList);
        UpdateResult updateResult = this.getMongoCollection(tenantId).updateMany(Filters.and(filters), updates);
        return updateResult.getMatchedCount();
    }
}
