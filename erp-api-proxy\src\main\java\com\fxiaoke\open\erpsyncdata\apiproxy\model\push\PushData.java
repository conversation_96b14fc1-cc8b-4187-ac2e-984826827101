package com.fxiaoke.open.erpsyncdata.apiproxy.model.push;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准格式数据
 */
@Data
public class PushData {
    /**
     * 是否直接同步,默认false
     */
    private boolean directSync;

    /**
     * 如果是 同步推送 必须赋值
     */
    private String destObjApiName;
    /**
     * 需要处理的数据,支持新增修改和作废，如果是同步推送，只能放一个类型的一条数据。
     */
    private Map<EventTypeEnum, List<StandardData>> dataMap = new LinkedHashMap<>();


    public Integer countData() {
        try {
            return dataMap.values().stream().filter(v -> v != null).map(List::size).reduce(Integer::sum).orElse(0);
        } catch (Exception e) {
            return 0;
        }
    }

    @NotNull
    public String extraFirstObjApiName() {
        try {
            return dataMap.values().stream()
                    .filter(v -> v != null && !v.isEmpty())
                    .findAny().map(v -> v.get(0))
                    .map(v -> v.getObjAPIName())
                    .orElse("unknown");
        } catch (Exception e) {
            return "unknown";
        }
    }
}
