package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.BIDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BIFieldDescArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.BIDateRangeEnum;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@ToString
public class DataScreenLog {

    @BsonId
    private ObjectId id;

    //企业id
    private String tenantId;

    //数据中心id
    private String screenMd5;

    private String dateRangeEnum;

    private String dashboardEnum;

    private BIDataResult biDataResult;

    private Date createTime;

    private Date updateTime;


}
