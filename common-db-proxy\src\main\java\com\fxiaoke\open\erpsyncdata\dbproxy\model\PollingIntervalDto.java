package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.DayLimitType;
import lombok.Data;

import java.util.List;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.DayLimitType.EVERY_DAY;

/**
 * <AUTHOR>
 * @Date: 18:50 2021/1/25
 * @Desc:轮询时间
 */
@Data
public class PollingIntervalDto {
    /**
     * 天的限定，每天，周几，每月几号
     */
    private DayLimitType dayLimitType = EVERY_DAY;
    /**
     * cron表达式
     */
    private String cronExpression;
    /**
     * 限定值，当{@link #dayLimitType}为{@link DayLimitType#DAY_OF_MONTH}或{@link DayLimitType#DAY_OF_WEEK}时使用
     * */
    private List<Integer> limitValues;

    //间隔分钟数
    private Integer minutes;
    //每天开始时间
    private String startDataTime;
    //每天结束时间
    private String endDataTime;

}
