package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("updateEmployeeMapping")
public class UpdateEmployeeMappingServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private EmployeeMappingService employeeMappingService;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("UpdateEmployeeMappingServiceImpl.arg="+arg);
        JSONObject params = JSONObject.parseObject(arg.getParams());
        if(params == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        String dataCenterId = params.getString("dataCenterId");
        if(StringUtils.isEmpty(dataCenterId)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        EmployeeMappingResult employeeMappingResult = JSONObject.parseObject(params.getString("employeeData"),EmployeeMappingResult.class);
        if(employeeMappingResult == null || employeeMappingResult.getFsEmployeeId() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        employeeMappingResult.setDataCenterId(dataCenterId);

        if(StringUtils.isEmpty(employeeMappingResult.getId())) {
            List<ErpFieldDataMappingEntity> entityList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                    .listNoSearch2(arg.getTenantId(),
                            employeeMappingResult.getDataCenterId(),
                            ErpFieldTypeEnum.employee,
                            employeeMappingResult.getFsEmployeeId() + "",
                            null);
            log.info("UpdateEmployeeMappingServiceImpl.entityList="+entityList);
            if(CollectionUtils.isEmpty(entityList)) {
                return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(),I18NStringEnum.s3667);
            }
            employeeMappingResult.setId(entityList.get(0).getId());
            log.info("UpdateEmployeeMappingServiceImpl.employeeMappingResult="+employeeMappingResult);
        }

        return employeeMappingService.bindEmployeeMappingByFsId(arg.getTenantId(),1000,employeeMappingResult);
    }
}
