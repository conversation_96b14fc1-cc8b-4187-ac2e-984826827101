package com.fxiaoke.open.erpsyncdata.dbproxy.util.interceptorUtil;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityField;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/4/23 17:38
 * @Version 1.0
 */
@Slf4j
public class SecurityUtil {

    /**
     * 根据这个秘钥得到一个aes对象
     */
    @Getter
    private final static AES aes = SecureUtil.aes(Base64.decode("aEsva0zDHECg47P8SuPzmw=="));


    /**
     * 加密
     *
     * @param paramsObject mapper中paramsType的实例
     * @return T
     * @throws IllegalAccessException 字段不可访问异常
     */
    public static void encrypt(Object paramsObject) throws Exception {
        if (!ConfigCenter.ENCRYPT_INTERCEPTOR) {//false则不拦截
            return;
        }
        Class<?> parameterObjectClass = paramsObject.getClass();
        Field[] declaredFields = parameterObjectClass.getDeclaredFields();
        Field channel = parameterObjectClass.getDeclaredField("channel");
        channel.setAccessible(true);
        ErpChannelEnum channelEnum = null;
        //先查出channel
        for (Field field : declaredFields) {
            if (field.getName().equals("channel") && field.getType() == ErpChannelEnum.class) {
                field.setAccessible(true);
                Object object = field.get(paramsObject);
                channelEnum = (ErpChannelEnum) object;
            }
        }
        if (channelEnum == null) {
            //不应该走到
            throw new ErpSyncDataException(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), "update erpConnect not allow channel is null", null, null);
        }
        for (Field field : declaredFields) {
            SecurityField sensitiveField = field.getAnnotation(SecurityField.class);
            if (!Objects.isNull(sensitiveField)) {
                field.setAccessible(true);
                Object object = field.get(paramsObject);
                //暂时只实现String类型的加密
                if (object instanceof String) {
                    //根据渠道类型转换成paramsType.找到对应的实体类上有需要加密的字段
                    if (ObjectUtils.isNotEmpty(object)) {
                        String value = (String) object;
                        if (ObjectUtils.isNotEmpty(channelEnum.getClassName())) {
                            Object chanelObj = GsonUtil.fromJson(value, channelEnum.getClassName());
                            Field[] declaredFields1 = channelEnum.getClassName().getDeclaredFields();
                            for (Field convertField : declaredFields1) {
                                SecurityField itemField = convertField.getAnnotation(SecurityField.class);
                                if (ObjectUtils.isNotEmpty(itemField)) {
                                    convertField.setAccessible(true);
                                    Object itemObject = convertField.get(chanelObj);
                                    if (ObjectUtils.isNotEmpty(itemObject)) {
                                        //这个才是字段
                                        if (itemObject instanceof SystemParams) {
                                            //对值加密，对key无法加密（不然反序列化不了）
                                            for (Map.Entry<String, String> entry : ((SystemParams) itemObject).entrySet()) {
                                                entry.setValue(safeEncrypt(entry.getValue()));
                                            }
                                        } else {
                                            //加密  这里我使用自定义的AES加密工具、
                                            String encryptValue = safeEncrypt(itemObject.toString());
                                            convertField.set(chanelObj, encryptValue);
                                        }
                                    }

                                }
                            }
                            value = GsonUtil.toJson(chanelObj);
                            field.set(paramsObject, value);
                        }

                    }
                }
            }
        }
    }

    private static String safeEncrypt(String itemValue) {
        try {
            return aes.encryptBase64(itemValue);
        } catch (Exception e) {
            log.debug("encrypt value fail:{}", e.getMessage());
        }
        return itemValue;
    }

    /**
     * 解密
     *
     * @param result resultType的实例
     * @throws IllegalAccessException 字段不可访问异常
     */
    public static void decrypt(Object result) throws IllegalAccessException {
        //取出resultType的类
        ErpChannelEnum erpChannelEnum = null;
        Class<?> resultClass = result.getClass();
        Field[] declaredFields = resultClass.getDeclaredFields();
        //先查出channel
        for (Field field : declaredFields) {
            if (field.getName().equals("channel") && field.getType() == ErpChannelEnum.class) {
                field.setAccessible(true);
                Object object = field.get(result);
                erpChannelEnum = (ErpChannelEnum) object;
            }
        }
        if (erpChannelEnum == null || erpChannelEnum.getClassName() == null) {
            //只有CRM连接器走到
            return;
        }
        for (Field field : declaredFields) {
            //取出所有被EncryptDecryptField注解的字段
            SecurityField sensitiveField = field.getAnnotation(SecurityField.class);
            if (sensitiveField == null || field.getType() != String.class) {
                //非加密字段 或者不是String类型字段
                continue;
            }
            field.setAccessible(true);
            String params = (String) field.get(result);
            if (StrUtil.isEmpty(params)) {
                continue;
            }
            //根据渠道类型转换成paramsType.找到对应的实体类上有需要加密的字段
            //为了不破坏数据，仅修改要修改的字段
            JsonElement json = JsonParser.parseString(params);
            if (!json.isJsonObject()) {
                continue;
            }
            JsonObject jsonObj = (JsonObject) json;
            Field[] paramsFields = erpChannelEnum.getClassName().getDeclaredFields();
            for (Field paramsField : paramsFields) {
                SecurityField securityField = paramsField.getAnnotation(SecurityField.class);
                if (securityField == null) {
                    continue;
                }
                String fieldName = paramsField.getName();
                JsonElement jsonElement = jsonObj.get(fieldName);
                if (jsonElement == null) {
                    continue;
                }
                if (String.class.isAssignableFrom(paramsField.getType()) && jsonElement.isJsonPrimitive()) {
                    //string类型
                    String securityValue = jsonElement.getAsString();
                    //解密
                    String decryValue = safeDecrypt(securityValue);
                    jsonObj.addProperty(fieldName, decryValue);
                }
                if (SystemParams.class.isAssignableFrom(paramsField.getType()) && jsonElement.isJsonObject()) {
                    //SystemParams类型
                    JsonObject newObj = new JsonObject();
                    jsonElement.getAsJsonObject().asMap().forEach((k, v) -> {
                        newObj.addProperty(k, safeDecrypt(v.getAsString()));
                    });
                    jsonObj.add(fieldName, newObj);
                }
            }
            //设置entity的属性
            field.set(result, jsonObj.toString());
        }
    }

    private static String safeDecrypt(String itemStr) {
        try {
            return aes.decryptStr(itemStr);
        } catch (Exception e) {
            log.warn("decrypt value fail:{}",e.getMessage());
        }
        return itemStr;
    }
}
