<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushDataDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushDataEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="object_api_name" jdbcType="VARCHAR" property="objectApiName"/>
        <result column="source_data_id" jdbcType="VARCHAR" property="sourceDataId"/>
        <result column="source_data" jdbcType="VARCHAR" property="sourceData"/>
        <result column="standard_format" jdbcType="VARCHAR" property="standardFormat"/>
        <result column="operation_type" jdbcType="SMALLINT" property="operationType"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        tenant_id,
        data_center_id,
        object_api_name,
        source_data_id,
        source_data,
        standard_format,
        operation_type,
        create_time,
        update_time
    </sql>

    <!--  <insert id="bachInsertErpPushData"  parameterType="java.util.List">
            INSERT INTO "public"."erp_push_data"("id", "tenant_id", "object_api_name", "source_data_id", "source_data", "standard_format", "operation_type", "create_time", "update_time")
             VALUES
          <foreach collection="list" separator="," item="val">
            (#{val.id}, #{val.tenantId},#{val.objectApiName},#{val.sourceDataId},#{val.sourceData},#{val.standardFormat},#{val.operationType},#{val.createTime},#{val.updateTime})
          </foreach>
      </insert>-->

    <select id="listErpObjDataByTime" resultType="java.lang.String">
        select standard_format
        from erp_push_data t where t.tenant_id = #{tenantId}
                               and t.object_api_name = #{objectApiName}
                               and t.update_time >= #{startTime}
                               and t.update_time &lt; #{endTime}
                               and t.standard_format is not null
                               and t.source_data_id is not null
                               and operation_type in
        <foreach collection="operationTypes" item="type" index="index" open="(" close=")" separator=",">
            #{type}
        </foreach>
        limit #{limit} offset #{offset}
    </select>

    <select id="findByTenantObjectId" resultMap="BaseResultMap">
        select *
        from erp_push_data t
        where t.tenant_id = #{tenantId}
          and t.object_api_name = #{objectApiName}
          and t.source_data_id = #{sourceDataId}
          and t.data_center_id = #{dataCenterId}
    </select>
    <delete id="deleteErpDataByCreateAndUpdateTime">
        delete
        from erp_push_data
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and object_api_name = #{objApiName,jdbcType=VARCHAR}
          and create_time &lt; #{createTime,jdbcType=BIGINT}
          and update_time &lt; #{updateTime,jdbcType=BIGINT}
    </delete>
    <select id="listByTenantIdLimit1000" resultType="java.lang.String">
        select
        id
        from erp_push_data
        where
        tenant_id = #{tenantId,jdbcType=VARCHAR}
        limit 1000;
    </select>
    <delete id="deleteByIdIn">
        delete from erp_push_data
        where
        id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>