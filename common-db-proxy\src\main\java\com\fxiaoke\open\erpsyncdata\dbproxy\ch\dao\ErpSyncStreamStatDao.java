package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ch.ErpSyncStreamStat;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DownstreamRelationManage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
@Deprecated
public interface ErpSyncStreamStatDao {
    ErpSyncStreamStat getLatestTraceIdAndTime(@Param("upTenantId") String upTenantId, @Param("dcId") String dcId, @Param("minCreateTime") Date minCreateTime);


    List<DownstreamRelationManage> calDownstreamStat(@Param("upTenantId") String upTenantId,
                                                     @Param("dcId") String dcId,
                                                     @Param("traceId") String traceId,
                                                     @Param("minCreateTime") Date minCreateTime,
                                                     @Param("limit") int limit,
                                                     @Param("offset") int offset,
                                                     @Param("orderBy") String orderBy);



    int countDownstreamStat(@Param("upTenantId") String upTenantId,
                                                     @Param("dcId") String dcId,
                                                     @Param("traceId") String traceId,
                                                     @Param("minCreateTime") Date minCreateTime);

    /**
     * 获取警告的所有企业
     */
    List<String> getAlertDownstreamIds(@Param("upTenantId") String upTenantId,
                                       @Param("dcId") String dcId,
                                       @Param("traceId") String traceId,
                                       @Param("minCreateTime") Date minCreateTime);
    /**
     * 获取异常的所有企业
     */
    List<String> getFailDownstreamIds(@Param("upTenantId") String upTenantId,
                                      @Param("dcId") String dcId,
                                      @Param("traceId") String traceId,
                                      @Param("minCreateTime") Date minCreateTime);
}
