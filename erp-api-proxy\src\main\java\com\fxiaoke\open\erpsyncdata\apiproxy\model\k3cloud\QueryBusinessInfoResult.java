package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/17
 */
@Data
public class QueryBusinessInfoResult {

    public enum EntityType {
        HeadEntity,
        EntryEntity,
        SubHeadEntity,
        SubEntryEntity,
    }
    @SerializedName("Result")
    @JsonProperty("Result")
    private Result result;

    @Data
    public static class Result {
        @SerializedName("ResponseStatus")
        @JsonProperty("ResponseStatus")
        private ResponseStatus responseStatus;

        @SerializedName("NeedReturnData")
        @JsonProperty("NeedReturnData")
        private BusinessInfo needReturnData;

    }

    @NoArgsConstructor
    @Data
    public static class BusinessInfo {
        @JsonProperty("Id")
        private String id;
        @JsonProperty("MasterPKFieldName")
        private String masterPkFieldName;
        @JsonProperty("PkFieldName")
        private String pkFieldName;
        @JsonProperty("PkFieldType")
        private String pkFieldType;
        @JsonProperty("Name")
        private List<NameBean> name;
        @JsonProperty("Entrys")
        private List<EntrysBean> entrys;
        @JsonProperty("Operations")
        private List<OperationsBean> operations;

        @NoArgsConstructor
        @Data
        public static class NameBean {
            /**
             * Key : 2052
             * Value : 客户
             */

            @JsonProperty("Key")
            private int key;
            @JsonProperty("Value")
            private String value;
        }

        @NoArgsConstructor
        @Data
        public static class EntrysBean {
            @JsonProperty("Id")
            private String id;
            @JsonProperty("Key")
            private String key;
            @JsonProperty("TableName")
            private String tableName;
            @JsonProperty("ParentKey")
            private String parentKey;
            @JsonProperty("EntryName")
            private String entryName;
            @JsonProperty("EntryPkFieldName")
            private String entryPkFieldName;
            @JsonProperty("PkFieldType")
            private String pkFieldType;
            @JsonProperty("EntityType")
            private String entityType;
            @JsonProperty("SeqFieldKey")
            private Object seqFieldKey;
            @JsonProperty("Name")
            private List<NameBean> name;
            @JsonProperty("Fields")
            private List<FieldsBean> fields;

            @NoArgsConstructor
            @Data
            public static class FieldsBean {
                /**
                 * Key : FDocumentStatus
                 * Name : [{"Key":2052,"Value":"单据状态"},{"Key":1033,"Value":"Doc Status"},{"Key":3076,"Value":"單據狀態"}]
                 * FieldName : FDOCUMENTSTATUS
                 * PropertyName : DocumentStatus
                 * FieldType : 167
                 * EntityKey : FBillHead
                 * TableName : T_BD_CUSTOMER
                 * ElementType : 40
                 * MustInput : 0
                 * LookUpObjectFormId : null
                 * EnumObjectId : null
                 * Extends : [{"Value":"Z","Caption":"暂存","Seq":0,"Invalid":false},{"Value":"A","Caption":"创建","Seq":2,"Invalid":false},{"Value":"B","Caption":"审核中","Seq":3,"Invalid":false},{"Value":"C","Caption":"已审核","Seq":4,"Invalid":false},{"Value":"D","Caption":"重新审核","Seq":5,"Invalid":false}]
                 * ControlFieldKey : null
                 * GroupFieldTableName : null
                 */

                @JsonProperty("Key")
                private String key;
                @JsonProperty("FieldName")
                private String fieldName;
                @JsonProperty("PropertyName")
                private String propertyName;
                @JsonProperty("FieldType")
                private int fieldType;
                @JsonProperty("EntityKey")
                private String entityKey;
                @JsonProperty("TableName")
                private String tableName;
                @JsonProperty("ElementType")
                private int elementType;
                @JsonProperty("MustInput")
                private int mustInput;
                @JsonProperty("LookUpObjectFormId")
                private String lookUpObjectFormId;
                @JsonProperty("EnumObjectId")
                private Object enumObjectId;
                @JsonProperty("ControlFieldKey")
                private Object controlFieldKey;
                @JsonProperty("GroupFieldTableName")
                private Object groupFieldTableName;
                @JsonProperty("Name")
                private List<NameBean> name;
                @JsonProperty("Extends")
                private List<ExtendsBean> extendValues;

                @NoArgsConstructor
                @Data
                public static class ExtendsBean {
                    /**
                     * Value : Z
                     * Caption : 暂存
                     * Seq : 0
                     * Invalid : false
                     */

                    @JsonProperty("Value")
                    private String value;
                    @JsonProperty("Caption")
                    private String caption;
                    @JsonProperty("Seq")
                    private int seq;
                    @JsonProperty("Invalid")
                    private boolean invalid;
                }
            }
        }

        @NoArgsConstructor
        @Data
        public static class OperationsBean {
            /**
             * OperationId : 0
             * OperationNumber : Delete
             * OperationName : [{"Key":2052,"Value":"删除"}]
             */

            @JsonProperty("OperationId")
            private int operationId;
            @JsonProperty("OperationNumber")
            private String operationNumber;
            @JsonProperty("OperationName")
            private List<NameBean> operationName;
        }
    }


}
