package com.fxiaoke.open.erpsyncdata.dbproxy.redis;

import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/5/12
 **/

@Slf4j
public class RedisDataSource {

    private MergeJedisCmd jedisCmd;

    /**从监控上看不出调用redis是集中在哪里，上报clickhouse也没有合适的地方. 因此这里先简单粗暴的统计一下调用来源。
     * 不需要精确，不需要考虑并发。
     **/
    private HashMap<String, Long> cntRedisCallMap = Maps.newHashMap();
    //设置为1，则开始统计redis调用方调用次数
    private static int cntRedisCallSwitch = 0;

    public MergeJedisCmd get() {
        try {
            //直接从异常堆栈取调用方类
            String className = new Throwable().getStackTrace()[1].getClassName();
            countCallRedisTimes(className);
        }catch (Throwable ignore){}
        return jedisCmd;
    }

    //为了更方便跟踪redis的调用方，要使用下面这个方法。
    public MergeJedisCmd get(String redisVisitoerName) {
        countCallRedisTimes(redisVisitoerName);
        return jedisCmd;
    }

    public static void setCntRedisCallSwitch(int status) {
        cntRedisCallSwitch=status;
        log.info("setCntRedisCallSwitch, cntRedisCallSwitch:{}", cntRedisCallSwitch);
    }
    private void countCallRedisTimes(String redisVisitoerName) {
        try {
            if (1 == cntRedisCallSwitch) {
                cntRedisCallMap.putIfAbsent(redisVisitoerName, 1L);
                Long old = cntRedisCallMap.get(redisVisitoerName);
                old += 1;
                if (old >= 20) {
                    log.info("rediscnt {}", redisVisitoerName);
                    old = 0L;
                }
                cntRedisCallMap.put(redisVisitoerName, old);
            }
        }catch (Exception e) {

        }
    }

    public void setJedisCmd(MergeJedisCmd jedisCmd) {
        this.jedisCmd = jedisCmd;
    }

    public Long incrAndExpire(String redisKey, long counts, long expireSeconds,String redisVisitorName) {
        String luaScript = "local q=redis.call('incrBy',KEYS[1],ARGV[1])  if tostring(q) == ARGV[1] then redis.call('expire',KEYS[1],ARGV[2]) return q else return q end";
        Object obj = get(redisVisitorName).eval(luaScript, 1, redisKey, String.valueOf(counts), String.valueOf(expireSeconds));
        return Long.parseLong(obj.toString());
    }

    /**
     * sadd,成功添加时刷新过期时间。
     * @param redisKey
     * @param value
     * @param expireSeconds
     * @return
     */
    public Long saddAndExpire(String redisKey, String value, long expireSeconds,String redisVisitorName) {
        String luaScript = "local q=redis.call('sadd',KEYS[1],ARGV[1])  if q == 1 then redis.call('expire',KEYS[1],ARGV[2]) return q else return q end";
        Object obj = get(redisVisitorName).eval(luaScript, 1, redisKey, value, String.valueOf(expireSeconds));
        return Long.parseLong(obj.toString());
    }


    /**
     * 当key不存在时或不存在超时时间时初始化值和超时时间。
     * @param redisKey
     * @param value
     * @param expireSeconds
     * @return 成功设置时返回OK，已经存在key时返回SET
     */
    public String initKey(String redisKey, String value, long expireSeconds,String redisVisitorName) {
        String luaScript = "local q=redis.call('ttl',KEYS[1])  if q<0 then  return redis.call('set',KEYS[1],ARGV[1],'EX' ,ARGV[2]) else return 'SET' end";
        Object obj = get(redisVisitorName).eval(luaScript, 1, redisKey, value, String.valueOf(expireSeconds));
        return String.valueOf(obj);
    }

    /**
     * 扫描键
     * @param keyPattern
     * @return
     */
    public Set<String> scanKeys( String keyPattern) {
        ScanParams scanParams = new ScanParams();
        scanParams.match(keyPattern);
        scanParams.count(10000);
        String cursor = ScanParams.SCAN_POINTER_START;
        Set<String> keys = new HashSet<>();
        for (int i = 0; i < 20; i++) {
            //最多扫20次吧
            ScanResult<String> scanResult = jedisCmd.scan(cursor, scanParams);
            keys.addAll(scanResult.getResult());
            cursor = scanResult.getCursor();
            if (cursor.equals(ScanParams.SCAN_POINTER_START)) {
                break;
            }
        }
        return keys;
    }

    public boolean isAllow(String key, Long limit, Long expireSeconds) {
        String luaScript =
                "local key = KEYS[1] " +
                        "local limit = tonumber(ARGV[1]) " +
                        "local expireSeconds = tonumber(ARGV[2]) " +
                        "local current = tonumber(redis.call('get', key) or '0') " +
                        "if current + 1 > limit then " +
                        "return 0 " + // 返回0表示没有增加计数，因为已经超过限制
                        "else " +
                        "redis.call('INCR', key) " +
                        "if current == 0 then " +
                        "redis.call('EXPIRE', key, expireSeconds) " + // 使用传入的过期时间
                        "end " +
                        "return 1 " + // 返回1表示成功增加计数
                        "end";

        // 调用eval，传入过期时间作为ARGV的第三个参数
        Object result = get().eval(luaScript, 1, key, String.valueOf(limit), String.valueOf(expireSeconds));
        return Integer.parseInt(result.toString()) == 1;
    }
}
