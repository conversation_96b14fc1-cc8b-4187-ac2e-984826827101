<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpK3UltimateTokenDao">

  <select id="findByToken" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateTokenEntity">
    select * from erp_k3_ultimate_token
    where
    tenant_id=#{tenantId} and token=#{token}
  </select>

  <select id="findData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateTokenEntity">
    select * from erp_k3_ultimate_token
    where tenant_id=#{tenantId}
    and data_center_id=#{dataCenterId}
    and erp_obj_api_name=#{erpObjApiName}
    and version=#{version}
  </select>
</mapper>