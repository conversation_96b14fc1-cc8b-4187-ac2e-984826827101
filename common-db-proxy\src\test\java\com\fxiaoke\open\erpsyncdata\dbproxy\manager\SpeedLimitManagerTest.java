package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.github.jedis.support.MergeJedisCmd;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class SpeedLimitManagerTest {

    @InjectMocks
    private SpeedLimitManager speedLimitManager;

    @Mock
    private RedisDataSource redisDataSource;

    @Mock
    private TenantConfigurationManager configurationManager;

    @Mock
    private ConfigCenterConfig configCenterConfig;

    @Mock
    private MergeJedisCmd redisTemplate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(redisDataSource.get(anyString())).thenReturn(redisTemplate);
        when(redisDataSource.get()).thenReturn(redisTemplate);
    }

    @Test
    void testCountAndCheck_WhenCountsIsNull_ShouldReturnTrue() {
        // 执行测试

        Boolean result = speedLimitManager.countAndCheck("90896", SpeedLimitTypeEnum.TO_ERP, 1l,false);

        // 验证结果
        assertTrue(result);
        verify(redisTemplate, never()).eval(anyString(), anyList(), anyList());
    }

    @Test
    void testCountAndCheck_WhenCountsIsZero_ShouldReturnTrue() {
        // 执行测试
        Boolean result = speedLimitManager.countAndCheck("testKey", SpeedLimitTypeEnum.TO_CRM, 0L,false);

        // 验证结果
        assertTrue(result);
        verify(redisTemplate, never()).eval(anyString(), anyList(), anyList());
    }

    @Test
    void testCountAndCheck_WhenCountsIsPositive_ShouldIncrementAndCheck() {
        // 准备测试数据
        String key = "testKey";
        SpeedLimitTypeEnum type = SpeedLimitTypeEnum.TO_CRM;
        Long counts = 1L;

        // 模拟Redis行为
        when(redisTemplate.eval(anyString(), anyList(), anyList())).thenReturn(1L);
        when(configurationManager.getDoubleConfig(type.getConfigType(), type.getDefaultIntervalS(), "interval")).thenReturn(type.getDefaultIntervalS());
        when(configurationManager.getDoubleConfig(type.getConfigType(), type.getDefaultTps(), key, "default")).thenReturn(type.getDefaultIntervalS());

        // 执行测试
        Boolean result = speedLimitManager.countAndCheck(key, type, counts,false);

        // 验证结果
        assertFalse(result);
        verify(redisTemplate).eval(anyString(), anyList(), anyList());
    }

    @Test
    void testCountAndSleep_WhenNotOverSpeed_ShouldNotSleep() {
        // 准备测试数据
        String key = "testKey";
        SpeedLimitTypeEnum type = SpeedLimitTypeEnum.TO_CRM;
        Long counts = 1L;

        // 模拟Redis行为
        when(redisTemplate.eval(anyString(), anyList(), anyList())).thenReturn(1L);

        // 执行测试
        speedLimitManager.countAndSleep(key, type, counts);

        // 验证结果
        verify(redisTemplate).eval(anyString(), anyList(), anyList());
    }

    @Test
    void testGetRemainLimit_WhenKeyExists_ShouldCalculateRemaining() {
        // 准备测试数据
        String key = "testKey";
        SpeedLimitTypeEnum type = SpeedLimitTypeEnum.TO_CRM;

        // 模拟Redis行为
        when(redisTemplate.get(anyString())).thenReturn("5");

        // 执行测试
        long result = speedLimitManager.getRemainLimit(key, type);

        // 验证结果
        assertTrue(result >= 0);
    }
}