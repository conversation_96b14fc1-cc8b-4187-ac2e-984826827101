package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ManageGroupAddTaskEntity;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.ImmutableMap;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/3/13 11:58:19
 */
@Repository
public class ManageGroupAddTaskDao extends BaseDao<ManageGroupAddTaskEntity> {
    @Override
    @Autowired
    public void setDatastore(DatastoreExt erpSyncDataMongoStore) {
        super.setDatastore(erpSyncDataMongoStore);
    }

    public ManageGroupAddTaskEntity queryByGroupId(String tenantId, String groupId) {
        final Query<ManageGroupAddTaskEntity> query = createQuery(ImmutableMap.of("tenant_id", tenantId, "group_id", groupId));
        query.order("-update_time");
        return query.get();
    }

    public void finishTask(String tenantId, String taskId) {
        final Query<ManageGroupAddTaskEntity> query = createQuery(ImmutableMap.of("tenant_id", tenantId, "id", taskId));
        final UpdateOperations<ManageGroupAddTaskEntity> update = createUpdate(ImmutableMap.of("status", ManageGroupAddTaskEntity.STATUS_END, "update_time", System.currentTimeMillis()));
        datastore.findAndModify(query, update);
    }
}
