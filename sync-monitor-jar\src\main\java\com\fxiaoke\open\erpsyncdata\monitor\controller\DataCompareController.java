package com.fxiaoke.open.erpsyncdata.monitor.controller;

import com.fxiaoke.open.erpsyncdata.monitor.manager.SyncDataTimeOutMappingManager;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.CompareArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.CompareTaskArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.CompareResult;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataCompareService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/10
 */
@RestController
@Slf4j
@RequestMapping("dataCompare")
public class DataCompareController {
    @Autowired
    private DataCompareService dataCompareService;
    @Autowired
    private SyncDataTimeOutMappingManager syncDataTimeOutMappingManager;

    @PostMapping("compare")
    public Result<CompareResult> compare(@RequestBody CompareArg arg){
        //刷新缓存
        refreshTask(arg.getMapping().getTenantId());
        Result<CompareResult> compareResultResult = dataCompareService.doCompare(arg);
        return compareResultResult;
    }


    @PostMapping("compareAndAlert")
    public Result<CompareResult> compareAndAlert(@RequestBody CompareArg arg){
        //刷新缓存
        refreshTask(arg.getMapping().getTenantId());
        Result<CompareResult> compareResultResult = dataCompareService.compareAndAlert(arg);
        return compareResultResult;
    }


    @PostMapping("executeCompareTask")
    public Result<Void> executeCompareTask(@RequestBody CompareTaskArg arg){
        Result<Void> compareResultResult = dataCompareService.compareAndAlert(arg.getTenantId(), arg.getCompareName());
        return compareResultResult;
    }


    @GetMapping("refreshTask")
    public Result<CompareResult> refreshTask(@RequestParam(required = false) String  tenantId){
        //刷新任务
        dataCompareService.refreshTask(tenantId);
        return Result.newSuccess();
    }

    @PostMapping("recoverStatus")
    public Result<Void> recoverStatus(@RequestBody ErpIdArg arg){
        syncDataTimeOutMappingManager.recoverStatus(arg.getTenantId());
        return Result.newSuccess();
    }
}
