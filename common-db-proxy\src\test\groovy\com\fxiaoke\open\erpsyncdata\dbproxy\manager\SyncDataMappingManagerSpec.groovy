package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.alibaba.fastjson.JSON
import com.alicp.jetcache.Cache
import com.alicp.jetcache.CacheManager
import com.fxiaoke.open.erpsyncdata.common.constant.SpuSkuConstant
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingCountGroupVo
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingCountVo
import com.fxiaoke.open.erpsyncdata.dbproxy.model.data.GetOrCreateByTwoWayData
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RecycleBinDao
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2024/10/23 14:38:02
 */
class SyncDataMappingManagerSpec extends Specification {
    Cache<String, MappingCountGroupVo> countCache = Mock()
    I18NStringManager i18NStringManager = Mock()
    SyncDataMappingsDao syncDataMappingsDao = Mock()
    IdGenerator idGenerator = Mock()
    SyncPloyDetailManager syncPloyDetailManager = Mock()
    RecycleBinDao recycleBinDao = Mock()
    SyncDataFixDao syncDataFixDao = Mock()
    TenantConfigurationManager tenantConfigurationManager = Mock()
    CacheManager cacheManager = Mock()
    RedissonClient redissonClient = Mock() {
        getLock(*_) >> Mock(RLock) {
            tryLock() >> true
        }
    }


    SyncDataMappingManager syncDataMappingManager = new SyncDataMappingManager(
            i18NStringManager: i18NStringManager
            ,
            syncDataMappingsDao: syncDataMappingsDao
            ,
            idGenerator: idGenerator
            ,
            syncPloyDetailManager: syncPloyDetailManager
            ,
            recycleBinDao: recycleBinDao
            ,
            syncDataFixDao: syncDataFixDao
            ,
            tenantConfigurationManager: tenantConfigurationManager
            ,
            cacheManager: cacheManager
            ,
            redissonClient: redissonClient
    )

    void setup() {
        cacheManager.getOrCreateCache(*_) >> countCache
        syncDataMappingManager.init()
        syncDataMappingsDao.setTenantId(*_) >> syncDataMappingsDao
    }


    @Unroll
    def "count By Stream Id Limit1000 where streamId=#streamId and tenantId=#tenantId and syncStatus=#syncStatus then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.countByObjectApiNamesOnlyLimit1000(*_) >> 1l
        syncPloyDetailManager.getEntryById(*_) >> new SyncPloyDetailEntity(detailObjectMappings: [
                new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "soan", destObjectApiName: "doan")] as DetailObjectMappingsData)

        expect:
        syncDataMappingManager.countByStreamIdLimit1000(tenantId, streamId, syncStatus) == expectedResult

        where:
        streamId   | tenantId | syncStatus || expectedResult
        "streamId" | "88521"  | 0          || 2l
    }

    @Unroll
    def "count By Stream Id where streamId=#streamId and tenantId=#tenantId and syncStatus=#syncStatus then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.countByObjectApiNames(*_) >> 0
        syncPloyDetailManager.getEntryById(*_) >> new SyncPloyDetailEntity(detailObjectMappings: [new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "soan", destObjectApiName: "doan")] as DetailObjectMappingsData)

        expect:
        syncDataMappingManager.countByStreamId(tenantId, streamId, syncStatus) == expectedResult

        where:
        streamId   | tenantId | syncStatus || expectedResult
        "streamId" | "88521"  | 0          || 0l
    }

    @Unroll
    def "count By Obj Limit1000 where tenantId=#tenantId and destObjApiName=#destObjApiName and syncStatus=#syncStatus and sourceObjApiName=#sourceObjApiName then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.countByObjectApiNamesOnlyLimit1000(*_) >> 1l

        expect:
        syncDataMappingManager.countByObjLimit1000(tenantId, sourceObjApiName, destObjApiName, syncStatus) == expectedResult

        where:
        tenantId | destObjApiName   | syncStatus | sourceObjApiName   || expectedResult
        "88521"  | "destObjApiName" | 0          | "sourceObjApiName" || 1l
    }

    @Unroll
    def "count All Group Vo where onlyCache=#onlyCache and tenantId=#tenantId and forceRefresh=#forceRefresh then expect: #expectedResult"() {
        given:
        countCache.get(_) >> cacheGroupVo
        syncDataMappingsDao.countGroup(*_) >> [new MappingCountVo()]

        expect:
        syncDataMappingManager.countAllGroupVo(tenantId, onlyCache, forceRefresh) == expectedResult

        where:
        cacheGroupVo                                                 | onlyCache | tenantId | forceRefresh || expectedResult
        new MappingCountGroupVo.MappingCountGroupVoBuilder().build() | true      | "88521"  | true         || cacheGroupVo
        null                                                         | false     | "88521"  | false        || null
    }

    @Unroll
    def "recycle where tenantId=#tenantId and syncDataMappingsEntity=#syncDataMappingsEntity"() {
        given:
        syncDataMappingsDao.deleteByTenantIdAndId(*_) >> 0
        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["destRoute": "dr"]
        def tenantId = "88521"

        when:
        syncDataMappingManager.recycle(tenantId, new SyncDataMappingsEntity())

        then:
        1 * syncDataMappingsDao.setTenantId("dr") >> syncDataMappingsDao
        1 * syncDataMappingsDao.setTenantId(tenantId) >> syncDataMappingsDao
    }

    @Unroll
    def "recycle Batch where tenantId=#tenantId and ids=#ids"() {
        given:
        syncDataMappingsDao.listByIds(*_) >> [new SyncDataMappingsEntity()]
        syncDataMappingsDao.deleteByTenantIdAndIds(*_) >> 0

        syncDataFixDao.deleteWithLogicBySourceDatas(*_) >> 0
        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["destRoute": "dr"]

        when:
        syncDataMappingManager.recycleBatch(tenantId, ids)

        then:
        1 * syncDataMappingsDao.setTenantId("dr") >> syncDataMappingsDao
        2 * syncDataMappingsDao.setTenantId(tenantId) >> syncDataMappingsDao

        where:
        tenantId | ids
        "88521"  | ["ids"]
    }

    @Unroll
    def "recycle Batch Mapping where syncDataMappingsEntities=#syncDataMappingsEntities and tenantId=#tenantId"() {
        given:
        syncDataMappingsDao.deleteByTenantIdAndIds(*_) >> 0

        syncDataFixDao.deleteWithLogicBySourceDatas(*_) >> 0
        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["destRoute": "dr"]


        def tenantId = "88521"
        when:
        syncDataMappingManager.recycleBatchMapping(tenantId, [new SyncDataMappingsEntity()])

        then:
        1 * syncDataMappingsDao.setTenantId("dr") >> syncDataMappingsDao
        1 * syncDataMappingsDao.setTenantId(tenantId) >> syncDataMappingsDao
    }

    @Unroll
    def "#id-existByTwoWay"() {
        given:
        syncDataMappingsDao.getByUninKey(*_) >> get
        syncDataMappingsDao.getByUninKeyReverse(*_) >> reverse

        syncPloyDetailManager.checkStreamExist(*_) >> exist

        expect:
        syncDataMappingManager.existByTwoWay("88521", "sourceObjectApiName", "sourceDataId", "destObjectApiName") == result

        where:
        id              | get                                          | reverse                                      | exist || result
        'reverse'       | null                                         | new SyncDataMappingsEntity(isCreated: false) | true  || false
        'reverse exist' | null                                         | new SyncDataMappingsEntity(isCreated: true)  | true  || true

        'get exist'     | new SyncDataMappingsEntity(isCreated: true)  | _                                            | _     || true
        'get'           | new SyncDataMappingsEntity(isCreated: false) | _                                            | _     || false

        'not exist'     | null                                         | _                                            | false  | false
    }

    @Unroll
    def "getMapping2Way"() {
        given:
        syncDataMappingsDao.getByUninKey(*_) >> get
        syncDataMappingsDao.getByUninKeyReverse(*_) >> reverse

        syncPloyDetailManager.checkStreamExist(*_) >> true

        when:
        def way = syncDataMappingManager.getMapping2Way("88521", "88521", "sourceObjectApiName", "sourceDataId", '88521', "destObjectApiName")

        then:
        way.getKey() == get
        way.getValue() == reverse

        where:
        id              | get                                          | reverse                                      | exist
        'reverse'       | null                                         | new SyncDataMappingsEntity(isCreated: false) | true
        'reverse exist' | null                                         | new SyncDataMappingsEntity(isCreated: true)  | true

        'get exist'     | new SyncDataMappingsEntity(isCreated: true)  | null                                         | _
        'get'           | new SyncDataMappingsEntity(isCreated: false) | null                                         | _

        'not exist'     | null                                         | null                                         | false
    }

    @Unroll
    def "get Or Create Sync Data Mapping By Two Way where destTenantId=#destTenantId and isCreate=#isCreate and destObjectApiName=#destObjectApiName and syncDataId=#syncDataId and tenantId=#tenantId and sourceData=#sourceData and sourceMasterId=#sourceMasterId and status=#status then expect: #expectedResult"() {
        given:
        def source = new SyncDataMappingsEntity(isCreated: false)
        def reverse = new SyncDataMappingsEntity(isCreated: true)
        syncDataMappingsDao.getByUninKey(*_) >> source
        syncDataMappingsDao.getByUninKeyReverse(*_) >> reverse

        syncDataMappingsDao.updateCreatedById(*_) >> 1
        syncDataMappingsDao.insertIgnore(*_) >> 1

        idGenerator.get(*_) >> "getResponse"
        syncPloyDetailManager.checkStreamExist(*_) >> true

        expect:
        syncDataMappingManager.getOrCreateSyncDataMappingByTwoWay("88521", "syncDataId", 0, "destObjectApiName", "88521", new ObjectData(object_describe_api_name: SpuSkuConstant.MULTI_UNIT_RELATED_OBJ), true, "sourceMasterId") == new GetOrCreateByTwoWayData(create: false, sourcedData: source, destData: reverse)
    }


    @Unroll
    def "getOrCreateSyncDataMappingByTwoWay"() {
        given:
        def source = new SyncDataMappingsEntity(isCreated: false)
        def reverse = new SyncDataMappingsEntity(isCreated: true)
        1* syncDataMappingsDao.getByUninKey(*_) >> null
        syncDataMappingsDao.getByUninKeyReverse(*_) >> reverse

        syncDataMappingsDao.updateCreatedById(*_) >> 1
        syncDataMappingsDao.insertIgnore(*_) >> 0

        idGenerator.get(*_) >> "getResponse"
        syncPloyDetailManager.checkStreamExist(*_) >> true
        1* syncDataMappingsDao.getByUninKey(*_) >> source

        expect:
        syncDataMappingManager.getOrCreateSyncDataMappingByTwoWay("88521", "syncDataId", 0, "destObjectApiName", "88521", new ObjectData(object_describe_api_name: SpuSkuConstant.MULTI_UNIT_RELATED_OBJ), true, "sourceMasterId") == new GetOrCreateByTwoWayData(create: false, sourcedData: source, destData: reverse)
    }

    @Unroll
    def "create Sync Data Mapping where destTenantId=#destTenantId and destObjectApiName=#destObjectApiName and syncDataId=#syncDataId and destDataId=#destDataId and tenantId=#tenantId and mappingIsCreated=#mappingIsCreated and sourceData=#sourceData and destDataName=#destDataName and sourceMasterId=#sourceMasterId and status=#status then expect: #expectedResult"() {
        given:
        def entity = new SyncDataMappingsEntity()
        syncDataMappingsDao.getByUninKey(*_) >> entity
        syncDataMappingsDao.insertIgnore(*_) >> 0

        idGenerator.get(*_) >> "getResponse"

        expect:
        syncDataMappingManager.createSyncDataMapping(tenantId, syncDataId, status, destObjectApiName, destTenantId, destDataId, destDataName, sourceData, mappingIsCreated, sourceMasterId) == new GetOrCreateByTwoWayData(sourcedData: entity)

        where:
        destTenantId   | destObjectApiName   | syncDataId   | destDataId   | tenantId | mappingIsCreated | sourceData       | destDataName   | sourceMasterId   | status
        "destTenantId" | "destObjectApiName" | "syncDataId" | "destDataId" | "88521"  | Boolean.TRUE     | new ObjectData(object_describe_api_name: SpuSkuConstant.MULTI_UNIT_RELATED_OBJ) | "destDataName" | "sourceMasterId" | 0
    }

    @Unroll
    def "list Mappings After Time And Id where maxTime=#maxTime and sourceDataId=#sourceDataId and destDataId=#destDataId and sourceDataName=#sourceDataName and remark=#remark and needReturnTotalNum=#needReturnTotalNum and destDataName=#destDataName and objectApiNameMappingData=#objectApiNameMappingData and maxId=#maxId and tenantId=#tenantId and limit=#limit and startTime=#startTime and status=#status then expect: #expectedResult"() {
        given:
        def entity = new SyncDataMappingsEntity()
        syncDataMappingsDao.getById(*_) >> entity
        syncDataMappingsDao.getBySourceData(*_) >> entity
        syncDataMappingsDao.getByDestData(*_) >> entity
        syncDataMappingsDao.countByObjectApiNames2Limit1000(*_) >> 0
        syncDataMappingsDao.listByObjectApiNames2(*_) >> [entity]

        tenantConfigurationManager.inWhiteList(*_) >> Boolean.TRUE
        def tenantId = "88521"


        def result = syncDataMappingManager.listMappingsAfterTimeAndId(tenantId, new SyncObjectAndTenantMappingData(), 1, 1l, "maxId", 0, "sourceDataId", "destDataId", "sourceDataName", "destDataName", "remark", 1l, true)

        def page = new Page<SyncDataMappingsEntity>(data: [entity, entity, entity], totalNum: 3)
        expect:
        JSON.toJSONString(result) == JSON.toJSONString(page)
    }

    @Unroll
    def "search By Id where searchId=#searchId and tenantId=#tenantId and objectApiNameMappingDatas=#objectApiNameMappingDatas then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.getById(*_) >> new SyncDataMappingsEntity()
        syncDataMappingsDao.getBySourceData(*_) >> new SyncDataMappingsEntity()
        syncDataMappingsDao.getByDestData(*_) >> new SyncDataMappingsEntity()


        expect:
        syncDataMappingManager.searchById(tenantId, searchId, objectApiNameMappingDatas) == expectedResult

        where:
        searchId   | tenantId | objectApiNameMappingDatas              || expectedResult
        "searchId" | "88521"  | [new SyncObjectAndTenantMappingData()] || [new SyncDataMappingsEntity(), new SyncDataMappingsEntity(), new SyncDataMappingsEntity()]
    }

    @Unroll
    def "update Last Sync Status By Id where tenantId=#tenantId and remark=#remark and id=#id and status=#status then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.updateLastSyncStatusById(*_) >> 0


        expect:
        syncDataMappingManager.updateLastSyncStatusById(tenantId, id, status, remark) == expectedResult

        where:
        tenantId | remark   | id   | status || expectedResult
        "88521"  | "remark" | "id" | 0      || 0
    }

    @Unroll
    def "update Source Data Id By Id where sourceDataId=#sourceDataId and tenantId=#tenantId and id=#id then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.updateSourceDataIdById(*_) >> 0

        expect:
        syncDataMappingManager.updateSourceDataIdById(tenantId, id, sourceDataId) == expectedResult

        where:
        sourceDataId   | tenantId | id   || expectedResult
        "sourceDataId" | "88521"  | "id" || 0
    }

    @Unroll
    def "update Dest Data Id By Id where destDataId=#destDataId and tenantId=#tenantId and id=#id then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.updateDestDataIdById(*_) >> 0

        expect:
        syncDataMappingManager.updateDestDataIdById(tenantId, id, destDataId) == expectedResult

        where:
        destDataId   | tenantId | id   || expectedResult
        "destDataId" | "88521"  | "id" || 0
    }

    @Unroll
    def "delete By Tenant Id And Id where tenantId=#tenantId and id=#id then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteByTenantIdAndId(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteByTenantIdAndId(tenantId, id) == expectedResult

        where:
        tenantId | id   || expectedResult
        "88521"  | "id" || 0
    }

    @Unroll
    def "delete By Tenant Id And Ids where tenantId=#tenantId and ids=#ids then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteByTenantIdAndIds(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteByTenantIdAndIds(tenantId, ids) == expectedResult

        where:
        tenantId | ids     || expectedResult
        "88521"  | ["ids"] || 0
    }

    @Unroll
    def "delete Sync Data Mappings where tenantId=#tenantId and destObjApiName=#destObjApiName and sourceObjApiName=#sourceObjApiName then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteSyncDataMappings(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteSyncDataMappings(tenantId, sourceObjApiName, destObjApiName) == expectedResult

        where:
        tenantId | destObjApiName   | sourceObjApiName   || expectedResult
        "88521"  | "destObjApiName" | "sourceObjApiName" || 0
    }

    @Unroll
    def "delete Mappings By Dest Id where sourceObjectApiName=#sourceObjectApiName and dataId=#dataId and destObjectApiName=#destObjectApiName and tenantId=#tenantId then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteMappingsByDestId(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteMappingsByDestId(tenantId, dataId, sourceObjectApiName, destObjectApiName) == expectedResult

        where:
        sourceObjectApiName   | dataId   | destObjectApiName   | tenantId || expectedResult
        "sourceObjectApiName" | "dataId" | "destObjectApiName" | "88521"  || 0
    }

    @Unroll
    def "delete Mappings By Source Id where sourceObjectApiName=#sourceObjectApiName and dataId=#dataId and destObjectApiName=#destObjectApiName and tenantId=#tenantId then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteMappingsBySourceId(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteMappingsBySourceId(tenantId, dataId, sourceObjectApiName, destObjectApiName) == expectedResult

        where:
        sourceObjectApiName   | dataId   | destObjectApiName   | tenantId || expectedResult
        "sourceObjectApiName" | "dataId" | "destObjectApiName" | "88521"  || 0
    }

    @Unroll
    def "delete Mappings By Master Id where sourceObjectApiName=#sourceObjectApiName and destObjectApiName=#destObjectApiName and tenantId=#tenantId and masterDataId=#masterDataId then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteMappingsByMasterId(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteMappingsByMasterId(tenantId, masterDataId, sourceObjectApiName, destObjectApiName) == expectedResult

        where:
        sourceObjectApiName   | destObjectApiName   | tenantId | masterDataId   || expectedResult
        "sourceObjectApiName" | "destObjectApiName" | "88521"  | "masterDataId" || 0
    }

    @Unroll
    def "delete By Ids where tenantId=#tenantId and ids=#ids then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.deleteByIds(*_) >> 0

        tenantConfigurationManager.needBidirectionalWriting(*_) >> ["needBidirectionalWritingResponse": "needBidirectionalWritingResponse"]

        expect:
        syncDataMappingManager.deleteByIds(tenantId, ids) == expectedResult

        where:
        tenantId | ids     || expectedResult
        "88521"  | ["ids"] || 0
    }

    @Unroll
    def "get Last Sync Data where sourceObjectApiName=#sourceObjectApiName and offset=#offset and destObjectApiName=#destObjectApiName and tenantId=#tenantId and limit=#limit then expect: #expectedResult"() {
        given:
        syncDataMappingsDao.getLastSyncData(*_) >> [new SyncDataMappingsEntity()]


        expect:
        syncDataMappingManager.getLastSyncData(tenantId, sourceObjectApiName, destObjectApiName, offset, limit) == expectedResult

        where:
        sourceObjectApiName   | offset | destObjectApiName   | tenantId | limit || expectedResult
        "sourceObjectApiName" | 0      | "destObjectApiName" | "88521"  | 0     || [new SyncDataMappingsEntity()]
    }
}
