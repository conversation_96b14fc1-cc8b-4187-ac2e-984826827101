package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.JsonStringCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.SyncDataCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseExpireIndexLogMongoStore;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataMonitoredException;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Repository
@Slf4j
public class SyncDataMongoStore extends BaseExpireIndexLogMongoStore<SyncDataEntity> {
    @Autowired
    private I18NStringManager i18NStringManager;

    protected final static String SyncDataCollectionPrefix = "sync_data_";

    protected SyncDataMongoStore() {
        super(SingleCodecHolder.codecRegistry);
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(CodecRegistries.fromCodecs(new SyncDataCodec(), new JsonStringCodec<>(MapListStringData.class), new JsonStringCodec<>(ObjectData.class)), MongoClientSettings.getDefaultCodecRegistry());
    }

    @Override
    public String getCollName(String tenantId) {
        if (tenantId == null) {
            throw new ErpSyncDataMonitoredException(I18NStringEnum.s114, tenantId);
        }
        return SyncDataCollectionPrefix + tenantId;
    }

    @Override
    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> toBeCreate = Lists.newArrayList();
        //过期自动清理时间,15天
        Bson idxExpire = expireIndex();
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time").expireAfter(getExpireDay(tenantId), TimeUnit.DAYS).background(true)));

        Bson idxUpTime = Indexes.compoundIndex(
                Indexes.ascending("status"),
                Indexes.ascending("isDeleted"),
                Indexes.ascending("updateTime"));
        toBeCreate.add(new IndexModel(idxUpTime, new IndexOptions().name("idx_status_updateTime").background(true)));

        Bson idxObjSrcId = Indexes.compoundIndex(
                Indexes.ascending("sourceObjectApiName"),
                Indexes.ascending("destObjectApiName"),
                Indexes.ascending("sourceDataId"));
        toBeCreate.add(new IndexModel(idxObjSrcId, new IndexOptions().name("index_obj_srcid2").background(true)));


        Bson idxObjTime = Indexes.compoundIndex(
                Indexes.ascending("sourceObjectApiName"),
                Indexes.ascending("destObjectApiName"),
                Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxObjTime, new IndexOptions().name("index_source_dest_update_time").background(true)));
        return toBeCreate;
    }

    @Override
    public Bson expireIndex() {
        return Indexes.descending("createTime");
    }
}
