package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.alibaba.fastjson2.JSON;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.model.QueryPolyDetail;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service("queryPolyDetail")
public class QueryPolyDetailServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        QueryPolyDetail.Arg arg = JsonUtil.fromJson(commonArg.getParams(), QueryPolyDetail.Arg.class);
        if (Objects.isNull(arg) || StringUtils.isBlank(arg.getSourceObjApiName()) || StringUtils.isBlank(arg.getDestObjApiName())) {
            return Result.newSystemError(I18NStringEnum.s28);
        }

        final SyncPloyDetailEntity entity = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, arg.getSourceObjApiName(), arg.getDestObjApiName());

        if (Objects.isNull(entity)) {
            return Result.newSystemError(I18NStringEnum.s29);
        }

        final QueryPolyDetail.Result result = JSON.parseObject(JSON.toJSONString(entity), QueryPolyDetail.Result.class);

        return Result.newSuccess(JsonUtil.toJson(result));
    }

}