package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.facishare.open.erp.connertor.sdk.model.Base;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.ErpInterfaceMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ProxyRequest;
import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AspectSpelUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/29 14:43:49
 */
@Aspect
@Component
@Slf4j
public class SaveOverseasInterfaceMonitorAspect {

    private ThreadLocal<Base.MonitorResult> monitorResultThreadLocal = new ThreadLocal<>();

    @Around("execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpOverseasProxyDataManager.*(..))")
    public Object invokeMonitor(ProceedingJoinPoint joinPoint) throws Throwable {
        Long callTime = System.currentTimeMillis();
        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
            return proceed;
        } finally {
            saveFacebookInterfaceMonitor(joinPoint, callTime, proceed);
            monitorResultThreadLocal.remove();
        }
    }

    private void saveFacebookInterfaceMonitor(final ProceedingJoinPoint joinPoint, final Long callTime, final Object proceed) {
        final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        final ErpInterfaceMonitor annotation = signature.getMethod().getAnnotation(ErpInterfaceMonitor.class);
        if (Objects.isNull(annotation)) {
            return;
        }

        final Base.MonitorResult monitorResult = monitorResultThreadLocal.get();
        if (Objects.isNull(monitorResult)) {
            return;
        }

        final StandardEvaluationContext context = AspectSpelUtil.getStandardEvaluationContext(joinPoint, proceed, signature);
        final Object[] args = joinPoint.getArgs();
        final TimeFilterArg timeFilterArg = Arrays.stream(args)
                .filter(arg -> arg instanceof TimeFilterArg)
                .map(arg -> (TimeFilterArg) arg)
                .findFirst().orElse(null);
        final ErpConnectInfoEntity connectInfo = Arrays.stream(joinPoint.getArgs())
                .filter(arg -> arg instanceof ErpConnectInfoEntity)
                .map(arg -> (ErpConnectInfoEntity) arg)
                .findFirst().orElse(null);

        BaseErpDataManager.saveErpInterfaceMonitor(connectInfo.getTenantId(), connectInfo.getId(), AspectSpelUtil.getSpelValue(annotation.objApiName(), context), annotation.type().name(), new ProxyRequest(monitorResult.getUrl(), monitorResult.getHeaderMap(), monitorResult.getParams()), monitorResult.getResponseBody(), monitorResult.getResponseStatus(), callTime, System.currentTimeMillis(), "", TraceUtil.get(), monitorResult.getCost(), timeFilterArg);
    }

    @Around("execution(* com.facishare.open.erp.connertor.service.ConnectorService.*(..))")
    public Object setMonitorResult(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
            return proceed;
        } finally {
            if (Objects.nonNull(proceed) && proceed instanceof Base.MonitorResult) {
                monitorResultThreadLocal.set((Base.MonitorResult) proceed);
            }
        }
    }
}
