package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base;

import com.github.mongo.support.DatastoreExt;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * Created by chenxb on 16-11-1.
 */
@Slf4j
public class BaseDao<T> implements InitializingBean {

    Logger logger = LoggerFactory.getLogger(getClass());

    @Getter
    protected Class<T> entityClazz = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];

    @Setter
    protected DatastoreExt datastore;

    public T newPo() {
        try {
            return entityClazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }


    public BaseDao() {
        try {
            descriptor = Stream.of(PropertyUtils.getPropertyDescriptors(entityClazz.newInstance()))
                    .filter(item -> !item.getName().equals("class"))

                    .collect(Collectors.toMap(item -> {
                        String field = item.getName();

                        return Stream.of(entityClazz.getDeclaredFields())
                                .filter(field1 -> Objects.equals(field1, field))
                                .findAny()
                                .map(field1 -> {
                                    Property property = field1.getAnnotation(Property.class);
                                    if (Objects.nonNull(property) && Objects.nonNull(property.value())) {
                                        return property.value();
                                    }

                                    Id id = field1.getAnnotation(Id.class);
                                    if (Objects.nonNull(id)) {
                                        return "_id";
                                    }

                                    return field1.getName();
                                }).orElse(field);
                    }, item -> item));

        } catch (IllegalAccessException | InstantiationException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    protected void ensureSet(UpdateOperations<T> updateOperations, String key, Object value) {
        if (Objects.isNull(value)) {
            return;
        }
        updateOperations.set(key, value);
    }

    protected DatastoreExt getInstance(String configName) {
        return datastore;
    }

    public T findBy(T obj) {
        return obj2Query(obj).get();
    }

    public List<T> findAllBy(T obj) {
        return obj2Query(obj).asList();
    }

    final Map<String, PropertyDescriptor> descriptor;

    public String create(T po) {
        return datastore.save(po).getId().toString();
    }

    public List<String> create(List<T> pos) {
        Iterable<Key<T>> keyIterable = datastore.insert(pos, null);
        return StreamSupport.stream(keyIterable.spliterator(), false)
                .map(key -> key.getId().toString()).collect(Collectors.toList());
    }

    private T update(T query, T value, boolean createIfMissing) {
        Query<T> q = createQuery();

        UpdateOperations<T> u = createUpdate();
        descriptor.entrySet().forEach(item -> {
            try {
                Object qv = item.getValue().getReadMethod().invoke(query);
                if (Objects.nonNull(qv)) {
                    q.field(item.getKey()).equal(qv);
                }

                Object uv = item.getValue().getReadMethod().invoke(value);
                if (Objects.nonNull(uv)) {
                    //默认追加好像不太合理，容易掉坑
//                    if (uv instanceof List) {
//                        u.addAll(item.getKey(), (List) uv, false);
//                    } else {
                    u.set(item.getKey(), uv);
//                    }
                }
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(e.getMessage(), e);
            }
        });

        return datastore.findAndModify(q, u, false, createIfMissing);
    }

    public T update(T query, T value) {
        return update(query, value, false);
    }

    public T createOrUpdate(T query, T value) {
        return update(query, value, true);
    }

    protected Query<T> obj2Query(T query) {
        Query<T> q = createQuery();
        descriptor.forEach((key, value) -> {
            try {
                Object qv = value.getReadMethod().invoke(query);
                if (Objects.nonNull(qv)) {
                    q.field(key).equal(qv);
                }
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error(e.getMessage(), e);
            }
        });
        return q;
    }

    protected Query<T> createQuery() {
        return datastore.createQuery(entityClazz);
    }

    protected Query<T> createQuery(final String key, final Object value) {
        Query<T> query = createQuery();
        query.field(key).equal(value);
        return query;
    }

    protected Query<T> createQuery(Map<String, Object> map) {
        Query<T> query = createQuery();
        map.forEach((key, value) -> query.field(key).equal(value));
        return query;
    }

    protected Query<T> createQuery(final List<String> keys, final List<Object> values) {
        Query<T> query = createQuery();
        if (keys.size() != values.size()) {
            logger.error("[{}] [createQuery] [keys:{}] [values:{}]", getClass().getSimpleName(), keys, values);
            return null;
        }

        for (int i = 0; i < keys.size(); i++) {
            query.field(keys.get(i)).equal(values.get(i));
        }

        return query;
    }

    protected UpdateOperations<T> createUpdate() {
        return datastore.createUpdateOperations(entityClazz);
    }

    protected UpdateOperations<T> createUpdate(final String key, final Object value) {
        UpdateOperations<T> update = createUpdate();
        update.set(key, value);
        return update;
    }

    protected UpdateOperations<T> createUpdate(Map<String, Object> map) {
        UpdateOperations<T> update = createUpdate();
        map.forEach(update::set);
        return update;
    }

    protected UpdateOperations<T> createUpdate(final List<String> keys, final List<Object> values) {
        UpdateOperations<T> update = createUpdate();
        if (keys.size() != values.size()) {
            logger.error("[{}] [createUpdate] [keys:{}] [values:{}]", getClass().getSimpleName(), keys, values);
            return null;
        }

        for (int i = 0; i < keys.size(); i++) {
            update.set(keys.get(i), values.get(i));
        }

        return update;
    }

    protected long count(final String key, final Object value) {
        return count(datastore.find(entityClazz, key, value));
    }

    protected long count(final Query<T> query) {
        return datastore.getCount(query);
    }

    protected long count(final List<String> keys, final List<Object> values) {
        return datastore.getCount(createQuery(keys, values));
    }

    protected T findOne(final String key, final Object value) {
        return datastore.find(entityClazz, key, value).get();
    }

    protected T findOne(final Map<String, Object> conditions) {
        Query<T> query = createQuery();
        conditions.forEach((key, value) -> query.field(key).equal(value));
        return query.get();
    }

    protected T findOne(final List<String> keys, final List<Object> values) {
        return createQuery(keys, values).get();
    }

    protected List<T> find(final String key, final Object value) {
        return datastore.find(entityClazz, key, value).asList();
    }

    protected List<T> find(final Map<String, Object> conditions) {
        Query<T> query = createQuery();
        conditions.forEach((key, value) -> query.field(key).equal(value));
        return query.asList();
    }

    protected List<T> find(final List<String> keys, final List<Object> values) {
        return createQuery(keys, values).asList();
    }

    protected boolean exists(final String key, final Object value) {
        return exists(datastore.find(entityClazz, key, value));
    }

    protected boolean exists(final Query<T> query) {
        return datastore.getCount(query) > 0;
    }

    public static <V> void ensureset(Consumer<V> callable, V value) {
        if (value == null) {
            return;
        }
        callable.accept(value);
    }

    public void delete(T obj) {
        datastore.delete(obj2Query(obj));
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        datastore.ensureIndexes(entityClazz, true);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchResult<T> {
        int count;
        List<T> dataList;
    }
}
