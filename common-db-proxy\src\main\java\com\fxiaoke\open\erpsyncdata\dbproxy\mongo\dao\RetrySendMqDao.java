package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;


import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.WriteConcern;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;

import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;



/**
 * <AUTHOR>
 * @Date: 15:41 2024/4/7
 * @Desc:
 */

@Slf4j
@Repository
@DependsOn("erpSyncDataMongoStore")
public class RetrySendMqDao {
    private static final String _id = "_id";
    private static final String f_tenantId = "tenantId";
    private static final String f_status = "status";
    private static final String f_mq_msg = "mqMsg";
    private static final String f_createTime = "createTime";
    private static final String f_updateTime = "updateTime";
    private static final String f_retry_type = "retryType";


    private DatastoreExt store;

    private String DATABASE;

    private final Set<String> docCollectionCache = Sets.newConcurrentHashSet();
    private final static String collectionName = "re_try_send_mq";


    RetrySendMqDao(@Qualifier("erpSyncDataLogMongoStore") DatastoreExt store) {
        this.store = store;
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
        store.getMongo().getDatabase(DATABASE)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.equals(collectionName)) {
                        docCollectionCache.add(v);
                    }
                });
    }

    public Long updateStatusById(List<ObjectId> mongoIds, Integer status) {
        List<Bson> updateList = Lists.newArrayList();
        updateList.add(Updates.set(f_updateTime, new Date()));
        updateList.add(Updates.set(f_status, status));
        Bson updates = Updates.combine(updateList);
        UpdateResult updateResult = this.getOrCreateReTrySendMqCollection().updateMany(Filters.in("_id", mongoIds), updates);
        return updateResult.getModifiedCount();
    }

    public List<ReTrySendMq> listRetrySendMqIdList(String ei,String retryType, Long startTime, Long endTime, List<Integer> status, Integer limit,Integer offset) {
        List<Bson> filters = Lists.newArrayList();
        if(ObjectUtils.isNotEmpty(startTime)){
            filters.add(Filters.gt(f_updateTime, new Date(startTime)));
        }
        if(ObjectUtils.isNotEmpty(endTime)){
            filters.add(Filters.lte(f_updateTime, new Date(endTime)));
        }
        if (StringUtils.isNotBlank(ei)) {
            filters.add(Filters.eq(f_tenantId, ei));
        }
        if (StringUtils.isNotBlank(retryType)) {
            filters.add(Filters.eq(f_retry_type, retryType));
        }
        if (status != null) {
            filters.add(Filters.in(f_status, status));
        }

        Bson andFilter = new Document();
        if(CollectionUtils.isNotEmpty(filters)){
            andFilter=Filters.and(filters);
        }
        FindIterable<ReTrySendMq> result = this.getOrCreateReTrySendMqCollection().find(andFilter).limit(limit).skip(offset);
        List<ReTrySendMq> dataList = Lists.newArrayList(result);
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList;
        }
        return Lists.newArrayList();
    }

    public List<ReTrySendMq> listByRetryIds(List<ObjectId> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        List<Bson> filters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ids)) {
            filters.add(Filters.in(_id, ids));
        }

        Bson andFilter = Filters.and(filters);
        FindIterable<ReTrySendMq> result = this.getOrCreateReTrySendMqCollection().find(andFilter);
        List<ReTrySendMq> dataList = Lists.newArrayList(result);
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList;
        }
        return Lists.newArrayList();
    }

    public Long countRetryData(String ei,String retryType, List<Integer> status,Long startTime, Long endTime) {
        List<Bson> filters = Lists.newArrayList();
        if(ObjectUtils.isNotEmpty(startTime)){
            filters.add(Filters.gt(f_updateTime, new Date(startTime)));
        }
        if(ObjectUtils.isNotEmpty(endTime)){
            filters.add(Filters.lte(f_updateTime, new Date(endTime)));
        }
        if (StringUtils.isNotBlank(ei)) {
            filters.add(Filters.eq(f_tenantId, ei));
        }
        if (StringUtils.isNotBlank(retryType)) {
            filters.add(Filters.eq(f_retry_type, retryType));
        }
        if (CollectionUtils.isNotEmpty(status)) {
            filters.add(Filters.in(f_status, status));
        }

        Bson andFilter = new Document();
        if(CollectionUtils.isNotEmpty(filters)){
            andFilter=Filters.and(filters);
        }
        long countDocuments = this.getOrCreateReTrySendMqCollection().countDocuments(andFilter);
        return countDocuments;
    }

    public Long getCollectionCounts() {
        EstimatedDocumentCountOptions options = new EstimatedDocumentCountOptions().maxTime(5, TimeUnit.SECONDS);
        return this.getOrCreateReTrySendMqCollection().estimatedDocumentCount(options);
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(ReTrySendMq.class)
                        .automatic(true).build()));
    }

    public ReTrySendMq getById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        Bson andFilter = Filters.and(Filters.eq(_id, new ObjectId(id)));
        FindIterable<ReTrySendMq> limit = this.getOrCreateReTrySendMqCollection().find(andFilter)
                //加个limit保险
                .limit(1);
        List<ReTrySendMq> dataList = Lists.newArrayList(limit);
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    public void deleteByIds(List<ObjectId> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        MongoCollection<ReTrySendMq> mongoCollection = this.getOrCreateReTrySendMqCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(_id, idList));
        mongoCollection.deleteMany(Filters.and(filters));
    }

    public String createReTrySendMq(ReTrySendMq reTrySendMq) {
        try {
            if (reTrySendMq.getId() == null) {
                reTrySendMq.setId(new ObjectId());
            }
            this.getOrCreateReTrySendMqCollection().withWriteConcern(WriteConcern.JOURNALED).insertOne(reTrySendMq);
            return reTrySendMq.getId().toString();
        } catch (Exception e) {
            log.warn("create retrySend mq faild:{}",reTrySendMq);
        }
        return null;
    }

    public void batchUpsertRetryData(List<ReTrySendMq> reTrySendMqs){
        List<WriteModel<ReTrySendMq>> requests = Lists.newArrayList();
        int size = reTrySendMqs.size();
        StopWatch sw = new StopWatch("batchUpsert-" + collectionName + '[' + size + ']');
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (int i = 0; i < reTrySendMqs.size(); i++) {
            ReTrySendMq dataNodeMsgDoc = reTrySendMqs.get(i);
            UpdateOneModel<ReTrySendMq> updateOneModel = new UpdateOneModel<>(this.updateBy(dataNodeMsgDoc), this.upsert(dataNodeMsgDoc), updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        MongoCollection<ReTrySendMq> collection = this.getOrCreateReTrySendMqCollection();
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
            sw.stop();
            long cost = sw.getTotalTimeMillis();
            if (cost > 5000) {
                log.warn("bulkWrite collection: {}, cost: {}ms, items: {}", collectionName, cost, size);
                log.warn(sw.prettyPrint());
            }
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }

    }
    private Bson updateBy(ReTrySendMq message) {
        Document result = new Document();
        result.put(_id, message.getId());
        return result;
    }
    private Bson upsert(ReTrySendMq message) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();
        Document addToSet = new Document();

        //更新
        updateDoc.append(f_updateTime, new Date());
        //插入
        setOnInsertDoc
                .append(f_tenantId, Objects.requireNonNull(message.getTenantId(), "tenantId cannot be null"))
                .append(f_mq_msg, Objects.requireNonNull(message.getMqMsg(), "f_mq_msg cannot be null"))
                .append(f_retry_type, Objects.requireNonNull(message.getRetryDataEnum(), "f_retry_type cannot be null"))
                .append(f_status, Objects.requireNonNull(message.getStatus(), "f_status cannot be null"))
                .append(f_createTime, new Date());


        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        if (!addToSet.isEmpty()) {
            doc.put("$addToSet", addToSet);
        }
        return doc;
    }

    public MongoCollection<ReTrySendMq> getOrCreateReTrySendMqCollection() {
        MongoCollection<ReTrySendMq> collectionList = store.getMongo().getDatabase(DATABASE).withCodecRegistry(SingleCodecHolder.codecRegistry).getCollection(collectionName, ReTrySendMq.class);
        if (!docCollectionCache.add(collectionName)) {
            return collectionList;
        }
        List<IndexModel> toBeCreate = Lists.newArrayList();

        //过期自动清理时间,3天
        Bson idxExpire = Indexes.descending("createTime");
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(5L, TimeUnit.DAYS).background(true)));

        Bson index_ei = Indexes.compoundIndex(Indexes.ascending("tenantId"));
        toBeCreate.add(new IndexModel(index_ei, new IndexOptions().name("index_ei").background(true)));

        Bson index_time_status = Indexes.compoundIndex(Indexes.ascending("updateTime"), Indexes.ascending("status"));
        toBeCreate.add(new IndexModel(index_time_status, new IndexOptions().name("index_time_status").background(true)));

        List<String> created = collectionList.createIndexes(toBeCreate);
        log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        return collectionList;
    }


}
