## 管理工具说明

### 使用在线编辑器编辑、调试管理页面

1. 启动web项目，配置jvm参数`-Derpdss.skip.super.auth=true`,用于本地跳过superAdmin校验
2. 改造想要修改的json文件
   1. 第一级增加data，设置baseUrl为`erp/syncdata/superadmin`。最后复制回项目里面时，再修改回`..`
   2. 将json内的url上所有的`..`都修改为`${baseUrl}`
3. 下载amis-editor-demo项目 https://github.com/aisuda/amis-editor-demo
4. 修改`amis.config.js`文件的`proxyTable`如下：
   ```javascript
    proxyTable: {
    /**
    * 将含有'/apiTest'路径的api代理到'http://api-test.com.cn'上，
    * 详细使用见 https://www.webpackjs.com/configuration/dev-server/#devserver-proxy
    */
    '/erp/syncdata': {
    target: 'http://localhost:8084', // 不支持跨域的接口根地址
    ws: true,
    changeOrigin: true,
    },
    }
   ```
5. 执行`npm i`和`npm run dev`
6. 进入编辑器后，新建或编辑页面
7. 左边选择源码，把当前项目想要修改的页面json，粘贴进去
8. 尽情修改吧！！！ `(*^▽^*)`